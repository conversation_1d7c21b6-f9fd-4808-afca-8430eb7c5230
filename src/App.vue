<template>
  <router-view />
</template>

<script>
import { MessageBox } from 'element-ui';
import { tryCheckAuditStatus } from './utils/auth';

export default {
  name: 'App',

  data() {
    return {
      audit_status: 'succeed',
      is_audit_n: 0,
      messageBox: ''
    };
  },
  created: function () {
    this.$Message.config({
      duration: 3
    });
    // 移除应用首次初始化时的加载界面
    // let loadingEl = document.getElementById('app-init-loading')
    // setTimeout(() => {
    //   document.body.removeChild(loadingEl);
    // }, 1)
  },

  mounted() {
    // 登录成功后调用
    tryCheckAuditStatus(this); // 立即校验
  },
  methods: {
    isAudit() {
      if (this.messageBox) return;
      this.$api.getAuthenticationInfo().then(res => {
        this.audit_status = res.audit_status;
        if (this.audit_status != 'succeed') {
          this.is_audit_n = Number(this.is_audit_n) + 1;
          sessionStorage.setItem('is_audit_n', this.is_audit_n);
          this.messageBox = MessageBox({
            title: '提示',
            showCancelButton: true,
            confirmButtonText: '去认证',
            cancelButtonText: '取消',
            type: 'warning',
            center: true,
            message: '系统检测到您尚未进行主体认证，若下属诊所产生业务订单将不会对您进行分账。',
            callback: res => {
              if (res == 'confirm') {
                this.$router.push({
                  path: '/setting/subject-auth/index'
                });
              }
            }
          });
        }
      });
    }
  },
  beforeDestroy() {
    // 组件销毁时清除定时器
    if (this.auditInterval) {
      clearInterval(this.auditInterval);
    }
  }
};
</script>
