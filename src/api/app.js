class Method_app{
	/**
	 *
	 * @param {*} extendCls 最终继承类
	 */
	async login(params){
		const res =  await this.$http.post('/pms_opc/index.login', params)
		console.log(res)
		return res
	}
	async getComList(params){
		return await this.$http.get('/pms_opc/index.opcenterlist',{params})
	}
	// 找回密码
	async changeRetrievepass(params) {
		const res = await this.$http.post('/pms_opc/index.retrievepass', params)
		return res
	}
	
	// 密码登录
	async getAccountlogin(params) {
		const res = await this.$http.post('/pms_opc/index.accountlogin', params)
		return res
	}
	
	// 更新密码
	async getChgpass(params) {
		const res = await this.$http.post('/pms_opc/member.chgpass', params)
		return res
	}
	// 修改密码
	async resetPassword(params) {
		return await this.$http.post('/pms_opc/index.resetpassword', params);
	}
}

export default Method_app
