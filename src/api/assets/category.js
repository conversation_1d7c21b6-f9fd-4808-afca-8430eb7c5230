export default class CategoryAPi {
  // 批量编辑分组
  async batchEditMtCategory(data) {
    return await this.$http.post(`/asset-center/v1/category/batch-edit`, data);
  }

  // 创建分组
  async createMtCategory(data) {
    return await this.$http.post(`/asset-center/v1/category/create`, data);
  }

  // 删除分组
  async deleteMtCategory(data) {
    return await this.$http.post('/asset-center/v1/category/del', data);
  }

  // 修改分组
  async updateMtCategory(data) {
    return await this.$http.post(`/asset-center/v1/category/edit`, data);
  }

  // 分组详情
  async getMtCategoryDetail(data) {
    return await this.$http.post(`/asset-center/v1/category/info`, data);
  }

  // 分组全部列表
  async getMtCategoryAllList(data) {
    return await this.$http.post(`/asset-center/v1/category/all`, data);
  }

  // 分组列表
  async getMtCategoryList(data) {
    return await this.$http.post(`/asset-center/v1/category/list`, data);
  }

  // 获取上传token
  async getAssetsUploadToken(params) {
    return await this.$http.get('/asset-center/v1/common/qiniu', { params });
  }
  // 获取首桢图
  async getDetailUrlByCenterUrl(params) {
    return await this.$http.post('/asset-center/v1/asset/video-cover-url', params);
  }
}
