export default class MaterialApi {
  //  素材选项
  getMaterialOptions(params) {
    return this.$http.get('/asset-center/v1/asset/option', { params });
  }

  //  获取素材列表
  getMaterialList(params) {
    return this.$http.post('/asset-center/v1/asset/list', params);
  }

  //  修改素材
  updateMaterial(params) {
    return this.$http.post('/asset-center/v1/asset/edit', params);
  }

  //  删除素材
  deleteMaterial(params) {
    return this.$http.post('/asset-center/v1/asset/delete', params);
  }

  //   修复素材分组
  repairMaterialGroup(params) {
    return this.$http.post('/asset-center/v1/asset/repair', params);
  }
  // 修改素材分组
  updateMaterialCategory(params) {
    return this.$http.post('/asset-center/v1/asset/edit/category', params);
  }
  // 上传网络图片
  uploadNetImg(params) {
    return this.$http.post('/asset-center/v1/asset/fetch-put-img', params);
  }
}
