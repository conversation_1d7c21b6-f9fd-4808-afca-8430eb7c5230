class Method_clinic {
  async getClinicList(params) {
    return await this.$http.get('/pms_opc/clinic.enter.list', { params });
  }
  async getClinicDetail(params) {
    return await this.$http.get('/pms_opc/clinic.enter.info', { params });
  }
  async editClinic(params) {
    return await this.$http.post('/pms_opc/clinic.enter.edit', params);
  }

  /**
   * @description库存
   */
  // 首采出入库列表
  async postStockRrcordList(params) {
    return await this.$http.get('/pms_opc/joinin.stock.recordlist', { params });
  }

  // 首采出入库详情
  async postStockRrcordInfo(params) {
    return await this.$http.get('/pms_opc/joinin.stock.recordinfo', { params });
  }

  // 出入库列表
  async getTStockRrcordList(params) {
    return await this.$http.get('/pms_opc/stock.record.list', { params });
  }

  // 诊所采购-枚举
  async getTStockRrcordOptions(params) {
    return await this.$http.get('/pms_opc/stock.record.options', { params });
  }

  /**
   * @description财务管理
   */
  // 获取枚举类型
  async getFinaceOptions(params) {
    return await this.$http.get('/pms_opc/finance.account.options', { params });
  }

  // 获取明细列表
  async postFinaceList(params) {
    return await this.$http.get('/pms_opc/finance.account.list', { params });
  }

  // 获取明细详情
  async getFinaceInfo(params) {
    return await this.$http.get('/pms_opc/finance.account.info', { params });
  }

  // 添加收入
  async getAccountSave(params) {
    return await this.$http.post('/pms_opc/finance.account.save', params);
  }

  // 新收入统计-列表
  async getFinaceList(params) {
    return await this.$http.get('/pms_opc/finance.income.statisticslist', { params });
  }

  // 新收入统计-导出
  async getIncomeUrl(params) {
    return await this.$http.get('/pms_opc/finance.report.getincomeurl', { params });
  }

  /**
   * @description: 2022.01.08 城市合伙人需求
   * */
  // 城市合伙人列表
  async getPartnerList(params) {
    return await this.$http.get('/pms_opc/clinic.enter.list', { params });
  }

  // 城市合伙人枚举数据
  async getPartnerOptions(params) {
    return await this.$http.get('/pms_opc/clinic.enter.options', { params });
  }

  // 城市合伙人详情数据
  async getPartnerInfo(params) {
    return await this.$http.get('/pms_opc/clinic.enter.info', { params });
  }

  // 城市合伙人新增/编辑
  async getPartnerEdit(params) {
    return await this.$http.post('/pms_opc/clinic.enter.edit', params);
  }

  // 城市合伙人状态变更(审核)
  async getPartnerStatus(params) {
    return await this.$http.post('/pms_opc/clinic.enter.status', params);
  }

  async exportClinicList(params) {
    return await this.$http.get('/pms_opc/clinic.enter.export', { params });
  }
  async getClinicOptions(params) {
    return await this.$http.get('/pms_opc/clinic.enter.options', { params });
  }
  // 诊所禁用启用
  async clinicDisabledAndEnable(params) {
    return this.$http.post('/pms_opc/clinic.enter.method', params);
  }

  // 小程序设置
  async getClinicEnterChangeweapp(params) {
    return this.$http.post('/pms_opc/clinic.enter.changeweapp', params);
  }
}

export default Method_clinic;
