class Method_Department_Manage {
	async getDepartList(params) {
		return this.$http.get('/pms_opc/department.list', { params });
	}

	// 部门范围(可选诊所列表)
	async getDepartClinicsList(params) {
		return this.$http.get('/pms_opc/department.cliniclist', { params });
	}

	// 部门创建编辑
	async saveDepartManage(params) {
		return this.$http.post('/pms_opc/department.save', params);
	}

	// 部门启用禁用状态设置
	async changeDepartManageStatus(params) {
		return this.$http.post('/pms_opc/department.status', params);
	}

	// 部门详情
	async getDepartManageDetail(params) {
		return this.$http.get('/pms_opc/department.show', { params });
	}

	// 部门操作日志
	async getDepartManageLog(params) {
		return this.$http.get('/pms_opc/department.log', { params });
	}

	// 可选部门列表
	async selectableDepartList(params) {
		return this.$http.get('/pms_opc/department.selectList', { params });
	}
}

export default Method_Department_Manage
