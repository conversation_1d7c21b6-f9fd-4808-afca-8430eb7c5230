class Api_balance {

	// 列表
	async getAWList(params) {
		return await this.$http.get('/pms_opc/finance.accountWallet.list', { params });
	}

	// 导出
	async getAWExportList(params) {
		return await this.$http.get('/pms_opc/finance.accountWallet.exportList', { params });
	}

	// 详情
	async getAWDetail(params) {
		return await this.$http.get('/pms_opc/finance.accountWallet.detail', { params });
	}

	// 预付款明细-枚举
	async getAWOptions(params) {
		return await this.$http.get('/pms_opc/finance.accountWallet.options', { params });
	}

	// 详情明细列表/预付款明细列表
	async getAWTxList(params) {
		return await this.$http.get('/pms_opc/finance.accountWallet.txList', { params });
	}

	// 详情交易明细列表导出
	async getAWExportDetailTxList(params) {
		return await this.$http.get('/pms_opc/finance.accountWallet.exportDetailTxList', { params });
	}


	// 预付款明细-导出
	async getAWExportTxList(params) {
		return await this.$http.get('/pms_opc/finance.accountWallet.exportTxList', { params });
	}

	// 查询对方主体名称
	async getAWSearchSide(params) {
		return await this.$http.get('/pms_opc/finance.accountWallet.searchSide', { params });
	}


}

export default Api_balance;
