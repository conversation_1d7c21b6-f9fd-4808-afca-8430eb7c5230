class Api_invoice {
  /**
   * @description: 开票管理(2022.10.22)
   * */
  // 获取开票列表数据
  async getInvoiceList(params) {
    return await this.$http.get('/pms_opc/finance.invoice.list', { params });
  }

  // 获取申请方枚举数据
  async getInvoiceApplicantlist(params) {
    return await this.$http.get('/pms_opc/finance.invoice.applicantlist', { params });
  }

  // 获取开票方枚举数据
  async getInvoiceDrawerList(params) {
    return await this.$http.get('/pms_opc/finance.invoice.drawerList', { params });
  }

  // 发票列表导出
  async exportInvoiceList(params) {
    return await this.$http.get('/pms_opc/finance.invoice.export', { params });
  }

  // 发票申请
  async createInvoiceGenerate(params) {
    return await this.$http.post('/pms_opc/finance.invoice.generate', params);
  }

  // 获取开票信息
  async getInvoiceInfo(params) {
    return await this.$http.post('/pms_opc/setup.getinvoice', params);
  }

  // 更新发票状态
  async UpdateInvoiceUpdateflag(params) {
    return await this.$http.post('/pms_opc/finance.invoice.updateflag', params);
  }

  // 重新提交开票
  async invoiceResubmit(params) {
    return await this.$http.post('/pms_opc/finance.invoice.resubmit', params);
  }

  // 更新开票信息
  async updateInvoiceInfo(params) {
    return await this.$http.post('/pms_opc/setup.invoice', params);
  }

  // 发票详情
  async getInvoiceDetails(params) {
    return await this.$http.get('/pms_opc/finance.invoice.detail', { params });
  }

  // 回显开票人信息
  async echoInvoiceApplicant(params) {
    return await this.$http.get('/pms_opc/finance.invoice.applicant', { params });
  }

  // 采购订单开票详情
  async getInvoicePurchasedetails(params) {
    return await this.$http.get('/pms_opc/finance.invoice.purchasedetail', { params });
  }

  // 获取采购订单
  async getInvoicePurchaseorder(params) {
    return await this.$http.get('/pms_opc/finance.invoice.purchaseorder', { params });
  }

  // 获取发票金额
  async getInvoiceAmount(params) {
    return await this.$http.get('/pms_opc/finance.invoice.getamount', { params });
  }

  // 获取采购订单枚举
  async getInvoicePurOderOptions(params) {
    return await this.$http.get('/pms_opc/finance.invoice.option', { params });
  }

  // 发票商品详情
  async getInvoiceGoodsdetails(params) {
    return await this.$http.get('/pms_opc/finance.invoice.goodsdetails', { params });
  }

  // 导出开票详情
  async getInvoiceExportdetail(params) {
    return await this.$http.get('/pms_opc/finance.invoice.exportdetail', { params });
  }

  /**
   * @description: 2023-05-27
   * */
  // 发票审核
  async getInvoiceAudit(params) {
    return await this.$http.post('/pms_opc/finance.invoice.audit', params);
  }

  // 标记开票
  async setInvoiceFlagInvoice(params) {
    return await this.$http.post('/pms_opc/finance.invoice.flagInvoice', params);
  }

  /**
   * 发票审核信息模块
   * */
  // 发票信息审核列表
  async getInvoiceExamineList(params) {
    return await this.$http.get('/pms_opc/purchase.invoiceExamine.list', { params });
  }

  // 发票信息审核枚举
  async getInvoiceExamineOption(params) {
    return await this.$http.get('/pms_opc/purchase.invoiceExamine.option', { params });
  }

  // 申请方开票信息申请主体
  async getEntList(params) {
    return await this.$http.get('/pms_opc/purchase.invoiceExamine.entList', { params });
  }

  // 发票信息审核详情
  async getInvoiceExamineDetail(params) {
    return await this.$http.get('/pms_opc/purchase.invoiceExamine.detail', { params });
  }

  // 导出诊所列表
  async exportClinicList(params) {
    return await this.$http.get('/pms_opc/clinic.enter.export', { params });
  }
  // 获取发票类型列表
  async getInvoiceTypeList(params) {
    return await this.$http.get('/pms_opc/finance.invoice.invoicetype', { params });
  }
}
export default Api_invoice;
