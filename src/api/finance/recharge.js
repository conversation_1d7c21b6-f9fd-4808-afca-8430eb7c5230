class Api_recharge {

	// 获取枚举
	async getAROptions(params) {
		return await this.$http.get('/pms_opc/finance.accountRecharge.options', { params });
	}

	// 获取列表
	async getARList(params) {
		return await this.$http.get('/pms_opc/finance.accountRecharge.list', { params });
	}

	// 列表导出
	async getARExportList(params) {
		return await this.$http.get('/pms_opc/finance.accountRecharge.exportList', { params });
	}

	// 创建省公司收入
	async getARInEdit(params) {
		return await this.$http.post('/pms_opc/finance.accountRecharge.inEdit', params);
	}

	// 创建省公司支出
	async getAROutEdit(params) {
		return await this.$http.post('/pms_opc/finance.accountRecharge.outEdit', params);
	}

	// 详情
	async getARtDetail(params) {
		return await this.$http.get('/pms_opc/finance.accountRecharge.detail', { params });
	}

	// 获取日志
	async getARGetLog(params) {
		return await this.$http.get('/pms_opc/finance.accountRecharge.getLog', { params });
	}

	// 充值审核
	async getARGetAudit(params) {
		return await this.$http.post('/pms_opc/finance.accountRecharge.audit', params);
	}

}

export default Api_recharge;
