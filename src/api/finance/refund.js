class Api_refund {

	// 获取枚举
	async getARRefundOptions(params) {
		return await this.$http.get('/pms_opc/finance.accountRechargeRefund.options', { params });
	}

	// 获取列表
	async getARRefundList(params) {
		return await this.$http.get('/pms_opc/finance.accountRechargeRefund.list', { params });
	}

	// 列表导出
	async getARRefundExportList(params) {
		return await this.$http.get('/pms_opc/finance.accountRechargeRefund.exportList', { params });
	}

	// 创建编辑
	async getARRefundEdit(params) {
		return await this.$http.post('/pms_opc/finance.accountRechargeRefund.edit', params);
	}

	// 详情
	async getARRefundDetail(params) {
		return await this.$http.get('/pms_opc/finance.accountRechargeRefund.detail', { params });
	}

	// 获取主体钱包余额
	async getAWentBalance(params) {
		return await this.$http.get('/pms_opc/finance.accountWallet.entBalance', { params });
	}

	// 获取日志
	async getARRefundGetLog(params) {
		return await this.$http.get('/pms_opc/finance.accountRechargeRefund.getLog', { params });
	}


	// 退款审核
	async getARRefundAudit(params) {
		return await this.$http.post('/pms_opc/finance.accountRechargeRefund.audit', params);
	}

}

export default Api_refund;
