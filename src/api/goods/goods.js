class Method_goods {
	/**
	 * @description: 服务列表(2022.9.15)
	 * */
	// 获取服务列表数据
	async getGoodsservicelibList(params){
		return await this.$http.get('/pms_opc/goods.libgoodsservice.list', {params})
	}

	// 获取服务类型
	async getGoodsservicelibOptions(params){
		return await this.$http.get('/pms_opc/goods.libgoodsservice.options', {params})
	}

	// 服务状态操作
	async getGoodsservicelibStatus(params){
		return await this.$http.post('/pms_opc/goods.libgoodsservice.status', params)
	}

	// 编辑服务
	async getGoodsservicelibEdit(params){
		return await this.$http.post('/pms_opc/goods.libgoodsservice.edit', params)
	}

	// 获取服务信息 - 暂时不需要此接口
	// async getGoodsservicelibGet(params){
	// 	return await this.$http.post('/pms_opc/goods.libgoodsservice.get', params)
	// }

	/*   货品列表选项  */
	async getGoodsListOptions(){
		return await this.$http.get('pms_opc/product.libprod.options')
	}

	/*   货品列表  */
	async getProductsList(params){
		return await this.$http.get('pms_opc/product.libprod.list',{params})
	}

	/*   货品详情  */
	async getProductsInfo(params){
		return await this.$http.get('pms_opc/product.libprod.info',{params})
	}

	/**
	 * @description: 商品库
	 */

	// 获取商品库列表
	async getGoodsLibList(params){
		return await this.$http.get('/pms_opc/goods.libgoods.list', {params})
	}

	// 编辑商品库
	async changeGoodsLib(params){
		return await this.$http.post('/pms_opc/goods.libgoods.edit', params)
	}

	// 获取商品库选项枚举值
	async getGoodsLibOptions(params){
		return await this.$http.get('/pms_opc/goods.libgoods.options', {params})
	}

	// 复制
	async copyGoodsLib(params){
		return await this.$http.get('pms_opc/goods.libgoods.copy', {params})
	}

	// 获取商品信息
	async getGoodsLibInfo(params){
		return await this.$http.get('pms_opc/goods.libgoods.getinfo', {params})
	}

	// 获取商品库关联货品列表
	async getProdLibList(params){
		return await this.$http.get('/pms_opc/product.libprod.list', {params})
	}

	// 获取商品库操作记录
	async getGoodsOperationlog(params){
		return await this.$http.get('/pms_opc/goods.libgoods.operationlog', {params})
	}

	// 获取商品库操作记录
	async getLogSnapshot(params){
		return await this.$http.get('/pms_opc/goods.libgoods.logsnapshot', {params})
	}

	// 改变商品状态
	async changeGoodsLibStatus(params){
		return await this.$http.post('/pms_opc/goods.libgoods.status', params)
	}

	/*   技能点选项  */
	async getSkillOptions(params) {
		return await this.$http.get('/pms_opc/goods.libgoodsservice.skillOptions', { params });
	}

	/*   获取可以做这个服务的理疗师  */
	async getCanPhysioList(params) {
		return await this.$http.get('/pms_opc/goods.libgoodsservice.physioList', { params });
	}
}
export default Method_goods
