class Manage_goods {
    /**
     * @description: 商品管理(2024.11.25)
     * */
    // 商品列表
    async getGoodsManageList(params) {
        return await this.$http.get('/pms_opc/goods.manager.list', {params})
    }
    // 获取可选诊所列表
    async getGoodsClinicList(params) {
        return await this.$http.get('/pms_opc/goods.manager.selectClinicList', {params})
    }
    // 上架
    async upShelvesGoods(params) {
        return await this.$http.post('/pms_opc/goods.manager.up', params)
    }
    // 下架
    async downShelvesGoods(params) {
        return await this.$http.post('/pms_opc/goods.manager.down', params)
    }
    // 获取模版价格信息
    async getTemplatePriceInfo(params) {
        return await this.$http.get('/pms_opc/goods.manager.templatePriceInfo', { params })
    }
    // 修改价格
    async updateGoodsPrice(params) {
        return await this.$http.post('/pms_opc/goods.manager.changePrice', params)
    }
    // 获取商品信息
    async getGoodsDetailInfo(params) {
        return await this.$http.get('/pms_opc/goods.libgoods.getinfo', { params })
    }
    // 获取商品信息
    async changePriceSelectClinicList(params) {
        return await this.$http.get('/pms_opc/goods.manager.changePriceSelectClinicList', { params })
    }
    // 获取指定销售状态中的诊所列表
    async getClinicListByStatus(params) {
        return await this.$http.get('/pms_opc/goods.manager.statusList', { params })
    }
}
export default Manage_goods;