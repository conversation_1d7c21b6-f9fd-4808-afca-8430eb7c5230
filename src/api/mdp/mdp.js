export default class Mdp {
  // mdp员工列表
  getMdpMemberList(params) {
    return this.$http.get('/pms_opc/mdp.member.list', { params });
  }

  // mdp员工所属诊所搜索
  getMdpMemberSearchCli(params) {
    return this.$http.get('/pms_opc/mdp.member.searchCli', { params });
  }

  // mdp员工所属诊所搜索
  getMdpMemberOptions(params) {
    return this.$http.get('/pms_opc/mdp.member.options', { params });
  }

  // mdp员工编辑
  getMdpMemberEdit(params) {
    return this.$http.post('/pms_opc/mdp.member.edit', params);
  }

  // mdp员工详情
  getMdpMemberInfo(params) {
    return this.$http.get('/pms_opc/mdp.member.info', { params });
  }

  // mdp员工审核
  getMdpMemberAudit(params) {
    return this.$http.post('/pms_opc/mdp.member.audit', params);
  }

  // mdp员工分发
  getMdpMemberDispatch(params) {
    return this.$http.get('/pms_opc/mdp.member.dispatch', { params });
  }

  // mdp创建来源搜索
  getMdpMemberSearchSource(params) {
    return this.$http.get('/pms_opc/mdp.member.searchSource', { params });
  }

  // mdp诊所员工列表
  getMemberClinicMemberList(params) {
    return this.$http.get('/pms_opc/mdp.member.clinicMemberList', { params });
  }

  // mdp诊所员工状态设置
  getMemberClinicMemberStatus(params) {
    return this.$http.get('/pms_opc/mdp.member.clinicMemberStatus', { params });
  }
}
