class Method_customer {
  // 筛选选项
  async getPCOptions(params) {
    return await this.$http.get('/pms_opc/personCustom.personCustom.options', { params });
  }

  // 列表
  async getPCList(params) {
    return await this.$http.get('/pms_opc/personCustom.personCustom.list', { params });
  }

  // 导出
  async getPCExportlist(params) {
    return await this.$http.get('/pms_opc/personCustom.personCustom.exportlist', { params });
  }

  // 禁用/启用
  async getPCDisabled(params) {
    return await this.$http.post('/pms_opc/personCustom.personCustom.disabled', params);
  }

  // 详情
  async getPCDetail(params) {
    return await this.$http.get('/pms_opc/personCustom.personCustom.detail', { params });
  }

  // 新增/编辑
  async getPCEdit(params) {
    return await this.$http.post('/pms_opc/personCustom.personCustom.edit', params);
  }

  // 获取字段变更记录
  async getPCGetfieldlog(params) {
    return await this.$http.get('/pms_opc/personCustom.personCustom.getfieldlog', { params });
  }

  // 根据手机号获取VIP客户信息
  async getUserVipByMobile(params) {
    return await this.$http.get('/pms_opc/uservip.getUserVipByMobile', { params });
  }
}

export default Method_customer;
