class outsideCompany {
  // 获取外部公司列表
  async getOutsideCompanyList(params) {
    return this.$http.get('/pms_opc/outside.outsideCompany.list', { params });
  }

  // 获取外部公司枚举
  async getOutsideCompanyOptions(params) {
    return this.$http.get('/pms_opc/outside.outsideCompany.options', { params });
  }

  // 获取外部公司详情
  async getOutsideCompanyDetail(params) {
    return this.$http.get('/pms_opc/outside.outsideCompany.detail', { params });
  }

  // 获取外部公司详情
  async exportOutsideCompanyList(params) {
    return this.$http.get('/pms_opc/outside.outsideCompany.exportlist', { params });
  }

  // 新增/编辑外部公司
  async editOutsideCompany(params) {
    return this.$http.post('/pms_opc/outside.outsideCompany.edit', params);
  }

  // 禁用/启用外部公司
  async changeOutsideCompanyStatus(params) {
    return this.$http.post('/pms_opc/outside.outsideCompany.disabled', params);
  }

  // 获取外部公司操作记录
  async getOutsideCompanyFieldLog(params) {
    return this.$http.get('/pms_opc/outside.outsideCompany.getfieldlog', { params });
  }

  // 远程搜索外部省公司
  async getExternalCompanyList(params) {
    return await this.$http.get('/pms_opc/common.outsideCompany.list', { params });
  }
}

export default outsideCompany;
