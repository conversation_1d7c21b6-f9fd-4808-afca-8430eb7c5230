export default class Statement_methods {
  sysInfo = {
    sys_code: 'RSJYZT'
  };

  // 获取对账单明细列表
  async getStatementList(params) {
    return await this.$http.get('/pms_opc/yztpay.reconciliation.list', { params: { ...params, ...this.sysInfo } });
  }

  // 导出对账单明细
  //   GET /pms_opc/yztpay.reconciliation.export
  //   接口ID：56565921
  //   接口地址：https://www.apifox.cn/link/project/404094/apis/api-56565921
  async exportStatementList(params) {
    return await this.$http.get('/pms_opc/yztpay.reconciliation.export', { params: { ...params, ...this.sysInfo } });
  }

  // 获取对账单明细详情
  async getStatementInfo(params) {
    return await this.$http.get('/pms_opc/yztpay.reconciliation.info', { params: { ...params, ...this.sysInfo } });
  }
}
