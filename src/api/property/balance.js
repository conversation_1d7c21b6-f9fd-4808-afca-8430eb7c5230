class Balance_methods {
  sysInfo = {
    sys_code: 'RSJYZT'
  };

  // 交易记录列表
  async getBlanceList(params) {
    return await this.$http.get('/pms_opc/yztpay.balance.list', { params: { ...params, ...this.sysInfo } });
  }

  async getBalanceOptions(params) {
    return await this.$http.get('/pms_opc/yztpay.balance.options', { params: { ...params, ...this.sysInfo } });
  }

  // 账户余额
  async getBlanceBlance(params) {
    return await this.$http.get('/pms_opc/yztpay.balance.balance', { params: { ...params, ...this.sysInfo } });
  }

  // 导出账户余额明细
  async getBlanceExport(params) {
    return await this.$http.get('/pms_opc/yztpay.balance.export', { params: { ...params, ...this.sysInfo } });
  }

  // 详情
  async getBlanceInfo(params) {
    return await this.$http.get('/pms_opc/yztpay.balance.info', { params: { ...params, ...this.sysInfo } });
  }
}

export default Balance_methods;
