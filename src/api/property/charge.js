class Charge_methods {
  sysInfo = {
    sys_code: 'RSJYZT'
  };

  // 选项列表
  async getBlanceOptions(params) {
    return await this.$http.get('/pms_opc/yztpay.balance.options', { params: { ...params, ...this.sysInfo } });
  }

  // 充值详情
  async getRechargeInfo(params) {
    return await this.$http.get('/pms_opc/yztpay.recharge.info', { params: { ...params, ...this.sysInfo } });
  }

  // 充值记录
  async getRechargeList(params) {
    return await this.$http.get('/pms_opc/yztpay.recharge.list', { params: { ...params, ...this.sysInfo } });
  }

  // 获取支付枚举
  async getRechargePayOptions(params) {
    return await this.$http.get('/pms_opc/recharge.pay.options', { params: { ...params, ...this.sysInfo } });
  }
}

export default Charge_methods;
