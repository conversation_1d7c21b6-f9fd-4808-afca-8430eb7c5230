export default class Withdrawal_methods {
  sysInfo = {
    sys_code: 'RSJYZT'
  };

  // 获取提现记录
  async getWithdrawalRecordList(params) {
    return await this.$http.get('/pms_opc/yztpay.withdraw.list', { params: { ...params, ...this.sysInfo } });
  }

  // 获取提现手续费
  async getWithdrawalCharge(params) {
    return await this.$http.get('/pms_opc/yztpay.withdraw.getcharge', { params: { ...params, ...this.sysInfo } });
  }

  // 获取对账单明细详情
  async getWithdrawalInfo(params) {
    return await this.$http.get('/pms_opc/yztpay.withdraw.info', { params: { ...params, ...this.sysInfo } });
  }

  // 提交提现申请
  async submitWithdrawal(params) {
    return await this.$http.get('/pms_opc/yztpay.withdraw.submit', { params: { ...params, ...this.sysInfo } });
  }

  // 获取是否认证资格
  async getIsyztopen(params) {
    return await this.$http.get('/pms_opc/yztpay.balance.isyztopen', { params: { ...params, ...this.sysInfo } });
  }
}
