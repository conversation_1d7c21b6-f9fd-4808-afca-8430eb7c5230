class Api_purchase {
  async getOrderList(params) {
    return await this.$http.get('/pms_opc/joinin.package.list', { params });
  }

  // 获取枚举类型
  async getPackageOptions() {
    return await this.$http.get('/pms_opc/joinin.package.options');
  }

  async getCreatInfo(params) {
    return await this.$http.get('/pms_opc/joinin.package.createinfo', { params });
  }

  //
  async getAdditionCreatInfo(params) {
    return await this.$http.get('/pms_opc/joinin.addition.createinfo', { params });
  }

  // 获取明细列表
  async getPackageInfo(params) {
    return await this.$http.get('/pms_opc/joinin.package.info', { params });
  }

  // 获取创建详情
  async createPackage(params) {
    return await this.$http.post('/pms_opc/joinin.package.create', params);
  }

  // 修改采购包
  async editPackage(params) {
    return await this.$http.post('/pms_opc/joinin.package.edit', params);
  }

  // ==============采购单相关===============

  //获取采购单列表
  async getPurchaseList(params) {
    return await this.$http.get('/pms_opc/joinin.purchase.list', { params });
  }

  //获取采购单选项信息
  async getPurchaseOptions() {
    return await this.$http.get('/pms_opc/joinin.purchase.options');
  }

  // 获取采购单信息
  async getPurchaseInfo(params) {
    return await this.$http.get('/pms_opc/joinin.purchase.info', { params });
  }

  //保存采购单(追加采购)
  async saveAdditionPur(params) {
    return await this.$http.post('/pms_opc/joinin.addition.save', params);
  }

  // 创建/编辑 采购单信息
  async editPurchaseOrder(params) {
    return await this.$http.post('/pms_opc/joinin.purchase.save', params);
  }

  // 获取会议活动列表
  async getPromotionInfo(params) {
    return await this.$http.get('/pms_opc/joinin.promotion.list', { params });
  }

  // 获取省公司列表
  async getCommonList(params) {
    return await this.$http.get('/pms_opc/common.opcenter.list', { params });
  }

  // ==============诊所采购单相关===============
  async getSpuList(params) {
    return await this.$http.get('/pms_opc/joinin.package.spulist', { params });
  }

  // 诊所采购单选项枚举
  async getCommonClinicList(params) {
    return await this.$http.get('/pms_opc/common.clinic.list', { params });
  }

  // 供应商选项枚举
  async getCommonSupplierList(params) {
    return await this.$http.get('/pms_opc/common.supplier.list', { params });
  }

  // 诊所采购单选项枚举
  async getClinicOrderOptions(params) {
    return await this.$http.get('/pms_opc/purchase.order.options', { params });
  }

  // 诊所采购单列表
  async getClinicOrderList(params) {
    return await this.$http.get('/pms_opc/purchase.order.listnew', { params });
  }

  // 诊所采购单详情
  async getClinicOrderDetail(params) {
    return await this.$http.get('/pms_opc/purchase.order.detailnew', { params });
  }

  // 诊所采购单支付
  async postPay(params) {
    return await this.$http.post('/pms_opc/purchase.order.pay', params);
  }

  // 开业退款单数据
  async getRefundList(params) {
    return await this.$http.get('/pms_opc/joinin.refund.list', { params });
  }

  // 开业退款单详情
  async getRefundInfo(params) {
    return await this.$http.get('/pms_opc/joinin.refund.info', { params });
  }

  // 退款单编辑
  async postRefundEdit(params) {
    return await this.$http.post('/pms_opc/joinin.refund.edit', params);
  }

  /**
   * @description: 2022.01.17迭代
   */
  // 开业采购单导出列表
  async getPurchaseGoodsUrl(params) {
    return await this.$http.get('/pms_opc/joinin.report.getpurchaselisturl', { params });
  }

  async getPurchaseOrderUrl(params) {
    return await this.$http.get('/pms_opc/joinin.report.getpurchaseorderlisturl', { params });
  }

  /**
   * @description: 采购相关(2022.3.2)
   */

  // 获取包裹详情
  async getPackinfo(params) {
    const res = await this.$http.get('/pms_opc/purchase.shiporder.packinfo', { params });
    return res;
  }

  // 获取包裹详情
  async getErpLogisticsQuery(params) {
    return await this.$http.get('/pms_opc/logistics.query', { params });
  }

  async changeSelfAutoPay(params) {
    return await this.$http.get('/pms_opc/purchase.order.autopay', { params });
  }

  async getReportUrl(params) {
    return await this.$http.get('/pms_opc/purchase.report.pururl', { params });
  }

  /**
   * @description: 采购售后相关(2022.3.17)
   */
  // 获取采购售后列表
  async getOrderRefundList(params) {
    return await this.$http.get('/pms_opc/purchase.orderrefund.list', { params });
  }

  // 获取采购售后明细
  async getOrderRefundDetail(params) {
    return await this.$http.get('/pms_opc/purchase.orderrefund.detail', { params });
  }

  // 获取采购售后枚举
  async getOrderRefundOptions(params) {
    return await this.$http.get('/pms_opc/purchase.orderrefund.options', { params });
  }

  // 获取采购单售后明细
  async getOrderRefundApplyList(params) {
    return await this.$http.get('/pms_opc/purchase.orderrefund.applyTabList', { params });
  }

  // 采购售后审核
  async changeOrderRefundAudit(params) {
    return await this.$http.post('/pms_opc/purchase.orderrefund.audit', params);
  }

  // 采购售后审核
  async getPurchaseOperationLogList(params) {
    return await this.$http.get('/pms_opc/purchase.orderrefund.operationLog', { params });
  }

  // 获取售后商品列表
  async getPurchaseOrderGoods(params) {
    return await this.$http.get('/pms_opc/joinin.purchase.getordergoods', { params });
  }

  // 提交售后
  async applyOrderRefund(params) {
    return await this.$http.post('/pms_opc/joinin.orderrefund.apply', params);
  }

  // 采购售后审核
  async getOfflineRefundOperationLog(params) {
    return await this.$http.get('/pms_opc/joinin.orderrefund.getoperationlog', { params });
  }

  // 撤销售后申请
  async revocationOrderRefund(params) {
    return await this.$http.post('/pms_opc/joinin.orderrefund.close', params);
  }

  // 录入寄回物流
  async entryExpressOrderRefund(params) {
    return await this.$http.post('/pms_opc/joinin.orderrefund.entryexpress', params);
  }

  // 获取快递公司列表
  async getExpressList(params) {
    return await this.$http.get('/pms_opc/common.getexpresslist', { params });
  }

  async getPduducList(params) {
    return await this.$http.get('/pms_opc/joinin.package.exportlist', {
      params
    });
  }

  async getClinicOptions(params) {
    return await this.$http.get('/pms_opc/clinic.enter.options', {
      params
    });
  }

  async getOrderrefundExportList(params) {
    return await this.$http.get('/pms_opc/purchase.orderrefund.exportList', {
      params
    });
  }

  // 远程搜索申请主体的省公司
  async getCommonCompanyList(params) {
    return await this.$http.get('/pms_opc/common.company.list', {
      params
    });
  }

  // 采购订单开票详情
  async getInvoicePurchasedetail(params) {
    return await this.$http.get('/pms_opc/finance.invoice.purchasedetail', { params });
  }

  // 获取首次开业包的操作记录
  async getPackageOperationlog(params) {
    return await this.$http.get('/pms_opc/joinin.package.operationlog', { params });
  }

  /*开业采购包新增接口*/
  async getPackageItems(params) {
    return await this.$http.get('/pms_opc/joinin.package.getItems', { params });
  }

  //   获取主体钱包余额
  async getMainWallet(params) {
    return await this.$http.get('/pms_opc/finance.accountWallet.entBalance', { params });
  }

  // 开业退款单导出列表
  async getRefundExportList(params) {
    return await this.$http.get('pms_opc/joinin.refund.exportList', { params });
  }
  // 获取会员列表
  async getUseVipList(params) {
    return await this.$http.get('pms_opc/common.userVip.osdOrderList', { params });
  }

  // 首次产品包财务审核
  async setPackageStatus(params) {
    return await this.$http.post('/pms_opc/joinin.package.status', params);
  }
}

export default Api_purchase;
