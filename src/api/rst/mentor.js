export default class Mentor {
  // 获取导师关联的理疗师
  getMentorPhysioList(params) {
    return this.$http.get('/pms_opc/advisor.physioList', { params });
  }
  // 获取导师列表
  getMentorList(params) {
    return this.$http.get('/pms_opc/advisor.list', { params });
  }
  // 导师新增or编辑
  async editMentor(params) {
    return await this.$http.post('/pms_opc/advisor.edit', params);
  }
  // 获取导师列表
  getMentorOptions(params) {
    return this.$http.get('/pms_opc/advisor.options', { params });
  }
  // 获取导师详情
  getMentorInfo(params) {
    return this.$http.get('/pms_opc/advisor.info', { params });
  }
  // 导师状态编辑
  async changeMentorStatus(params) {
    return await this.$http.post('/pms_opc/advisor.status', params);
  }
  // 导师审核
  async auditMentor(params) {
    return await this.$http.post('/pms_opc/advisor.audit', params);
  }

  // 获取导师详情
  getRstOperationLog(params) {
    return this.$http.get('/pms_opc/operationlog.record', { params });
  }

}
