export default class RstOrder {
  // 获取导师关联的理疗师
  getRstOrderList(params) {
    return this.$http.get('/pms_opc/order.order.list', { params });
  }

  getShopOrderDetail(params) {
    return this.$http.get('/pms_opc/order.order.detail', { params });
  }

  getHisOrderDetail(params) {
    return this.$http.get('/pms_opc/order.order.hisdetail', { params });
  }

  getOrderOptions(params) {
    return this.$http.get('/pms_opc/order.order.options', { params });
  }
  getOrderRevenueDetail(params) {
    return this.$http.get('/pms_opc/order.revenue.detail', { params });
  }
  getRstClinicList(params) {
    return this.$http.get('/pms_opc/common.clinic.list', { params });
  }

  // // 导师新增or编辑
  // async editMentor(params) {
  //   return await this.$http.post('/clinic/advisor.edit', params);
  // }
}
