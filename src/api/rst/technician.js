export default class Methods_technician {
  // 获取理疗师列表列表
  getPhysioList(params) {
    return this.$http.get('/pms_opc/physio.list', { params });
  }

  // 获取理疗师枚举
  getPhysioOptions(params) {
    return this.$http.get('/pms_opc/physio.options', { params });
  }

  // 获取枚举选项
  getPhysioSkillOptions(params) {
    return this.$http.get('/pms_opc/physio.selectOptions', { params });
  }

  // 技能设置
  getPhysioSkill(params) {
    return this.$http.post('/pms_opc/physio.skill', params);
  }

  // 技术新增/编辑
  getPhysioEdit(params) {
    return this.$http.post('/pms_opc/physio.edit', params);
  }

  // 获取详情
  getPhysioInfo(params) {
    return this.$http.get('/pms_opc/physio.info', { params });
  }

  // 搜索门店
  getPhysioSearchCli(params) {
    return this.$http.get('/pms_opc/physio.searchCli', { params });
  }

  // 搜索导师
  getPhysioSearchAdvisor(params) {
    return this.$http.get('/pms_opc/physio.searchAdvisor', { params });
  }

  // 搜索创建来源
  getPhysioSourceList(params) {
    return this.$http.get('/pms_opc/physio.sourceList', { params });
  }

  // 理疗师服务列表
  getPhysioServiceList(params) {
    return this.$http.get('/pms_opc/physio.serviceList', { params });
  }

  // 分发到合伙人
  getPhysioDispatch(params) {
    return this.$http.post('/pms_opc/physio.dispatch', params);
  }

  // 关联导师
  technicianRelateAdvisor(params) {
    return this.$http.post('/pms_opc/physio.advisor', params);
  }

  // 检查关联导师
  checkRelateAdvisor(params) {
    return this.$http.post('/pms_opc/physio.advisorCheck', params);
  }

  // 理疗师审核
  getPhysioAudit(params) {
    return this.$http.post('/pms_opc/physio.audit', params);
  }

  // 理疗师修改记录
  getPhysioEditRecord(params) {
    return this.$http.get('/pms_opc/operationlog.record', { params });
  }

  // 理疗师启用停用
  getPhysioStatus(params) {
    return this.$http.post('/pms_opc/physio.status', params);
  }
}
