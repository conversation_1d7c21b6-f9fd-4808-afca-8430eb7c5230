
class Method_rxj {
	// 获取养疗馆列表
	async getRxjList(params) {
		const res = await this.$http.get('/pms_opc/rxj.enter.list', {params})
		return res
	}

  // 获取养疗馆状态枚举列表
	async getRxjEnterOptions(params) {
		const res = await this.$http.get('/pms_opc/rxj.enter.options', {params})
		return res
	}

  // 榕小家入驻详情
	async getRxjEnterInfo(params) {
		const res = await this.$http.get('/pms_opc/rxj.enter.info', {params})
		return res
	}

  // 榕小家入驻信息审核
	async getRxjEnterExamine(params) {
		const res = await this.$http.post('/pms_opc/rxj.enter.examine', params)
		return res
	}

  // 榕小家入驻编辑
	async editRxjEnter(params) {
		const res = await this.$http.post('/pms_opc/rxj.enter.edit', params)
		return res
	}

	// 远程搜索采购主体
	/**
	 * @param { name } 主体名
	 * @parma { type } 默认为空查询全部，CLI : 所有诊所主体，RXJ：所有榕小家主体
	 * */
	async searchPurchasesubject(params) {
		return  await this.$http.get('/pms_opc/common.index.purchasesubject', {params})
	}
	
	
 
}

export default Method_rxj
