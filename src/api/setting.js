class Methods_Setting {
  // // 获取开票信息
  // async getInvoiceInfo(params){
  //   return await this.$http.get('/pms_opc/setup.get', {params})
  // }

  // 更新开票信息
  async updateInvoiceInfo(params) {
    return await this.$http.post('/pms_opc/setup.updateInvoice', params);
  }

  // 获取开票信息
  async getExamineInvoice(query) {
    return await this.$http.get('/pms_opc/joinin.invoiceExamine.getInvoice', { params: query });
  }

  // 更新开票信息
  async updateExamineInvoice(params) {
    return await this.$http.post('/pms_opc/joinin.invoiceExamine.updateInvoice', params);
  }
}

export default Methods_Setting;
