class Authentication_API {
  // 获取认证信息
  sysInfo = {
    sys_code: 'RSJYZT'
  };

  async getAuthenticationInfo(params) {
    return await this.$http.get('/pms_opc/setting.authentication.get', { params: { ...params, ...this.sysInfo } });
  }

  async authenticationInto(params) {
    return await this.$http.post('/pms_opc/setting.authentication.into', { ...params, ...this.sysInfo });
  }

  async uploadAuthenticationImg(params) {
    return await this.$http.post('/pms_opc/setting.authentication.upload', { ...params, ...this.sysInfo });
  }

  // 获取认证验证码
  async getAuthenticationSmsCode(params) {
    return await this.$http.get('/pms_opc/setting.authentication.getSmsCode', {
      params: { ...params, ...this.sysInfo }
    });
  }

  // 通过银行网点获取银行信息
  async getBankInfo(params) {
    return await this.$http.get('/pms_opc/setting.authentication.getBankInfo', {
      params: { ...params, ...this.sysInfo }
    });
  }

  // 打款认证
  async remitAuthentication(params) {
    return await this.$http.post('/pms_opc/setting.authentication.validate', { ...params, ...this.sysInfo });
  }

  // 获取选项
  async getAuthOptions(params) {
    return await this.$http.get('/pms_opc/setting.authentication.options', {
      params: { ...params, ...this.sysInfo }
    });
  }

  // 获取认证历史记录
  async getAuthRecords(params) {
    return await this.$http.get('/pms_opc/setting.authentication.applylist', {
      params: { ...params, ...this.sysInfo }
    });
  }
}

export default Authentication_API;
