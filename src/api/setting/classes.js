class Methods_Classes {
  // 获取班次列表
  async getR2RangeList(params) {
    return await this.$http.get('/pms_opc/reserve.range.list', { params });
  }

  // 创建班次
  async getR2RangeStore(params) {
    return await this.$http.post('/pms_opc/reserve.range.store', params);
  }

  // 修改班次
  async getR2RangeUpdate(params) {
    return await this.$http.post('/pms_opc/reserve.range.update', params);
  }

  // 获取班次详情
  async getR2RangeShow(params) {
    return await this.$http.get('/pms_opc/reserve.range.show', { params });
  }

  // 班次冲突检查
  async getR2RangeCheckclash(params) {
    return await this.$http.post('/pms_opc/reserve.range.checkclash', params);
  }
  // 班次冲突检查
  async delReserveRange(params) {
    return await this.$http.post('/pms_opc/reserve.range.destroy', params);
  }

  // 获取排班诊所列表
  async getRangeClinicList(params) {
    return await this.$http.get('/pms_opc/reserve.range.cliniclist', { params });
  }

  // 获取理疗师列表
  async getPhysicalTList(params) {
    return await this.$http.get('/pms_opc/physicalTherapist.list', { params });
  }
  // 获取排班设置
  async getReserveRangeSetting(params) {
    return await this.$http.get('/pms_opc/reserve.range.setting', { params });
  }
}

export default Methods_Classes;
