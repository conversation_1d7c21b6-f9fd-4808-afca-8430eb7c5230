class Methods_Management {
  // 获取排班列表
  async getR2ScheduleList(params) {
    return await this.$http.get('/pms_opc/reserve.schedule.list', { params });
  }

  // 批量设置排班
  async getR2ScheduleBatchsave(params) {
    return await this.$http.post('/pms_opc/reserve.schedule.batchsave', params);
  }

  // 医生排班冲突检查
  async getR2ScheduleCheckclash(params) {
    return await this.$http.get('/pms_opc/reserve.schedule.checkclash', { params });
  }
  // 获取员工列表（理疗师+医生）
  async getPhysicalTMemberlist(params) {
    return await this.$http.get('/pms_opc/physicalTherapist.memberlist', { params });
  }
  // 获取员工列表（理疗师+医生）
  async getR2SPhysioServiceInfo(params) {
    return await this.$http.get('/pms_opc/reserve.reserve.serviceinfo', { params });
  }

  // 设置排除项
  async getR2SPhysioeExcludeservice(params) {
    return await this.$http.get('/pms_opc/reserve.schedule.excludeService', { params });
  }

  // 排班列表（员工维度统计）
  async getScheduleListByMember(params) {
    return await this.$http.get('/pms_opc/reserve.schedule.listbymember', { params });
  }

  // 清除排班
  async getR2ScheduleBatchclean(params) {
    return await this.$http.post('/pms_opc/reserve.schedule.batchclean', params);
  }
  // 导入模版
  async getScheduleImportTemplate(params) {
    return await this.$http.get('/pms_opc/reserve.schedule.downimporttemplateurl', { params });
  }
  // 批量导入排班
  async importSchedule(params) {
    return await this.$http.post('/pms_opc/reserve.schedule.import', params);
  }
  // 设置排班
  async saveMemberSchedule(params) {
    return await this.$http.post('/pms_opc/reserve.schedule.save', params);
  }
}

export default Methods_Management;
