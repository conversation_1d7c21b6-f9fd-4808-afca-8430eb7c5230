class Wallet {
  // 榕树堂员工钱包列表
  async getRstMemberWalletList(params) {
    return this.$http.get('/pms_opc/RstMemberWallet.list', { params });
  }
  // 榕树堂员工钱包绑定银行卡
  async bindRstMemberWalletBank(params) {
    return await this.$http.post('/pms_opc/RstMemberWallet.bindbank', params);
  }
  // 员工钱包资产列表
  async getRstMemberBalanceList(params) {
    return this.$http.get('/pms_opc/RstMemberWallet.balancelist', { params });
  }
  // 获取员工资产，按月度聚合
  async getMonthlyAssetsList(params) {
    return this.$http.get('/pms_opc/RstMemberWallet.monthlyAssetsList', { params });
  }
  // 获取员工资产明细
  async getAssetsList(params) {
    return this.$http.get('/pms_opc/RstMemberWallet.assetsList', { params });
  }
  // 获取员工资产明细
  async getExcelassetslisturl(params) {
    return this.$http.get('/pms_opc/RstMemberWallet.excelassetslisturl', { params });
  }
}

export default Wallet;
