class statiscis {
  /**
   * @file: statiscis.js
   * @description: 数据统计接口
   * @author: jiuxia
   * @date: 2022.3.29
   */

  /**营业目标-诊所 */
  async getBusinessClinic(query) {
    return await this.$http.get('/pms_opc/statisv2.business.cliniclist', { params: query });
  }

  /**营业目标-枚举 */
  async getBusinessOpts(query) {
    return await this.$http.get('/pms_opc/statisv2.business.option', { params: query });
  }
  /**营业目标-数据 */
  async getBusinessTargets(query) {
    return await this.$http.get('/pms_opc/statisv2.business.targets', { params: query });
  }

  /**营业目标-月度目标 */
  async getBusinessMonthTargets(query) {
    return await this.$http.get('/pms_opc/statisv2.business.monthTargets', { params: query });
  }

  /**营业目标-创建 */
  async postBusinessSave(params) {
    return await this.$http.post('/pms_opc/statisv2.business.save', params);
  }

  /**理疗师-数据 */
  async getPhysioList(query) {
    return await this.$http.get('/pms_opc/statisv2.physio.list', { params: query });
  }
  /**理疗师-枚举 */
  async getPhysioOpt(query) {
    return await this.$http.get('/pms_opc/statisv2.physio.option', { params: query });
  }
  /**理疗师-导出 */
  async getPhysioReport(query) {
    return await this.$http.get('/pms_opc/statisv2.report.physioefficiencyurl', { params: query });
  }

  /**医生-数据 */
  async getDoctorList(query) {
    return await this.$http.get('/pms_opc/statisv2.doctor.list', { params: query });
  }
  /**医生-枚举 */
  async getDoctorOpt(query) {
    return await this.$http.get('/pms_opc/statisv2.doctor.option', { params: query });
  }
  /**医生-导出 */
  async getDoctorReport(query) {
    return await this.$http.get('/pms_opc/statisv2.report.doctorefficiencyurl', { params: query });
  }
}

export default statiscis;
