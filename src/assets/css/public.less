@properties: ~"margin-right", ~"margin-left", ~"margin-top", ~"margin-bottom", ~"margin", ~"padding-top", ~"padding-bottom", ~"padding-left", ~"padding-right", ~"padding";
@propertiesShort: ~"mr", ~"ml", ~"mt", ~"mb", ~"m", ~"pt", ~"pb", ~"pl", ~"pr", ~"p";

// Loop through each property
.loopProperties(@index) when (@index <= length(@properties)) {
@property: e(extract(@properties, @index));
  @propertyShort: e(extract(@propertiesShort, @index));

  // Loop through each value for the current property
  .loopValues(@valueIndex) when (@valueIndex <= 50) {
    .@{propertyShort}-@{valueIndex} {
        @{property}: unit(@valueIndex, px);
    }
    .loopValues(@valueIndex + 1);
  }

  .loopValues(0);
  .loopProperties(@index + 1);
}

.loopProperties(1);

.vertical-center-modal{
  display: flex;
  align-items: center;
  justify-content: center;
  .ivu-modal{
    top: 0;
  }
}

.auth_confirm_btn_class{
  background-color: #155bd4;
  border-color: #155bd4;

  &:hover{
      background-color: #447cdd;
      border-color: #447cdd;
  }
}
.auth_cancel_btn_class{
  &:hover{
   border-color: #DCDFE6;
   background-color: #fff;
   color: #447cdd;
  }
}


.color-999{
  color:#999999
}