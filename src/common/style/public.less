.f-title {
  font-size: 16px;
  font-weight: 700;
  padding-bottom: 10px;
}
.hidden-scroll::-webkit-scrollbar {
  display: none;
}
.hidden-scroll {
  scrollbar-width: none; //兼容火狐
  -ms-overflow-style: none; //兼容IE10
}

// 去除number框的加减框图标
input[type='number']::-webkit-outer-spin-button,
input[type='number']::-webkit-inner-spin-button {
  -webkit-appearance: none !important;
  margin: 0;
}
input[type='number'] {
  -moz-appearance: textfield;
}

// ...existing code...

/* 美化所有滚动条 */
::-webkit-scrollbar {
  // width: 8px;
  // height: 8px;
  // border-radius: 8px;
  // background: #e4e4e4;
}
