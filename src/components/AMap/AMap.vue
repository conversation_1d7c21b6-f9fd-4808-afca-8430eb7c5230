<template>
  <div class="groupbuy-map">
    <div id="container" ref="map-view">
      <Input
        search
        :value="selectAddressName"
        @input="searchAddress"
        class="groupbuy-input"
        :disabled="open_status == 'EXAMINE' || checkDetail == 'true'"
        placeholder="请输入搜索地址"
      />
      <ul class="groupbuy-map-result">
        <li v-for="item in addressList" :key="item.id" class="groupbuy-map-result-item" @click="selectAddress(item)">
          <span class="groupbuy-map-result-item-name">{{ item.name }}</span>
          <span class="groupbuy-map-result-item-district">{{ item.district }}</span>
        </li>
      </ul>
    </div>
  </div>
</template>

<script>
// import axios from 'axios'
// import { TextToCode } from 'utils/chinaMap'

window._AMapSecurityConfig = {
  securityJsCode: '4bd48ade814ed0cbeed3d72d8596c2ab'
};

import AMapLoader from '@amap/amap-jsapi-loader';

export default {
  name: 'MapView',
  props: {
    id: {
      type: String,
      default: ''
    },
    center: {
      type: Array,
      default: () => [121.59996, 31.197646]
    },
    hasPosition: {
      type: Array,
      default: () => []
    },
    hasAddress: {
      type: String,
      default: ''
    },
    open_status: {
      type: String,
      default: ''
    },
    checkDetail: {
      type: String,
      default: 'false'
    },
    isApiReady: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      map: null,
      marker: null,
      geoCoder: null,
      mapConfigure: {
        amapKey: '5c8bc422e6f0edb650006c678f57e353', // 申请好的Web端开发者Key
        options: {
          resizeEnable: true, // 是否监控地图容器尺寸变化
          center: [121.553958, 29.869472], // 初始地图中心点
          zoom: 14 // 初始地图级别
        }
      },
      poiPicker: null, // poi选择器
      addressList: [], //搜索地址列表
      selectAddressName: '', //选择的地址
      currentMarkPosition: [] //当前标记的位置
    };
  },
  created() {},
  mounted() {
    if (!this.id) {
      this.$nextTick(() => {
        this.initAMap();
      });
    }
  },
  watch: {
    // hasPosition: {
    //   handler(val){
    //     if(val.length){
    //
    //     }
    //   },deep: true
    // }
    isApiReady: {
      handler(val) {
        if (val && this.id) {
          this.$nextTick(() => {
            this.initAMap();
          });
        }
      }
    }
  },
  beforeDestroy() {
    this.map && this.map.destroy();
    this.map = null;
  },
  deactivated() {
    this.map && this.map.destroy();
    this.map = null;
  },
  methods: {
    selectAddress(value) {
      console.log('-> %c value  === %o ', 'font-size: 15px;color: green;', value);
      if (!value.location) {
        this.$Message.error('请选择精确地址');
        this.addressList = [];
        return;
      }
      this.selectAddressName = value.name;
      this.addressList = [];
      const position = [value.location.lng, value.location.lat];
      this.getLocation(position, value.name);
    },
    initAMap() {
      AMapLoader.load({
        key: '5991ca36c0bd172a543c60522223dabe', //设置您的key
        version: '1.4.3', //设置您的version
        AMapUI: {
          version: '1.1'
        }
      })
        .then(AMap => {
          this.map = new AMap.Map('container', {
            zoom: 16,
            resizeEnable: true,
            center: [121.59996, 31.197646]
          });
          //地址搜索
          this.initBasePlugins(AMap);
          this.initMapEvent();
          this.initMapCenter();
        })
        .catch(e => {
          console.log(e);
          throw '地图加载失败，请重新加载';
        });
    },
    initMapCenter() {
      this.map.on('complete', () => {
        console.log('🚀 ~ file: AMap.vue ~ line 104 ~ initAMap ~ this.hasPosition', this.hasPosition);
        this.$nextTick(() => {
          if (this.hasPosition.length) {
            // this.setMapMarker( this.hasPosition )
            // this.setMapWindow( this.hasAddress, this.hasPosition )
            this.getLocation(this.hasPosition);
          }
        });
      });
    },
    initBasePlugins(AMap) {
      AMap.plugin('AMap.Geocoder', () => {
        this.geoCoder = new AMap.Geocoder();
      });
      AMap.plugin('AMap.Autocomplete', () => {
        // 实例化Autocomplete
        const autoOptions = {
          //city 限定城市，默认全国
          city: '全国'
        };
        this.autoComplete = new AMap.Autocomplete(autoOptions);
      });
    },
    getLocation(position, address, isDrag) {
      this.geoCoder.getAddress(position, (status, result) => {
        console.log('-> %c status, result  === %o ', 'font-size: 15px;color: green;', status, result);
        if (status === 'complete' && result.regeocode) {
          if (!address) {
            address = result.regeocode.formattedAddress;
          }
          // const prov = result.regeocode.addressComponent.province
          // const city = result.regeocode.addressComponent.city||result.regeocode.addressComponent.province
          // const district = result.regeocode.addressComponent.district
          // console.log(TextToCode[prov][city][district])
          // const formatAddress = {
          //   prov: {
          //     name: prov,
          //     code: TextToCode[prov].code,
          //   },
          //   city: {
          //     name: city,
          //     code: TextToCode[prov][city].code,
          //   },
          //   county: {
          //     name: result.regeocode.addressComponent.district,
          //     code: TextToCode[prov][city][district].code,
          //   },
          //   other: ''
          // }
          // let index = result.regeocode.formattedAddress.indexOf(district)
          // formatAddress.other = result.regeocode.formattedAddress.slice((index+district.length))
          /*  在此处将地址信息抛出
           * this.$emit('eventName',formatAddress)
           * */
          this.$emit('getAddressDetail', position);
          !isDrag && this.setMapMarker(position);
          isDrag && this.map.setCenter(position);
          this.setMapWindow(address, position);
        } else {
          console.error('根据地址查询位置失败');
        }
      });
    },
    setMapWindow(address, position) {
      let info = [];
      info.push(`<div style="font-size: 15px;font-weight: 600;">${address}</div>`);
      // info.push( `<span class="info-window">经度：${ position[0] }</span>` )
      // info.push( `<div class="info-window">纬度：${ position[1] }</div>` )
      const infoWindow = new AMap.InfoWindow({
        size: '260px',
        content: info.join(''), //使用默认信息窗体框样式，显示信息内容
        offset: new AMap.Pixel(5, -35)
      });
      infoWindow.open(this.map, position);
    },
    searchAddress(e) {
      console.log('-> %c e  === %o ', 'font-size: 15px;color: green;', e);
      this.autoComplete.search(e, (status, result) => {
        console.log('-> %c status  === %o ', 'font-size: 15px;color: green;', status);
        console.log('-> %c result  === %o ', 'font-size: 15px;color: green;', result);
        // 搜索成功时，result即是对应的匹配数据
        if (status === 'complete') {
          this.addressList = result.tips.length ? result.tips.filter(item => item.id) : [];
        }
      });
    },
    initMapEvent() {
      this.map.on('click', e => {
        console.log('-> %c e  === %o ', 'font-size: 15px;color: green;', e);
        // this.getAddress(e)
        // this.map.setFitView(  )
        if (this.open_status == 'EXAMINE' || this.checkDetail == 'true') {
          return;
        }
        const position = [e.lnglat.lng, e.lnglat.lat];
        console.log('=>(AMap.vue:243) position', position);
        this.getLocation(position);
      });
    },
    clearMarker() {
      if (this.marker) {
        this.marker.setMap(null);
        this.marker = null;
      }
    },
    // 设置点击位置的标记
    setMapMarker(position) {
      console.log('-> %c position  === %o ', 'font-size: 15px;color: green;', position);
      let flag = true;
      if (this.open_status == 'EXAMINE' || this.checkDetail == 'true') {
        flag = false;
      }
      this.clearMarker();
      this.marker = new AMap.Marker({
        position: position,
        icon: 'https://webapi.amap.com/theme/v1.3/markers/n/mark_b.png',
        autoRotation: true,
        draggable: flag,
        cursor: 'move'
      });
      this.map.add(this.marker);
      this.map.setCenter(position);
      flag &&
        this.marker.on('dragend', e => {
          console.log('-> %c e  === %o ', 'font-size: 15px;color: green;', e);
          this.getLocation([e.lnglat.lng, e.lnglat.lat], '', true);
        });

      // this.map.setFitView( this.marker )
    }
  }
};
</script>

<style lang="less" scoped>
.groupbuy-map {
  position: relative;
  width: 500px;
  height: 400px;

  #container {
    padding: 0px;
    margin: 0px;
    width: 100%;
    height: 100%;
    position: absolute;

    .groupbuy-input {
      position: absolute;
      top: 20px;
      width: 220px;
      height: 36px;
      left: 10px;
      z-index: 150;
    }

    .groupbuy-map-result {
      position: absolute;
      left: 11px;
      top: 53px;
      background: #fff;
      font-size: 12px;
      z-index: 20;

      .groupbuy-map-result-item {
        text-overflow: ellipsis;
        white-space: nowrap;
        overflow: hidden;
        display: flex;
        padding: 0 10px;
        width: 360px;
        height: 30px;
        line-height: 30px;
        box-sizing: border-box;
        cursor: pointer;

        &:hover {
          background: #155bd4;

          .groupbuy-map-result-item-name,
          .groupbuy-map-result-item-district {
            color: #ffffff;
          }
        }

        .groupbuy-map-result-item-name {
          color: #323233;
          font-size: 13px;
        }

        .groupbuy-map-result-item-district {
          margin-left: 10px;
          color: #969799;
          font-size: 12px;
        }
      }
    }
  }
}

::v-deep .amap-info-outer {
  width: 260px;
  font-size: 13px;
  padding-right: 20px;
}

::v-deep .amap-info-close {
  right: 8px !important;
}

::v-deep .amap-copyright {
  z-index: 10;
  bottom: 9px;
}

::v-deep .amap-logo {
  z-index: 10;
}
</style>
>
