<template>
  <div class="audit-btn">
    <Button type="primary" @click="confirmVisible = true">{{ btnText }}</Button>
    <confirm-modal :confirmVisible.sync="confirmVisible" v-on="$listeners"></confirm-modal>
  </div>
</template>

<script>
import ConfirmModal from '../confirmModal/confirmModal.vue';

export default {
  name: 'AuditPass',
  mixins: [],

  components: { ConfirmModal },

  props: {
    btnText: {
      type: String,
      default: '审核通过',
    },
  },

  data() {
    return {
      confirmVisible: false,
    };
  },

  computed: {},

  watch: {},

  created() {},

  mounted() {},

  methods: {},

  destroyed() {},
};
</script>

<style scoped lang="less">
.audit-btn {
  display: inline-block;
}
</style>
