import DatePicker from './week-picker'
import locale from 'element-ui/src/locale';

function install(Vue, opts = {}) {
  locale.use(opts.locale);
  locale.i18n(opts.i18n);

  Vue.component(DatePicker.name, DatePicker);
}

DatePicker.install = install

/* istanbul ignore if */
if (typeof window !== 'undefined' && window.Vue) {
  install(window.Vue);
}

export const DateWeekRange = DatePicker;

export {
  install,
  locale
}

export default DatePicker;
