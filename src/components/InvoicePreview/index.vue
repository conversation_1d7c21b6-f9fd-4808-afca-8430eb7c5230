<template>
  <Modal
    :value="visible"
    :title="title"
    width="630px"
    :closable="false"
    class-name="confirm-modal"
    transfer
    @on-visible-change="changeVisible"
  >
    <div class="content">
      <Picture v-model="invoiceUrls" :is-query-detail="true"></Picture>
    </div>
    <div slot="footer">
      <Button @click="cancel">关闭</Button>
    </div>
  </Modal>
</template>

<script>
import Picture from '../upload/picture';

export default {
  name: 'InvoicePreviewModal',

  components: { Picture },
  mixins: [],
  model: {
    prop: 'visible',
    event: 'update:visible',
  },
  props: {
    title: {
      type: String,
      default: '发票预览',
    },
    visible: {
      type: Boolean,
      default: false,
    },
    invoiceUrls: {
      type: Array,
      default: () => [],
    },
  },

  data() {
    return {};
  },

  computed: {},

  watch: {},

  created() {},

  mounted() {},

  destroyed() {},

  methods: {
    cancel() {
      this.changeVisible(false);
    },
    changeVisible(val) {
      !val && this.$emit('update:visible', val);
    },
  },
};
</script>

<style scoped lang="less">
.confirm-modal {
  .content {
    padding: 0px;
  }
}

::v-deep .ivu-modal-footer {
  border-top: none;
}

.content-text {
  padding-left: 52px;
}
</style>
