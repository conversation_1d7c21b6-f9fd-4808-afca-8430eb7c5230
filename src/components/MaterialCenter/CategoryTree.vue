import TooltipOver from '@/components/tooltipOver';
<template>
  <div>
    <ul>
      <li v-for="(category, index) in categoryTree" :key="category.id">
        <div
          :class="[
            'tree-bar',
            !category.isOpen ? 'off' : '',
            !isManager && category.type === 1 && category.level === 0 && !isUpload ? 'disabled' : '',
          ]"
          @click="handleCategoryClick(category)"
        >
          <div class="tree-node">
            <div
              :class="[
                'tree-content',
                !category.parent_id ? 'tree-bar-root' : '',
                currentCategoryId === category.id ? 'active' : '',
                category.id === deletingId ? 'deleting' : '',
              ]"
            >
              <div :style="{ width: deps * 20 + 'px' }" />
              <i
                v-if="category.sub_count > 0 && (isManager || (!isManager && category.level < 2))"
                class="switcher"
                @click.stop="switchCategory(category)"
              />
              <div class="tree-title">
                <Tooltip
                  placement="top"
                  theme="light"
                  style="height: 36px"
                  :content="category.name"
                  :disabled="category.name.length <= 8 || (!isManager && !showCount)"
                >
                  <div :class="[isManager || showCount ? 'ellipse-text' : '']">{{ category.name }}</div>
                </Tooltip>
              </div>

              <template>
                <div v-if="isManager || showCount" class="tree-count">{{ category.asset_count }}</div>
                <div v-if="isManager && category.parent_id" class="tree-edit">
                  <svg-icon name="edit" style="width: 18px" @click.stop="editCategory(category)" />
                  <svg-icon
                    name="trash"
                    style="width: 18px"
                    class="ml-4"
                    @click.stop="e => categoryDelete(e, category)"
                  />
                </div>
              </template>
            </div>
          </div>
        </div>
        <div
          v-if="category.children?.length && (isManager || (!isManager && category.level < 2))"
          :class="{ 'tree-child': true, 'tree-child-open': category.isOpen }"
        >
          <category-tree
            :category-select="categorySelect"
            :category-edit="categoryEdit"
            :current-category-id="currentCategoryId"
            :category-tree="category.children"
            :deps="deps + 1"
            :is-manager="isManager"
            :category-delete="categoryDelete"
            :deleting-id="deletingId"
            :is-upload="isUpload"
            :show-count="showCount"
          />
        </div>
      </li>
    </ul>
  </div>
</template>

<script>
export default {
  name: 'CategoryTree',
  // inject: ['categorySelect', 'categoryEdit'],
  props: {
    categoryTree: {
      type: Array,
      required: true,
    },
    deps: {
      type: Number,
      default: 0,
    },
    currentCategoryId: {
      type: [String, Number],
      default: '',
    },
    updateCategory: {
      type: Function,
      default: () => {},
    },
    categorySelect: {
      type: Function,
      default: () => {},
    },
    categoryEdit: {
      type: Function,
      default: () => {},
    },
    categoryDelete: {
      type: Function,
      default: () => {},
    },
    isManager: {
      type: Boolean,
      default: false,
    },
    showCount: {
      type: Boolean,
      default: false,
    },
    deletingId: {
      type: [String, Number],
      default: '',
    },
    isUpload: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {};
  },
  computed: {
    isDefaultDisabled() {
      return category => {
        return !this.isManager && category.type === 1 && category.level === 0 && !this.isUpload;
      };
    },
  },
  directives: {
    ellipsisTooltip: {
      // 超出宽度显示省略号
      bind(el, binding) {
        // 获取配置的行数（默认为 2 行）
        const maxLines = binding.value || 1;

        // 设置 CSS 样式
        const style = `
            display: -webkit-box;
            -webkit-line-clamp: ${maxLines};
            -webkit-box-orient: vertical;
            overflow: hidden;
            white-space: nowrap;
            word-break: break-all;
            word-wrap: break-word;
            text-overflow: ellipsis;
            width: 124px
          `;
        el.style.cssText += style;

        // 监听鼠标悬停事件，显示 tooltip
        el.addEventListener('mouseenter', () => {
          console.log(
            '%c [ el, binding ]-141',
            'font-size:13px; background:#b825a0; color:#fc69e4;',
            el.scrollHeight,
            el.clientHeight,
            binding
          );
          // 判断文本是否溢出
          if (el.scrollHeight > el.clientHeight) {
            // 只有在文本溢出时，才显示 tooltip
            const tooltip = document.createElement('div');
            tooltip.style.position = 'absolute';
            tooltip.style.backgroundColor = '#333';
            tooltip.style.color = '#fff';
            tooltip.style.padding = '5px 10px';
            tooltip.style.borderRadius = '4px';
            tooltip.style.fontSize = '12px';
            tooltip.style.maxWidth = '200px';
            tooltip.style.wordWrap = 'break-word';
            tooltip.style.zIndex = '9999';
            tooltip.style.top = `${el.getBoundingClientRect().top - 40}px`;
            tooltip.style.left = `${el.getBoundingClientRect().left}px`;
            tooltip.textContent = el.textContent.trim();

            document.body.appendChild(tooltip);

            // 移除 tooltip
            el.addEventListener('mouseleave', () => {
              document.body.removeChild(tooltip);
            });
          }
        });
      },
      unbind(el) {
        // 在指令解绑时移除事件监听
        el.removeEventListener('mouseenter');
        el.removeEventListener('mouseleave');
      },
    },
  },
  created() {},
  methods: {
    handleCategoryClick(category) {
      if (this.isDefaultDisabled(category)) {
        return;
      }
      if (!category.isOpen && category.sub_count > 0 && this.isManager) {
        category.isOpen = true;
      }
      this.categorySelect(category);
    },
    editCategory(category) {
      this.categoryEdit(category);
    },
    switchCategory(category) {
      category.isOpen = !category.isOpen;
    },
  },
};
</script>

<style lang="less" scoped>
.tree-bar {
  height: 36px;
  margin-bottom: 2px;
  line-height: 36px;
  font-size: 14px;
  padding: 0 4px;

  .tree-content {
    display: flex;
    align-items: center;
    cursor: pointer;
    user-select: none;
    -webkit-user-select: none;
    padding-left: 12px;
    padding-right: 8px;

    .tree-count {
      color: #969799;
    }

    .tree-title {
      display: flex;
      padding-right: 12px;
      flex: auto;
      overflow: hidden;
      width: 0;
      white-space: nowrap;
      align-items: center;

      .ellipse-text {
        overflow: hidden;
        word-break: break-all;
        word-wrap: break-word;
        text-overflow: ellipsis;
        width: 124px;
      }
    }

    &.active {
      background-color: #f2f3f5;
    }

    &:hover {
      &:not(.tree-bar-root) {
        .tree-edit {
          display: block;
        }

        .tree-count {
          display: none;
        }
      }

      background-color: #e8effa;
    }

    &.deleting {
      .tree-edit {
        display: block !important;
      }

      .tree-count {
        display: none;
      }

      background-color: #e8effa;
    }

    .switcher {
      width: 16px;
      height: 36px;
      line-height: 36px;
      display: block;
      vertical-align: middle;
      border: 0 none;
      cursor: pointer;
      outline: none;
      transform: rotate(90deg);
      transform-origin: 23.09% 50%;
      transition: transform 0.3s ease;

      &::after {
        border: 5px solid transparent;
        border-left-color: #39393a;
        content: '';
        display: inline-block;
        border-left-width: 7px;
      }
    }

    .tree-edit {
      display: none;
    }
  }
}

.tree-bar.off {
  .tree-content {
    .switcher {
      transform: rotate(0deg);
    }
  }
}

.tree-bar.disabled {
  .tree-content {
    cursor: not-allowed;
    color: #969799;
  }
}

.tree-child {
  transition: height 200ms;
  overflow: hidden;
  height: 0;

  &-open {
    height: auto;
  }
}
</style>
