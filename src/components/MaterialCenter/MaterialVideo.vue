<template>
  <div style="display: inline-block">
    <div class="video-item" v-for="(item, index) in videoUrls" :key="index" @click="previewVideo(item)">
      <div v-if="!disabled" class="delete-icon" @click.stop="deleteVideo(index)"></div>
      <div class="video-box">
        <video-thumbnail 
          :video-url="item" 
          class="video-thumbnail"
          @loaded="onThumbnailLoaded(index)"
        />
        <div class="play-icon" v-show="thumbnailLoaded[index]">
          <i class="ivu-icon ivu-icon-ios-play" style="font-size: 20px; margin-left: 3px"></i>
        </div>
      </div>
    </div>

    <div v-if="getLimit > 0 && !disabled" slot="footer" class="video-item upload-btn" @click="myMaterialVisible = true">
      <div class="upload-v2">
        <div class="upload-v2-icon">
          <svg-icon style="font-size: 24px" name="upload-v2"></svg-icon>
        </div>
      </div>
    </div>
    <my-material
      v-model="myMaterialVisible"
      assetType="video"
      :limit="getLimit"
      @uploadSuccess="uploadSuccess"
    ></my-material>
    <video-preview v-model="videoPreviewVisible" :video-url="previewUrl"></video-preview>
  </div>
</template>

<script>
import MyMaterial from './MyMaterial.vue';
import VideoPreview from './VideoPreview.vue';
import VideoThumbnail from './VideoThumbnail.vue';

export default {
  name: 'MaterialVideo',
  components: {
    MyMaterial,
    VideoPreview,
    VideoThumbnail,
  },
  props: {
    limit: {
      type: Number,
      default: 1,
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    value: {
      type: [String, Array],
      default() {
        return () => [];
      },
    },
  },
  data() {
    return {
      myMaterialVisible: false,
      videoUrls: [],
      videoPreviewVisible: false,
      previewUrl: '',
      thumbnailLoaded: {},
    };
  },
  computed: {
    getLimit() {
      return this.limit - this.videoUrls.length;
    },
    getVideoUrls: {
      get() {
        return this.videoUrls;
      },
      set(val) {
        this.videoUrls = val;
      },
    },
  },
  watch: {
    value: {
      immediate: true,
      handler: function () {
        console.log(this.value);
        if (typeof this.value == 'string') {
          this.videoUrls = this.value != '' ? [this.value] : [];
        } else {
          if (this.value instanceof Array) {
            this.videoUrls = this.value;
          }
        }
      },
    },
  },
  methods: {
    previewVideo(url) {
      this.previewUrl = url;
      this.videoPreviewVisible = true;
    },
    onThumbnailLoaded(index) {
      this.$set(this.thumbnailLoaded, index, true);
    },
    deleteVideo(index) {
      this.videoUrls.splice(index, 1);
      this.$delete(this.thumbnailLoaded, index);
      this.notify();
    },
    uploadSuccess(data) {
      if (Array.isArray(data)) {
        this.videoUrls = data;
      } else {
        this.videoUrls.push(data);
      }
      this.notify();
    },
    notify() {
      this.$emit('input', this.limit > 1 ? this.videoUrls : this.videoUrls[0] || '');
    },
  },
};
</script>

<style lang="less" scoped>
.app-image-list {
  max-width: 720px;
  min-height: 88px;
}

.video-item {
  float: left;
  margin: 0 8px 8px 0;
  width: 78px;
  height: 78px;
  // border: 1px solid #ddd;
  background: #f5f6f9;
  position: relative;
  overflow: visible;
  cursor: pointer;
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;

  &:hover {
    .delete-icon {
      display: block;
    }
  }

  img {
    height: 100%;
    width: 100%;
    object-fit: contain;
  }

  .video-box {
    width: 100%;
    height: 100%;
    position: relative;

    .video-thumbnail {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }

    .play-icon {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      background: rgba(0, 0, 0, 0.5);
      border-radius: 50%;
      width: 32px;
      height: 32px;
      display: flex;
      align-items: center;
      justify-content: center;
      color: #fff;
      
      &:hover {
        background: rgba(0, 0, 0, 0.7);
      }

      i {
        line-height: 1;
      }
    }
  }

  .delete-icon {
    display: none;
    position: absolute;
    width: 20px;
    height: 20px;
    top: -10px;
    right: -10px;
    z-index: 10;
    cursor: pointer;
    background: url(https://static.rsjxx.com/backstage/2019/0509/103116_32332.png) no-repeat;
    background-size: 20px 20px;
    background-position: 50%;
  }
}

.upload-btn {
  border: none;
  background: #f5f6f9;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  margin-right: 0;
  margin-bottom: 0;

  .upload-v2 {
    display: inline-block;
  }
}
</style>
