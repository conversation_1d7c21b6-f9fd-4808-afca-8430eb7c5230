<template>
  <div class="image-upload">
    <div class="upload-list-wrapper">
      <div class="upload-list">
        <div class="local-image-card" v-for="(item, index) in files" :key="index">
          <img :src="item.url" class="local-image" />
          <svg-icon @click="deleteFile(index)" class="upload-delete-icon" name="upload-delete"></svg-icon>
        </div>
        <div class="upload-trigger" @click="startUpload">
          <svg-icon name="upload-icon" style="font-size: 24px"></svg-icon>
          <input
            @change="handleFileChange"
            ref="fileInput"
            style="display: none"
            type="file"
            :multiple="limit > 1 ? true : false"
            accept=".jpg, .gif, .png, .bmp, .jpeg"
          />
        </div>
      </div>
    </div>
    <div class="help-desc">支持 .jpg, .gif, .png, .bmp .jpeg 格式，单个图片不超过 10 MB。</div>
  </div>
</template>

<script>
export default {
  name: 'ImageUpload',
  props: {
    limit: {
      type: Number,
      default: 9,
    },
  },
  data() {
    return {
      files: [],
    };
  },
  watch: {
    'files.length': {
      handler(val) {
        console.log('%c [ val ]-37', 'font-size:13px; background:#e89383; color:#ffd7c7;', val);
        this.$emit('input', this.files);
      },
    },
  },
  methods: {
    startUpload() {
      this.$refs.fileInput.click();
    },
    deleteFile(index) {
      this.files.splice(index, 1);
    },
    handleFileChange(e) {
      const files = e.target.files;
      console.log('%c [ this.limit ]-60', 'font-size:13px; background:#33f972; color:#77ffb6;', this.limit);
      if (files.length > this.limit) {
        this.$Message.error(`最多只能上传${this.limit}张图片`);
        return;
      }
      // 保存本地文件
      for (let i = 0; i < files.length; i++) {
        const file = files[i];
        const size = file.size / 1024 / 1024;
        if (size > 10) {
          this.$Message.error(`图片大小不能超过10M，请重新选择`);
          continue;
        }
        const reader = new FileReader();
        reader.readAsDataURL(file);
        reader.onload = e => {
          const url = e.target.result;
          file.url = url;
          this.files.push(file);
        };
      }
    },
  },
};
</script>

<style lang="less" scoped>
.upload-list {
  display: flex;
  -webkit-box-pack: start;
  -webkit-justify-content: flex-start;
  justify-content: flex-start;
  -webkit-flex-wrap: wrap;
  flex-wrap: wrap;
  margin-bottom: -12px;

  .local-image-card {
    display: flex;
    position: relative;
    align-items: flex-end;
    width: 80px;
    height: 80px;
    box-sizing: border-box;
    font-size: 12px;
    background-color: #f7f8fa;
    margin-bottom: 12px;
    margin-right: 6px;
    background-size: 100% 100%;

    &:hover {
      .upload-delete-icon {
        display: block;
      }
    }

    .local-image {
      width: 100%;
      height: 100%;
      object-fit: contain;
    }

    .upload-delete-icon {
      display: none;
      position: absolute;
      right: 0px;
      top: 0px;
      width: 20px;
      height: 20px;
      cursor: pointer;
    }
  }

  .upload-trigger {
    border-radius: 2px;
    width: 80px;
    height: 80px;
    box-sizing: border-box;
    cursor: pointer;
    background-color: #f7f8fa;
    color: #646566;
    border-color: #dcdee0;
    display: flex;
    justify-content: center;
    align-items: center;
    border-width: 1px;
    margin-bottom: 12px;
  }
}

.help-desc {
  color: #969799;
  line-height: 14px;
  font-size: 12px;
  margin-top: 10px;
  margin-bottom: 0;
}
</style>
