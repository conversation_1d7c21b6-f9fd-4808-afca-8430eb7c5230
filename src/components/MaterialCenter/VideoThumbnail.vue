<template>
  <div class="video-thumbnail">
    <video
      ref="video"
      :src="videoUrl"
      crossorigin="anonymous"
      preload="metadata"
      style="display: none"
      @loadeddata="generateThumbnail"
    ></video>
    <img :src="thumbnailUrl" v-if="thumbnailUrl" class="thumbnail-img" />
    <div v-else class="loading-placeholder">
      <i class="ivu-icon ivu-icon-ios-loading loading-icon"></i>
    </div>
  </div>
</template>

<script>
export default {
  name: 'VideoThumbnail',
  props: {
    videoUrl: {
      type: String,
      required: true,
    },
  },
  data() {
    return {
      thumbnailUrl: '',
    };
  },
  methods: {
    generateThumbnail() {
      const video = this.$refs.video;

      // 设置视频时间为1秒
      video.currentTime = 1;

      // 监听视频时间更新事件
      video.addEventListener(
        'timeupdate',
        () => {
          const canvas = document.createElement('canvas');
          canvas.width = video.videoWidth;
          canvas.height = video.videoHeight;

          const ctx = canvas.getContext('2d');
          ctx.drawImage(video, 0, 0, canvas.width, canvas.height);
          this.thumbnailUrl = canvas.toDataURL();

          // 清理视频元素
          video.removeAttribute('src');
          video.load();
          this.$emit('loaded');
        },
        { once: true }
      ); // 只执行一次
    },
  },
  watch: {
    videoUrl: {
      immediate: true,
      handler(newUrl) {
        if (newUrl) {
          this.thumbnailUrl = '';
          this.$nextTick(() => {
            if (this.$refs.video) {
              this.$refs.video.src = newUrl;
              this.$refs.video.load();
            }
          });
        }
      },
    },
  },
};
</script>

<style lang="less" scoped>
.video-thumbnail {
  width: 100%;
  height: 100%;
  background: #f5f6f9;

  .thumbnail-img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  .loading-placeholder {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;

    .loading-icon {
      font-size: 24px;
      animation: rotate 1s linear infinite;
    }
  }
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
</style>
