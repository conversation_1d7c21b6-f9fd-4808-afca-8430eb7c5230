
/**
 * 挂载的存在页面锁定的问题，暂时不要用，
 * */
import index from './index.vue';
import Vue from 'vue';
// 用Vue构造器，创建一个子类
const CropperConstructor = Vue.extend(index);

// 创建组件实例
let instance = null;
// 保存原始 body 的 overflow 状态
let originalBodyOverflow = '';
// 创建调用函数
const middleCropper = function (props) {
  // 在打开弹窗前保存并禁用 body 滚动
  originalBodyOverflow = document.body.style.overflow;
  document.body.style.overflow = 'hidden';
  return new Promise((resolve, reject) => {
    // 赋值组件实例
    instance = new CropperConstructor({
      propsData: {
        ...props,
      },
      methods: {
        closeModal() {
          unMount();
        },
        success: res => {
          resolve(res);
        },
      },
    });

    // 初始化组件
    instance.$mount();

    // 挂载到全局
    document.body.appendChild(instance.$el);
    instance.value = true;

    // 声明卸载方法
    const unMount = function () {
      if (!instance) return;
      // 恢复 body 的滚动状态
      document.body.style.overflow = originalBodyOverflow;
      instance.value = false;
      setTimeout(() => {
        if (instance && instance.$el) {
          instance.$el.remove();
          instance.$destroy();
        }
        instance = null;
      }, 300); // 等待过渡动画结束
    };
  });
};

export default middleCropper;
