<template>
  <Modal
    ref="customModal"
    :value="value"
    width="600px"
    :title="title"
    :footer-hide="false"
    :mask-closable="false"
    :lock-scroll="true"
    @on-visible-change="changeVisible"
  >
    <div class="content">
      <div class="cropper-box">
        <vue-cropper ref="cropper" :img="currentImg.img" v-bind="options" @realTime="realTime" />
      </div>
      <div class="btn-box">
        <Button @click="changeScale(1)">放大</Button>
        <Button @click="changeScale(-1)">缩小</Button>
        <Button @click="rotateLeft">左转</Button>
        <Button @click="rotateRight">右转</Button>
      </div>
    </div>
    <div slot="footer">
      <Button @click="closeModal">取消</Button>
      <Button :loading="confirmLoading" type="primary" @click="confirm">确定</Button>
    </div>
  </Modal>
</template>

<script>
import { VueCropper } from 'vue-cropper'
import request from 'utils/request';
import axios from 'axios'
import { blobToFile } from '@/utils/helper'

export default {
  name: 'cropper',
  mixins: [],

  components: {
    VueCropper,
  },
  props: {
    value: {
      type: Boolean,
      default: false,
    },
    title: {
      type: String,
      default: '裁剪',
    },
    // 需要被裁剪的图片
    imgUrl: {
      type: String,
      default: '',
    },
  },

  data() {
    return {
      confirmLoading: false,
      showCropper: false,
      options: {
        size: 1,
        autoCropWidth: 300, // 默认生成截图框宽度 默认容器的 80%
        autoCropHeight: 300, // 默认生成截图框高度 默认容器的 80%
        outputType: 'jpg', // 裁剪生成图片的格式 jpeg, png, webp
        autoCrop: true, // 是否默认生成截图框
        fixedBox: false, // 固定截图框大小
        fixed: true, // 是否开启截图框宽高固定比例
        fixedNumber: [1, 1], // 截图框的宽高比例, 开启fixed生效
        centerBox: true, // 截图框是否被限制在图片里面
      },
      currentImg: {
        img: null,
        imgName: '',
      },
      previews: {
        url: '',
      },
    };
  },

  computed: {},

  watch: {},

  created() {},

  mounted() {},

  destroyed() {},

  methods: {
    // 打开裁剪弹窗
    openDialog() {
      this.showCropper = true;
    },
    // 修改图片大小 正数为变大 负数变小
    changeScale(num) {
      num = num || 1;
      this.$refs.cropper.changeScale(num);
    },
    // 向左边旋转90度
    rotateLeft() {
      this.$refs.cropper.rotateLeft();
    },
    // 向右边旋转90度
    rotateRight() {
      this.$refs.cropper.rotateRight();
    },
    // 实时预览事件
    realTime(data) {
      this.previews = data;
    },

    changeVisible(visible) {
      if (visible) {
        this.currentImg.img = this.imgUrl;
      } else {
        this.closeModal();
      }
    },

    clearData() {
      this.currentImg = {
        img: null,
        imgName: '',
      };
      this.previews = {
        url: '',
      };
    },

    closeModal() {
      this.clearData();
      this.$emit('input', false);
    },

    confirm() {
      this.determine()
    },

    // 提交图片
    determine() {
      const fileBlob = this.$refs.cropper.getCropBlob(async data => {
        const config = {
          headers: { 'Content-Type': 'multipart/form-data' }, //上传文件所需headers格式
        }
        const file = await blobToFile(data, this.currentImg.imgName)
        const ext = this.currentImg.imgName?.split('.').slice(-1)?.[0]
        const formData = new FormData()
        formData.append('file', file)
        this.confirmLoading = true
        request.get('/pms_opc/qiniu.getuptoken', { params: { ext } }).then(
          data => {
            this.token = data.token
            this.domain = data.domain
            formData.append('token', this.token)
            axios({
              method: 'post',
              url: 'https://upload.qiniup.com',
              headers: {
                'Content-Type': 'multipart/form-data',
              },
              data: formData,
            })
              .then(res => {
                if (res?.status === 200) {
                  const imgUrl = this.domain + '/' + res?.data.key
                  // this.success(imgUrl)
                  this.$emit('success', imgUrl)
                  this.closeModal();
                }
              })
              .catch(err => {
                console.log(err)
              }).finally(() => this.confirmLoading = false)
          },
          reject => {
            this.$Message.error({
              content: reject.messgae || String(reject),
              duration: 3,
            })
            this.confirmLoading = false
            throw new Error(reject.messgae || reject)
          }
        )
      })
    },
  },
};
</script>

<style scoped lang="less">
@import './index.less';
</style>
