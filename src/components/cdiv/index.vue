<template>
  <el-tooltip v-bind="$attrs" :disabled="disabled" :content="innerText">
    <div ref="cdiv" :style="styles">
      <slot />
    </div>
  </el-tooltip>
</template>

<script>
import { isEmpty } from 'lodash';

export default {
  name: 'cdiv',
  props: {
    line: {
      type: Number,
      default: 1,
    },
    width: {
      type: [Number, String],
      default: 0,
    },
  },
  data() {
    return {
      styles: {},
      innerText: '',
      disabled: true,
    };
  },
  created() {
    this.$nextTick(() => {
      this.initStyle();
    });
  },
  methods: {
    isEmpty,
    initStyle() {
      // 获取组件传下来的所有的style
      this.disabled = true;
      // const styles = window.getComputedStyle(el);
      const result = {};
      // for (let i = 0; i < styles.length; i++) {
      //   const prop = styles[i];
      //   result[prop] = styles.getPropertyValue(prop);
      // }
      if (this.width) {
        result.width = `${typeof this.width === 'number' ? this.width + 'px' : this.width}`;
      }
      result.overflow = 'hidden';
      this.styles = result;
      this.$nextTick(() => {
        this.genEllipsis();
      });
    },
    genEllipsis() {
      const el = this.$refs.cdiv;
      if (!el) return;
      // 检查文本是否超出容器
      const text = el?.innerText;
      const childrenNode = document.createElement('div');

      // el.innerText = '';
      this.innerText = text;
      childrenNode.innerText = text;
      childrenNode.style.width = 'fit-content';
      childrenNode.style.cursor = 'default';
      childrenNode.style.whiteSpace = 'nowrap';

      el.appendChild(childrenNode);
      const containerWidth = el.clientWidth;
      const textWidth = childrenNode.clientWidth;
      childrenNode.remove();
      const result = {};
      if (textWidth > containerWidth) {
        result.display = '-webkit-box';
        result.webkitLineClamp = this.line;
        result.webkitBoxOrient = 'vertical';
        result.textOverflow = 'ellipsis';
        this.styles = {
          ...this.styles,
          ...result,
        };
        el.innerText = this.innerText;
        this.disabled = false;
      }
    },
  },
};
</script>

<style scoped lang="scss"></style>
