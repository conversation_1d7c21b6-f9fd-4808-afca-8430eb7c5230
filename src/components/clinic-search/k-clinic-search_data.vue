<template>
  <div>
    <!--    <Select-->
    <!--        transfer-->
    <!--        :value="value"-->
    <!--        :clearable="isClearable"-->
    <!--        :loading="searchLoading"-->
    <!--        :remote-method="search"-->
    <!--        filterable-->
    <!--        :disabled="disable"-->
    <!--        :transfer-class-name="className"-->
    <!--        transfer-->
    <!--        @on-clear="clearSub"-->
    <!--        @on-query-change="queryChange"-->
    <!--        class="filterable-select"-->
    <!--        placeholder="请输入搜索诊所"-->
    <!--        ref='clinic'-->
    <!--        @on-select="selectSup">-->
    <!--      <Option value="" label="全部诊所">全部诊所</Option>-->
    <!--      <Option v-for="(option, index) in clinic_list" :key="option.id" :value="option.id">{{ option.name }}</Option>-->
    <!--    </Select>-->
    <el-select
      :value="value"
      size="small"
      filterable
      remote
      reserve-keyword
      :clearable="isClearable"
      class="com-search"
      popper-class="k-clinic-filterable-select"
      :append-to-body="true"
      placeholder="请输入搜索诊所"
      :remote-method="searchMethod"
      @change="selectSup"
      :loading="searchLoading"
    >
      <el-option label="全部诊所" v-if="showAll" value=""></el-option>
      <el-option v-for="item in clinic_list" :key="item.id" :label="item.name" :value="item.id"> </el-option>
    </el-select>
    <input style="position: absolute; left: -9999px" type="text" name="username" />
  </div>

  <!-- <Select :value="value" style="width:200px" @on-select="selectSup" @on-clear="clearSub" :clearable="isClearable" placeholder="请选择供应商">
    <Option v-for="(option, index) in supplier_list"  :key="option.id" :value="option.id">{{ option.name }}</Option>
  </Select> -->
</template>

<script>
import util from '@/utils/util';

export default {
  name: 'k-clinic-search',
  components: {},
  mixins: [],
  props: {
    isClearable: {
      type: Boolean,
      default: true
    },
    value: {
      type: String,
      default: 'all'
    },
    disable: {
      type: Boolean,
      default: false
    },
    className: {
      type: String,
      default: ''
    },
    showAll: {
      type: Boolean,
      default: true
    },
    extraParams: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      searchLoading: false,
      // supplierList: [],
      clinic_list: [],
      query: ''
    };
  },
  computed: {},
  watch: {},
  created() {
    this.searchMethod('');
  },
  mounted() {},
  methods: {
    searchMethod: util.debounce(function (query) {
      this.searchLoading = true;
      const params = { name: query, pageSize: 100, ...this.extraParams };
      this.$api.getCommonClinicList(params).then(res => {
        this.searchLoading = false;
        this.clinic_list = res.list;
        sessionStorage.setItem('clinic_list', JSON.stringify(this.clinic_list));
        sessionStorage.setItem('store_list', JSON.stringify(this.clinic_list));
      });
    }, 200),
    search() {},
    selectSup(val) {
      console.log('val', val);
      this.searchMethod('');
      this.$emit('input', val);
      this.$emit('change', val);
      this.$emit('clickItem', val);
    },
    queryChange(val) {
      this.query = val;
      this.searchMethod(val);
    },
    clearSub() {
      this.searchMethod('');

      this.$emit('input', '');
      this.$emit('clickItem');
    }
  }
};
</script>

<style lang="less" scoped>
.filterable-select {
  ::v-deep .ivu-select-input {
    margin-top: -1px;
  }
}
</style>
<style>
.k-clinic-filterable-select {
  z-index: 9999 !important;
}
</style>
