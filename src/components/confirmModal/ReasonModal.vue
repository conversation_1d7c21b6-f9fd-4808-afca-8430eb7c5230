<template>
  <div class="ReasonModal-wrapper">
    <Modal :value="visible" :mask-closable="false" @on-visible-change="visibleChange" :title="title">
      <div>
        <p class="mb10">
          {{ label }} <span class="audit-tip" v-if="showTips">(建议提前联系买家确认情况后再操作)</span>
        </p>
        <Input ref="reasonIpt" v-model="refuseText" class="refuse-input" :placeholder="placeholder" type="textarea" />
      </div>

      <div slot="footer">
        <Button @click="closeModal">取消</Button>
        <Button type="primary" @click="refuseOk">确定</Button>
      </div>
    </Modal>
  </div>
</template>

<script>
export default {
  name: 'ReasonModal',
  mixins: [],
  model: {
    prop: 'visible',
    event: 'change',
  },
  components: {},

  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    title: {
      type: String,
      default: '审核驳回',
    },
    label: {
      type: String,
      default: '请输入驳回原因：',
    },
    placeholder: {
      type: String,
      default: '请输入驳回原因',
    },
    showTips: {
      type: [String, Boolean],
      default: false,
    },
    // 是否是审核驳回弹窗
    isRequired: {
      type: Boolean,
      default: true,
    },
    defaultClose: {
      type: Boolean,
      default: true,
    },
  },

  data() {
    return {
      refuseText: '',
    };
  },

  computed: {},

  watch: {},

  created() {},

  mounted() {},

  methods: {
    visibleChange(val) {
      if (!val) {
        this.refuseText = '';
        this.closeModal();
      } else {
        this.$nextTick(()=>{
          this.$refs.reasonIpt.focus();
        })
      }
    },
    closeModal() {
      this.$emit('change', false);
    },
    refuseOk() {
      if (this.isRequired && !this.refuseText) {
        this.$Message.error('请输入驳回原因');
        return;
      }
      this.defaultClose && this.closeModal();
      this.$emit('ok', this.refuseText);
    },
  },

  destroyed() {},
};
</script>

<style scoped lang="less">
.audit-tip {
  color: #e6a23c;
}

.mb10 {
  margin-bottom: 10px;
}

.refuse-input {
  width: 100%;
  max-width: 100%;

  /deep/ .ivu-input {
    height: 100px;
  }
}
</style>
