<template>
  <Modal
      :value="visible"
      :title="title"
      width="400px"
      @on-visible-change="changeVisible"
      class-name="tips-modal vertical-center-modal"
      transfer
      :closable="false"
  >
    <div class="content">
<!--      <div class="title" v-if="contentTitle">-->
<!--        <Icon type="ios-help-circle" size="26" color="#ff9900"></Icon>-->
<!--        <span class="h-title">{{ contentTitle }}</span>-->
<!--      </div>-->
      <div class="content-text">
        <p>{{ contentText }}</p>
      </div>
    </div>
    <div slot="footer">
      <Button v-if="showCancel" @click="cancel" >
        <slot name="cancel" :row="{title: contentText, value: 1123}">{{ cancelText }}</slot>
      </Button>
      <Button type="primary" @click="confirmPass">{{ confirmText }}</Button>
    </div>
  </Modal>
</template>

<script>
export default {
  name: 'TipsModal',
  mixins: [],

  components: {},
  model: {
    prop: 'visible',
    event: 'update:visible'
  },
  props: {
    title: {
      type: String,
      default: '温馨提示'
    },
    confirmText:{
      type: String,
      default: '确定'
    },
    cancelText:{
      type: String,
      default: '取消'
    },
    showCancel:{
      type: Boolean,
      default: true
    },
    visible: {
      type: Boolean,
      default: false
    },
    contentText: {
      type: String,
      default: '确定要通过审核吗？'
    }

  },

  data() {
    return {}
  },

  computed: {},

  watch: {},

  created() {
  },

  mounted() {
  },

  destroyed() {
  },

  methods: {
    cancel(){
      this.changeVisible(false)
      this.$emit('onCancel')
    },
    confirmPass(){
      this.$emit('onOk')
    },
    changeVisible(val){
      !val&&this.$emit('update:visible', val)
    },
  },
}
</script>

<style scoped lang="less">
.tips-modal{

  .content{
    padding: 0px;
  }
}
::v-deep .ivu-modal-footer{
  border-top: none;
}
.title{
  display: flex;
  align-items: center;
  padding-left: 10px;
  .h-title{
    font-size: 15px;
    font-weight: 500;
    margin-left: 6px;
    line-height: 26px;
  }
}
.content-text{
  //padding: 16px;
  line-height: 21px;
  font-size: 13px;
}
</style>
