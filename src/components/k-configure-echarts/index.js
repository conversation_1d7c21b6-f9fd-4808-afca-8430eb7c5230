// 用来遍历所有的options
const modulesFiles = require.context('./options', true, /index.js$/)
let modules = {}
let moduleNames = []
modulesFiles.keys().forEach(item => {
  for ( const funName in modulesFiles(item).default ) {
    moduleNames.push(funName)
  }
  modules = Object.assign({}, modules, modulesFiles(item).default)
})
checkForDuplicates(moduleNames)
function checkForDuplicates(array) {
  if(new Set( array ).size !== array.length) {
    throw new Error('不允许申明重复的方法名')
  }
}
export default modules