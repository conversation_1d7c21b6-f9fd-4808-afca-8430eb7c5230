<template>
		<div :id='id' :ref="id" style="height:300px;width: 100%"></div>
</template>
<script>
export default {
	name: "KAnnular<PERSON>ie",
	props: {
		id: {
			type: String,
			default: () => 'annularPie'
		},
	},
	data() {
		return {
		};
	},
	watch: {
	},
	created() {
	},
	mounted() {
		this.init()
	},
	methods: {
		init() {
			this.$nextTick(() => {
				this.drawLine();
				// var myChart = this.$echarts.init(document.getElementById(this.id));
				// window.addEventListener("resize", function() {
				// 	myChart.resize();
				// });
			})
		},
		drawLine() {
			var myChart = this.$echarts.init(document.getElementById(this.id));
			window.addEventListener("resize", function() {
					myChart.resize();
				});
			myChart.setOption({
				title: {
					text: "",
					subtext: "",
					left: 'center',
					top: "bottom"
				},
        legend: {
          top: 'bottom',
          left: 'center',
					show: false
        },
        tooltip: {
          trigger: 'item'
        },
				series: [
					{
            name: 'Access From',
            type: 'pie',
            radius: ['40%', '70%'],
            avoidLabelOverlap: false,
            itemStyle: {
              borderColor: '#fff',
              borderWidth: 2,
              borderRadius: 10,
            },
            label: {
              show: false,
              position: 'center'
            },
            emphasis: {
              label: {
                show: true,
                fontSize: '18',
                fontWeight: 'bold'
              }
            },
            labelLine: {
              show: false
            },
            data: [
              { value: 1048, name: 'Search Engine' },
              { value: 735, name: 'Direct' },
              { value: 580, name: 'Email' },
              { value: 484, name: 'Union Ads' },
              { value: 300, name: 'Video Ads' }
            ]
          }
				],
			});
		}
	}
};
</script>

<style lang="less" scoped>
</style>
