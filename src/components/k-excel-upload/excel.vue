<template>
  <div class="wrapper">
    <div class="buttonBox">
      <el-upload
        ref="upload"
        type="button"
        action
        accept=".xlsx, .xls"
        :auto-upload="false"
        :show-file-list="false"
        :on-change="handle"
        class="flex flex-item-align"
      >
        <Button v-if="btnType === 'button'" type="primary" class="space6" :loading="excelUploadLoading">{{
          btnText
        }}</Button>
        <a v-if="btnType === 'link'">{{ btnText }}</a>
        <p>
          <a v-if="btnType === 'text' && !name">{{ btnText }}</a>
          <span v-if="name" style="color: #155bd4">{{ this.name }}</span>
        </p>
      </el-upload>
    </div>
  </div>
</template>

<script>
// import xlsx from 'xlsx'
import { utils, read } from 'xlsx';
export default {
  name: 'excel',
  components: {},
  mixins: [],
  props: {
    excelUploadLoading: {
      type: Boolean,
      default: false
    },
    btnText: {
      type: String,
      default: '上传,'
    },
    btnType: {
      type: String,
      default: 'button'
    },
    isStock: {
      type: Boolean,
      default: false
    },
    isWarn: {
      type: Boolean,
      default: false
    },
    isSmart: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      name: ''
    };
  },
  computed: {},
  watch: {},
  created() {},
  mounted() {},
  methods: {
    // 将流数据转化为json数据
    async handle(file) {
      //读取FILE中的数据（变为JSON格式）
      let excelJsonList = await this.readFile(file.raw);
      let workbook = read(excelJsonList, { type: 'binary' }),
        worksheet = workbook.Sheets[workbook.SheetNames[0]];
      this.html = utils.sheet_to_html(worksheet);
      excelJsonList = utils.sheet_to_json(worksheet);

      if (this.isStock) {
        // 出入库
        this.stockExcelUpload(excelJsonList);
      } else if (this.isWarn) {
        // 批量库存预警
        this.excelWarnUpload(excelJsonList, file.name);
      } else if (this.isSmart) {
        this.excelSmartUpload(excelJsonList, file.name);
      } else {
        // 诊所货品
        this.excelUpload(excelJsonList, file.name);
      }
    },
    // 读取数据
    readFile(file) {
      return new Promise(resolve => {
        let reader = new FileReader();
        reader.readAsBinaryString(file);
        reader.onload = ev => {
          resolve(ev.target.result);
        };
      });
    },
    // 将excel读取的日期数字改为时间格式
    formatDate(numb, format) {
      if (numb.toString().length == 5) {
        const old = numb - 1;
        const t = Math.round((old - Math.floor(old)) * 24 * 60 * 60);
        const time = new Date(1900, 0, old, 0, 0, t);
        const year = time.getFullYear();
        const month = time.getMonth() + 1;
        const date = time.getDate();
        return year + format + (month < 10 ? '0' + month : month) + format + (date < 10 ? '0' + date : date);
      } else {
        return numb;
      }
    },
    // 单独处理excel的日期
    handleDate(time) {
      if (time == '' || time == undefined || time.toString().includes('.')) return '';
      if (time.toString().includes('-') || time.toString().includes('/')) {
        return time;
      } else {
        return this.formatDate(time, '/');
      }
    },
    // 将出入库模板提取好的数据转发出去
    stockExcelUpload(excelJsonList) {
      let hasHandleExcelList = this.handleStockExcelJsonList(excelJsonList);
      // if (!hasHandleExcelList.length) {
      //   this.$Message.error('导入表格数据无效')
      //   return
      // }
      this.$emit('stockExcelUpload', hasHandleExcelList);
    },
    excelSmartUpload(excelJsonList, fileName) {
      let hasHandleExcelList = this.handleSmartJsonList(excelJsonList);
      if (!hasHandleExcelList.length) {
        this.$Message.error('导入表格数据无效');
        return;
      }
      this.name = fileName;
      this.$emit('excelUpload', hasHandleExcelList);
    },
    // 将提取好的数据转发出去
    excelUpload(excelJsonList, fileName) {
      let hasHandleExcelList = this.handleExcelJsonList(excelJsonList);
      if (!hasHandleExcelList.length) {
        this.$Message.error('导入表格数据无效');
        return;
      }
      this.name = fileName;
      this.$emit('excelUpload', hasHandleExcelList);
    },
    // 将入库单的excel数据处理
    handleStockExcelJsonList(excelJsonList) {
      // 如果待处理的数据为空则直接返回空数组
      if (!excelJsonList || !excelJsonList.length) {
        return [];
      }
      let resultArr = [];
      const id = '编码';
      const generic_name = '货品';
      const prod_spec = '规格';
      const grade_desc = '品级';
      const manufacturer = '厂家';
      const warehouse_unit = '单位';
      // 以下六个字段读取为空的时候，该数据不要
      const purchase_stock_num = '数量';
      const purchase_price = '单价';
      const batch_code = '批号';
      const produce_time = '生产日期';
      const expire_time = '有效期';
      // const invoice_code = '发票号'; // 2023_07_25优化功能中去除发票号
      excelJsonList.forEach(item => {
        let abandonArr = [purchase_stock_num, purchase_price, batch_code, produce_time, expire_time];
        let isAbandon = abandonArr.every(abandon_item => {
          return item[abandon_item] == undefined;
        });
        if (isAbandon) return;
        console.log('-> this.handleDate(item[produce_time])', this.handleDate(item[produce_time]));
        resultArr.push({
          id: item[id],
          warehouse_unit: item[warehouse_unit],
          generic_name: item[generic_name],
          prod_spec: item[prod_spec],
          grade_desc: item[grade_desc],
          manufacturer: item[manufacturer],
          purchase_price: item[purchase_price],
          batch_code: item[batch_code],
          purchase_stock_num: item[purchase_stock_num],
          // invoice_code: item[invoice_code],
          produce_time: this.handleDate(item[produce_time]),
          expire_time: this.handleDate(item[expire_time])
        });
      });
      return resultArr || [];
    },
    /**
     * 将excel的json数据按照以下标准进行过滤
     * 修改零售价字段有数据且不等于零售价的数据视为有效数据
     * 过滤出对应的商品编号，零售价，修改零售价三个字段，
     * 数据格式为二位数组
     */
    handleExcelJsonList(excelJsonList) {
      // 如果待处理的数据为空则直接返回空数组
      if (!excelJsonList || !excelJsonList.length) {
        return [];
      }
      let resultArr = [];
      const id = '商品编码';
      const after_retail_price = '修改零售价';
      const retail_price = '零售价';
      excelJsonList.forEach(item => {
        if (!!item[after_retail_price] && item[after_retail_price] !== item[retail_price]) {
          resultArr.push({
            id: item[id],
            after_retail_price: item[after_retail_price],
            retail_price: item[retail_price]
          });
        }
      });
      return resultArr || [];
    },
    handleSmartJsonList(excelJsonList) {
      console.log('smart', excelJsonList);
      // 如果待处理的数据为空则直接返回空数组
      if (!excelJsonList || !excelJsonList.length) {
        return [];
      }
      let resultArr = [];
      const id = '编码';
      const after_retail_price = '修改后的零售价';
      const retail_price = '零售价';
      excelJsonList.forEach(item => {
        if (!!item[after_retail_price] && item[after_retail_price] !== item[retail_price]) {
          resultArr.push({
            id: item[id],
            after_retail_price: item[after_retail_price],
            retail_price: item[retail_price]
          });
        }
      });
      console.log(resultArr);
      return resultArr || [];
    },

    /* 批量库存预警的excel数据逻辑 */
    excelWarnUpload(excelJsonList, fileName) {
      let hasHandleExcelList = this.handleWarnExcelJsonList(excelJsonList);
      if (!hasHandleExcelList.length) {
        this.$Message.error('导入表格数据无效');
        return;
      }
      this.name = fileName;
      this.$emit('excelUpload', hasHandleExcelList);
    },

    /**
     * 将excel的json数据按照以下标准进行过滤
     * 库存预警数量不为空的数据视为有效数据
     * 过滤出对应的商品编号，库存预警数量，两个字段，
     * 数据格式为二位数组
     */
    handleWarnExcelJsonList(excelJsonList) {
      // 如果待处理的数据为空则直接返回空数组
      if (!excelJsonList || !excelJsonList.length) {
        return [];
      }
      let resultArr = [];
      const id = '编码';
      const stock_warning = '预警数量';
      excelJsonList.forEach(item => {
        console.log('item[stock_warning]', item[stock_warning]);
        if (item[stock_warning] !== undefined) {
          resultArr.push({
            id: item[id],
            stock_warning: item[stock_warning]
          });
        }
      });

      return resultArr || [];
    }
  },
  filters: {}
};
</script>
<style lang="less" scoped>
p {
  margin-bottom: 0px;
}
</style>
