<template>
  <Modal v-model="modalValue" title="选择产品" :mask-closable="false" :width="750" class-name="vertical-center-modal">
    <div style="position: relative">
      <Button to="/goods/warehouse/list" target="_blank">产品列表</Button>
      <Dvd /><Dvd />
      <a @click="onRefresh">刷新</a>
      <div style="position: absolute; right: 0px; top: 0px">
        <Select
          v-model="queryFormData.goods_type"
          placeholder="商品类型"
          clearable
          class="mr10"
          style="width: 150px"
          @on-change="onSearch"
        >
          <Option v-for="(type, typeKey) in typeDesc" :value="type.kw" :key="type.kw">{{ type.desc }}</Option>
        </Select>

        <Input
          v-model="queryFormData.name"
          placeholder="输入商品ID或名称搜索"
          clearable
          class="cursor-wrapper"
          @keyup.enter.native="onSearch"
          @on-clear="onSearch"
          icon="ios-search"
          @on-click="onSearch"
          style="width: 180px"
        >
          <!-- <Icon type="ios-search" slot="suffix" /> -->
        </Input>
      </div>
    </div>
    <div class="block_10"></div>
    <div style="position: relative; height: 380px; overflow: auto">
      <Table
        ref="selection"
        height="310"
        @on-select="onSelect"
        @on-select-cancel="onSelectCancel"
        @on-select-all="onSelectAll"
        @on-select-all-cancel="onSelectAllCancel"
        :columns="tableCols"
        :data="list"
        :loading="tableLoading"
      >
        <template slot-scope="{ row }" slot="info">
          <span class="clip">
            <KLink :to="{ path: '/goods/item/list', query: { id: row.id } }" target="_blank">{{ row.name }}</KLink>
          </span>
        </template>
        <template slot-scope="{ row }" slot="price">
          <p v-if="row.price">￥{{ row.price | number_format }}</p>
          <p v-else>-</p>
        </template>
      </Table>

      <div class="block_20"></div>

      <KPage
        v-if="total > 0"
        :total="+total"
        :page-size="+queryFormData.pageSize"
        :page-size-opts="[5]"
        :current="queryFormData.page"
        @on-change="handleCurrentChange"
        @on-page-size-change="handleSizeChange"
        style="text-align: center"
      />
    </div>
    <div slot="footer">
      <div v-if="Object.keys(selected_items).length > 0" style="display: inline-block" class="lr15 text-muted">
        已选: 产品(<span class="text-error">{{ Object.keys(selected_items).length }}</span
        >)
      </div>
      <Button @click="modalValue = false">取消</Button>
      <Button type="primary" @click="onConfirm">确定</Button>
    </div>
  </Modal>
</template>

<script>
import S from '@/utils/util';

let init_query_from_data = {
  page: 1,
  pageSize: 5,
  name: '',
  goods_type: '',
  is_relation_goods: 1,
  with_sku: 1, // 获取sku属性
};

export default {
  name: 'k-goods-services',
  props: {
    value: {
      type: Boolean,
      default: false,
    },
    disabledItemIds: {
      type: Array,
      default() {
        return [];
      },
    },
  },

  data() {
    return {
      queryFormData: { ...init_query_from_data },
      modalValue: false,

      tableCols: [
        { type: 'selection', width: 60 },
        { title: 'ID', key: 'id' },
        { title: '商品名', key: 'name' },
        { title: '类型', key: 'goods_type_text' },
        { title: '商品来源', key: 'source_platform_text' },
        { title: '分发状态', key: 'status_text' },
        { title: '价格', slot: 'price', width: 100 },
      ],
      tableLoading: false,

      list: [],
      total: 0,
      statusDesc: {},

      selected_items: {},
      typeDesc: [
        { desc: '实物商品', kw: '10' },
        { desc: '虚拟商品', kw: '15' },
        { desc: '通兑券', kw: '25' },
      ],
    };
  },

  methods: {
    onSearch: function () {
      this.queryFormData.page = 1;
      this.get();
    },

    handleSizeChange(val) {
      this.queryFormData.page = 1;
      this.queryFormData.pageSize = val;
      this.get();
    },
    handleCurrentChange(val) {
      this.queryFormData.page = val;
      this.get();
    },

    onRefresh: function () {
      this.get();
    },

    onSelect: function (selection, row) {
      this.$set(this.selected_items, row.id, row);
    },

    onSelectAll: function (selection) {
      selection.forEach(item => {
        this.$set(this.selected_items, item.id, item);
      });
    },

    onSelectCancel: function (selection, row) {
      this.$delete(this.selected_items, row.id);
    },

    onSelectAllCancel: function (selection) {
      for (let k in this.list) {
        this.$delete(this.selected_items, this.list[k].id);
      }
    },

    onConfirm: function () {
      let items = [];
      for (let key in this.selected_items) {
        items.push(this.selected_items[key]);
      }
      this.$emit('on-selected', items);
    },

    get: function () {
      this.tableLoading = true;
      this.$api
        .getGoodsLibList({ ...this.queryFormData })
        .then(data => {
          this.list = this.handler(data.list);
          this.total = data.total;

          this.tableLoading = false;
        })
        .catch(error => {
          this.$Message.error(error.errmsg);
        });
    },

    handler: function (list) {
      for (let k in list) {
        // 待分发的禁止勾选
        if ( list[k].status === '800' ) {
          list[k]['_disabled'] = true; // 禁止选中
        }
        for (let j in this.selected_items) {
          if (list[k].id == this.selected_items[j].id) {
            list[k]['_checked'] = true; // 选中已选项
          }
        }

        if (S.inArray(Number(list[k].id), this.disabledItemIds)) {
          list[k]['_disabled'] = true; // 选中已选项
        }
      }
      return list;
    },

    clearQuery: function () {
      this.queryFormData = { ...init_query_from_data };
      this.queryFormData.page = 1;
      this.list = [];
      this.total = 0;
      this.selected_items = {};
    },
  },

  watch: {
    value: function (val) {
      this.modalValue = val;
      if (val == true) {
        this.clearQuery();
        this.get();
      }
    },

    modalValue: function (val) {
      this.$emit('input', val);
    },

    selected_items: function (val) {
      // S.log(this.selected_items)
    },
  },
};
</script>

<style lang="less" scoped>
.vertical-center-modal {
  display: flex;
  align-items: center;
  justify-content: center;
  .ivu-modal {
    top: 0;
  }
  .ivu-modal-body {
    padding-bottom: 0;
  }
}
.clip {
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
  width: 90%;
  display: inline-block;
}
::v-deep .cursor-wrapper {
  .ivu-icon-ios-search {
    cursor: pointer;
  }
}
</style>
