<template>
  <div style="width: 800px">
    <!-- <KWidget label="直营中心售价：" required> -->
    <div class="mt-8">
      <a @click="openOpc">选择直营中心（{{ formData?.opc_price_list.length }}）</a>
      <div class="text-gray">
        选择指定直营中心单独设置售价后，分发到下属诊所售价会以所属的直营中心单独设置售价为准。
      </div>
    </div>
    <Table border :data="formData?.opc_price_list" :columns="opc_price_columns">
      <template v-slot:price="{ row, index }">
        <InputNumber v-model="formData.opc_price_list[index].price" />
      </template>
      <template v-slot:vip_price="{ row, index }">
        <InputNumber v-model="formData.opc_price_list[index].vip_price" />
      </template>
      <template v-slot:operate="{ row }">
        <a style="color: red" @click="onDel(row, 'opc_price_list')">删除</a>
      </template>
    </Table>
    <!-- </KWidget> -->
    <select-opc-modal v-model="opcVisible" @on-selected="onSelected" />
  </div>
</template>

<script>
import selectOpcModal from '@/components/select-opcenter-modal/select-opc-modal.vue';

export default {
  name: 'OpcPrice',
  components: { selectOpcModal },
  props: {
    data: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      opcVisible: false,
      formData: {
        // 直营中心售价
        opc_price: [],
        opc_price_list: [],
      },
      opc_price_columns: [
        {
          title: '直营中心名称',
          key: 'opc_name',
        },
        {
          title: '*销售价',
          slot: 'price',
        },
        {
          title: '*会员价',
          slot: 'vip_price',
        },
        {
          title: '操作',
          slot: 'operate',
        },
      ],
    };
  },
  watch: {
    data: {
      handler(val) {
        this.formData = val;
      },
      immediate: true,
    },
  },
  created() {},
  mounted() {},
  methods: {
    openOpc() {
      this.opcVisible = true;
    },

    /**
     * 添加直营中心
     */
    onSelected(arr) {
      // 直营中心售价添加
      const curIds = this.formData.opc_price_list?.map(item => item.opc_id);

      // 筛选出新数组中未添加的项
      const newItems = arr
        .filter(item => !curIds.includes(item.id))
        .map(item => {
          return {
            opc_id: item.id,
            opc_name: item.name,
            price: 0,
            vip_price: 0,
          };
        });

      if (newItems.length === 0) {
        // 如果没有新项目可以添加，给出提示
        this.$Message.warning('所选项已添加');
      } else {
        this.formData.opc_price_list = [...this.formData.opc_price_list, ...newItems];
      }

      this.opcVisible = false;
    },

    onDel(row, key) {
      const index = this.formData?.[key].findIndex(item => item.id == row.id);
      if (index > -1) {
        this.$delete(this.formData?.[key], index);
      }
    },
  },
};
</script>
<style scoped lang="less"></style>
