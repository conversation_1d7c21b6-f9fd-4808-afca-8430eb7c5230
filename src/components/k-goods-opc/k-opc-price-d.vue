<template>
  <div style="width: 800px">
    <Table border :data="data" :columns="opc_price_columns">
      <template v-slot:price="{ row }">
        <span>¥ {{ row.price | number_format }}</span>
      </template>
      <template v-slot:vip_price="{ row }">
        <span>¥ {{ row.vip_price | number_format }}</span>
      </template>
    </Table>
  </div>
</template>

<script>
export default {
  name: 'OpcPriceD',
  props: {
    data: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      opc_price_columns: [
        {
          title: '直营中心名称',
          key: 'opc_name',
        },
        {
          title: '*销售价',
          slot: 'price',
        },
        {
          title: '*会员价',
          slot: 'vip_price',
        },
      ],
    };
  },
  watch: {
    data: {
      handler(val) {
        this.formData = val;
      },
      immediate: true,
    },
  },
  created() {},
  mounted() {},
  methods: {},
};
</script>
<style scoped lang="less"></style>
