<template>
  <Modal v-model="modalValue" title="选择商品" :mask-closable="false" :width="750" class-name="vertical-center-modal">
    <div style="position: relative">
      <Button to="/goods/item/list" target="_blank">商品管理</Button>
      <Dvd /><Dvd />
      <a @click="onRefresh">刷新</a>
      <div style="position: absolute; right: 0px; top: 0px">
        <Input
          v-model="queryFormData.goods_keyword"
          placeholder="搜索商品标题或者商品ID"
          clearable
          @keyup.enter.native="onSearch"
          @on-clear="onSearch"
          style="width: 180px"
        >
          <Icon type="ios-search" slot="suffix" />
        </Input>
      </div>
    </div>
    <div class="block_10"></div>
    <div style="position: relative; height: 445px; overflow: auto">
      <Table
        ref="selection"
        @on-select="onSelect"
        @on-select-cancel="onSelectCancel"
        @on-select-all="onSelectAll"
        @on-select-all-cancel="onSelectAllCancel"
        :columns="tableCols"
        :data="list"
        :loading="tableLoading"
      >
        <template slot-scope="{ row }" slot="id">
          {{ row.id }}
        </template>
        <template slot-scope="{ row }" slot="info">
          <div class="media-left media-middle">
            <img
              :src="row.main_img | imageStyle('B.w300')"
              style="width: 35px; margin-right: 5px"
              class="img-rounded"
              :title="'id:' + row.id"
            />
          </div>
          <div class="media-body">
            <div class="clip">
              <KLink :to="{ path: '/goods/item/list', query: { id: row.id } }" target="_blank">{{ row.name }}</KLink>
            </div>
            <div>{{ row.price }}</div>
          </div>
        </template>
        <template slot-scope="{ row }" slot="status">
          {{ statusDesc[row.status].desc }}
        </template>
      </Table>

      <div class="block_20"></div>

      <KPage
        v-if="total > 0"
        :total="total"
        :page-size.sync="queryFormData.pageSize"
        :page-size-opts="[5]"
        :current.sync="queryFormData.page"
        @on-change="onPageChange"
        style="text-align: center"
      />
    </div>
    <div slot="footer">
      <div v-if="Object.keys(selected_items).length > 0" style="display: inline-block" class="lr15 text-muted">
        已选: 商品(<span class="text-error">{{ Object.keys(selected_items).length }}</span
        >)
      </div>
      <Button @click="modalValue = false">取消</Button>
      <Button type="primary" @click="onConfirm">确定</Button>
    </div>
  </Modal>
</template>

<script>
/* eslint-disable */
import S from 'utils/util'; // Some commonly used tools
import io from 'utils/request'; // Http request
import * as runtime from 'utils/runtime'; // Runtime information
/* eslint-disable */

let init_query_from_data = {
  page: 1,
  pageSize: 5,
  goods_keyword: '',
  status_list: [],
  xn_scope: '0,1,9'
};

export default {
  name: 'k-goods-select-multiple',
  props: {
    value: {
      type: Boolean,
      default: false
    },
    disabledItemIds: {
      type: Array,
      default() {
        return [];
      }
    }
  },

  data() {
    return {
      queryFormData: { ...init_query_from_data },
      modalValue: false,

      tableCols: [
        { type: 'selection', width: 60 },
        { title: 'ID', slot: 'id', width: 50 },
        { title: '商品信息', slot: 'info' },
        { title: '状态', slot: 'status', width: 100 }
      ],
      tableLoading: false,

      list: [],
      total: 0,
      statusDesc: {},
      selected_items: {}
    };
  },

  methods: {
    onSearch: function () {
      this.queryFormData.page = 1;
      this.get();
    },

    onPageChange: function (page, pageSize) {
      console.log('-> page, pageSize', page, pageSize);
      this.queryFormData.page = page;
      this.queryFormData.pageSize = pageSize;
      this.get();
    },

    onRefresh: function () {
      this.get();
    },

    onSelect: function (selection, row) {
      this.$set(this.selected_items, row.id, row);
    },

    onSelectAll: function (selection) {
      selection.forEach(item => {
        this.$set(this.selected_items, item.id, item);
      });
    },

    onSelectCancel: function (selection, row) {
      this.$delete(this.selected_items, row.id);
    },

    onSelectAllCancel: function (selection) {
      for (let k in this.list) {
        this.$delete(this.selected_items, this.list[k].id);
      }
    },

    onConfirm: function () {
      let items = [];
      for (let key in this.selected_items) {
        items.push(this.selected_items[key]);
      }
      this.$emit('on-selected', items);
    },

    get: function () {
      this.tableLoading = true;

      io.get('clinic/goods.index.list', { data: this.queryFormData })
        .then(data => {
          this.list = this.handler(data.goods_items);
          this.statusDesc = data.statusDesc;
          this.total = data.total;

          this.tableLoading = false;
        })
        .catch(error => {
          this.$Message.error(error.errmsg);
        });
    },

    handler: function (list) {
      for (let k in list) {
        for (let j in this.selected_items) {
          if (list[k].id == this.selected_items[j].id) {
            list[k]['_checked'] = true; // 选中已选项
          }
        }

        if (S.inArray(Number(list[k].id), this.disabledItemIds)) {
          list[k]['_disabled'] = true; // 选中已选项
        }
      }
      return list;
    },

    clearQuery: function () {
      this.queryFormData = { ...init_query_from_data };
      this.queryFormData.page = 1;
      this.list = [];
      this.total = 0;
      this.selected_items = {};
    }
  },

  watch: {
    value: function (val) {
      this.modalValue = val;
      if (val == true) {
        this.clearQuery();
        this.get();
      }
    },

    modalValue: function (val) {
      this.$emit('input', val);
    },

    selected_items: function (val) {
      // S.log(this.selected_items)
    }
  }
};
</script>

<style lang="less">
.vertical-center-modal {
  display: flex;
  align-items: center;
  justify-content: center;
  .ivu-modal {
    top: 0;
  }
  .ivu-modal-body {
    padding-bottom: 0;
  }
}
.clip {
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
  width: 90%;
  display: inline-block;
}
</style>
