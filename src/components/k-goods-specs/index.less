.specs-wrapper {
  .picture-list {
    display: inline-block;
  }
  .picture-list .picture-list__pic {
    position: relative;
    display: inline-block;
    background: no-repeat;
    background-size: cover;
    background-position: 50%;
    width: 60px;
    height: 60px;
    margin: 0 10px 10px 0;
    cursor: move;
  }
  .picture-list .picture-list__remove {
    position: absolute;
    width: 20px;
    height: 20px;
    top: -10px;
    right: -10px;
    cursor: pointer;
    background: url(https://img-sn-i01s-cdn.rsjxx.com/backstage/2019/0509/103116_32332.png) no-repeat;
    background-size: 20px 20px;
    background-position: 50%;
  }
  .picture-list .ks-dd-dragging {
    opacity: 0.5;
    z-index: 2;
  }
  .picture-list .ks-dd-proxy {
    opacity: 1;
    position: absolute;
  }
  .picture-list .ks-dd-dragging .picture-list__remove {
    display: none;
  }
  .picture__pic {
    position: relative;
    display: inline-block;
    background: no-repeat;
    background-size: cover;
    background-position: 50%;
    width: 40px;
    height: 40px;
    margin: 0;
  }
  .picture__remove {
    position: absolute;
    width: 20px;
    height: 20px;
    top: -10px;
    right: -10px;
    cursor: pointer;
    background: url(https://img-sn-i01s-cdn.rsjxx.com/backstage/2019/0509/103116_32332.png) no-repeat;
    background-size: 20px 20px;
    background-position: 50%;
  }
  .picture-upload {
    display: inline-block;
    margin: 0 10px 10px 0;
  }
  .picture-upload .picture-upload-icon {
    display: inline-block;
    width: 60px;
    height: 60px;
    margin: 0 0 10px;
    border: 1px dashed #bbb;
    border-radius: 2px;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
    cursor: pointer;
    background: url(https://img-sn-i01s-cdn.rsjxx.com/backstage/2019/0509/103258_76866.png) no-repeat;
    background-size: 26px 22px;
    background-position: 50%;
  }
  .picture-upload {
    width: 60px;
    height: 60px;
  }
  .picture-upload img {
    width: 100%;
  }
  .input-tiny {
    width: 40px;
  }
  .input-mini {
    width: 80px!important;
  }
  .input-mini1 {
    width: 100px!important;
  }
  .input-mini2 {
    width: 120px!important;
    max-width: none !important;
  }
  .goods-specs-table .picture-upload {
    margin: 0;
    width: 40px;
    height: 40px;
  }
  .goods-specs-table .picture-upload .picture-upload-icon {
    margin: 0;
    width: 40px;
    height: 40px;
    background-size: 13px 11px;
  }
  .goods-specs-table {
    margin-bottom: 0;
  }
  .from-category-select {
    width: 100px;
  }
  .from-template-select {
    width: 230px;
  }
  .video-upload-box .picture-upload {
    width: 80px;
    height: 80px;
  }
  .video-upload-box .picture-upload-icon {
    text-align: center;
    background-image: none!important;;
    line-height: 80px;
    width: 80px;
    height: 80px;
  }
  .video-upload-progress {
    display:none;
    height: 7px;
    position: relative;
    z-index: 9999999;
    margin: 0 5px;
    top: 48%;
  }
  .progress-bar {
    background-color: #00cc99;
  }
  .picture-video__remove {
    position: absolute;
    width: 20px;
    height: 20px;
    top: -10px;
    right: -10px;
    cursor: pointer;
    background: url(https://img-sn-i01s-cdn.rsjxx.com/backstage/2019/0509/103116_32332.png) no-repeat;
    background-size: 20px 20px;
    background-position: 50%;
  }
  .video-picture-preview {
    display:none;
    cursor: pointer;
  }
  .video-picture-preview img {
    width: 80px;
    height: 80px;
    position: absolute;
    top: 0;
  }

  .video-play-layer{
    width: 100%;
    left: 0px;
    top: 0px;
    height: 100%;
    position: fixed;
    z-index: 10001;
    background-color: rgba(0,0,0,0.6);
    text-align: center;
    display: flex;
    justify-content: center;
  }
  .video-play-layer video{
    max-width:500px;
    max-height: 550px;
    margin:auto;
  }
  .video-play-layer .video-play-closed{
    position: absolute;
    width: 20px;
    height: 20px;
    top: -10px;
    right: -10px;
    cursor: pointer;
    background: url(https://img-sn-i01s-cdn.rsjxx.com/backstage/2019/0509/103116_32332.png) no-repeat;
    background-size: 20px 20px;
    background-position: 50%;
  }
  .video-play-layer .video-play-layer__box {
    position: relative;
    top: 8%;
  }
  textarea[name=content] {
    height: 160px;
  }
  .block-header {
    background-color: #efefef;
    padding: 10px;
    margin: 20px 0px;
    position: relative;
  }
  .block-header .block-header-right {
    position: absolute;
    right: 10px;
    top: 4px;
  }
  .block-header span {
    font-size: 14px;
    padding-left: 6px;
    font-weight: bold;
  }
  .widget-form-group .form-control {
    max-width: 80%;
  }
  label input[type=checkbox] {
    position: relative;
    top: 2px;
  }
  .btn-delete {
    display: none;
    position: absolute;
    top: 0px;
    right: 0px;
    color: #fff;
    text-align: center;
    cursor: pointer;
    width: 18px;
    height: 18px;
    font-size: 14px;
    line-height: 16px;
    background: rgba(153,153,153,0.6);
    border-radius: 10px;
    text-indent: 0;
    z-index: 10;
  }
  .btn-delete:hover {
    background: rgba(25,25,25,0.6);
  }

  .specs-container {
    position: relative;
    padding: 10px 10px 10px 10px;
    border: 1px solid #e5e5e5;
  }
  .specs-group .media, .media-body {
    overflow: inherit;
  }
  .specs-group_item .specs-group_item-name {
    position: relative;
    padding: 7px 0 7px 10px;
    margin: 0;
    background-color: #f8f8f8;
    line-height: 16px;
  }
  .specs-group_item .specs-group_item-value {
    position: relative;
    padding: 7px 0 7px 10px;
    margin: 0;
    line-height: 16px;
  }
  .specs-group_item .media-left {
    min-width: 50px;
    padding: 6px 0px 0 0 ;
  }
  .specs-group_item .specs-group_item-del {
    top: 12px;
    right: 10px;
  }
  .specs-group_item:hover .specs-group_item-del{
    display:block;
  }
  .img-comp .img-del{
    top: -12px;
    right: -12px;
  }
  .img-comp:hover .img-del{
    display:block;
  }
  .atom-list {
    display: initial;
  }
  .atom-list .atom {
    position: relative;
    display: inline-block;
    margin: 0px 4px 4px 0;
  }
  .atom-list .atom.active {
    margin-bottom: 100px;
  }

  .atom-list .atom .atom-del {
    top: -8px;
    right: -8px;
  }
  .atom:hover .atom-del{
    display:block
  }
  .specs-group .atom-add {
    display: inline-block;
    vertical-align: top;
    margin-top: 7px;
  }
  .specs-container .specs-op {
    background-color: #f8f8f8;
    padding: 7px 0 7px 10px;
  }
  .specs-container .specs-disable-op {
    position: absolute;
    background-color: #ff000000;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
  }
  .img-comp {
    position: absolute;
    top: 36px;
    left: 0;
    padding: 2px;
    width: 90px;
    background: #fff;
    border-radius: 4px;
    border: 1px solid #dcdcdc;
  }
  .img-comp .arrow {
    position: absolute;
    width: 0;
    height: 0;
    top: -8px;
    left: 44%;
    border-style: solid;
    border-color: transparent;
    border-left: 5px solid transparent;
    border-right: 5px solid transparent;
    border-bottom: 5px solid #000000;
  }
  .img-comp .arrow::after {
    position: absolute;
    display: block;
    width: 0;
    height: 0;
    border-color: transparent;
    border-style: solid;
    top: -10px;
    margin-left: -10px;
    border-bottom-color: #fff;
    border-top-width: 0;
    border-width: 10px;
    content: ""
  }
  .img-comp .img-container {
    position: relative;
  }
  .img-comp .img-container .img-upload {
    width: 84px;
    height: 84px;
    line-height: 84px;
    text-align: center;
    background: #fff;
    font-size: 30px;
    color: #e5e5e5;
    cursor: pointer;
  }
  .img-comp .img-container img {
    width: 84px;
    height: 84px;
    cursor: pointer;
    vertical-align: middle;
  }
  .img-note {
    position: absolute;
    bottom: -2px;
    color: red;
    text-align: center;
    width: 100%;
  }
  .ks-specs-table-body td[rowspan] {
    border-right: 1px solid #ddd;
  }
}
