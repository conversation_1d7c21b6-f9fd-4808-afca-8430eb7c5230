<template>
  <router-link v-if="!isUpdate" :to="to">
    <slot></slot>
  </router-link>
  <a v-else :href="link_url">
    <slot></slot>
  </a>
<!--	<router-link  :to="to">-->
<!--		<slot></slot>-->
<!--	</router-link>-->
</template>

<script>
import {mapState} from "vuex";

export default {
  name: "k-link",

  props: {
    to: {
      type: [String, Object, Location],
      default: ''
    },
  },

  data() {
    return {
      link_url: '',
    }
  },

  computed: {
    ...mapState('version', {
      isUpdate: state => state.is_update,
    }),
  },

  watch: {
    to: {
      immediate: true,
      handler: function (to) {
        this.link_url = this.$router.resolve(to).href
      }
    }
  }
}
</script>
