<template>
	<Page v-on="$listeners"
	      :class-name="className" :current="current"
	      :page-size="pageSize" :page-size-opts="pageSizeOpts"
	      :styles="styles"
	      :total="+total"
	      show-elevator show-sizer show-total transfer
	>
	</Page>
</template>

<script>

export default {
	name: 'k-page',
	props: {
		current: {
			type: [Number, String],
			default: 1,
		},
		total: {
			type: [Number, String],
			default: 0,
		},
		pageSize: {
			type: [Number, String],
			default: 20,
		},
		pageSizeOpts: {
			type: Array,
			default() {
				return [10, 20, 50, 80, 100, 200]
			},
		},
		className: {
			type: String,
		},
		styles: {
			type: Object,
		},
	},
	data() {
		return {}
	},
	methods: {},
	watch: {},
}
</script>

<style lang="less">
.ivu-page-item:hover {
	color: #fff;
}

.ivu-page-item-active {
	background-color: #155BD4;
}

.ivu-page-item-active a, .ivu-page-item-active:hover a {
	color: #fff;
}
</style>
