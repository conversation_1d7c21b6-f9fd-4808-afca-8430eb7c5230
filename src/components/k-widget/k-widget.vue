<template>
  <div class="widget-form-group">
    <label class="widget-form-label" v-if="$scopedSlots.label" :style="{minWidth: labelWidth + 'px'}">
      <slot name="label"></slot>
    </label>
    <label class="widget-form-label" v-else-if="labelWidth > 0" :style="{minWidth: labelWidth + 'px'}"><span v-if="required" class="text-error">*</span> {{ label }}</label>
    <label class="widget-form-label" v-else><span v-if="required" class="text-error">*</span> {{ label }}</label>
    <div class="widget-form-content" :class="{'text': text}" :style="styleWidth">
      <div :style="styleWidth">
        <slot></slot>
      </div>
    </div>
  </div>
</template>

<script>
/* eslint-disable */
/* eslint-disable */
export default {
  name: "k-widget",
  props: {
    label: {
      type: String,
      default: ''
    },

    labelWidth: {
      type: Number,
      default: 0
    },

    width: {
      type: Number,
      default: 0
    },

    required: {
      type: Boolean,
      default: false
    },

    text: {
      type: Boolean,
      default: false
    }
  },

  data() {
    return {
      styleWidth: {}
    }
  },

  methods: {
    //
  },

  watch: {
    width: {
      immediate: true,
      handler: function (val) {
        if (val > 0) {
          this.styleWidth = {
            width: val + 'px'
          }
        } else {
          this.styleWidth = {}
        }
      }
    }
  }
}
</script>

<style lang="less">

</style>
