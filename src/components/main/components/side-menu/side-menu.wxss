.app-first-sidebar {
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  height: 100%;
  width: 92px;
  background-color: #444;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  z-index: 2;
}
.app-first-sidebar .app-logo {
  position: fixed;
  top: 0;
  bottom: 0;
  height: 56px;
  width: 92px;
  word-break: break-word;
  background: #444;
  z-index: 1002;
}
.app-first-sidebar .logo-wrap {
  display: block;
  margin: 0 16px;
  height: 56px;
  overflow: hidden;
}
.app-first-sidebar .logo-wrap .logo-img {
  margin: 12px auto 0;
  width: 32px;
  height: 32px;
  -webkit-border-radius: 50%;
  border-radius: 50%;
  background-size: cover;
  background-position: 50% 50%;
  background-color: #fff;
  border: 1px solid #fff;
}
.app-first-sidebar .app-first-sidebar-nav {
  margin-top: 56px;
  padding-right: 20px;
  box-sizing: content-box;
  width: 100%;
  flex: 1 1 100%;
  overflow-x: hidden;
  overflow-y: scroll;
}
.app-first-sidebar .app-first-sidebar-nav li {
  width: 92px;
  font-size: 14px;
  height: 40px;
  line-height: 40px;
  cursor: pointer;
}
.app-first-sidebar .app-first-sidebar-nav li:hover,
.app-first-sidebar .app-first-sidebar-nav li.hover {
  background: #666;
}
.app-first-sidebar .app-first-sidebar-nav li.active {
  background: #fff;
}
.app-first-sidebar .app-first-sidebar-nav li.active a {
  color: #333;
}
.app-first-sidebar .app-first-sidebar-nav li i.fa {
  padding-right: 3px;
  font-size: 14px;
  width: 14px;
  height: 14px;
}
.app-first-sidebar .app-first-sidebar-nav li a {
  color: #c8c9cc;
  display: block;
  padding-left: 20px;
  text-decoration: none;
}
.app-first-sidebar .app-user-info {
  display: flex;
  flex: 0 0 0;
  flex-direction: row;
  color: #e5e5e5;
  padding: 10px 0;
  word-break: break-word;
  background: #535353;
  z-index: 100;
}
.app-first-sidebar .app-user-info .name {
  width: 90px;
  display: block;
  line-height: 26px;
  text-align: center;
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
}
.app-second-sidebar {
  transition: all 0.2s;
  width: 118px;
  height: 100%;
  margin-left: 92px;
  padding-bottom: 40px;
  background-color: #fff;
  border-right: 1px solid #ebedf0;
  z-index: 1;
  position: absolute;
  top: 0;
  transform: translateX(0);
  overflow: hidden;
  display: -webkit-box;
  display: -webkit-flex;
  display: -moz-box;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
}
.app-second-sidebar.preview {
  z-index: 2;
}
.app-second-sidebar .second-sidebar-title {
  padding-left: 24px;
  font-size: 14px;
  height: 56px;
  line-height: 56px;
  box-sizing: border-box;
  border-bottom: 1px solid #f2f2f2;
}
.app-second-sidebar .second-sidebar-nav {
  padding-top: 12px;
  padding-bottom: 12px;
  padding-left: 12px;
  padding-right: 12px;
  width: 110px;
  margin: 0;
  overflow-x: hidden;
  overflow-y: scroll;
  box-sizing: content-box;
}
.app-second-sidebar li {
  font-size: 14px;
  height: 36px;
  width: 92px;
  line-height: 36px;
  margin-bottom: 14px;
  overflow: hidden;
  cursor: pointer;
  border-radius: 2px;
}
.app-second-sidebar li.active {
  background: #f8f8f8;
}
.app-second-sidebar li.active a {
  color: #000;
}
.app-second-sidebar li a {
  color: #666;
  display: inline-block;
  padding: 0 10px;
}
.app-second-sidebar li a:hover {
  color: #3283FA;
}
@-webkit-keyframes fadeIn {
  0% {
    color: #66666621;
  }
  20% {
    color: #66666642;
  }
  50% {
    color: #6666668a;
  }
  70% {
    color: #666666bf;
  }
  100% {
    color: #666;
  }
}
