<template>
  <div class="app-wrapper" :class="{ 'show-help': $store.state.app.showHelpWrapper }">
    <div class="app-sidebar">
      <side-menu></side-menu>
    </div>
    <div class="app-container">
      <div class="app-header" v-if="showThirdSidebar">
        <div class="app-header_left">
          <Breadcrumb class="ks-breadcrumb">
            <template v-for="(item, key) in breadcrumbs">
              <BreadcrumbItem v-if="item.path" :to="item.path" class="bbSize" :key="key">
                <i v-if="item.icon" class="fa" :class="item.icon"></i>
                {{ item.name }}
              </BreadcrumbItem>
              <BreadcrumbItem v-else class="bbSize" :key="key"> {{ item.name }}</BreadcrumbItem>
            </template>
          </Breadcrumb>
          <div class="back-btn-box" v-show="showBack">
            <Button size="small" @click="routerBack">
              <svg-icon iconClass="back-btn" class="back-icon"></svg-icon>
              <span class="back-text">返回</span>
            </Button>
          </div>
        </div>
        <div class="app-header_right">
          <div class="app-header_trigger" @click="notifyDropdown = true">
            <Dropdown trigger="custom" :visible="notifyDropdown" @on-clickoutside="notifyDropdown = false">
              <Icon type="ios-notifications-outline" size="22" />
              <DropdownMenu slot="list" style="width: 300px; height: 400px">
                <p style="text-align: center">暂无通知</p>
              </DropdownMenu>
            </Dropdown>
          </div>
          <div class="app-header_trigger">
            <Poptip title="" placement="bottom" ref="headerPop">
              <div style="display: flex; align-items: center">
                <Avatar icon="ios-person" size="small" class="space6" style="background-color: #87d068" />
                <div class="flex flex-c" style="height: 56px; justify-content: center">
                  <span class="space6" style="line-height: 1.5; text-align: left; font-weight: 400">{{
                    userInfo.name
                  }}</span>
                  <span class="space6" style="line-height: 1; font-size: 12px; color: #999999">{{
                    userInfo.opc_name
                  }}</span>
                </div>
                <Icon type="ios-arrow-down" />
              </div>
              <div slot="content" class="pop-info-box">
                <div class="user-info">
                  <Avatar
                    icon="ios-person"
                    size="40"
                    class="space6"
                    style="background-color: #87d068; margin-right: 10px; min-width: 40px"
                  />
                  <div>
                    <div class="user-name">{{ userInfo.name }}</div>
                    <div class="user-character">{{ userInfo.role_name }}</div>
                  </div>
                </div>
                <div class="clinic-content">
                  <div class="clinic-item">
                    <div class="clinic-label">当前门店</div>
                    <div class="clinic-value">{{ userInfo.opc_name }}</div>
                  </div>
                </div>
                <div class="user-content">
                  <div class="user-item">
                    <div class="user-label">手机号</div>
                    <div class="user-value">{{ userInfo.mobile }}</div>
                  </div>
                  <div class="user-item">
                    <div class="user-label">登录密码</div>
                    <a class="user-value" @click="changePassword">修改密码 ></a>
                  </div>
                </div>
                <div class="logout-btn" @click="logout">
                  <img src="https://img-sn-i01s-cdn.rsjxx.com/rsjxx/2024/1111/155818_78761.png" class="logout-icon" />
                  <img
                    src="https://img-sn-i01s-cdn.rsjxx.com/rsjxx/2024/1211/170337_59015.png"
                    class="logout-icon-hover"
                  />
                  退出登录
                </div>
              </div>
            </Poptip>
          </div>
        </div>
      </div>
      <div class="app">
        <div class="app-inner" :class="{ 'app-inner-no-margin': !showThirdSidebar }" :style="appInnerStyles">
          <template v-if="!isServeRun">
            <!-- 只在build模式下缓存导航 -->
            <vue-page-stack>
              <router-view />
            </vue-page-stack>
          </template>
          <template v-else>
            <router-view />
          </template>
        </div>
      </div>
    </div>

    <div class="app-footer">
      <span>海南榕树家信息科技有限公司提供技术支持 V1.0</span>
      <span style="display: none">{{ code_version }}</span>
    </div>
    <ChangePassword :visible.sync="changeVisible" :user-info="userInfo"></ChangePassword>
    <Modal v-model="logoutModal" :width="300" @on-ok="onLogout"> 确定退出登录？ </Modal>
  </div>
</template>

<script>
/* eslint-disable */
import S from 'utils/util'; // Some commonly used tools
import * as runtime from 'utils/runtime'; // Runtime information
/* eslint-disable */
import config from '@/config';
import SideMenu from './components/side-menu';
import ChangePassword from './components/ChangePassword.vue';
import './main.less';
import { mapState } from 'vuex';
import { handleLogout } from '../../utils/auth';

export default {
  name: 'Main',
  components: {
    SideMenu,
    ChangePassword
  },
  data() {
    return {
      breadcrumbs: [],
      appInnerStyles: {
        minHeight: ''
      },

      userInfo: {},
      isServeRun: false,
      code_version: '',

      logoutModal: false,
      notifyDropdown: false,
      changeVisible: false
    };
  },

  created() {
    this.code_version = config.codeVersion;

    this.isServeRun = S.isServeRun;

    this.userInfo = runtime.getUser();
    console.log('-> this.userInfo', this.userInfo);
  },

  methods: {
    appInnerHeight: function () {
      let currentScreenHeight = document.documentElement.clientHeight;
      let dh = 94;
      if (!this.showThirdSidebar) {
        dh = 25;
      }
      this.appInnerStyles.minHeight = currentScreenHeight - dh + 'px';
    },

    onDropDownClick: function (name) {
      if (name == 'logout') {
        this.logoutModal = true;
      }
    },

    onLogout: function () {
      runtime.logout();
      this.$router
        .push({
          path: '/login',
          query: { from: encodeURIComponent(this.$route.fullPath) }
        })
        .then(() => {
          // 清空认证限制
          handleLogout();
        });
    },

    getBreadcrumbs: function (to) {
      let path = to.path;
      let breadcrumbs = [
        {
          path: '/',
          icon: 'fa-home',
          name: '首页'
        }
      ];

      let sup_menu_path = '',
        sup_menu_name = '',
        sup_menu_icon = '',
        activeFirstMenuId = '';

      for (let pid in this.secondMenusAll) {
        let items = this.secondMenusAll[pid];
        items.forEach(item => {
          if (item.path == path) {
            if (item.type != 'SUB_MENU') {
              // sup_menu_path = item.path
              // sup_menu_name = items[0].name
              items.forEach(t => {
                if (t.id == item.p_id) {
                  activeFirstMenuId = t.p_id;
                  sup_menu_name = t.name;
                  sup_menu_path = t.path;
                }
              });
            } else {
              activeFirstMenuId = item.p_id;
            }
          }
        });
      }

      // 一级标题
      let firstMenu = '';
      this.firstMenus.forEach(item => {
        if (item.id == activeFirstMenuId) {
          firstMenu = item.name;
        }
      });

      if (sup_menu_path) {
        breadcrumbs.push({
          path: sup_menu_path,
          name: firstMenu + ' - ' + sup_menu_name
        });

        breadcrumbs.push({
          path: '',
          name: to.meta.title
        });
      } else {
        breadcrumbs.push({
          path: '',
          name: firstMenu + ' - ' + to.meta.title
        });
      }

      return breadcrumbs;
    },

    routerBack() {
      this.$router.back();
    },
    changePassword() {
      this.$refs.headerPop.handleClose();
      this.changeVisible = true;
    },
    logout() {
      console.log('=>(main.vue:273) this.$refs.headerPop', this.$refs.headerPop);
      this.$refs.headerPop.handleClose();
      this.logoutModal = true;
    }
  },
  mounted() {
    this.appInnerHeight();
    window.addEventListener('resize', () => {
      return (() => {
        this.appInnerHeight();
      })();
    });
  },

  computed: {
    ...mapState('menus', {
      firstMenus: state => state.firstMenus,
      secondMenusAll: state => state.secondMenusAll
    }),

    showThirdSidebar: function () {
      return this.$store.state.menus.showSecondMenu;
    },
    showBack() {
      return this.$route.meta.type === 'PAGE' && window.history.length > 2;
    }
  },

  watch: {
    $route: {
      immediate: true,
      handler: function (to) {
        this.breadcrumbs = this.getBreadcrumbs(to);
        this.$nextTick(() => {
          const app = document.getElementsByClassName('app-inner');
          if (to.path.includes('statistics')) {
            app[0].style.background = '#f2f2f2';
            app[0].style.padding = 0;
            app[0].style.marginTop = 0;
          } else {
            app[0].style.background = '#fff';
            app[0].style.padding = '12px';
            app[0].style.marginTop = '12px';
          }
        });
      }
    },

    showThirdSidebar: function () {
      this.appInnerHeight();
    }
  }
};
</script>

<style lang="less">
.app-header_left {
  display: flex;
  align-items: center;
}

.app-header_right {
  display: inline-block;
  float: right;
  margin-right: 20px;
}

.app-header_trigger {
  display: inline-block;
  width: auto;
  padding: 0 12px;
  text-align: center;
  cursor: pointer;
  transition: all 0.2s ease-in-out;

  &:hover {
    color: #2277ff;
    background-color: #f8f8f9;
  }
}

.ks-breadcrumb {
  padding: 0 28px;
  color: #969799;
  line-height: 0;

  &.ivu-breadcrumb a {
    color: #969799;
  }

  &.ivu-breadcrumb > span:last-child {
    color: #323233;
    font-weight: 400;
  }
}

.app-inner {
  margin: 10px;
  padding: 16px;
  min-width: 1150px;
  //min-height: 450px;
  background-color: #fff;
}

.app-inner-no-margin {
  margin: 0 0 10px 0;
}

.bbSize {
  font-size: 14px;
}
</style>
<style lang="less" scoped>
.pop-info-box {
  padding: 16px;
  min-width: 280px;
  max-width: 280px;

  .user-info {
    display: flex;
    align-items: center;
    padding-bottom: 16px;
    border-bottom: 1px solid #ebedf0;
    margin-bottom: 16px;
    .user-name {
      font-weight: 600;
      font-size: 16px;
      color: #303133;
    }
    .user-character {
      white-space: pre-wrap;
      font-weight: 400;
      font-size: 12px;
      color: #909399;
    }
  }
  .clinic-content {
    margin-bottom: 16px;
    border-bottom: 1px solid #ebedf0;
    .clinic-item {
      display: flex;
      justify-content: space-between;
      margin-bottom: 16px;
      .clinic-label {
        font-weight: 400;
        font-size: 14px;
        color: #303133;
        margin-right: 30px;
      }
      .clinic-value {
        font-weight: 400;
        font-size: 14px;
        color: #909399;
        white-space: pre-wrap;
        text-align: right;
      }
    }
  }
  .user-content {
    .user-item {
      display: flex;
      justify-content: space-between;
      margin-bottom: 20px;
      .user-label {
        font-weight: 400;
        font-size: 14px;
        color: #303133;
      }
      .user-value {
        font-weight: 400;
        font-size: 14px;
        color: #909399;
      }
    }
  }
  .logout-btn {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 248px;
    height: 32px;
    background: #f5f6f8;
    border-radius: 4px;
    cursor: pointer;
    font-weight: 600;
    font-size: 13px;
    color: #303133;
    .logout-icon {
      width: 13px;
      height: 14px;
      margin-right: 8px;
    }
    .logout-icon-hover {
      display: none;
      width: 13px;
      height: 14px;
      margin-right: 8px;
    }
    &:hover {
      background: rgba(48, 136, 255, 0.06);
      color: #3088ff;
      .logout-icon {
        display: none;
      }
      .logout-icon-hover {
        display: block;
      }
    }
  }
}
</style>
