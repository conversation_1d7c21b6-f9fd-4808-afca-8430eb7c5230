<template>
  <div>
    <!--    <Poptip ref="poptip" trigger="hover" transfer :width="width" popper-class="custom-poptip&#45;&#45;table" placement="top-start">-->
    <!--      <div slot="content" class="poptip-content">-->
    <!--        <Table :columns="columns" :data="data" :class="{ distance: data.length > 9 }" :height="data.length > 9 ? 400 : ''">-->
    <!--          <template slot-scope="{row}" :slot="c_item.slot"  v-for="(c_item, c_index) in columns" :c_index="c_index">-->
    <!--            <div :style="{color: c_item.textColor || ''}">-->
    <!--              <div v-if="c_item.isMoney">¥{{ row[c_item.slot] || 0 }}</div>-->
    <!--              <div v-else>{{ row[c_item.slot] || '-' }}</div>-->
    <!--            </div>-->
    <!--          </template>-->
    <!--        </Table>-->
    <!--      </div>-->
    <!--      <slot></slot>-->
    <!--    </Poptip>-->

    <Poptip
      ref="poptip"
      trigger="hover"
      transfer
      :width="width"
      popper-class="custom-poptip--table"
      placement="top-start"
      @on-popper-show="popperShow"
      @on-popper-hide="popperHide"
    >
      <div slot="content" class="poptip-content">
        <table class="native-table" cellspacing="0" v-if="show">
          <thead>
            <tr>
              <th
                :style="{
                  'min-width': column.width ? `${column.width}px` : '',
                  'max-width': column.width ? `${column.width}px` : '',
                }"
                v-for="column in columns"
                :key="column.key"
              >
                <div :style="{ textAlign: column.align || 'left' }" class="cell-box">{{ column.title }}</div>
              </th>
            </tr>
          </thead>
          <tbody :style="{ height: data.length > 4 ? '200px' : 'auto' }">
            <tr v-for="(row, index) in data" :key="index">
              <td
                :style="{
                  'min-width': column.width ? `${column.width}px` : '',
                  'max-width': column.width ? `${column.width}px` : '',
                }"
                v-for="column in columns"
                :key="column.key"
              >
                <div
                  class="cell-box"
                  :style="{
                    color: column.textColor || '',
                    textAlign: column.align || 'left',
                    paddingRight: data.length > 4 ? 0 : '10px',
                  }"
                >
                  <div v-if="column.isMoney">¥{{ row[column.key] || 0 }}</div>
                  <div v-else>{{ row[column.key] || '-' }}</div>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
      <slot></slot>
    </Poptip>
  </div>
</template>

<script>
export default {
  name: 'poptipTable',
  components: {},
  mixins: [],
  props: {
    width: {
      type: [String, Number],
      default: 4000,
    },
    columns: {
      type: Array,
      default: () => [],
    },
    data: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      show: false,
    };
  },
  computed: {},
  watch: {},
  created() {},
  mounted() {},
  methods: {
    popperShow () {
      this.show = true
    },
    popperHide () {
      this.show = false
    },
  },
};
</script>

<style lang="less">
.custom-poptip--table {
  .ivu-poptip-arrow {
    display: none;
  }
}
</style>

<style lang="less" scoped>
.native-table {
  width: 100%;

  thead {
    tr {
      display: flex;

      th {
        flex: 1;
        min-height: 36px;
        padding: 8px 8px;
        line-height: 20px;
        white-space: pre-line;
        display: flex;
        align-items: center;
        color: #333;

        &:last-child {
          div {
            padding-right: 10px;
          }
        }
      }
    }
  }

  tbody {
    display: block;
    overflow-y: auto;

    tr {
      display: flex;
      border-bottom: 1px solid #ebedf0;
      font-size: 12px;
      &:hover {
        background: #ebf7ff;
      }

      td {
        min-height: 36px;
        flex: 1;
        color: #333;
        flex-wrap: wrap;
        line-height: 20px;
        padding: 8px 8px;
        white-space: pre-line;
        display: flex;
        align-items: center;
      }
    }
  }

  .cell-box {
    width: 100%;
  }
}
</style>
