<template>
  <div class="print-wrapper" v-if="chargeList.length" ref="bills">
    <div class="print-head">
      <div class="print-title">
        <h1>{{  clinicInfo.name || '' }}收费单</h1>
      </div>
      <div class="print-number">
        <span>收费单号：{{ orderInfo.out_trade_no || '' }}</span>
        <span>收费日期：{{ orderInfo.create_time || '' }}</span>
      </div>
      <div class="user-info">
        <div class="user-info-item user-info-item-1">
          <span>姓名：{{ patientInfo.name || '' }}</span>
          <span>性别：{{ patientInfo.sex_text }}</span>
          <span>年龄：{{ patientInfo.age }}</span>
          <span>电话：{{ patientInfo.mobile }}</span>
        </div>
      </div>
    </div>
    <div class="bill-info">
      <div class="charge-list">
        <h3>收费列表</h3>
        <div class="charge-table">
          <table border="1px" cellspacing="0" v-for="item in chargeList" :key="item.id">
            <thead>
            <tr>
              <th>项目名称</th>
              <th>金额</th>
            </tr>
            </thead>
            <tbody>
            <tr v-for="subItem in item.item" :key="subItem.id">
              <td>{{ subItem.type_text }}</td>
              <td>{{ subItem.payment_fee }}</td>
            </tr>
            </tbody>
          </table>
        </div>
      </div>
      <div class="charge-detail">
        <div class="table-wrapper"  v-if="herbBill.type === 'HERBS'">
          <h3>{{ herbBill.type_text }}收费详情</h3>
          <table border="1px" cellspacing="0" >
            <thead>
            <tr>
              <th class="td-25">项目名称</th>
              <th>单价/1g</th>
              <th>重量(g)</th>
              <th>小计</th>
              <th>折扣</th>
              <th>折后价</th>
            </tr>
            </thead>
            <tbody>
            <tr v-for="item in herbBill.attrs" :key="item.id">
              <td class="td-25">{{item.name}}</td>
              <td>¥ {{item.price}}</td>
              <td>{{item.quantity}}</td>
              <td>{{item.total_fee}}</td>
              <td>{{item.discount_rate + '%'}}</td>
              <td>{{item.payment_fee}}</td>
            </tr>
            </tbody>
          </table>
        </div>
        <div class="table-wrapper"  v-if="medicineBill.type === 'MEDICINE'">
          <h3>{{ medicineBill.type_text }}收费详情</h3>
          <table border="1px" cellspacing="0" >
            <thead>
            <tr>
              <th class="td-25">项目名称</th>
              <th>单价</th>
              <th>数量</th>
              <th>小计</th>
              <th>折扣</th>
              <th>折后价</th>
            </tr>
            </thead>
            <tbody>
            <tr v-for="item in medicineBill.attrs" :key="item.id">
              <td class="td-25">{{item.name}}</td>
              <td>¥ {{item.price}}</td>
              <td>{{item.quantity}}</td>
              <td>{{item.total_fee}}</td>
              <td>{{item.discount_rate + '%'}}</td>
              <td>{{item.payment_fee}}</td>
            </tr>
            </tbody>
          </table>
        </div>
        <div class="table-wrapper"  v-if="treatBill.attrs&&treatBill.attrs.length">
          <h3>{{ treatBill.type_text }}收费详情</h3>
          <table border="1px" cellspacing="0" >
            <thead>
            <tr>
              <th class="td-25">项目名称</th>
              <th>单价</th>
              <th>数量</th>
              <th>小计</th>
              <th>折扣</th>
              <th>折后价</th>
            </tr>
            </thead>
            <tbody>
            <tr v-for="item in treatBill.attrs" :key="item.id">
              <td class="td-25">{{item.name}}</td>
              <td>¥ {{item.price}}</td>
              <td>{{item.quantity}}</td>
              <td>{{item.total_fee}}</td>
              <td>{{item.discount_rate + '%'}}</td>
              <td>{{item.payment_fee}}</td>
            </tr>
            </tbody>
          </table>
        </div>

      </div>
    </div>
    <div class="bill-foot">
      <div class="amount">
        <span>合计金额：{{orderInfo.total_fee}}</span>
        <span style="margin: 0 8px">－ 优惠金额：{{orderInfo.discount_fee}}</span>
        <span> =&nbsp;&nbsp; 实收金额：{{orderInfo.payment_fee}}</span>
      </div>
      <div class="doctor">
        医生:  &nbsp; {{  doctorName}}
      </div>
    </div>
  </div>
</template>

<script>
import noment from 'moment'
export default {
  name: 'print-bill',
  data() {
    return {
      patientInfo: {},
      chargeList: [],
      orderInfo: {},
      billDetailList: [],
      doctorName: '',
      medicineBill: {},
      treatBill: {},
      herbBill: {},
      clinicInfo: {}
    }
  },
  methods: {
    printBill(pay_order_id ) {
      console.log(this.chunkArr([1, 2, 3, 4, 5], 3))
      let query
      if (pay_order_id) {
        query = {pay_order_id}
      } else {
        query = {pay_order_id: this.$route.query.id}
      }
      this.$api.getBillDetails(query).then(res => {
        console.log(res)
        this.patientInfo = res.patient
        res.pres_list.map(item => {
          item.type_text = item.type_text + '费'
        })
        this.clinic = res.clinic //诊所信息
        this.chargeList = []
        res.pres_list =  res.pres_list.concat([{type_text: '挂号费',payment_fee: res.order.reg_fee}, {type_text: '诊疗费',payment_fee: res.order.cons_fee},])
        console.log("-> res.", res.pres_list);
        console.log('chunk',this.chunkArr(res.pres_list, 3))
        this.chunkArr(res.pres_list, 3).map((item, index) => {
          console.log(item)
          this.chargeList.push({item, id: index})
        })
        console.log('-> this.chargeList', this.chargeList)
        res.order.create_time = noment(res.order.create_time*1000).format('YYYY.MM.DD')
        this.orderInfo = res.order
        this.doctorName = res.doctor_name

        res.pres_list.map(item=>{
          switch (item.type){
            case "MEDICINE":
              this.medicineBill = item
              break
            case "TREAT":
              this.treatBill = item
              break
            case "HERBS":
              this.herbBill = item
          }
        })
        setTimeout(()=>{
          this.$print(this.$refs.bills, {},this.beforePrint,this.afterPrint)
        },100)
      })
    },
    beforePrint(){

    },
    afterPrint(){

    },
    chunkArr(arr, size) {
      //判断如果不是数组(就没有length)，或者size没有传值，size小于1，就返回空数组
      if (!arr.length || !size || size < 1) return []
      let [start, end, result] = [null, null, []]
      console.log(Math.ceil(arr.length / size))
      for (let i = 0; i < Math.ceil(arr.length / size); i++) {
        start = i * size
        end = start + size
        result.push(arr.slice(start, end))
      }
      return result
    },
  },
}
</script>

<style scoped lang="less">
@media print {
  body {
    margin: 1cm;
  }

  .is-split {
    page-break-after: always;
  }

  @page {
    margin: 0;
    size: A4 portrait; // A4大小 纵向
  }
}

.print-wrapper {
  width: 100%;
  margin-left: -8px;
  font-family: STSong, SimSun;
  display: flex;
  height: 100%;
  flex-direction: column;
  padding: 100px 100px 66px;
  font-size: 16px;

  .print-head {
    .print-title {
      text-align: center;

      > h1 {
        font-size: 32px;
        font-weight: bold;
        color: #000000;
        letter-spacing: 3px;
        margin-bottom: 40px;
      }

    }

    .print-number {
      width: 100%;
      display: flex;
      justify-content: space-between;
      padding-bottom: 16px;
      padding-top: 20px;

      > span {
        font-size: 18px;
        font-weight: 400;
        color: #000000;
      }
    }

    .user-info {
      border-top: 1px solid #777777;
      width: 100%;
      padding-top: 16px;

      .user-info-item {
        margin-bottom: 12px;
        display: flex;
        justify-content: space-between;
        line-height: 28px;
        > span {
          font-size: 18px;
          font-weight: 400;
          color: #000000;
        }
      }
      .user-info-item-1{
        margin-bottom: 25px;
      }
    }
  }

  .bill-info {
    h3 {
      margin-bottom: 8px;
      font-size: 16px;
    }

    .charge-list {
      .charge-table {
        display: flex;
        table {
          flex: 1;

        }
      }
      table:nth-of-type(2) {
        margin-left: 10px;
      }
    }
  }


  table {
    text-align: center;
    margin-bottom: 20px;
    width: 100%;
    thead {
      background: #E4E4E4;
    }
    .td-25{
      width: 20%;
    }
    td{
      width: 16%;
    }
    thead, td {
      height: 28px;
      line-height: 28px;
    }
  }
  .bill-foot{
    display: flex;
    justify-content: space-between;
  }
}
</style>
