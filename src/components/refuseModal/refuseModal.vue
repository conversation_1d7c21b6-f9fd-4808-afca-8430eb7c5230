<template>
  <Modal :value="value" :mask-closable="false" :title="title">
    <div>
      <p class="mb10">请输入驳回原因</p>
      <Input
        v-model="refuseText"
        :maxlength="maxlength"
        show-word-limit
        class="refuse-input"
        placeholder="请输入驳回原因"
        type="textarea"
      />
    </div>

    <div slot="footer">
      <Button @click="cancel">取消</Button>
      <Button type="primary" @click="refuseOk">确定</Button>
    </div>
  </Modal>
</template>

<script>
export default {
  name: 'confirmModal',
  mixins: [],

  components: {},

  props: {
    value: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      default: '审核驳回'
    },
    maxlength: Number
  },

  data() {
    return {
      refuseText: ''
    };
  },

  computed: {},

  watch: {},

  created() {},

  mounted() {},

  destroyed() {},

  methods: {
    cancel() {
      this.refuseText = '';
      this.changeVisible(false);
    },
    refuseOk() {
      if (!this.refuseText) {
        this.$Message.error('请输入驳回原因');
        return;
      }
      this.$emit('ok', this.refuseText);
      this.cancel();
    },
    changeVisible(val) {
      !val && this.$emit('input', val);
    }
  }
};
</script>

<style scoped lang="less">
::v-deep .refuse-input {
  width: 100% !important;
  max-width: 100%;
  .ivu-input {
    height: 100px !important;
  }
}
</style>
