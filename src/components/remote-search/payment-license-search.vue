<template>
  <div class="flex" style="margin-top: 1px">
    <Select
      v-if="showType"
      v-model="ent_type"
      style="width: 120px"
      @on-change="changeEntType"
      placeholder="全部"
      clearable
    >
      <Option
        v-for="(item, index) in typeList"
        :key="'license' + index"
        :value="item.value"
        v-if="item.value !== 'OUTSIDE' || (item.value === 'OUTSIDE' && showOutside)"
        >{{ item.label }}</Option
      >
    </Select>
    <Select
      ref="collection"
      transfer
      :value="value"
      :clearable="isClearable"
      :loading="searchLoading"
      :remote-method="search"
      filterable
      :transfer-class-name="className"
      @on-query-change="queryChange"
      class="filterable-select"
      :placeholder="placeholder"
      @on-clear="clearSub"
      @on-select="selectSup"
    >
      <Option
        v-for="(option, index) in license_list"
        :key="option.organization_name + option.ent_code"
        :value="option.ent_code"
        >{{ option.organization_name }}</Option
      >
    </Select>
  </div>
</template>

<script>
import util from '@/utils/util';
export default {
  name: 'payment-license-search',
  components: {},
  mixins: [],
  props: {
    placeholder: {
      type: String,
      default: '请输入营业执照名称（付款方）'
    },
    isClearable: {
      type: Boolean,
      default: true
    },
    value: {
      type: String,
      default: ''
    },
    className: {
      type: String,
      default: ''
    },
    // 是否展示搜索条件
    showType: {
      type: Boolean,
      default: false
    },
    p_ent_type: {
      type: String,
      default: ''
    },
    typeList: {
      type: Array,
      default: () => {
        return [];
      }
    },
    // 默认补充参数
    default_params: {
      type: Object,
      default: () => {}
    },
    showOutside: {
      type: Boolean,
      default: false
    },
    // 外部绑定的字段
    routerKey: {
      type: String,
      default: 'organ_payer_id'
    },
    // 存储在localStoage里面的值，用于刷新回显
    localStorageListName: {
      type: String,
      default: 'license_list'
    }
  },
  data() {
    return {
      searchLoading: false,
      license_list: []
    };
  },
  computed: {},
  watch: {
    p_ent_type: {
      handler: function (val, oldVal) {
        this.ent_type = val;
      },
      immediate: true
    }
  },
  created() {
    if (!this.$route.query[this.routerKey]) {
      this.searchMethod();
    } else {
      this.$nextTick(() => {
        let list = JSON.parse(localStorage.getItem([this.localStorageListName])) || [];
        this.license_list = list;
        this.$emit('input', list[0] && list[0].ent_code);
        this.$emit('getOrganEntType', list[0] && list[0].ent_type);
        // 用label代替id进行二次搜索
        setTimeout(() => {
          this.$refs.collection.query = list[0] && list[0].organization_name;
        }, 0);
      });
    }
  },
  mounted() {},
  methods: {
    searchMethod: util.debounce(function (query) {
      this.searchLoading = true;
      let params = {
        ent_type: this.ent_type,
        filter_organ_name: 1,
        organization_name: query || '',
        ...this.default_params
      };
      this.$api.searchPurchasesubject(params).then(res => {
        this.searchLoading = false;
        this.license_list = res.list;
        localStorage.setItem([this.localStorageListName], JSON.stringify(this.license_list));
      });
    }, 200),
    search() {},
    clear() {
      this.$refs.collection.clearSingleSelect();
      this.ent_type = '';
    },
    selectSup(val) {
      let type = '';
      this.license_list.some(item => {
        if (item.ent_code === val.value) {
          type = item.ent_type;
          return true;
        }
      });
      this.$emit('input', val.value);
      this.$emit('getOrganEntType', this.ent_type || type);
    },
    queryChange(val) {
      this.searchMethod(val);
    },
    clearSub() {
      this.searchMethod();
      this.$emit('input', '');
      // 当展示前置搜索条件的时候，需要清除type类型
      if (!this.showType) {
        this.$emit('getOrganEntType', '');
      }
    },
    changeEntType() {
      this.$emit('input', '');
      this.$emit('getOrganEntType', this.ent_type);
      this.searchMethod();
    }
  }
};
</script>

<style lang="less" scoped>
.filterable-select {
  ::v-deep .ivu-select-input {
    margin-top: -1px;
  }
}
</style>
