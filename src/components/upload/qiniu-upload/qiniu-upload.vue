<template>
  <Upload
    action="https://upload.qiniup.com"
    ref="upload"
    name="file"
    type="select"
    accept="image/jpg,image/jpeg,image/png"
    :multiple="multiple"
    :data="{token: token}"
    :format="['jpg','jpeg','png']"
    :max-size="maxSize"
    :show-upload-list="false"
    :before-upload="onBeforeUpload"
    :on-progress="onProgress"
    :on-success="onSuccess"
    :on-error="onError"
    :disabled="disabled"
  >
    <slot></slot>
  </Upload>
</template>

<script>
import S from "utils/util";
import request from 'utils/request'
export default {
  name: "qiniu-upload",
  props: {
    multiple: {
      type: Boolean,
      default: false
    },
	  disabled: {
      type: Boolean,
      default: false
    },
    maxSize: {
      type: Number,
      default: 3072
    },
    fileUrls: {
      type: Array,
      default () {
        return [];
      }
    },
  },
  data() {
    return {
      token: "",
      domain: "",
    }
  },
  methods: {
    onBeforeUpload: async function (file) {
      if(file.size/1024 >= this.maxSize ){
        this.$Message.error('上传文件大小不能超过3M')
        return
      }
      let ext = file.name.lastIndexOf(".") > 0
        ? file.name.substring(file.name.lastIndexOf(".")+1, file.name.length).toLowerCase()
        : ''
      await request.get('/pms_opc/qiniu.getuptoken', {data:{ext}}).then(data => {
        this.token = data.token
        this.domain = data.domain
      }, reject => {
        console.log(2131232)
        this.$Message.error({
          content: reject.errmsg || String(reject),
          duration: 3,
        })
        throw new Error(reject.errmsg || reject);
      })
    },

    onProgress: function (event, file, fileList) {
      // S.log(event, 'onProgress event')
      // S.log(file, 'onProgress file')
      // S.log(fileList, 'onProgress fileList')
    },

    onSuccess: function (response, file, fileList) {
      console.log("-> response", response);
      let fileUrl = this.domain + "/" + response.key
      this.fileUrls.push(fileUrl)
      this.$emit('on-success', fileUrl, this.fileUrls);
      // S.log(response, 'onSuccess response')
      // S.log(file, 'onSuccess file')
      // S.log(fileList, 'onSuccess fileList')
    },

    onError: function (response, file, fileList) {
      this.$Message.error({
        content: file.error,
      })
      // S.log(response, 'onError response')
      // S.log(file, 'onError file')
      // S.log(fileList, 'onError fileList')
    },
  },
}
</script>

<style scoped>

</style>
