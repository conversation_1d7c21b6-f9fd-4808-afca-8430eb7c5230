const configFiles = require.context("./", true, /\.js$/);
const configs = configFiles.keys().reduce((configs, configPath) => {
  const fileName = configPath.replace(/^\.\/(.*)\.\w+$/, "$1");
  if (fileName == "index") {
    return configs;
  }
  const value = configFiles(configPath);
  configs[fileName] = value;
  return configs;
}, {});

let env = process.env.VUE_APP_NODE_ENV;
if(env === 'production') {
  env = 'prod';
}

export default configs["index." + env];
