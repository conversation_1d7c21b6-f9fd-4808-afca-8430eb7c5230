module.exports = {
  /**
   * 项目标题
   */
  title: "直营运营中心后台",

  /**
   * 项目短标题
   */
  shortTitle: "直营运营中心后台",

  /**
   * 代码版本
   */
  codeVersion: process.env.VUE_APP_CODE_VERSION,

  /**
   * @description: crypto 加密相关信息
   * */
  cryptoVersion: "2",

  /**
   * @description:加密key
   * @description:满足16位，php对于key的处理与js不同，统一保持16位key即可
   */
  cryptoKey: "OrN0AYkd5WRGjndw",

  /**
   * @description:加密iv偏移量
   */
  cryptoIv: "NXJ07Yj5M6V45j3x",
  // 素材中心接口签名相关配置
  AssetsMapiSignKey: 'pamkju9K18wjBEtnfkm',
  AssetsApiDomain: 'https://asset.rsjxx.com/api-server',
  
  /**
   * 接口请求签名Key
   */
  mapiSignKey: "WJn0oYVW9JGkRAqz69JkO",

  /**
   * 接口地址
   * 如：https://mapi.biranmall.com/ 、https://mapi.biranmall.com/app/
   */
  ApiDomain: "https://pms.rsjxx.com/api-server/",

  /**
   * 静态资源地址
   * 如：https://wwwcdn.biranmall.com/ 、 https://wwwcdn.biranmall.com/app/
   */
  CdnDomain: "https://wwwcdn.rsjxx.com/pms-opcenter/",

  /**
   * 图片资源地址
   * 如：https://img01.biranmall.com/
   */
  ImageDomian: "http://static.rsjxx.com",

  /**
   * 本地存储Key的前缀
   * 如：测试环境前缀 TEST
   */
  storageNamePrefix: "PSM-OPC",
};
