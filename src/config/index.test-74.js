module.exports = {
  /**
   * 项目标题
   */
  title: "直营运营中心后台 - 测试",

  /**
   * 项目短标题
   */
  shortTitle: "直营运营中心后台 - 测试",

  /**
   * 代码版本
   */
  codeVersion: process.env.VUE_APP_CODE_VERSION,

  /**
   * @description: crypto 加密相关信息
   * */
  cryptoVersion: "2",

  /**
   * @description:加密key
   * @description:满足16位，php对于key的处理与js不同，统一保持16位key即可
   */
  cryptoKey: "e8B3gH6kL2nP9mQz",

  /**
   * @description:加密iv偏移量
   */
  cryptoIv: "e8B3gH6kL2nP9mQz",

  // 素材中心接口签名相关配置
  AssetsMapiSignKey: 'CmCSbdHYPrHm6gfM2kF',
  AssetsApiDomain: 'https://asset2sit.rsjxx.com/api-server',
  /**
   * 接口请求签名Key
   */
  mapiSignKey: "J8K3L9V5E2",

  /**
   * 接口地址
   * 如：https://mapi.biranmall.com/ 、https://mapi.biranmall.com/app/
   */
  // ApiDomain: 'http://bapi-clinic2test.rsjxx.com/',
  // ApiDomain: "https://74pms.rsjxx.com/api-server/",
  ApiDomain: "https://pms2uat-tnjzhytdlmguyogu2m.rsjxx.com/api-server/",
  // ApiDomain: 'http://lsm2bapi-clinic.rsjadd.com/',

  /**
   * 静态资源地址
   * 如：https://wwwcdn.biranmall.com/ 、 https://wwwcdn.biranmall.com/app/
   */
  // CdnDomain: 'http://wwwcdn2test.rsjxx.com/pms-com/',
  CdnDomain: "https://wwwcdn2uat.rsjxx.com/pms-opcenter/",

  /**
   * 图片资源地址
   * 如：https://test2img.biranmall.com/
   */
  ImageDomian: "https://static.rsjxx.com",

  /**
   * 本地存储Key的前缀
   * 如：测试环境前缀 TEST
   */
  storageNamePrefix: "UAT-PSM-OPC",
};
/*测试环境
	'5qLqYMX2dx', // h5 门店加盟接口
 '0pQpYWAov3', // PMS平台接口
 'nAYA9Vy6Lv', // PMS供应商接口
 'j3236VNDny', // PMS省公司接口*/

/*正式环境
 
 'L9y9DPJbxE', // h5 门店加盟接口
 '1505VLEOXA', // PMS平台接口
 '4D6DYJEwWB', // PMS供应商接口
 'wyYykj0xN3', // PMS省公司接口*/
