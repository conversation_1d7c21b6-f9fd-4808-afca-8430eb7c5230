import Vue from 'vue';
import App from './App.vue';
import router from './router';
import store from './store';
import ViewUI from 'view-design';
import './directives';
import config from '@/config';
import '@/plugins/element';
import '@/plugins/svg-icon.js';
import '@/plugins/iview';
import 'font-awesome/css/font-awesome.min.css';
import './index.less';
import './flex.less';
import '@/common/style/public.less';
import '@/assets/font/font.css';
// import imageUrl from '@/utils/imageUrl' // 图片样式处理工具
import util from '@/utils/util'; // 工具
import VuePageStack from '@/components/vue-page-stack'; // 页面导航管理器
import * as echarts from 'echarts';

Vue.prototype.$echarts = echarts;
import * as filters from '@/utils/filters';
import { Bus } from '@/utils/bus';
import API from './api';
import _ from 'lodash';
import request from '@/utils/request';
import prints from '@/utils/print';
import Viewer from 'v-viewer';
import 'viewerjs/dist/viewer.css';
import KPage from '@/components/k-page';
import KWidget from '@/components/k-widget';
import KLink from '@/components/k-link';
import eChartFn from '@/components/k-configure-echarts/index.js';
import ChartPanel from '@/components/k-configure-echarts/index.vue';
import Dvd from '@/components/dvd';
import BackButton from '@/components/BackButton/BackButton';
import MarkStatus from './components/MarkStatus';
import AuditRefuse from './components/AuditButton/AuditRefuseBtn.vue';
import AuditPass from './components/AuditButton/AuditPassBtn.vue';
import OperationFolding from './components/operationFolding/index.vue';

import MaterialPicture from './components/MaterialCenter/MaterialPicture.vue';
import MaterialVideo from './components/MaterialCenter/MaterialVideo.vue';
import cdiv from './components/cdiv';

// Vue.use(FullTabs);
Vue.component('AuditRefuse', AuditRefuse);
Vue.component('AuditPass', AuditPass);
Vue.component('OperationFolding', OperationFolding);
Vue.component('MaterialPicture', MaterialPicture);
Vue.component('MaterialVideo', MaterialVideo);
Vue.component('cdiv', cdiv);
Vue.prototype.$lodash = _;
Vue.prototype._ = _;
Vue.use(prints);
Vue.use(Viewer);
Vue.component(ChartPanel.name, ChartPanel);
for (const key in filters) {
  Vue.filter(key, filters[key]);
}
Vue.prototype.$eChartFn = eChartFn;
Vue.prototype.$http = request;
//事件bus
Vue.prototype.$bus = new Bus();
Vue.prototype.$api = new API(request);
Vue.prototype.$moment = require('moment');
Vue.use(ViewUI, {
  transfer: true,
  capture: false
});
Vue.prototype.$Message.config({
  duratrequestn: 4
});
Vue.use(VuePageStack, { router });
Vue.component('Dvd', Dvd);
Vue.component('KPage', KPage);
Vue.component('KWidget', KWidget);
Vue.component('KLink', KLink);
Vue.component('BackButton', BackButton);
Vue.component('MarkStatus', MarkStatus);
// Vue.prototype.$echarts = echarts
if (config.codeVersrequestn == undefined)
  util.log('环境变量VUE_APP_CODE_VERSrequestN未定义', 'VUE_APP_CODE_VERSrequestN');
/**
 * 生产环境关掉提示
 */
Vue.config.productrequestnTip = false;

// if (!util.isProdEnv) {
// 	require('./mock')
// 	util.log(process.env.VUE_APP_NODE_ENV, 'NODE_ENV')
// }
new Vue({
  router,
  store,
  render: h => h(App),
  mounted() {
    store.dispatch('app/setClientHeight', document.documentElement.clientHeight).then();
  }
}).$mount('#app');

window.addEventListener('resize', () => {
  return (() => store.dispatch('app/setClientHeight', document.documentElement.clientHeight))();
});
