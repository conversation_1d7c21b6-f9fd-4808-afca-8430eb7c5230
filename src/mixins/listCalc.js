import S from '@/utils/util';

export default {
  data() {
    return {
      expandStatus: 'collapse', // 默认收起
      isShowExpandDom: false, // 是否展示更多筛选功能
      calc_search_height: 0, // 搜索条件高度 32 + 20
      calc_function_height: 0, // 功能区域高度 32 + 20
      calc_panel_height: 0, // 表格tab快捷切换高度
      calc_page_height: 0, // 分页区域高度  33 + 20
      calc_tab_height: 0 // 标签切换高度  33 + 20
    };
  },
  computed: {
    /**
     * @description 获取除表格以外的全部高度
     * reserve_height： 预留的额外占位空间
     * */
    _getOtherHeight() {
      return (
        136 +
        Number(this.calc_search_height || 0) +
        Number(this.calc_function_height || 0) +
        Number(this.calc_panel_height || 0) +
        Number(this.calc_page_height || 0) +
        Number(this.calc_tab_height || 0) +
        Number(this.reserve_height || 0)
      );
    }
  },
  mounted() {
    // 初始化触发
    this._getSearchBoxHeight();

    // 页面横向触发
    window.addEventListener('resize', () => {
      this._getSearchBoxHeight();
    });
  },
  activated() {},
  methods: {
    expandCollapseEvent() {
      if (this.expandStatus === 'collapse') {
        this.expandStatus = 'expand';
      } else {
        this.expandStatus = 'collapse';
      }
      this._getSearchBoxHeight();
    },
    // 进行计算
    _getSearchBoxHeight() {
      this.$nextTick(() => {
        let searchDom = document.getElementById('searchForm') || [];
        let search_item_length = Array.from(searchDom)?.length;

        // 计算所有搜索条件的总宽度
        let search_item_length_distance = Number(search_item_length || 0) * 190;
        // 计算form控件的宽度
        let form_dom_distance = searchDom.offsetWidth;
        // this.isShowExpandDom = search_item_length_distance > form_dom_distance;

        let search_height = this.$refs.searchRefs?.offsetHeight;
        let function_height = this.$refs.functionRefs?.offsetHeight;
        let panel_height = this.$refs.panelRefs?.offsetHeight;
        let page_height = this.$refs.pageRefs?.offsetHeight;
        let tab_height = this.$refs.tabRefs?.offsetHeight;
        // 计算搜索区
        this.calc_search_height = Number(search_height || 0);
        // 计算功能区
        this.calc_function_height = Number(function_height || 0);
        // 计算表格tab快捷切换区
        this.calc_panel_height = Number(panel_height || 0);
        // 计算分页区
        this.calc_page_height = Number(page_height || 0);
        // 计算标签切换区
        this.calc_tab_height = Number(tab_height || 0);
      });
    }
  }
};
