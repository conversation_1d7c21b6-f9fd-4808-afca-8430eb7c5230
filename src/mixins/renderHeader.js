export default {
  methods: {
    _renderHeader( h, params, content = '请输入悬浮显示内容', maxWidth = '200' ) {
      {
        return h( 'div', {
            style: {
              display: 'flex',
              alignItem: 'center',
            }
          },
          [
            h( 'Tooltip', {
              props: {
                content,
                theme: 'dark',
                placement: 'top',
                maxWidth
              },
            }, [
              h( 'Icon', {
                props: {
                  type: 'md-help-circle'
                },
                style: {
                  cursor: 'pointer',
                  fontSize: '18px',
                  marginRight: '4px'
                }
              } )
            ] ),
            params.column.title,
          ],
        )
      }
    }
  }
}