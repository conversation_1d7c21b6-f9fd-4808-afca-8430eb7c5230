import S from '@/utils/util';

export default {
  data() {
    return {
      tableLoading: false,
      list: [],
      total: 0,
      num_info: {},
      timeRange: [],
      status_count: {}
    };
  },
  mounted() {},
  activated() {
    if (this.$store.state.app.shouldRefresh) {
      this.onSearch();
    }
  },
  methods: {
    onSearch() {
      this.queryFormData.page = 1;
      this.pageSize = 10;
      console.log('queryFormData', this.queryFormData);
      this.submitQueryForm();
    },
    loadList() {
      this.tableLoading = true;
      this.$api[this.apiName](this.queryFormData)
        .then(data => {
          this.total = +data.total;
          this.list = data.list;
          if (data.num_info) {
            this.num_info = data.num_info;
          }
          if (this.status_total) {
            this.status_total = data.status_total;
          }
          if (this.listCount) {
            this.listCount = data.list_status_count;
          }
          if (this.total_fee) {
            this.total_fee = data.total_fee;
          }
          this.status_count = data.status_count;
          this.handlerListData && this.handlerListData(data);
          this.handleList && this.handleList(data);
        })
        .catch(error => {
          this.$Message.error(error.errmsg);
        })
        .finally(() => {
          this.tableLoading = false;
          this.$store.commit('app/CHANGE_FRESH_STATUS', false);
        });
    },
    handleSizeChange(val) {
      console.log('-> val', val);
      this.queryFormData.pageSize = val - 0;
      this.queryFormData.page = 1;
      this.submitQueryForm();
    },
    handleCurrentChange(val) {
      console.log('-> val', val);
      this.queryFormData.page = val;
      this.submitQueryForm();
    },
    submitQueryForm(replace) {
      console.log('为啥不执行', replace);
      this.queryFormData.r = S.random(6); // 只有在参数发生变化时才会触发前置守卫；所以添加随机数，保证url参数一定有修改
      let searchObj = {};
      for (const searchKey in this.queryFormData) {
        if (this.queryFormData[searchKey]) {
          searchObj[searchKey] = this.queryFormData[searchKey];
        }
      }
      if (replace) {
        this.$router.replace({ query: searchObj });
      } else {
        this.$router.push({ query: searchObj });
      }
    },
    handleTimeChange(times, startTime = 'st', endTime = 'et') {
      if (times) {
        this.queryFormData[startTime] = times[0];
        this.queryFormData[endTime] = times[1];
      } else {
        this.queryFormData[startTime] = '';
        this.queryFormData[endTime] = '';
      }
    },
    /**
     *  针对query对时间选择器无法回显
     *  新增参数处理多个日期选择器情况
     *  startTime: 开始时间
     *  endTime: 结束时间
     *  dateRangeValue: 日期组件取值（timeRange为默认字段，当列表筛选条件只有一个日期选择器时，取值请使用该字段）
     */
    getTimeRange(startTime = 'st', endTime = 'et', dateRangeValue = 'timeRange') {
      if (this.queryFormData[startTime] && this.queryFormData[endTime]) {
        this[dateRangeValue] = [this.queryFormData[startTime], this.queryFormData[endTime]];
      } else {
        this[dateRangeValue] = [];
      }
    }
  }
};
