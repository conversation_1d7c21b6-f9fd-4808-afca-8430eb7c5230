import S from '@/utils/util'

export default {
  data() {
    return {
      tableLoading: false,
      list: [],
      total: 0,
      timeRange: [],
    }
  },
  mounted() {
  },
  activated(){
    // if(this.$store.state.app.shouldRefresh){
    //   this.onSearch()
    // }
  },
  methods: {
    onSearch() {
      this.queryFormData.page = 1
      this.queryFormData.pageSize = 20
      this.submitQueryForm()
    },
    /**
     * @param { boolean } needRefresh:true,表示表格高度要刷新
     * */
    loadList(needRefresh = true) {
      this.tableLoading = true
      this.$api[this.apiName]( this.queryFormData ).then( data => {
        console.log("-> %c data  === %o", "font-size: 15px;color: green;", data)
        this.total = Number(data.total)||0
        this.list = data.list
        this.list_count = data.list_count||{}
        this.statusCount = data.statusCount || {}
        if ( this.status_total ) {
          this.status_total = data.status_total
        }
        data.status_count && (this.status_count = data.status_count)
        if ( needRefresh ) {
          this.resetScroll()
        }
      }, error => {
        this.$Message.error( error.errmsg )
      } ).finally( () => {
        this.tableLoading = false
        // this.$store.commit('app/CHANGE_FRESH_STATUS',false)
      } )
    },
    // handleSizeChange( val ) {
    //   console.log( '-> val', val )
    //   this.queryFormData.page = 1
    //   this.queryFormData.pageSize = val
    //   this.submitQueryForm()
    // },
    // handleCurrentChange( val ) {
    //   this.queryFormData.page = val
    //   this.submitQueryForm()
    // },
    onPageChange(page, pageSize) {
      this.queryFormData.page = page
      this.queryFormData.pageSize = pageSize
      this.submitQueryForm()
    },
    submitQueryForm( replace ) {
      this.queryFormData.r = S.random( 6 ) // 只有在参数发生变化时才会触发前置守卫；所以添加随机数，保证url参数一定有修改
      let searchObj = {}
      for ( const searchKey in this.queryFormData ) {
        if ( this.queryFormData[searchKey] ) {
          searchObj[searchKey] = this.queryFormData[searchKey]
        }
      }
      if ( replace ) {
        this.$router.replace( { query: searchObj } )
      } else {
        this.$router.push( { query: searchObj } )
      }
    },
    handleTimeChange(times, startTime = 'st', endTime = 'et') {
      if (times) {
        this.queryFormData[startTime] = times[0];
        this.queryFormData[endTime] = times[1];
      } else {
        this.queryFormData[startTime] = '';
        this.queryFormData[endTime] = '';
      }
    },

    /**
     *  针对query对时间选择器无法回显
     *  新增参数处理多个日期选择器情况
     *  startTime: 开始时间
     *  endTime: 结束时间
     *  dateRangeValue: 日期组件取值（timeRange为默认字段，当列表筛选条件只有一个日期选择器时，取值请使用该字段）
     */
    getTimeRange(startTime = 'st', endTime = 'et', dateRangeValue = 'timeRange') {
      if (this.queryFormData[startTime] && this.queryFormData[endTime]) {
        this[dateRangeValue] = [this.queryFormData[startTime], this.queryFormData[endTime]];
      } else {
        this[dateRangeValue] = [];
      }
    },
    resetScroll(){
      this.$nextTick(()=>{
        let overflowY = this.$el.getElementsByClassName("ivu-table-body")[0];
        if (!overflowY) {
          return;
        }
        overflowY.scrollTop = 0;
      })
    }
  },
}
