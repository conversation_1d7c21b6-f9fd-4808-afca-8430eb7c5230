const routes = [
		{
			"id": "10",
			"path": "purchase/product/list",
			"query": {},
			"type": "MENU",
			"meta": {
				"title": "采购",
				"icon": "fa-shopping-basket"
			},
			"children": [
				{
					"id": "11",
					"p_id": "10",
					"path": "purchase/product/list",
					"query": {},
					"type": "SUB_MENU",
					"meta": {
						"title": "首次产品包"
					}
				},
				{
					"id": "12",
					"p_id": "11",
					"path": "purchase/product/detail",
					"query": {},
					"type": "PAGE",
					"meta": {
						"title": "详情"
					}
				},
				{
					"id": "13",
					"p_id": "11",
					"path": "purchase/product/create",
					"query": {},
					"type": "PAGE",
					"meta": {
						"title": "新增/编辑"
					}
				},
				{
					"id": "14",
					"p_id": "10",
					"path": "purchase/order/list",
					"query": {},
					"type": "SUB_MENU",
					"meta": {
						"title": "采购单"
					}
				},
				{
					"id": "15",
					"p_id": "14",
					"path": "purchase/order/detail",
					"query": {},
					"type": "PAGE",
					"meta": {
						"title": "采购单详情"
					}
				},
				{
					"id": "16",
					"p_id": "10",
					"path": "purchase/clinic/list",
					"query": {},
					"type": "SUB_MENU",
					"meta": {
						"title": "诊所采购单"
					}
				},
				{
					"id": "17",
					"p_id": "16",
					"path": "purchase/clinic/detail",
					"query": {},
					"type": "PAGE",
					"meta": {
						"title": "诊所采购单详情"
					}
				},
			]
		},
	{
		"id": "20",
		"path": "clinic/list",
		"query": {},
		"type": "MENU",
		"meta": {
			"title": "诊所",
			"icon": "fa-institution"
		},
		"children": [
			{
				"id": "21",
				"p_id": "20",
				"path": "clinic/list",
				"query": {},
				"type": "SUB_MENU",
				"meta": {
					"title": "诊所列表"
				}
			},{
				"id": "22",
				"p_id": "21",
				"path": "clinic/detail",
				"query": {},
				"type": "PAGE",
				"meta": {
					"title": "诊所详情"
				}
			},
		]
	},
		{
			"id": "16",
			"path": "storehouse/list",
			"query": {},
			"type": "MENU",
			"meta": {
				"title": "库存",
				"icon": "fa-database"
			},
			"to_his": "0",
			"children": [
				{
					"id": "17",
					"p_id": "16",
					"path": "storehouse/list",
					"query": {},
					"type": "SUB_MENU",
					"meta": {
						"title": "首采出入库"
					}
				},
				{
					"id": "18",
					"p_id": "17",
					"path": "storehouse/detail",
					"type": "PAGE",
					"meta": {
						"title": "出入库详情"
					}
				},
				{
					"id": "19",
					"p_id": "16",
					"path": "storehouse/stock/list",
					"query": {},
					"type": "SUB_MENU",
					"meta": {
						"title": "出入库列表"
					}
				},
			]
		},
	{
			"id": "22",
			"path": "finance/list",
			"query": {},
			"type": "MENU",
			"meta": {
				"title": "财务",
				"icon": "fa-bar-chart"
			},
			"to_his": "0",
			"children": [
				{
					"id": "23",
					"p_id": "22",
					"path": "finance/list",
					"query": {},
					"type": "SUB_MENU",
					"meta": {
						"title": "首采预付款"
					}
				},
				{
					"id": "24",
					"p_id": "23",
					"path": "finance/earnings/edit",
					"type": "PAGE",
					"meta": {
						"title": "新增/编辑 收入明细"
					}
				},
				{
					"id": "25",
					"p_id": "23",
					"path": "finance/expenses/edit",
					"type": "PAGE",
					"meta": {
						"title": "新增/编辑 支出明细"
					}
				},
				{
					"id": "26",
					"p_id": "22",
					"path": "finance/income/list",
					"query": {},
					"type": "SUB_MENU",
					"meta": {
						"title": "收入统计"
					}
				},
			]
		},
		{
			"id": "121",
			"path": "platform/role/list",
			"query": {},
			"type": "MENU",
			"meta": {
				"title": "菜单",
				"icon": "fa-wrench"
			},
			"children": [
				{
					"id": "129",
					"p_id": "121",
					"path": "platform/role/list",
					"query": {},
					"type": "SUB_MENU",
					"meta": {
						"title": "角色管理"
					}
				},{
					"id": "130",
					"p_id": "129",
					"path": "platform/role/permission",
					"query": {},
					"type": "PAGE",
					"meta": {
						"title": "权限设置"
					}
				},
				{
					"id": "131",
					"p_id": "141",
					"path": "platform/menus/menus",
					"query": {},
					"type": "SUB_MENU",
					"meta": {
						"title": "菜单管理"
					}
				},
				{
					"id": "132",
					"p_id": "143",
					"path": "platform/menus/resource",
					"query": {},
					"type": "SUB_MENU",
					"meta": {
						"title": "功能管理"
					}
				}
			]
		},
		{
			"id": "150",
			"path": "member/list",
			"query": {},
			"type": "MENU",
			"meta": {
				"title": "设置",
				"icon": "fa-cog"
			},
			"children": [
				// {
				// 	"id": "151",
				// 	"p_id": "150",
				// 	"path": "store/general_set",
				// 	"query": {},
				// 	"type": "SUB_MENU",
				// 	"meta": {
				// 		"title": "通用设置"
				// 	}
				// },
				{
					"id": "152",
					"p_id": "150",
					"path": "member/list",
					"query": {},
					"type": "SUB_MENU",
					"meta": {
						"title": "员工信息"
					}
				}
			]
		},
]
export default {
	'get|/routes/query': (option) => {
		return {
			status: 200,
			message: 'success',
			data: routes,
		}
	},
}
