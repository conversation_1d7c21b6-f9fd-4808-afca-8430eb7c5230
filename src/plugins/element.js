import Vue from 'vue';
import {
    Select,
    Option,
    Cascader,
    Upload,
    Autocomplete,
    Popover,
    Card,
    Input,
    Row,
    Col,
    Empty,
    Button,
    Form,
    FormItem,
    Radio,
    RadioGroup,
    Dialog,
    Loading,
    Table,
    TableColumn,
    RadioButton,
    Image,
    Icon,
    DatePicker,
    Tabs,
    TabPane,
    CheckboxGroup,
    Checkbox,
    Descriptions,
    DescriptionsItem,
    progress,
    InputNumber,
    Tooltip
} from 'element-ui';
Vue.use(Select);
Vue.use(Option).use(Cascader);
Vue.use(Upload);
Vue.use(Autocomplete);
Vue.use(Popover);
Vue.use(Card);
Vue.use(Input);
Vue.use(Row);
Vue.use(Col);
Vue.use(Empty);
Vue.use(Button);
Vue.use(Form);
Vue.use(FormItem);
Vue.use(Radio);
Vue.use(RadioGroup);
Vue.use(Dialog);
Vue.use(Table);
Vue.use(TableColumn);
Vue.use(RadioButton);
Vue.use(Image);
Vue.use(Icon);
Vue.use(DatePicker);
Vue.use(Tabs);
Vue.use(TabPane);
Vue.use(Loading);
Vue.use(Descriptions);
Vue.use(DescriptionsItem);
Vue.use(Loading.directive);
Vue.use(CheckboxGroup);
Vue.use(Checkbox);
Vue.use(progress);
Vue.use(InputNumber);
Vue.use(Tooltip);
