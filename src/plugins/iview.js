import Vue from 'vue';
import <PERSON><PERSON> from 'view-design';
import selectMixin from './iviewCustomPlugin/selectMixin';
// ViewUI.Table.components.tableBody.components.TableTr.methods.
ViewUI.Table.components.tableBody.components.TableTr.methods.rowClsName = function (_index) {
  return this.$parent.$parent.rowClassName(this.row, _index);
};
ViewUI.Select.mixins.push(selectMixin);
ViewUI.Tooltip.deactivated = function () {
  this.handleClosePopper();
};
Vue.use(ViewUI, {
  transfer: true,
  capture: false
});

Vue.prototype.$Message.config({
  duration: 4
});
