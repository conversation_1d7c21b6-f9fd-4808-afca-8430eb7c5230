# 本地临时路由
在模块开发过程中，如果需要有临时的路由，可以使用下面的办法。

在 router.js 同级目录中创建以*.dev.js格式的文件，文件内容如下：
```
import Main from "_c/main";

/**
 * 开发调试页面
 */
export default [
  {
    path: '/test',
    component: Main,
    meta: {
      title: '调试页',
    },
    children: [
      {
        path: '/test',
        meta: {
          title: '调试页',
        },
        component: () => import('@/view/test/test')
      }
    ]
  }
]

```
