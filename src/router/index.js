import Vue from 'vue';
import VueRouter from 'vue-router';
import store from '@/store';
import { constantRoutes } from './router';
import S from '@/utils/util';
import { getAssetAppKey, isLogin, logout } from '@/utils/runtime';

Vue.use(VueRouter);

const router = new VueRouter({
  mode: 'history',
  base: '/opc',
  routes: constantRoutes
});

router.beforeEach(async (to, from, next) => {
  Vue.prototype.$Modal.remove();
  if (isLogin()) {
    const has = store.state.router.routes && store.state.router.routes.length > 0;
    if (has) {
      next();
    } else {
      let debug = false,
        userRoutes = [];
      if (!debug) {
        userRoutes = await store.dispatch('router/getUserRoutes');
        const app_key = getAssetAppKey();
        if (!app_key) {
          const app_key_res = await store.dispatch('app/getAssetAppKey');
          console.log('%c=>(index.js:41) app_key_res', 'color: #ECA233;font-size: 16px;', app_key_res);
        }
      } else {
        //初始化项目时没有路由的情况下使用下面的逻辑，来访问已有的页面，如:菜单管理页面...
        userRoutes.push({
          path: to.path,
          component: () => import(`../view${to.path}`)
        });
        store.commit('router/SET_ROUTES', userRoutes);
      }

      router.addRoutes(userRoutes);
      next({ ...to, replace: true });
    }
  } else {
    if (to.matched.some(_ => _.meta.notNeedLogin)) {
      next();
    } else {
      next('/login?from=' + encodeURIComponent(to.fullPath));
    }
  }
});

router.afterEach(to => {
  S.setTitle(to);
  window.scrollTo(0, 0);

  // 20210506 by lsm 尝试解决开启Modal浏览器返回前一个页面时，body中overflow hidden样式未清除问题
  document.body.removeAttribute('style');
  // Vue.prototype.$Modal.remove()
});

export default router;
