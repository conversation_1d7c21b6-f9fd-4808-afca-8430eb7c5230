import Vue from 'vue'
import Vuex from 'vuex'
import util from '@/utils/util'
import createLogger from 'vuex/dist/logger'

Vue.use(Vuex)

const modulesFiles = require.context('./modules', true, /\.js$/)
const debug = process.env.NODE_ENV !== 'production'
const modules = modulesFiles.keys().reduce((modules, modulePath) => {
	// set './app.js' => 'app'
	const moduleName = modulePath.replace(/^\.\/(.*)\.\w+$/, '$1')
	const value = modulesFiles(modulePath)
	modules[moduleName] = value.default
	return modules
}, {})
const logger = createLogger({
	collapsed: true, // 自动展开记录的 mutation
	filter(mutation, stateBefore, stateAfter) {
		// 若 mutation 需要被记录，就让它返回 true 即可
		// 顺便，`mutation` 是个 { type, payload } 对象
		let blackList = ['app/SET_CLIENT_HEIGHT', 'app/SHOW_HELP_WRAPPER','menus/SET_SHOW_SECOND_MENU','menus/SET_FIRST_MENUS','menus/handlerMenus']
		return !mutation.type.indexOf(blackList)
	},
	actionFilter(action, state) {
		// 和 `filter` 一样，但是是针对 action 的
		// `action` 的格式是 `{ type, payload }`
		return action.type !== 'app/setClientHeight'
	},
	transformer(state) {
		// 在开始记录之前转换状态
		// 例如，只返回指定的子树
		return state.subTree
	},
	mutationTransformer(mutation) {
		// mutation 按照 { type, payload } 格式记录
		// 我们可以按任意方式格式化
		return mutation.type
	},
	actionTransformer(action) {
		// 和 `mutationTransformer` 一样，但是是针对 action 的
		return action.type
	},
	logActions: true, // 记录 action 日志
	logMutations: true, // 记录 mutation 日志
	logger: console, // 自定义 console 实现，默认为 `console`
})

const store = new Vuex.Store({
	strict: !util.isProdEnv, // 严格模式会深度监测状态树来检测不合规的状态变更
	modules,
	plugins: debug? [logger]:[],
})
export default store
