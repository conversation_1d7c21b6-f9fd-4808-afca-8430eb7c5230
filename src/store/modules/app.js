import { getOpcName, writeLoginCookie, getUser } from 'utils/runtime';
import request from 'utils/request';

const state = {
  title: '',
  clientHeight: '', // 浏览器高度
  showHelpWrapper: false, // 显示右侧帮助栏区域
  opcName: getOpcName()
};

const mutations = {
  SET_TITLE: (state, title) => {
    state.title = title;
  },
  SET_CLIENT_HEIGHT: (state, clientHeight) => {
    state.clientHeight = clientHeight;
  },
  SHOW_HELP_WRAPPER: (state, showHelpWrapper) => {
    state.showHelpWrapper = showHelpWrapper;
  },
  CHANGE_FRESH_STATUS: (state, payload) => {
    state.shouldRefresh = payload;
  }
};

const actions = {
  setTitle({ commit }, title) {
    commit('SET_TITLE', title);
  },
  setClientHeight({ commit }, clientHeight) {
    commit('SET_CLIENT_HEIGHT', clientHeight);
  },
  showHelpWrapper({ commit }, showHelpWrapper) {
    commit('SHOW_HELP_WRAPPER', showHelpWrapper);
  },
  getAssetAppKey({ commit }) {
    console.log(213213, '2132');
    return new Promise((resolve, reject) => {
      request
        .get('/pms_opc/common.getassetappkey')
        .then(res => {
          console.log('%c=>(app.js:50) res', 'color: #ECA233;font-size: 16px;', res);
          const user = getUser();
          writeLoginCookie({ ...user, asset_app_key: res.asset_app_key });
          resolve(res.asset_app_key);
        })
        .catch(err => {
          resolve(err);
        });
    });
  }
};

export default {
  namespaced: true,
  state,
  mutations,
  actions
};
