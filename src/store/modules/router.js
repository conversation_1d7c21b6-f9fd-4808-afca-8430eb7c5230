import { developRoutes, errorRoutes } from '@/router/router';
import Main from '@/components/main';
import io from 'utils/request';
import store from '@/store';
import cloneDeep from 'lodash.clonedeep';
// import axios from 'axios';

const state = {
  routes: [],
  // 所有的元素编码
  idcodes: [],
  allRoutes: new Set()
};

const mutations = {
  SET_ROUTES: (state, routes) => {
    state.routes = routes.concat(developRoutes).concat(errorRoutes);
  },
  // 设置所有的元素编码
  SET_IDCODES: (state, payload) => {
    state.idcodes = payload;
  },
  SET_ALL_ROUTES: (state, payload) => {
    state.allRoutes = payload;
  }
};

const actions = {
  // 获取用户的路由
  getUserRoutes({ commit }) {
    return new Promise((resolve, reject) => {
      // io.get('/pms_opc/permission.index.routes',{params:{new:1}}).then(menus => {
      io.get('/pms_opc/permission.index.routes', { params: { new: 1 } })
        .then(
          menus => {
            // axios.get('/routes/query').then(menus => {
            // 处理路由
            // let routes = handlerRoutes(menus.data.data)
            let { routes, allRoutes } = handlerRoutes(menus);
            commit('SET_ROUTES', routes);
            // 设置元素编码
            let codes = handlerIdCodes(menus);
            commit('SET_IDCODES', codes);
            commit('SET_ALL_ROUTES', allRoutes);
            console.log(state);
            // 处理菜单
            store.dispatch('menus/handlerMenus', state.routes).then();
            resolve(handleAddRoutes(state.routes));
          },
          data => {
            reject(data.errmsg);
          }
        )
        .catch(error => {
          reject(error);
        });
    });
  }
};

export default {
  namespaced: true,
  state,
  mutations,
  actions
};

function handlerRoutes(routes) {
  let res = [];
  let allRoutes = new Set();
  let i = 0;
  if (routes.length > 0)
    routes.forEach(route => {
      let tmpRoute = route;
      tmpRoute.path = `/${route.path}`;
      tmpRoute.component = Main;
      if (route.children != undefined) {
        let tmpChildren = [];
        route.children.forEach(c_route => {
          c_route.path && allRoutes.add('/' + c_route.path);
          let tmpCRoute = c_route;
          tmpCRoute.path = `/${c_route.path}`;
          tmpCRoute.component = () => import(`@/view${c_route.path}`);
          tmpCRoute.meta.type = c_route.type;
          tmpCRoute.meta.p_id = c_route.p_id || '';
          tmpChildren.push(tmpCRoute);
        });
        tmpRoute.children = tmpChildren;
      }

      if (i == 0) {
        tmpRoute.redirect = tmpRoute.path;
        tmpRoute.path = '/';
      }
      i++;
      res.push(tmpRoute);
    });

  return { routes: res, allRoutes };
}

function handleAddRoutes(routes) {
  // console.log("-> %c routes  === %o", "font-size: 15px;color: green;", routes)
  const copyRoutes = cloneDeep(routes);
  copyRoutes.map(route => {
    if (route.children && route.children.length > 0) {
      route.children = route.children.filter(child => child.type !== 'GROUP');
    }
    if (route.path.startsWith('/manual/list')) {
      route.children = route.children.filter(child => {
        return !child.path.startsWith('/manual/list') || child.path.indexOf(':') > -1;
      });
    }
  });
  return copyRoutes;
}

/**
 * @description: 获取路由中所有的元素编码
 */
function handlerIdCodes(routes) {
  let codes = [];
  routes.forEach(item => {
    item.children.forEach(child_item => {
      if (child_item.type == 'PAGE') {
        child_item.element_list &&
          child_item.element_list.forEach(ele_item => {
            codes.push(ele_item.idcode);
          });
      }
    });
  });
  return codes;
}
