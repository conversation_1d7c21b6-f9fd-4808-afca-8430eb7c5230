import config from '@/config'

const state = {
  // 版本号
  last_version: '',

  // 是否需要更新应用
  is_update: false,
}

const mutations = {
  CHANGE_VERSION: (state, version) => {
    state.last_version = version
  },
  CHANGE_IS_UPDATE: (state, is_update) => {
    state.is_update = is_update
  },
}

const actions = {
  checkVersion({ commit }, last_version) {
    return new Promise((resolve) => {
      if (config.codeVersion != last_version) {
        commit('CHANGE_IS_UPDATE', true)
      }
      resolve()
    })
  }
}

export default {
  namespaced: true,
  state,
  mutations,
  actions
}
