import { MessageBox } from 'element-ui';

const AUDIT_PROMPT_KEY = 'audit_last_prompt_time';
const AUDIT_PROMPT_COUNT_KEY = 'audit_prompt_count';
// const AUDIT_PROMPT_INTERVAL = 3 * 60 * 1000; // 6小时
const AUDIT_PROMPT_INTERVAL = 6 * 60 * 60 * 1000; // 6小时

let auditIntervalTimer = '';

// 登录成功后调用
export function tryCheckAuditStatus(vm, force = false) {
  checkAuditStatus(vm, force);
  if (auditIntervalTimer) clearInterval(auditIntervalTimer);
  auditIntervalTimer = setInterval(() => {
    checkAuditStatus(vm, force);
  }, AUDIT_PROMPT_INTERVAL);
}

// 全局校验（如路由守卫或定时调用）
export function checkAuditStatus(vm, force = false) {
  const lastPrompt = localStorage.getItem(AUDIT_PROMPT_KEY);
  const promptCount = Number(localStorage.getItem(AUDIT_PROMPT_COUNT_KEY) || 0);
  const now = Date.now();

  // 只允许提示2次
  if (promptCount >= 2) {
    return;
  }

  if (!force && lastPrompt && now - Number(lastPrompt) < AUDIT_PROMPT_INTERVAL) {
    return;
  }

  vm.$api.getAuthenticationInfo().then(res => {
    if (res?.hide_audit_popup != 1) {
      if (res.audit_status !== 'succeed') {
        localStorage.setItem(AUDIT_PROMPT_KEY, now);
        localStorage.setItem(AUDIT_PROMPT_COUNT_KEY, promptCount + 1);
        MessageBox({
          title: '提示',
          showCancelButton: true,
          confirmButtonText: '去认证',
          cancelButtonText: '取消',
          closeOnClickModal: false,
          confirmButtonClass: 'auth_confirm_btn_class',
          cancelButtonClass: 'auth_cancel_btn_class',
          type: 'warning',
          center: true,
          message: '系统检测到您尚未进行主体认证，若下属诊所产生业务订单将不会对您进行分账。',
          callback: action => {
            if (action === 'confirm') {
              vm.$router.push({ path: '/setting/subject-auth/index' });
            }
          }
        });
      }
    }
  });
}

// 退出登录时调用
export function handleLogout() {
  localStorage.removeItem(AUDIT_PROMPT_KEY);
  localStorage.removeItem(AUDIT_PROMPT_COUNT_KEY);
  if (auditIntervalTimer) clearInterval(auditIntervalTimer);
}
