// import config from '@/config';
const VALID_IMAGE_DOMAINS = ['.rsjxx.com', '.rsjitcm.com'];

// URL 解析函数，支持中文字符
function parseUrl(url) {
  try {
    // 先尝试使用 URL API
    const urlObj = new URL(url);
    return {
      hostname: () => urlObj.hostname,
      protocol: () => urlObj.protocol.replace(':', ''),
      path: () => urlObj.pathname
    };
  } catch (e) {
    // 如果 URL 无效或包含非法字符，使用自定义解析
    const pattern = /^(https?:\/\/)([^/\s]+)([/][^\s]*)?$/i;
    const match = url.match(pattern);

    if (match) {
      return {
        hostname: () => match[2] || '',
        protocol: () => (match[1] ? match[1].replace('://', '') : 'http'),
        path: () => match[3] || ''
      };
    }

    // 如果都解析失败，返回空对象
    return {
      hostname: () => '',
      protocol: () => 'http',
      path: () => ''
    };
  }
}
let imageUrl = {};

/**
 *
 * 原图添加图片样式  !!!不在七牛存储中的图片使用默认的cdn域名，也不会添加图片样式
 * @param path string  图片相对路径\图片URL
 * @param style string|null 图片样式规格 例如：\KIF\Qiniu\ImageStyle::IMG_STYLE_200
 * @param defaultPath string|null 默认图片路径
 * @return string $url 返回完整的图片URL
 */
imageUrl.imageStyle = function (path, style = 'B.w300', defaultPath = '') {
  if (!path || path.trim().length <= 0) {
    return defaultPath ? imageUrl.imageStyle(defaultPath) : defaultPath;
  }

  const uri = parseUrl(path);
  const domain = uri.hostname();
  // 如果域名不在七牛存储中，则使用默认的cdn域名   或者 图片路径是本地的
  const isValidDomain = VALID_IMAGE_DOMAINS.some(
    validDomain => domain.endsWith(validDomain) && !domain.includes('wwwcdn')
  );
  if (!isValidDomain) {
    return path;
  }

  if (!path.match(/^https?:\/\//)) {
    return path;
  }

  let url = this.imageCdnDomain(path);

  if (url.split('.').pop().toLowerCase() === 'gif') {
    return url;
  }

  return url + '-' + style;
};

imageUrl.imageCmds = function (path, cmds) {
  if (path.trim().length <= 0) {
    return path;
  }

  let url = this.imageCdnDomain(path);
  return url + '?' + cmds;
};

imageUrl.imageCdnDomain = function (path) {
  if (path.trim().length <= 0) {
    return path;
  }

  const uri = parseUrl(path);
  const domain = uri.hostname();
  const fullDomain = uri.protocol() + '://' + domain;
  path = this.getImagePath(path);
  return fullDomain + '/' + path;
};

imageUrl.getImagePath = function (path) {
  if (path.trim().length <= 0) {
    return path;
  }

  if (path.match(/http/)) {
    path = parseUrl(path).path();
  }

  path = path.replace(/(-B\..+)$/, '');
  return path.replace(/^\//, '');
};

export const getImageKey = function (tmpPath, space) {
  var ext = tmpPath.split('.').pop().toLowerCase();
  Date.prototype.format = function (format) {
    var o = {
      'M+': this.getMonth() + 1,
      'd+': this.getDate(),
      'h+': this.getHours(),
      'm+': this.getMinutes(),
      's+': this.getSeconds(),
      'q+': Math.floor((this.getMonth() + 3) / 3),
      S: this.getMilliseconds()
    };
    if (/(y+)/.test(format)) {
      format = format.replace(RegExp.$1, (this.getFullYear() + '').substr(4 - RegExp.$1.length));
    }
    for (var k in o) {
      if (new RegExp('(' + k + ')').test(format)) {
        format = format.replace(RegExp.$1, RegExp.$1.length == 1 ? o[k] : ('00' + o[k]).substr(('' + o[k]).length));
      }
    }
    return format;
  };

  space = space ? space : 'image';
  var date = new Date();
  var year = date.format('yyyy');
  var month = date.format('MM');
  var day = date.format('dd');
  var hour = date.format('hh');
  var minute = date.format('mm');
  var second = date.format('ss');
  return (
    space +
    '/' +
    year +
    '/' +
    month +
    day +
    '/' +
    hour +
    minute +
    second +
    '_' +
    Math.ceil(Math.random() * 100000) +
    '.' +
    ext
  );
};

export default imageUrl;
