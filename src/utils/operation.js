// /**
//  * $operator 包含加减乘除以及toPrecision五个方法，能确保浮点数运算不丢失精度
//  *
//  * 我们知道计算机编程语言里浮点数计算会存在精度丢失问题（或称舍入误差），其根本原因是二进制和实现位数限制有些数无法有限表示
//  * 以下是十进制小数对应的二进制表示
//  *      0.1 >> 0.0001 1001 1001 1001…（1001无限循环）
//  *      0.2 >> 0.0011 0011 0011 0011…（0011无限循环）
//  * 计算机里每种数据类型的存储是一个有限宽度，比如 JavaScript 使用 64 位存储数字类型，因此超出的会舍去。舍去的部分就是精度丢失的部分。
//  *
//  * ** method **
//  *  add / subtract / multiply /divide / toPrecision
//  *
//  * ** explame **
//  *  0.1 + 0.2 == 0.30000000000000004 （多了 0.00000000000004）
//  *  0.2 + 0.4 == 0.6000000000000001  （多了 0.0000000000001）
//  *  19.9 * 100 == 1989.9999999999998 （少了 0.0000000000002）
//  *  1.335.toPrecision(2) // 1.33 不是1.34
//  *
//  * $operator.add(0.1, 0.2) >> 0.3
//  * $operator.multiply(19.9, 100) >> 1990
//  * $operator.toPrecision(1.335, 2) >> 1.34
//  *
//  */
// /*
//   * 判断obj是否为一个整数
//   */
// const isInteger = ( obj ) => Math.floor( obj ) === obj
//
// /*
// * 将一个浮点数转成整数，返回整数和倍数。如 3.14 >> 314，倍数是 100
// * @param floatNum {number} 小数
// * @return {object}
// *   {times:100, num: 314}
// */
// const toInteger = ( floatNum ) => {
//   var res = { times: 1, num: 0 }
//   var isNegative = floatNum < 0
//   if ( isInteger( floatNum ) ) {
//     res.num = floatNum
//     return res
//   }
//   var strfi = floatNum + ''
//   var dotPos = strfi.indexOf( '.' )
//   var len = strfi.substr( dotPos + 1 ).length
//   var times = Math.pow( 10, len )
//   var intNum = parseInt( Math.abs( floatNum ) * times + 0.5, 10 )
//   res.times = times
//   if ( isNegative ) {
//     intNum = -intNum
//   }
//   res.num = intNum
//   return res
// }
//
// /*
// * 核心方法，实现加减乘除运算，确保不丢失精度
// * 思路：把小数放大为整数（乘），进行算术运算，再缩小为小数（除）
// *
// * @param a {number} 运算数1
// * @param b {number} 运算数2
// * @param digits {number} 精度，保留的小数点数，比如 2, 即保留为两位小数
// * @param op {string} 运算类型，有加减乘除（add/subtract/multiply/divide）
// *
// */
// function operation( a, b, digits, op ) {
//   var o1 = toInteger( a )
//   var o2 = toInteger( b )
//   var n1 = o1.num
//   var n2 = o2.num
//   var t1 = o1.times
//   var t2 = o2.times
//   var max = t1 > t2 ? t1 : t2
//   var result = null
//   switch ( op ) {
//     // 加法
//     case 'add':
//       if ( t1 === t2 ) { // 两个小数位数相同
//         result = n1 + n2
//       } else if ( t1 > t2 ) { // o1 小数位 大于 o2
//         result = n1 + n2 * (t1 / t2)
//       } else { // o1 小数位 小于 o2
//         result = n1 * (t2 / t1) + n2
//       }
//       return toPrecision(result / max,digits)
//     //减法
//     case 'subtract':
//       if ( t1 === t2 ) {
//         result = n1 - n2
//       } else if ( t1 > t2 ) {
//         result = n1 - n2 * (t1 / t2)
//       } else {
//         result = n1 * (t2 / t1) - n2
//       }
//       return toPrecision(result / max,digits)
//     //乘法
//     case 'multiply':
//       return toPrecision((n1 * n2) / (t1 * t2),digits)
//     //除法
//     case 'divide':
//       if(n1===0||n2===0)return 0
//       return toPrecision((n1 / n2) * (t2 / t1),digits)
//   }
// }
//
// function add( a, b, digits = 2 ) {
//   return operation( a, b, digits, 'add' )
// }
//
// function subtract( a, b, digits = 2 ) {
//   return operation( a, b, digits, 'subtract' )
// }
//
// function multiply( a, b, digits = 2 ) {
//   return operation( a, b, digits, 'multiply' )
// }
//
// function divide( a, b, digits = 2 ) {
//   return operation( a, b, digits, 'divide' )
// }
//
// function toPrecision( num, s = 2 ) {
//   var times = Math.pow( 10, s )
//   var des = num * times + 0.5
//   des = parseInt( des, 10 ) / times
//   return des
// }
//
//
// export const $operator = {
//   add,
//   subtract,
//   multiply,
//   divide,
//   toPrecision
// }
import { plus, minus, times, divide as div, round } from 'number-precision';

function add(a, b, p = 2) {
  return round(plus(a, b), p);
}

function subtract(a, b, p = 2) {
  return round(minus(a, b), p);
}

function multiply(a, b, p = 2) {
  return round(times(a, b), p);
}

function divide(a, b, p = 2) {
  return round(div(a, b), p);
}

function toPrecision(num, p = 2) {
  console.log('-> %c num, p  ===    %o', 'font-size: 15px;color: #F56C6C ;', num, p);
  return round(num, p);
}

export const $operator = {
  add,
  subtract,
  multiply,
  divide,
  toPrecision
};
