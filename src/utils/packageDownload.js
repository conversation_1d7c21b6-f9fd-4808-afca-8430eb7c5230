import JSZip from 'jszip';
import { saveAs } from 'file-saver';

export async function downloadImagesAsZip(items, zipFileName = 'images.zip') {
  const zip = new JSZip();

  // 将每张图片添加到 ZIP
  const imagePromises = items.map(async (item, index) => {
    const url = item.url;
    try {
      const response = await fetch(url); // 获取图片数据
      const blob = await response.blob(); // 转换为 Blob
      const extension = url.split('.').pop().split('?')[0]; // 根据 URL 获取文件后缀
      zip.file(item.name + '.' + extension, blob); // 将图片添加到 ZIP
    } catch (error) {
      console.error(`Error fetching image at ${url}:`, error);
    }
  });
  // 等待所有图片处理完成
  await Promise.all(imagePromises);

  // 生成 ZIP 文件并触发下载
  zip.generateAsync({ type: 'blob' }).then(zipBlob => {
    saveAs(zipBlob, zipFileName); // 使用 FileSaver.js 下载
  });
}
