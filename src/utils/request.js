/**
 * 请求接口
 * <AUTHOR>
 */
// import { Message } from 'view-design';
import axios from 'axios';
import global_config from '@/config';
import S from '@/utils/util';
import moment from 'moment';
import { getCompanyid, getUid, getUlid, getAssetAppKey, isLogin, prolongCookie } from '@/utils/runtime';
import store from '@/store';

// 创建 axios 实例
const instance = axios.create({
  baseURL: global_config.ApiDomain,
  timeout: 10000,
  headers: { 'content-type': 'application/x-www-form-urlencoded' }
});

function changeParams(config, params) {
  Object.keys(params).forEach(item => {
    if (typeof params[item] == 'object') {
      params[item] = JSON.stringify(params[item]);
    }
    if (typeof params[item] == 'undefined') {
      params[item] = '';
    }
  });
  config.params = params;
  config.params['ifsign'] = S.makeApiSign(config.params);
  config.data = undefined;
}

function getSignKeyType(config) {
  if (isMTRequest(config.url)) {
    return 'assets';
  }
  return 'default';
}

function isMTRequest(url) {
  return url.startsWith('/asset-center');
}

function proxyRequest(config) {
  if (isMTRequest(config.url)) {
    config.baseURL = global_config.AssetsApiDomain;
    config.headers['X-Env'] = JSON.stringify({ app_key: getAssetAppKey() });
    config.headers['content-type'] = 'application/json;charset=UTF-8';
    getUlid() && (config.headers['X-Device-Id'] = getUlid());
  }
}

// 请求拦截器
instance.interceptors.request.use(
  config => {
    // console.log(config,"请求拦截器");
    if (isLogin()) {
      const preHours = localStorage.getItem('is_jj') ? 1 / 12 : 24;
      prolongCookie(preHours, preHours);
    }

    // 设置请求签名参数
    // 并重新设置请求的参数
    const isGoReq = isMTRequest(config.url);
    proxyRequest(config);
    if (config.method == 'get') {
      let params = config.params || config.data || {};
      if (!isGoReq) {
        changeParams(config, params);
      } else {
        params.ts = moment().unix();
        config.params = {
          ...params,
          ifsign: S.makeApiSign(params, config, getSignKeyType(config))
        };
        config.data = undefined;
      }
    } else {
      if (config.method === 'post') {
        store.commit('app/CHANGE_FRESH_STATUS', true);
      }
      //将数组和对象类型的数据转换为JSON字符串
      if (!isGoReq) {
        let data = config.data || config.params || {};
        Object.keys(data).forEach(item => {
          if (typeof data[item] == 'object') {
            data[item] = JSON.stringify(data[item]);
          }
          if (typeof data[item] == 'undefined') {
            data[item] = '';
          }
        });
        data['ifsign'] = S.makeApiSign(data, config, getSignKeyType(config));
        const params = new URLSearchParams();
        for (let k in data) params.append(k, data[k]);
        config.data = params;
        config.params = undefined;
      } else {
        config.data.ts = moment().unix();
        let data = config.data || config.params || {};
        config.params = {
          ifsign: S.makeApiSign(data, config, getSignKeyType(config))
        };
      }
    }

    // 环境参数
    let spm = '';
    let uid = getUid();
    let opc_id = getCompanyid();
    let evnParams = {
      timezone: 8,
      resolution: document.body.clientWidth + '*' + document.body.clientHeight,
      channel: 'h5',
      os: 'h5',
      device_id: '',
      uidentity: uid,
      opc_id,
      spm: spm,
      app_version: global_config.codeVersion
    };
    config.headers['IFENV'] = JSON.stringify(evnParams);
    return config;
  },
  error => {
    return Promise.reject(error);
  }
);

// 响应拦截器
instance.interceptors.response.use(
  response => {
    // console.log(response,"响应拦截器");
    const res = response.data;

    if (res.errcode != 0 && res.code != 0) {
      // TODO 登录超时
      // Message.error(res.errmsg)
      return Promise.reject(res || 'Error');
      // return res
    }

    let last_version = response.headers['x-version'];
    if (!S.isServeRun && last_version != undefined && last_version != '') {
      store.dispatch('version/checkVersion', last_version).then();
    }

    return res.data;
  },
  error => {
    return Promise.reject({
      errmsg: String(error)
    });
  }
);

export default instance;
