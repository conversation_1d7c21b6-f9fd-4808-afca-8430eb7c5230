import Cookies from 'js-cookie';
import S from '@/utils/util';
import { debounce } from 'lodash';
const LoginCookieName = 'BACKEND_PASSPORT_V001';

export const isLogin = () => {
  let uid = getUid();
  return uid > 0;
};

export const getUid = () => {
  let user = getUser();
  return Number(user.uid) || 0;
};

export const getOpcName = () => {
  let user = getUser();
  return user.opc_name;
};

export const getCpName = () => {
  let user = getUser();
  return user.cp_name;
};

export const getUname = () => {
  let user = getUser();
  return user.name;
};

export const getCompanyid = () => {
  let user = getUser();
  return user.opc_id || '';
};

export const getUser = () => {
  return getLoginInfo();
};

export const getLoginInfo = () => {
  let info = Cookies.get(S.generateStorageKey(LoginCookieName)) || '{}';
  return JSON.parse(info);
};

export const writeLoginCookie = info => {
  console.log(new Date(info.expires), 'S.generateStorageKey(LoginCookieName)');
  Cookies.set(S.generateStorageKey(LoginCookieName), JSON.stringify(info), {
    expires: new Date(info.expires) || '0'
  });
  return true;
};

export const logout = () => {
  Cookies.remove(S.generateStorageKey(LoginCookieName));
  return true;
};

export const getEnv = () => {
  return process.env.VUE_APP_NODE_ENV;
};

export const getUlid = () => {
  return Cookies.get('_sj_ulid') || '';
};
// 是否是直营诊所
export const isDirectClinic = () => {
  return getUser().is_direct === '1';
};
export const getAssetAppKey = () => {
  return getLoginInfo().asset_app_key;
};

/**
 * @method prolongCookie
 * @param {number} timeDuration - 延长的时间  单位：分钟
 * @param {number} minimumPeriod - 有效期低于此时间才延长  单位：小时
 * @description: 延长 cookie 的有效期
 * @author: yangyi
 * @date: 2023/12/23
 */
export const prolongCookie = debounce(
  function (timeDuration = 24, minimumPeriodHours = 24) {
    return new Promise((resolve, reject) => {
      if (!isLogin()) {
        resolve(false);
        return;
      }
      try {
        let info = getLoginInfo();
        // 获取过期时间的时间戳
        let time_stamp = info.time_stamp;
        // 获取当前时间戳
        let current_time_stamp = Math.floor(new Date().getTime() / 1000);
        // 获取剩余时间
        let surplus_time = Number(time_stamp) - Number(current_time_stamp);
        let effective_section_time = minimumPeriodHours * 60 * 60;
        let add_time_stamp = timeDuration * 60 * 60;
        if (0 < surplus_time && surplus_time <= effective_section_time) {
          // 说明有效时间小于两个小时了,此时延长cookie有效期
          info.time_stamp = info.time_stamp + add_time_stamp;
          const newExpiresTime = new Date(new Date(time_stamp * 1000).getTime() + add_time_stamp * 1000);
          info.expires = newExpiresTime;
          writeLoginCookie(info);
          resolve(true);
        }
      } catch (e) {
        reject(false);
      }
    });
  },
  1000,
  {
    leading: true,
    trailing: true
  }
);
// 获取理疗师/理疗师名称（榕树堂展示理疗师，其他展示理疗师） todo 本次移植仅处理榕树堂诊所
export const getPhysioName = () => {
  // let user = getUser();
  // return user.is_rst === '1' ? '技师' : '理疗师';
  return '理疗师';
};
// todo 本次移植仅处理榕树堂诊所
export const isRstClinic = () => {
  // let user = getUser();
  // return user.is_rst === '1';
  return true;
};
