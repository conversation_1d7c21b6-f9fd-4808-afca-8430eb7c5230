<template>
  <div class="f_order-wrapper">
    <Form inline @submit.native.prevent @keyup.enter.native="onSearch">
      <Row>
        <FormItem>
          <Input v-model="queryFormData.biz_code" placeholder="请输入关联订单号" clearable />
        </FormItem>

        <FormItem>
          <surning-search
            option_code="ent_c_code"
            localStorageListName="entList"
            :p_ent_type="queryFormData.ent_type"
            routerKey="ent_id"
            searchKeyword="nameId"
            :extra-params="{
              fill_opc: 1
            }"
            :showType="true"
            :typeList="typeList"
            :showOutside="true"
            ref="nursing-search"
            v-model="queryFormData.ent_c_code"
            @getType="type => (queryFormData.ent_type = type)"
            @getOptionId="id => (queryFormData.ent_id = id)"
            placeholder="主体名称/主体ID/编号"
          ></surning-search>
        </FormItem>

        <FormItem>
          <payment-license-search
            style="width: 250px"
            :p_ent_type="queryFormData.organ_ent_type"
            routerKey="organ_ent_id"
            :default_params="{
              fill_opc: 1
            }"
            :showType="true"
            :typeList="typeList"
            :showOutside="true"
            ref="license-search"
            v-model="queryFormData.organ_ent_id"
            @getOrganEntType="val => (queryFormData.organ_ent_type = val)"
            placeholder="请输入营业执照名称"
          >
          </payment-license-search>
        </FormItem>

        <FormItem>
          <clinic-type-search
            v-model="queryFormData.clinic_type"
            :clinic_direct_type="queryFormData.clinic_direct_type"
            :directTypeDesc="directTypeDesc"
            :typeDesc="clinicTypeDesc"
            @directChange="directChange"
          ></clinic-type-search>
        </FormItem>

        <FormItem>
          <Select v-model="queryFormData.custom_type" placeholder="请选择个人客户类型" clearable>
            <Option v-for="item in customTypeDesc" :value="item.id" :key="item.value">{{ item.desc }}</Option>
          </Select>
        </FormItem>

        <FormItem>
          <Select v-model="queryFormData.item_type" placeholder="请选择业务类型" clearable>
            <Option v-for="item in itemTypeDesc" :key="item.id" :value="item.id">{{ item.desc }}</Option>
          </Select>
        </FormItem>

        <FormItem>
          <Select v-model="queryFormData.pur_type" placeholder="请选择采购单类型" clearable>
            <Option v-for="item in purTypeDesc" :key="item.id" :value="item.id">{{ item.desc }}</Option>
          </Select>
        </FormItem>

        <FormItem>
          <Select v-model="queryFormData.action_type" placeholder="请选择余额变动类型" clearable>
            <Option v-for="item in actionTypeDesc" :key="item.id" :value="item.id">{{ item.list_desc }}</Option>
          </Select>
        </FormItem>

        <FormItem>
          <InputNumber
            style="width: 160px"
            :precision="2"
            :active-change="false"
            v-model="queryFormData.min_amount"
            placeholder="请输入余额变动最小金额"
          ></InputNumber>
          -
          <InputNumber
            style="width: 160px"
            :precision="2"
            :active-change="false"
            v-model="queryFormData.max_amount"
            placeholder="请输入余额变动最大金额"
          ></InputNumber>
        </FormItem>

        <FormItem>
          <DatePicker
            type="daterange"
            clearable
            :options="disabledTime"
            format="yyyy-MM-dd"
            placeholder="请选择余额变化时间"
            v-model="txTimeRange"
            @on-change="times => handleTimeChange(times, 'tx_st', 'tx_et')"
          ></DatePicker>
        </FormItem>

        <FormItem>
          <DatePicker
            type="daterange"
            clearable
            :options="disabledTime"
            format="yyyy-MM-dd"
            placeholder="请选择订单创建时间"
            v-model="createTimeRange"
            @on-change="times => handleTimeChange(times, 'biz_create_st', 'biz_create_et')"
          ></DatePicker>
        </FormItem>

        <FormItem>
          <other-surning-search
            localStorageListName="sideEntList"
            :p_ent_type="queryFormData.side_ent_type"
            routerKey="side_ent_id"
            searchKeyword="nameId"
            :default_params="default_params"
            :showType="true"
            ref="other-search"
            v-model="queryFormData.side_ent_id"
            @getType="type => (queryFormData.side_ent_type = type)"
            placeholder="对方主体名称/主体ID"
          ></other-surning-search>
        </FormItem>

        <FormItem style="text-align: left">
          <Button type="primary" class="mr10" @click="onSearch">筛选</Button>
          <Button type="default" class="mr10" @click="onResetSearch">重置</Button>
          <Button :loading="exportLoading" type="default" @click="exportExcel">导出</Button>
        </FormItem>
      </Row>
    </Form>
    <div class="table-wrapper">
      <Table :loading="tableLoading" :columns="tableCols" :data="list" :height="$store.state.app.clientHeight - 290">
        <template slot-scope="{ row }" slot="biz_code">
          <k-link
            v-if="row.biz_url_type && isHasJumpPage(router_path_menu[row.biz_url_type]?.path) && row.biz_code"
            :to="{
              path: router_path_menu[row.biz_url_type]?.path,
              query: {
                [router_path_menu[row.biz_url_type]?.key]: row[router_path_menu[row.biz_url_type]?.infoKey]
              }
            }"
            target="_blank"
            >{{ row.biz_code || '-' }}
          </k-link>
          <div v-else>{{ row.biz_code || '-' }}</div>
        </template>

        <template slot-scope="{ row }" slot="ent_name">
          <div>{{ row.ent_name || '-' }}</div>
        </template>

        <template slot-scope="{ row }" slot="ent_type_desc">
          <div>{{ row.ent_type_desc || '-' }}</div>
          <div v-if="row.ent_type === 'CLI' && row.ent_clinic_type">{{ row.ent_clinic_type || '-' }}</div>
        </template>

        <template slot-scope="{ row }" slot="custom_type_desc">
          <span v-if="row.custom_type_desc?.length">{{ row.custom_type_desc.join('、') || '-' }}</span>
          <span v-else>-</span>
        </template>

        <template slot-scope="{ row }" slot="ent_organization_name">
          <div>{{ row.ent_organization_name || '-' }}</div>
        </template>

        <template slot-scope="{ row }" slot="partner_name">
          {{ row.partner_name || '-' }}
        </template>

        <template slot-scope="{ row }" slot="pur_type_desc">
          <div>{{ row.pur_type_desc || '-' }}</div>
        </template>

        <template slot-scope="{ row }" slot="company_name">
          <div>{{ row.company_name || '-' }}</div>
        </template>

        <template slot-scope="{ row }" slot="tx_time">
          <div>{{ row.tx_time | date_format }}</div>
        </template>

        <template slot-scope="{ row }" slot="biz_create_time">
          <div>{{ row.biz_create_time | date_format }}</div>
        </template>

        <template slot-scope="{ row }" slot="side_ent_name">
          <div>{{ row.side_ent_name || '-' }}</div>
        </template>
        <template slot-scope="{ row }" slot="side_ent_id">
          <div>{{ row.side_ent_id || '-' }}</div>
        </template>
        <template slot-scope="{ row }" slot="side_ent_type_desc">
          <div>{{ row.side_ent_type_desc || '-' }}</div>
        </template>
      </Table>
      <div class="block_20"></div>
      <KPage
        :total="total"
        :page-size="+queryFormData.pageSize"
        :current="+queryFormData.page"
        @on-change="handleCurrentChange"
        @on-page-size-change="handleSizeChange"
        style="text-align: center"
      />
    </div>
  </div>
</template>

<script>
// import downloadCenter from '@/mixins/downloadCenter';
import downloadExcel from '@/mixins/downloadExcel';

import S from 'utils/util';
import search from '@/mixins/search';
import surningSearch from '@/components/surning-search/surning-search';
import otherSurningSearch from '@/components/other-surning-search/other-surning-search';
import clinicTypeSearch from '@/components/remote-search/clinic-type-search.vue';
import paymentLicenseSearch from '@/components/remote-search/payment-license-search.vue';
import renderHeader from '@/mixins/renderHeader';
import { subJect_tip_content } from '../balance/data';

const init_query_form_data = {
  page: 1,
  pageSize: 20,
  biz_code: '',
  custom_type: '', // 个人客户类型
  cp_id: '', // 所属城市合伙人
  ent_type: '', // 主体类型
  ent_id: '', // 主体id
  ent_c_code: '', // 主体id/编号
  organ_ent_type: '', // 营业执照类型
  organ_ent_id: '', // 营业执照id
  company_id: '',
  clinic_type: '', // 诊所类型
  clinic_direct_type: '', // 诊所子类型
  item_type: '', // 业务类型
  pur_type: '', // 采购单类型
  action_type: '', // 余额变动类型
  min_amount: null,
  max_amount: null,
  tx_st: '',
  tx_et: '',
  biz_create_st: '',
  biz_create_et: '',
  side_ent_type: '', // 对方主体类型
  side_ent_id: '', // 对方主体id
  r: ''
};
export default {
  name: 'list',
  mixins: [search, renderHeader, downloadExcel],
  components: { surningSearch, otherSurningSearch, clinicTypeSearch, paymentLicenseSearch },
  data() {
    return {
      downloadApiName: 'getAWExportTxList',
      apiName: 'getAWTxList', // 获取明细列表
      queryFormData: { ...init_query_form_data },
      txTimeRange: [],
      createTimeRange: [],
      disabledTime: {
        disabledDate(date) {
          return date && date.valueOf() > Date.now();
        }
      },
      // 付款方默认参数
      default_params: {
        fill_cp: 1,
        fill_com: 1,
        fill_person_c: 1,
        fill_outside: 1,
        fill_opc: 1
      },
      typeList: [
        { label: '诊所', value: 'CLI' },
        { label: '直营运营中心', value: 'OPC' },
        { label: '个人客户', value: 'PERSON_C' },
        { label: '外部公司', value: 'OUTSIDE' }
      ],
      router_path_menu: {
        RECHARGE: {
          key: 'id',
          infoKey: 'biz_id',
          desc: '余额充值',
          path: '/finance/recharge/detail'
        },
        RECHARGE_REF: {
          key: 'id',
          infoKey: 'biz_id',
          desc: '余额退款',
          path: '/finance/refund/detail'
        },
        JOININ_OPENING_ORDER: {
          key: 'order_code',
          infoKey: 'biz_code',
          desc: '首次产品包',
          path: '/order/product/detail'
        },
        JOININ_OPENING_ORDER_REF: {
          key: 'id',
          infoKey: 'biz_id',
          desc: '开业退款单',
          path: '/order/refund/detail'
        },
        JOININ_PURCHASE_ORDER: {
          key: 'code',
          infoKey: 'biz_code',
          desc: '开业采购单',
          path: '/order/purchase/detail'
        },
        JOININ_PURCHASE_ORDER_REF: {
          key: 'apply_code',
          infoKey: 'biz_code',
          desc: '采购售后（只有线下）',
          path: '/order/after-sale/offline-detail'
        }
      },

      tableCols: [
        { title: '关联订单号', slot: 'biz_code', align: 'center', width: 100 },
        { title: '主体ID/编号', key: 'ent_code', align: 'center', width: 100 },
        { title: '主体名称', slot: 'ent_name', align: 'center', width: 100 },
        { title: '主体类型', slot: 'ent_type_desc', align: 'center', width: 110 },
        { title: '个人客户类型', slot: 'custom_type_desc', align: 'center', width: 110 },
        { title: '营业执照名称', slot: 'ent_organization_name', align: 'center', width: 100 },
        { title: '业务类型', key: 'item_type_desc', align: 'center', width: 100 },
        { title: '采购单类型', slot: 'pur_type_desc', align: 'center', width: 100 },
        { title: '余额变动类型', key: 'action_type_desc', align: 'center', width: 100 },
        { title: '余额变动（元）', key: 'amount', align: 'center', width: 100 },
        { title: '预付款可用余额（元）', key: 'after_balance', align: 'center', width: 140 },
        { title: '已冻结金额（元）', key: 'after_freeze', align: 'center', width: 120 },
        { title: '余额变化时间', slot: 'tx_time', align: 'center', minWidth: 130 },
        { title: '订单创建时间', slot: 'biz_create_time', align: 'center', minWidth: 130 },
        {
          title: '对方主体名称',
          slot: 'side_ent_name',
          align: 'center',
          width: 120,
          renderHeader: (h, params) =>
            this._renderHeader(h, params, subJect_tip_content, 500, 'custom-balance-tb-tooltip')
        },
        { title: '对方主体ID', slot: 'side_ent_id', align: 'center', width: 100 },
        { title: '对方主体类型', slot: 'side_ent_type_desc', align: 'center', width: 100 }
      ],
      clinicTypeDesc: [],
      directTypeDesc: [],
      itemTypeDesc: [],
      purTypeDesc: [],
      actionTypeDesc: [],
      customTypeDesc: [],
      exportLoading: false
    };
  },
  computed: {
    isHasJumpPage() {
      return path => {
        return S.rootPageCheck(path);
      };
    }
  },
  watch: {},
  created() {
    this.handleQueryMerge(this.$route.query);
    this.getAWOptions();
    this.getClinicOptions();
    this.submitQueryForm(true);
  },
  mounted() {},
  methods: {
    exportExcel() {
      // this.exportLoading = true;
      // let params = {
      //   ...this.queryFormData
      // };
      // this.createDownloadCenterMission('accountTransactionList', params);
      // setTimeout(() => {
      //   this.exportLoading = false;
      // }, 1000);

      this.downloadExcel(this.queryFormData);
    },
    directChange(val) {
      this.$set(this.queryFormData, 'clinic_direct_type', val);
    },

    onResetSearch() {
      this.queryFormData = { ...init_query_form_data };
      this.timeRange = [];
      this.submitQueryForm();
      this.$refs['nursing-search'].clear();
      this.$refs['license-search'].clear();
      this.$refs['other-search'].clear();
    },
    // *api 获取枚举类型
    getAWOptions() {
      this.$api.getAWOptions().then(res => {
        this.itemTypeDesc = S.descToArrHandle(res.itemTypeDesc);
        this.purTypeDesc = S.descToArrHandle(res.purTypeDesc);
        this.actionTypeDesc = S.descToArrHandle(res.actionTypeDesc);
        this.customTypeDesc = S.descToArrHandle(res.customTypeDesc);
      });
    },
    getClinicOptions() {
      this.$api.getClinicOptions().then(res => {
        this.directTypeDesc = S.descToArrHandle(res.directTypeDesc);
        this.clinicTypeDesc = S.descToArrHandle(res.typeDesc);
      });
    },

    handleQueryMerge(query) {
      this.queryFormData = S.merge(init_query_form_data, query);
      this.queryFormData.min_amount = this.queryFormData.min_amount === null ? null : +this.queryFormData.min_amount;
      this.queryFormData.max_amount = this.queryFormData.max_amount === null ? null : +this.queryFormData.max_amount;
    }
  },
  beforeRouteUpdate(to, from, next) {
    this.handleQueryMerge(to.query);
    this.getTimeRange('tx_st', 'tx_et', 'txTimeRange');
    this.getTimeRange('biz_create_st', 'biz_create_et', 'createTimeRange');
    this.loadList();
    next();
  }
};
</script>

<style scoped lang="less">
.custom-balance-tb-tooltip {
  .ivu-tooltip-inner {
    min-width: 500px;
    max-width: 500px;
  }
}
</style>
