<template>
  <Modal
    :value="value"
    width="800px"
    :footer-hide="false"
    :closable="false"
    :mask-closable="false"
    :lock-scroll="true"
    @on-visible-change="changeVisible"
  >
    <div class="content">
      <div class="balance-constitute">
        <Divider plain>
          <div class="flex flex-item-align ">
            <span>当前余额构成</span>
            <Tooltip transfer transfer-class-name="custom-balance-700-tooltip" :content="balance_constitute_tip">
              <Icon type="ios-help-circle" class="ml9 cursor" color="#9FA6C1" size="18" />
            </Tooltip>
          </div>
        </Divider>

        <!-- 个人 -->
        <div class="constitute-box">
          <div
            class="constitute-item"
            v-for="(item, index) in makeup_list"
            :key="index"
            :style="{ width: dynamicRatio }"
          >
            <div class="constitute-item-label">{{ item.name }}</div>
            <div class="constitute-item-value">¥{{ item.balance || '0' }}</div>
          </div>
        </div>
      </div>
    </div>
    <div slot="footer">
      <Button :loading="confirmLoading" type="primary" @click="confirm">确定</Button>
    </div>
  </Modal>
</template>

<script>
import { balance_constitute_tip_content } from '../data';

export default {
  name: 'balanceComposition',
  mixins: [],

  components: {},
  props: {
    value: {
      type: Boolean,
      default: false
    },
    makeup_list: {
      type: Array,
      default: () => []
    },
    detailInfo: {
      type: Object,
      default: () => {}
    }
  },

  data() {
    return {
      confirmLoading: false,
      balance_constitute_tip: balance_constitute_tip_content
    };
  },

  computed: {
    // 余额构成单个盒子的占比
    dynamicRatio() {
      let type = this.detailInfo.ent_type || 'CLI';

      let per = '';
      switch (type) {
        case 'CP':
          per = '33%';
          break;
        case 'CLI':
          break;
        case 'RXJ':
          break;
        case 'COM':
          per = '25%';
          break;
        case 'PERSON_C':
          per = '50%';
          break;
        default:
          break;
      }
      return per;
    }
  },

  watch: {},

  created() {},

  mounted() {},

  destroyed() {},

  methods: {
    /**
     * @description: 弹窗状态检测
     * @params  { Boolean } visible true: 弹窗打开 false:弹窗关闭
     * */
    changeVisible(visible) {
      if (!visible) {
        this.closeModal();
      }
    },

    /**
     * @description: 弹窗数据清除
     * */
    clearData() {
      // todo
    },

    /**
     * @description: 弹窗关闭
     * */
    closeModal() {
      this.clearData();
      this.$emit('input', false);
    },

    /**
     * @description: 确定事件
     * */
    confirm() {
      this.$emit('success');
      this.closeModal();
    }
  }
};
</script>

<style scoped lang="less">
.content {
  .balance-constitute {
    margin-top: 16px;

    .constitute-box {
      display: flex;
      flex-wrap: wrap;

      .constitute-item {
        margin-top: 20px;
        display: flex;
        width: 25%;
        flex-direction: column;

        .constitute-item-label {
          font-weight: 400;
          font-size: 12px;
          color: #888888;
          line-height: 17px;
        }

        .constitute-item-value {
          font-weight: 500;
          font-size: 12px;
          color: #333333;
          line-height: 17px;
          margin-top: 6px;
        }
      }
    }
  }
}

.cursor {
  cursor: pointer;
}
</style>

<style scoped lang="less">
::v-deep .ivu-modal-body {
  max-height: 500px;
  min-height: 100px;
  overflow-y: auto;
}
::v-deep .ivu-modal-header {
  display: none;
}
::v-deep .ivu-modal-footer {
  border-top: none;
}
</style>
