<template>
  <div class="detail-wrapper">
    <div class="top-title-box">
      <div class="title-item">
        主体名称:<span style="margin-left: 6px">{{ detailInfo.ent_name || '-' }}</span>
      </div>
      <div class="title-item ml20">
        主体ID:<span style="margin-left: 6px">{{ detailInfo.ent_code || '-' }}</span>
      </div>
    </div>

    <div class="header mt16">预付款余额</div>

    <div class="money-box-wrapper">
      <div class="money-box-item balance-box">
        <div class="money-title">
          <div class="title">预付款可用余额</div>
          <Tooltip transfer transfer-class-name="custom-balance-tooltip" :content="balance_tip">
            <Icon type="ios-help-circle" class="ml9 cursor" color="#9FA6C1" size="18" />
          </Tooltip>
          <a class="composition-text" @click="compositionVisible = true">查看余额构成</a>
        </div>
        <div class="money">¥{{ detailInfo.balance || '0' }}</div>
      </div>

      <div class="money-box-item freeze-box">
        <div class="money-title">
          <div class="title">已冻结金额</div>
          <Tooltip transfer transfer-class-name="custom-balance-tooltip" :content="freeze_tip">
            <Icon type="ios-help-circle" class="ml9 cursor" color="#9FA6C1" size="18" />
          </Tooltip>
        </div>
        <div class="money">¥{{ detailInfo.freeze_balance || '0' }}</div>
      </div>

      <div class="money-box-item recharge-box" v-if="detailInfo.ent_type === 'CP'">
        <div class="money-title">
          <div class="title">累计预付款充值金额</div>
          <Tooltip transfer transfer-class-name="custom-balance-tooltip" :content="recharge_tip">
            <Icon type="ios-help-circle" class="ml9 cursor" color="#9FA6C1" size="18" />
          </Tooltip>
        </div>
        <div class="money">¥{{ detailInfo.total_rech_balance || '0' }}</div>
      </div>
    </div>

    <div class="header mt16">交易明细</div>

    <Form class="mt20" inline @submit.native.prevent @keyup.enter.native="onSearch">
      <Row>
        <FormItem>
          <Input v-model="queryFormData.biz_code" placeholder="请输入关联单号" clearable />
        </FormItem>

        <FormItem>
          <Select v-model="queryFormData.item_type" placeholder="请选择业务类型" clearable>
            <Option v-for="item in itemTypeDesc" :key="item.id" :value="item.id">{{ item.desc }}</Option>
          </Select>
        </FormItem>

        <FormItem>
          <Select v-model="queryFormData.pur_type" placeholder="请选择采购单类型" clearable>
            <Option v-for="item in purTypeDesc" :key="item.id" :value="item.id">{{ item.desc }}</Option>
          </Select>
        </FormItem>

        <FormItem>
          <Select v-model="queryFormData.action_type" placeholder="请选择余额变动类型" clearable>
            <Option v-for="item in actionTypeDesc" :key="item.id" :value="item.id">{{ item.list_desc }}</Option>
          </Select>
        </FormItem>

        <FormItem>
          <DatePicker
            type="daterange"
            clearable
            :options="disabledTime"
            format="yyyy-MM-dd"
            placeholder="请选择余额变化时间"
            v-model="txTimeRange"
            @on-change="times => handleTimeChange(times, 'tx_st', 'tx_et')"
          ></DatePicker>
        </FormItem>
      </Row>

      <Row>
        <FormItem>
          <DatePicker
            type="daterange"
            clearable
            :options="disabledTime"
            format="yyyy-MM-dd"
            placeholder="请选择订单创建时间"
            v-model="createTimeRange"
            @on-change="times => handleTimeChange(times, 'biz_create_st', 'biz_create_et')"
          ></DatePicker>
        </FormItem>

        <FormItem style="text-align: left">
          <Button type="primary" class="mr10" @click="onSearch">筛选</Button>
          <Button type="default" class="mr10" @click="onResetSearch">重置</Button>
          <Button
            :loading="downloadLoading"
            type="default"
            @click="downloadExcel({ ...queryFormData, id: $route.query.id })"
            >导出</Button
          >
        </FormItem>
      </Row>
    </Form>
    <div>
      <Table :loading="tableLoading" :columns="tableCols" :data="list" height="600">
        <template slot-scope="{ row }" slot="biz_code">
          <k-link
            v-if="row.biz_url_type && isHasJumpPage(router_path_menu[row.biz_url_type]?.path) && row.biz_code"
            :to="{
              path: router_path_menu[row.biz_url_type]?.path,
              query: {
                [router_path_menu[row.biz_url_type]?.key]: row[router_path_menu[row.biz_url_type]?.infoKey]
              }
            }"
            target="_blank"
            >{{ row.biz_code }}
          </k-link>
          <div v-else>{{ row.biz_code || '-' }}</div>
        </template>
        <template slot-scope="{ row }" slot="pur_type_desc">
          {{ row.pur_type_desc || '-' }}
        </template>
        <template slot-scope="{ row }" slot="tx_time">
          {{ row.tx_time | date_format }}
        </template>
        <template slot="biz_create_time" slot-scope="{ row }">
          {{ row.biz_create_time | date_format }}
        </template>

        <template slot="side_ent_name" slot-scope="{ row }">
          {{ row.side_ent_name || '-' }}
        </template>
        <template slot="side_ent_id" slot-scope="{ row }">
          {{ row.side_ent_id || '-' }}
        </template>
        <template slot="side_ent_type_desc" slot-scope="{ row }">
          {{ row.side_ent_type_desc || '-' }}
        </template>
      </Table>
      <div class="block_20"></div>
      <KPage
        :current="+queryFormData.page"
        :page-size="+queryFormData.pageSize"
        :total="+total"
        @on-change="onPageChange"
        @on-page-size-change="handleSizeChange"
        style="text-align: center"
      />
    </div>
    <!-- 余额构成 -->
    <balance-composition
      v-model="compositionVisible"
      :makeup_list="makeup_list"
      :detailInfo="detailInfo"
    ></balance-composition>
  </div>
</template>

<script>
import S from 'utils/util';
import downloadExcel from '@/mixins/downloadExcel';
import renderHeader from '@/mixins/renderHeader';
import balanceComposition from './components/balanceComposition.vue';
import { balance_tip_content, freeze_tip_content, recharge_tip_content, subJect_tip_content } from './data';

const init_query_form_data = {
  page: 1,
  pageSize: 20,
  biz_code: '', // 单号
  item_type: '', // 业务类型
  pur_type: '', // 采购单类型
  action_type: '', // 余额变动类型
  tx_st: '',
  tx_et: '',
  biz_create_st: '',
  biz_create_et: ''
};
export default {
  name: 'detail',
  components: { balanceComposition },
  mixins: [renderHeader, downloadExcel],
  data() {
    return {
      downloadApiName: 'getAWExportDetailTxList',
      balance_tip: balance_tip_content,
      freeze_tip: freeze_tip_content,
      recharge_tip: recharge_tip_content,
      queryFormData: { ...init_query_form_data },
      disabledTime: {
        disabledDate(date) {
          return date && date.valueOf() > Date.now();
        }
      },
      router_path_menu: {
        RECHARGE: {
          key: 'id',
          infoKey: 'biz_id',
          desc: '余额充值',
          path: '/finance/recharge/detail'
        },
        RECHARGE_REF: {
          key: 'id',
          infoKey: 'biz_id',
          desc: '余额退款',
          path: '/finance/refund/detail'
        },
        JOININ_OPENING_ORDER: {
          key: 'order_code',
          infoKey: 'biz_code',
          desc: '首次产品包',
          path: '/purchase/product/detail'
        },
        JOININ_OPENING_ORDER_REF: {
          key: 'id',
          infoKey: 'biz_id',
          desc: '开业退款单',
          path: '/purchase/refund/detail'
        },
        JOININ_PURCHASE_ORDER: {
          key: 'pur_code',
          infoKey: 'biz_code',
          desc: '开业采购单',
          path: '/purchase/order/detail'
        },
        JOININ_PURCHASE_ORDER_REF: {
          key: 'apply_code',
          infoKey: 'biz_code',
          desc: '采购售后（只有线下）',
          path: '/purchase/after-sale/offline-detail'
        }
      },
      detailInfo: {},
      makeup_list: [], // 余额构成, 顺序由后端控制
      itemTypeDesc: [],
      purTypeDesc: [],
      actionTypeDesc: [],
      txTimeRange: [],
      createTimeRange: [],
      tableCols: [
        { title: '关联单号', slot: 'biz_code', align: 'center', minWidth: 60 },
        { title: '业务类型', key: 'item_type_desc', align: 'center', minWidth: 40 },
        { title: '采购单类型', slot: 'pur_type_desc', align: 'center', minWidth: 60 },
        { title: '余额变动类型', key: 'action_type_desc', align: 'center', minWidth: 60 },
        { title: '余额变动（元）', key: 'amount', align: 'center', minWidth: 70 },
        { title: '预付款可用余额（元）', key: 'after_balance', align: 'center', minWidth: 110 },
        { title: '已冻结金额（元）', key: 'after_freeze', align: 'center', minWidth: 80 },
        { title: '余额变化时间', slot: 'tx_time', align: 'center', minWidth: 60 },
        { title: '订单创建时间', slot: 'biz_create_time', align: 'center', minWidth: 60 },
        {
          title: '对方主体名称',
          slot: 'side_ent_name',
          align: 'center',
          minWidth: 90,
          renderHeader: (h, params) =>
            this._renderHeader(h, params, subJect_tip_content, 500, 'custom-balance-tb-tooltip')
        },
        { title: '对方主体ID', slot: 'side_ent_id', align: 'center', minWidth: 50 },
        { title: '对方主体类型', slot: 'side_ent_type_desc', align: 'center', minWidth: 56 }
      ],
      tableLoading: false,
      list: [],
      total: 0,
      num_info: {},
      compositionVisible: false // 余额构成
    };
  },
  computed: {
    isHasJumpPage() {
      return path => {
        return S.rootPageCheck(path);
      };
    }
  },
  watch: {},
  created() {
    this.getAWDetail();
    this.getAWOptions();

    this.getClinicOptions();
    this.onSearch();
  },
  mounted() {},
  methods: {
    onResetSearch() {
      this.queryFormData = { ...init_query_form_data };
      this.txTimeRange = [];
      this.createTimeRange = [];
      this.onSearch();
    },
    getClinicOptions() {
      this.$api.getClinicOptions().then(res => {
        this.directTypeDesc = S.descToArrHandle(res.directTypeDesc);
        this.clinicTypeDesc = S.descToArrHandle(res.typeDesc);
      });
    },

    handleSizeChange(pageSize) {
      this.queryFormData.pageSize = pageSize;
      if (this.queryFormData.page == 1) {
        this.onSearch();
      }
    },
    onPageChange(page) {
      this.queryFormData.page = page;
      this.onSearch();
    },

    handleTimeChange(times, startTime = 'st', endTime = 'et') {
      if (times) {
        this.queryFormData[startTime] = times[0];
        this.queryFormData[endTime] = times[1];
      } else {
        this.queryFormData[startTime] = '';
        this.queryFormData[endTime] = '';
      }
    },

    onSearch() {
      this.tableLoading = true;
      let params = {
        id: this.$route.query.id,
        ...this.queryFormData
      };
      this.$api
        .getAWTxList(params)
        .then(res => {
          this.list = res.list;
          this.total = res.total;
        })
        .finally(() => (this.tableLoading = false));
    },

    getAWDetail() {
      let params = {
        id: this.$route.query.id
      };
      this.$api.getAWDetail(params).then(
        res => {
          this.detailInfo = res.detail;
          this.makeup_list = res.detail.makeup_list || [];
        },
        err => this.$Message.error(err.errmsg)
      );
    },

    // *api 获取枚举类型
    getAWOptions() {
      this.$api.getAWOptions().then(res => {
        this.itemTypeDesc = S.descToArrHandle(res.itemTypeDesc);
        this.purTypeDesc = S.descToArrHandle(res.purTypeDesc);
        this.actionTypeDesc = S.descToArrHandle(res.actionTypeDesc);
      });
    }
  }
};
</script>

<style lang="less" scoped>
.detail-wrapper {
  .header {
    height: 41px;
    background: #f2f2f2;
    padding: 12px;
    font-weight: 500;
    font-size: 12px;
    color: #333333;
    line-height: 17px;
  }

  .top-title-box {
    display: flex;
    align-items: center;

    .title-item {
      font-weight: 500;
      font-size: 16px;
      color: #333333;
      line-height: 22px;
    }
  }

  .money-box-wrapper {
    margin-top: 20px;
    display: flex;

    .money-box-item {
      background: #f7f8fa;
      border-radius: 4px;
      padding: 16px 24px;
      flex: 1;
      margin-right: 14px;

      &:last-child {
        margin-right: 0px;
      }
    }

    .money-title {
      display: flex;
      align-items: center;
      font-weight: 400;
      font-size: 16px;
      color: #767c96;
      line-height: 22px;

      .composition-text {
        font-size: 12px;
        margin-left: 10px;
      }
    }

    .money {
      margin-top: 25px;
      font-weight: normal;
      font-size: 38px;
      color: #757c98;
      line-height: 42px;
    }

    .balance-box {
      flex: 2;
    }
  }
}

.ml9 {
  margin-left: 9px;
}

.ml20 {
  margin-left: 50px;
}

.mt16 {
  margin-top: 16px;
}

.mt20 {
  margin-top: 20px;
}

.cursor {
  cursor: pointer;
}

::v-deep .ivu-divider-with-text-center {
  font-weight: 500;
  font-size: 14px;
  color: #333333;
  line-height: 20px;
  margin: 0px;
}
</style>

<style lang="less">
.custom-balance-tooltip {
  .ivu-tooltip-inner {
    min-width: 300px;
    max-width: 300px;
    white-space: pre-wrap;
  }
}
.custom-balance-tb-tooltip {
  .ivu-tooltip-inner {
    min-width: 500px;
    max-width: 500px;
    white-space: pre-wrap;
  }
}
.custom-balance-700-tooltip {
  .ivu-tooltip-inner {
    min-width: 700px;
    max-width: 700px;
    max-height: 400px;
    white-space: pre-wrap;
  }
}
</style>
