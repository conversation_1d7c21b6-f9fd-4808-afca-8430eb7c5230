<template>
  <div class="f_order-wrapper">
    <Form inline @submit.native.prevent @keyup.enter.native="onSearch">
      <Row>
        <FormItem>
          <surning-search
            option_code="ent_c_code"
            :p_ent_type="queryFormData.ent_type"
            :showAll="false"
            routerKey="ent_id"
            searchKeyword="nameId"
            :extra-params="{
              fill_opc: 1
            }"
            :showType="true"
            :typeList="typeList"
            :showOutside="true"
            ref="nursing-search"
            v-model="queryFormData.ent_c_code"
            @getType="type => (queryFormData.ent_type = type)"
            @getOptionId="id => (queryFormData.ent_id = id)"
            placeholder="主体名称/主体ID/编号"
          ></surning-search>
        </FormItem>

        <FormItem>
          <payment-license-search
            :p_ent_type="queryFormData.organ_ent_type"
            routerKey="organ_ent_id"
            :default_params="{
              fill_opc: 1
            }"
            :showType="true"
            :typeList="typeList"
            :showOutside="true"
            ref="license-search"
            v-model="queryFormData.organ_ent_id"
            @getOrganEntType="val => (queryFormData.organ_ent_type = val)"
            placeholder="营业执照名称"
          >
          </payment-license-search>
        </FormItem>

        <FormItem>
          <clinic-type-search
            v-model="queryFormData.clinic_type"
            :clinic_direct_type="queryFormData.clinic_direct_type"
            :directTypeDesc="directTypeDesc"
            :typeDesc="clinicTypeDesc"
            @directChange="directChange"
          ></clinic-type-search>
        </FormItem>

        <FormItem>
          <Select v-model="queryFormData.custom_type" placeholder="请选择个人客户类型" clearable>
            <Option v-for="item in customTypeDesc" :value="item.id" :key="item.value">{{ item.desc }}</Option>
          </Select>
        </FormItem>
      </Row>
      <Row>
        <FormItem>
          <InputNumber
            style="width: 140px"
            :precision="2"
            :active-change="false"
            v-model="queryFormData.min_amount"
            placeholder="请输入余额的最小金额"
          ></InputNumber>
          -
          <InputNumber
            style="width: 140px"
            :precision="2"
            :active-change="false"
            v-model="queryFormData.max_amount"
            placeholder="请输入余额的最大金额"
          ></InputNumber>
        </FormItem>

        <FormItem style="text-align: left">
          <Button type="primary" class="mr10" @click="onSearch">筛选</Button>
          <Button type="default" class="mr10" @click="onResetSearch">重置</Button>
          <Button :loading="downloadLoading" type="default" @click="downloadExcel(queryFormData)">导出</Button>
        </FormItem>
      </Row>
    </Form>
    <div class="table-wrapper">
      <Table :loading="tableLoading" :columns="tableCols" :data="list" :height="$store.state.app.clientHeight - 290">
        <template slot-scope="{ row }" slot="ent_name">
          <k-link
            v-if="router_path_menu[row.ent_type] && PageCheck(router_path_menu[row.ent_type]) && row.ent_name"
            :to="{
              path: router_path_menu[row.ent_type],
              query: { id: row.url_id, status: row.ent_type === 'OUTSIDE' ? 'onlyRead' : '' }
            }"
            target="_blank"
          >
            {{ row.ent_name || '-' }}
          </k-link>
          <div v-else>{{ row.ent_name || '-' }}</div>
        </template>
        <template slot="ent_organization_name" slot-scope="{ row }">
          {{ row.ent_organization_name || '-' }}
        </template>
        <template slot="ent_type_desc" slot-scope="{ row }">
          <div>{{ row.ent_type_desc || '-' }}</div>
          <div v-if="row.ent_type === 'CLI' && row.ent_clinic_type">{{ row.ent_clinic_type || '-' }}</div>
        </template>
        <template slot-scope="{ row }" slot="custom_type">
          <div v-if="row.custom_type_desc?.length">{{ row.custom_type_desc.join('、') }}</div>
          <div v-else>-</div>
        </template>
        <template slot="balance" slot-scope="{ row }">
          {{ row.balance || '-' }}
        </template>
        <template slot="freeze_balance" slot-scope="{ row }">
          {{ row.freeze_balance || '-' }}
        </template>
        <template slot="last_tx_time" slot-scope="{ row }">
          {{ row.last_tx_time | date_format }}
        </template>

        <template slot-scope="{ row }" slot="action">
          <a @click="toDetail(row)">余额详情</a>
          <template v-if="row.ent_type === 'COM'">
            <k-link
              v-if="showRechargeButton('/finance/recharge/edit')"
              class="ml-4"
              to="/finance/recharge/edit"
              target="_blank"
              >充值
            </k-link>
          </template>
          <template v-else>
            <k-link
              v-if="showRechargeButton('/finance/recharge/edit')"
              class="ml-4"
              :to="{
                path: '/finance/recharge/edit',
                query: {
                  payer_type: row.ent_type,
                  payer_id: row.ent_id
                }
              }"
              target="_blank"
              >充值
            </k-link>
          </template>

          <template v-if="showRechargeButton('/finance/refund/edit')">
            <k-link
              class="ml-4"
              :to="{
                path: '/finance/refund/edit',
                query: {
                  ent_type: row.ent_type,
                  ent_id: row.ent_id
                }
              }"
              target="_blank"
              >退款
            </k-link>
          </template>
        </template>
      </Table>
      <div class="block_20"></div>
      <KPage
        :total="total"
        :page-size="+queryFormData.pageSize"
        :current="+queryFormData.page"
        @on-change="handleCurrentChange"
        @on-page-size-change="handleSizeChange"
        style="text-align: center"
      />
    </div>
  </div>
</template>

<script>
import downloadExcel from '@/mixins/downloadExcel';
import S from 'utils/util';
import search from '@/mixins/search';
import surningSearch from '@/components/surning-search/surning-search';
import clinicTypeSearch from '@/components/remote-search/clinic-type-search.vue';
import paymentLicenseSearch from '@/components/remote-search/payment-license-search.vue';
import renderHeader from '@/mixins/renderHeader';
// import partnerSearch from '@/components/partner-search/partner-search';
import { balance_tip_content, freeze_tip_content } from './data';
import utils from '@/utils/util';

const init_query_form_data = {
  page: 1,
  pageSize: 20,
  ent_type: '', // 主体类型
  ent_id: '', // 主体id
  ent_c_code: '', // 主体id/编码
  organ_ent_type: '', // 营业执照类型
  organ_ent_id: '', // 营业执照id
  clinic_type: '', // 诊所子类型
  clinic_direct_type: '', // 诊所类型
  cp_id: '',
  custom_type: '',
  min_amount: null,
  max_amount: null,
  r: ''
};
export default {
  name: 'list',
  mixins: [search, renderHeader, downloadExcel],
  components: { surningSearch, clinicTypeSearch, paymentLicenseSearch },
  data() {
    return {
      downloadApiName: 'getAWExportList',
      apiName: 'getAWList', // 获取明细列表
      queryFormData: { ...init_query_form_data },
      disabledTime: {
        disabledDate(date) {
          return date && date.valueOf() > Date.now();
        }
      },
      // 付款方默认参数
      default_params: {
        fill_cp: 1,
        fill_com: 1,
        fill_person_c: 1,
        fill_outside: 1,
        fill_opc: 1
      },

      typeList: [
        { label: '诊所', value: 'CLI' },
        { label: '直营运营中心', value: 'OPC' },
        { label: '个人客户', value: 'PERSON_C' },
        { label: '外部公司', value: 'OUTSIDE' }
      ],
      router_path_menu: {
        CLI: '/organization/clinic/detail',
        RXJ: '/organization/nursing/edit',
        CP: '/organization/partner/detail',
        PERSON_C: '/organization/customer/edit',
        OUTSIDE: '/organization/outside-company/edit'
      },
      tableCols: [
        { title: '主体ID/编号', key: 'ent_code', align: 'center', minWidth: 80 },
        { title: '主体名称', slot: 'ent_name', align: 'center', minWidth: 90 },
        { title: '营业执照名称', slot: 'ent_organization_name', align: 'center', width: 150 },
        { title: '主体类型', slot: 'ent_type_desc', align: 'center', minWidth: 80 },
        { title: '个人客户类型', slot: 'custom_type', align: 'center', minWidth: 90 },
        {
          title: '预付款可用余额（元）',
          slot: 'balance',
          align: 'center',
          minWidth: 160,
          renderHeader: (h, params) => this._renderHeader(h, params, balance_tip_content, 400)
        },
        {
          title: '已冻结金额（元）',
          slot: 'freeze_balance',
          align: 'center',
          minWidth: 140,
          renderHeader: (h, params) => this._renderHeader(h, params, freeze_tip_content, 400)
        },
        { title: '上次余额变化时间', slot: 'last_tx_time', align: 'center', minWidth: 130 },
        { title: '操作', slot: 'action', align: 'center', fixed: 'right', width: 150 }
      ],
      clinicTypeDesc: [],
      directTypeDesc: [],
      customTypeDesc: []
    };
  },
  computed: {
    showRechargeButton() {
      return path => {
        return utils.rootPageCheck(path);
      };
    },
    PageCheck() {
      return path => {
        return S.rootPageCheck(path);
      };
    }
  },
  watch: {},
  created() {
    this.handleQueryMerge(this.$route.query);
    this.getClinicOptions();
    this.getAWOptions();
    this.submitQueryForm(true);
  },
  mounted() {},
  methods: {
    rechargeEvent() {},
    refundEvent() {},
    directChange(val) {
      this.$set(this.queryFormData, 'clinic_direct_type', val);
    },

    onResetSearch() {
      this.queryFormData = { ...init_query_form_data };
      this.submitQueryForm();
      this.$refs['nursing-search'].clear();
      this.$refs['license-search'].clear();
    },
    toDetail(row) {
      this.$router.push({
        path: '/finance/balance/detail',
        query: {
          id: row.id
        }
      });
    },
    getClinicOptions() {
      this.$api.getClinicOptions().then(res => {
        this.directTypeDesc = S.descToArrHandle(res.directTypeDesc);
        this.clinicTypeDesc = S.descToArrHandle(res.typeDesc);
      });
    },
    getAWOptions() {
      this.$api.getAWOptions().then(res => {
        this.customTypeDesc = S.descToArrHandle(res.customTypeDesc);
      });
    },
    handleQueryMerge(query) {
      this.queryFormData = S.merge(init_query_form_data, query);
      this.queryFormData.min_amount = this.queryFormData.min_amount === null ? null : +this.queryFormData.min_amount;
      this.queryFormData.max_amount = this.queryFormData.max_amount === null ? null : +this.queryFormData.max_amount;
    }
  },
  beforeRouteUpdate(to, from, next) {
    this.handleQueryMerge(to.query);
    this.getTimeRange();
    this.loadList();
    next();
  }
};
</script>

<style scoped lang="less"></style>
