<template>
  <div class="Invoice-wrapper">
    <div class="block-header"><span>开票信息审核</span></div>
    <div style="width: 60%">
      <Form :label-width="170" label-colon :model="formData" ref="invoiceForm" :disabled="true">
        <FormItem label="审核状态">
          <div>
            <span>{{ formData.status_desc }}</span>
            <span v-if="formData.status === 'REJECT'" class="red">（驳回原因：{{ formData.reject_reason }}）</span>
          </div>
        </FormItem>
        <FormItem label="开票申请方类型">
          <div>{{ formData.ent_type_desc }}</div>
        </FormItem>
        <FormItem label="开票申请方名称">
          <div>{{ formData.ent_name }}</div>
        </FormItem>
        <FormItem label="开票申请方系统编号/ID">
          <div>{{ detail_info.system_code }}</div>
        </FormItem>
        <FormItem label="发票抬头" prop="organization_code">
          <Input style="width: 100%" v-model="formData.organization_name" placeholder="请输入发票抬头"></Input>
        </FormItem>

        <FormItem label="公司税号" prop="organization_code">
          <Input style="width: 100%" v-model="formData.organization_code" placeholder="请输入公司税号"></Input>
        </FormItem>
        <FormItem label="邮箱" prop="invoice.email">
          <Input style="width: 100%" v-model="formData.invoice.email" placeholder="请输入邮箱"></Input>
        </FormItem>

        <FormItem label="注册地址">
          <div class="flex">
            <div class="address flex-1">
              <el-cascader
                :disabled="true"
                v-model="selectedAddress"
                :options="options"
                clearable
                placeholder="请选择注册地址"
                size="small"
                popper-class="address-com"
                style="width: 100%"
                @change="regionChange"
              >
              </el-cascader>
            </div>
            <div class="flex-1 ml10">
              <Input v-model.trim="formData.invoice.reg_address.detail" placeholder="详细地址"></Input>
            </div>
          </div>
        </FormItem>
        <FormItem label="注册电话">
          <Input style="width: 100%" v-model="formData.invoice.reg_mobile" placeholder="请输入注册电话"></Input>
        </FormItem>
        <FormItem label="开户银行">
          <Input style="width: 100%" v-model="formData.invoice.bank" placeholder="请输入开户银行"></Input>
        </FormItem>
        <FormItem label="银行账号">
          <Input style="width: 100%" v-model="formData.invoice.bank_account" placeholder="请输入银行账号"></Input>
        </FormItem>
      </Form>
    </div>
    <div class="fixed-bottom-wrapper">
      <back-button></back-button>
    </div>
  </div>
</template>

<script>
import { CodeToText, regionData } from '@/utils/chinaMap';
export default {
  name: 'detail',
  mixins: [],

  components: {
  },

  props: {},

  data() {
    return {
      options: regionData,
      selectedAddress: [],
      detail_info: {},
      formData: {
        organization_code: '', //公司税号
        organization_name: '', //公司名称

        invoice: {
          reg_address: {
            //注册地址
            prov: {
              name: '',
              code: '',
            },
            city: {
              name: '',
              code: '',
            },
            county: {
              name: '',
              code: '',
            },
            detail: '',
          },
          reg_mobile: '', //注册电话
          bank: '', //开户银行
          bank_account: '', //银行账号
          email: '', //邮箱
        },
      },
    };
  },

  computed: {},

  watch: {},

  created() {
    this.getBaseInfo();
  },

  mounted() {},

  destroyed() {},

  methods: {
    getBaseInfo() {
      let params = {
        id: this.$route.query.id,
      };
      this.$api.getInvoiceExamineDetail(params).then(res => {
        this.detail_info = res
        this.formData.ent_type_desc = res.ent_type_desc;
        this.formData.ent_name = res.ent_name;
        this.formData.status_desc = res.status_desc;
        this.formData.status = res.status;
        this.formData.reject_reason = res.reject_reason;

        this.formData.invoice.bank = res.invoice.bank;
        this.formData.invoice.bank_account = res.invoice.bank_account;
        this.formData.organization_name = res.organization_name;
        this.formData.organization_code = res.organization_code;
        this.formData.invoice.reg_address = res.invoice.reg_address;
        this.formData.invoice.reg_mobile = res.invoice.reg_mobile;
        if (res.invoice.reg_address.county.code) {
          this.selectedAddress = [
            res.invoice.reg_address.prov.code,
            res.invoice.reg_address.city.code,
            res.invoice.reg_address.county.code,
          ];
        } else {
          this.selectedAddress = [res.invoice.reg_address.prov.code, res.invoice.reg_address.city.code];
        }
        this.formData.invoice.email = res.invoice.email;
      });
    },

    regionChange(address) {
      console.log(address);
      if (address.length) {
        console.log(CodeToText[address[0]]);
        const prov = {
          name: CodeToText[address[0]],
          code: address[0],
        };
        const city = {
          name: CodeToText[address[1]],
          code: address[1],
        };
        const county = {
          name: CodeToText[address[2]],
          code: address[2],
        };
        this.formData.invoice.reg_address.prov = prov;
        this.formData.invoice.reg_address.city = city;
        this.formData.invoice.reg_address.county = county;
      } else {
        this.formData.invoice.reg_address = {
          prov: {
            name: '',
            code: '',
          },
          city: {
            name: '',
            code: '',
          },
          county: {
            name: '',
            code: '',
          },
          detail: '',
        };
      }

      // 手动触发校验
      // this.$refs.formData.validateField('selectedAddress')
    },
  },
};
</script>

<style scoped lang="less">
.ml10 {
  margin-left: 10px;
}
.red {
  color: red;
}
</style>
