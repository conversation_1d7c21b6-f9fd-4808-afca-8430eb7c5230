<template>
  <div class="list-wrapper">
    <Form inline :label-width="0" @submit.native.prevent @keyup.enter.native="onSearch">
      <Row>
        <Col>
          <FormItem>
            <Select filterable placeholder="开票申请方类型" v-model="queryFormData.ent_type" clearable>
              <Option v-for="item in entTypeList" :key="item.id" :value="item.id" :label="item.desc"> </Option>
            </Select>
          </FormItem>
        </Col>

        <Col>
          <FormItem>
            <Input v-model="queryFormData.system_code" placeholder="开票申请方系统编号/ID" clearable></Input>
          </FormItem>
        </Col>

        <Col>
          <FormItem>
            <invoice-applyEnt-search
              ref="applyEnt-search"
              v-model="queryFormData.ent_id"
              :isClearable="true"
              placeholder="开票申请方名称"
            ></invoice-applyEnt-search>
          </FormItem>
        </Col>

        <Col>
          <FormItem>
            <Input v-model="queryFormData.organization_name" placeholder="发票抬头" clearable></Input>
          </FormItem>
        </Col>

        <Col>
          <FormItem>
            <Input v-model="queryFormData.organization_code" placeholder="公司税号" clearable></Input>
          </FormItem>
        </Col>
      </Row>
      <Row>
        <Col>
          <FormItem>
            <DatePicker
              type="daterange"
              placement="bottom-end"
              @on-change="times => handleTimeChange(times)"
              placeholder="请选择提交时间"
              v-model="timeRange"
              clearable
            ></DatePicker>
          </FormItem>
        </Col>

        <Col>
          <FormItem style="text-align: left">
            <Button type="primary" class="mr10" @click="onSearch">筛选</Button>
            <Button type="default" class="mr10" @click="onResetSearch">重置</Button>
          </FormItem>
        </Col>
      </Row>
    </Form>
    <div class="table-wrapper">
      <div class="table-func flex flex-item-between">
        <div class="panel-nav">
          <a
            class="nav"
            :class="{ active: $route.query.status == '' || !$route.query.status }"
            @click.prevent.capture="onStatusChange('')"
          >
            全部
          </a>
          <a
            class="nav"
            v-for="item in tabStatus"
            :key="item.id"
            :class="{ active: $route.query.status == item.id }"
            @click.prevent.capture="onStatusChange(item.id)"
          >
            {{ item.desc }}
            <Tag :color="getTagColor(item.id)">{{ status_count[item.id] }}</Tag>
          </a>
        </div>
      </div>
      <Table :loading="tableLoading" :columns="tableCols" :data="list" :height="$store.state.app.clientHeight - 350">
        <!-- 发票信息 -->
        <template slot-scope="{ row }" slot="invoiceInfo">
          <div>
            <div class="flex table-slot">
              <span class="label">发票抬头:</span>
              <span class="value">{{ row.organization_name || '-' }}</span>
            </div>
            <div class="flex table-slot">
              <span class="label">公司税号:</span>
              <span class="value">{{ row.organization_code || '-' }}</span>
            </div>
            <div class="flex table-slot">
              <span class="label">注册地址:</span>
              <span class="value">{{ (row.invoice && row.invoice.reg_address_text) || '-' }}</span>
            </div>
            <div class="flex table-slot">
              <span class="label">注册电话:</span>
              <span class="value">{{ (row.invoice && row.invoice.reg_mobile) || '-' }}</span>
            </div>
            <div class="flex table-slot">
              <span class="label">开户银行:</span>
              <span class="value">{{ (row.invoice && row.invoice.bank) || '-' }}</span>
            </div>
            <div class="flex table-slot">
              <span class="label">银行账号:</span>
              <span class="value">{{ (row.invoice && row.invoice.bank_account) || '-' }}</span>
            </div>
          </div>
        </template>

        <!-- 提交申请时间 -->
        <template slot-scope="{ row }" slot="apply_time">
          {{ row.apply_time | date_format }}
        </template>

        <!-- 操作 -->
        <template slot-scope="{ row }" slot="action">
          <div>
            <a @click="toDetail(row)">详情</a>
          </div>
        </template>
      </Table>
      <div class="block_20"></div>
      <KPage
        :total="total"
        :page-size="+queryFormData.pageSize"
        :current="+queryFormData.page"
        @on-change="handleCurrentChange"
        @on-page-size-change="handleSizeChange"
        style="text-align: center"
      />
    </div>
  </div>
</template>
<script>
import search from '@/mixins/search';
import S from 'utils/util';
import invoiceApplyEntSearch from '@/components/invoice-applyEnt-search/invoice-applyEnt-search';

const init_query_form_data = {
  page: 1,
  pageSize: 20,
  st: '',
  et: '',
  status: '',
  r: '',
  ent_id: '', // 诊所/榕小家名称/编号
  ent_type: '', // 开票申请方类型
  system_code: '',
  organization_name: '',
  organization_code: ''
};
export default {
  name: 'list',
  mixins: [search],
  components: {
    invoiceApplyEntSearch
  },

  props: {},

  data() {
    return {
      apiName: 'getInvoiceExamineList',
      queryFormData: {
        ...init_query_form_data
      },
      timeRange: [],
      tableCols: [
        { title: '开票申请方类型', key: 'ent_type_desc', align: 'center' },
        { title: '开票申请方系统编号/ID', key: 'system_code', align: 'center', minWidth: 100 },
        { title: '开票申请方名称', key: 'ent_name', align: 'center' },
        { title: '所属直营运营中心', key: 'opc_name', align: 'center' },
        { title: '开票信息', slot: 'invoiceInfo', align: 'center', width: 240 },
        { title: '提交申请时间', slot: 'apply_time', align: 'center' },
        { title: '状态', key: 'status_desc', align: 'center' },
        { title: '操作', slot: 'action', align: 'center' }
      ],
      list: [],
      tabStatus: [], // 状态枚举
      entTypeList: [] // 开票申请方类型
    };
  },

  computed: {
    getTagColor() {
      return type => {
        switch (type) {
          case 'EXAMINE': // 待审核
            return 'warning';
          case 'REJECT': // 已驳回
            return 'error';
          case 'PASSED': // 已通过
            return 'success';
          default:
            return 'default';
        }
      };
    }
  },

  watch: {},

  destroyed() {},
  created() {
    this.queryFormData = S.merge(this.queryFormData, this.$route.query);
    this.submitQueryForm(true);
    // 获取枚举列表
    this.getInvoiceexamineOption();
  },
  mounted() {},
  methods: {
    onStatusChange(status) {
      this.queryFormData.page = 1;
      this.queryFormData.status = status;
      this.submitQueryForm();
    },
    onResetSearch() {
      this.queryFormData = { ...init_query_form_data };
      this.timeRange = [];
      this.submitQueryForm();
      this.$refs['applyEnt-search'].clear();
    },

    toDetail(row) {
      let query = {
        id: row.id
      };
      this.$router.push({
        path: '/finance/invoice-examine/detail',
        query: query
      });
    },

    // api-获取枚举值
    getInvoiceexamineOption() {
      this.$api.getInvoiceExamineOption().then(res => {
        this.tabStatus = S.descToArrHandle(res.statusDesc);
        this.entTypeList = S.descToArrHandle(res.entTypeDesc);
      });
    }
  },
  beforeRouteUpdate(to, from, next) {
    this.queryFormData = S.merge(init_query_form_data, to.query);
    this.getTimeRange();
    this.loadList();
    next();
  }
};
</script>

<style scoped lang="less">
.ml10 {
  margin-left: 10px;
}

.mr10 {
  margin-right: 10px;
}

.table-slot {
  font-size: 12px;

  .label {
    width: 64px;
    text-align: right;
    margin-right: 4px;
  }

  .value {
    text-align: left;
    flex: 1;
  }
}

.error {
  color: red;
}
</style>
