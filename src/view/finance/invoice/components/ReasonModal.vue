<template>
  <div class="reasonModal-wrapper">
    <Modal
        :value="reasonVisible"
        title="驳回开票申请"
        width="680px"
        @on-visible-change="changeVisible"
        >
        <Form style="margin-left: 40px;">
          <FormItem label="请选择驳回原因">
            <Select v-model="reasonType" style="width:300px" @on-change="changeReasonType" label-in-value	>
              <Option v-for="item in reasonList" :value="item.key"  :key="item.key">{{ item.value }}</Option>
            </Select>
          </FormItem>
          <FormItem label="请输入驳回原因" v-show="reasonType===3">
             <Input v-model="reason" type="textarea" :autosize="{minRows:4,maxRows:6}"  style="width:300px"/>
          </FormItem>
        </Form>
      <div slot="footer">
        <Button @click="cancel">取消</Button>
        <Button type="primary" @click="confirmReject">确认驳回</Button>
      </div>
    </Modal>

  </div>
</template>

<script>
export default {
  name: 'ReasonDialog',
  mixins: [],

  components: {
  },

  props: {
    reasonVisible: {
      type: Boolean,
      default: false
    },
    rejectInvoice: {
      type: Function,
    },
  },

  data() {
    return {
      reasonList: [
        {
          key: 1,
          value: '发票抬头或税号信息有误'
        },
        {
          key: 2,
          value: '联系人或邮箱信息有误'
        },
        {
          key: 3,
          value: '自定义驳回原因'
        },
      ],
      reasonType: '',
      reason: '',

    }
  },

  computed: {},

  watch: {},

  created() {
  },

  mounted() {
  },

  destroyed() {
  },

  methods: {
    changeReasonType( reasonObj ) {
      console.log("-> %c reasonObj  === %o ", "font-size: 15px", reasonObj)
      if ( reasonObj?.value === 3 ) {
        this.reason = ''
      } else {
        this.reason = reasonObj?.label
      }
    },
    confirmReject() {
      if ( !this.reason ) {
        this.$Message.error( '请输入驳回原因' )
        return
      }
      this.rejectInvoice( this.reason )
    },
    cancel() {
      this.closeModal()
    },
    closeModal() {
      this.$emit('update:reasonVisible',false)
    },
    changeVisible( flag ) {
      console.log( '-> %c flag  === %o ', 'font-size: 15px', flag )
      if(!flag){
        this.reason = ''
        this.reasonType = ''
        this.$emit( 'update:reasonVisible', false )
      }
    }
  },
}
</script>

<style scoped lang="less">

</style>