<template>
  <Modal 
    :value="modalValue"
    title="选择订单"
    :mask-closable="false"
    @on-visible-change="changeVisible"
    :width="750"
  >
    <div>
      <div class="mb10">
        <Alert type="warning"> ！请注意，平台订单与线下订单请分开提交，无法合并开票。</Alert>
      </div>
      <div class="flex flex-item-l-end">
        <DatePicker type="month" placeholder="选择月份" style="width: 100px" @on-change="dateChange($event)" :value="queryFormData.date"></DatePicker>
        <dvd></dvd>
        <!-- 订单类型 -->
        <Select ref="type" v-model="queryFormData.type" @on-change="selectChange()" style="width:150px" placeholder="选择订单类型">
          <Option v-for="item in purchaseTypeDesc" :value="item.id" :key="item.id">{{ item.desc }}</Option>
        </Select>
        <dvd></dvd>

        <!-- 子类型 -->
        <Select v-if="queryFormData.type == 'CLINIC'" @on-change="selectChange()" ref="order_type" clearable v-model="queryFormData.order_type" style="width:180px" placeholder="选择子类型">
          <Option  v-for="item in typeList()" :value="item.id" :key="item.id">{{ item.com_desc }}</Option>
        </Select>
        <Select v-else @on-change="selectChange()" ref="order_type" clearable v-model="queryFormData.order_type" style="width:180px" placeholder="选择子类型">
          <Option v-for="item in typeList()" :value="item.id" :key="item.id">{{ item.desc}}</Option>
        </Select>
        <dvd></dvd>
        <div>
          <Input v-model="queryFormData.order_code" placeholder="输入订单编号筛选"
              clearable @keyup.enter.native="onSearch" @on-clear="onSearch"
              style="width: 180px">
            <Icon type="ios-search" slot="suffix" @click="onSearch" />
          </Input>
        </div>
        <dvd />
        <Button type="primary" @click="onSearch">搜索</Button>
      </div>
    </div>
    <div class="block_10"></div>
    <div style="position: relative; height: 345px; overflow: auto;">
      <Table ref="selection" height="280"
          @on-select="onSelect" @on-select-cancel="onSelectCancel"
          @on-select-all="onSelectAll" @on-select-all-cancel="onSelectAllCancel"
          :columns="tableCols" :data="list" :loading="tableLoading">
        <!-- 金额 -->
        <template slot-scope="{row}" slot="amount">
          <span> {{ row.amount ? `￥${row.amount}` : '' }} </span>
        </template>
      </Table>

      <div class="block_20"></div>

      <KPage v-if="total > 0" :total="+total"
        :page-size="queryFormData.pageSize"
        :page-size-opts="[10,20,50]"
        :current="queryFormData.page"
        @on-change="handleCurrentChange"
        @on-page-size-change="handleSizeChange"
        style="text-align:center"
      />

    </div>
    <div slot="footer">
      <div v-if="Object.keys(selected_items).length > 0" style="display: inline-block;" class="lr15 text-muted">
        已选择<span class="text-error">{{Object.keys(selected_items).length}}</span>订单
        <dvd/>
        <dvd/>
        <dvd/>
        <span>总金额：￥<span v-html="totalAmount"></span></span>
      </div>
      <Button @click="changeVisible(false)">取消</Button>
      <Button type="primary" @click="onConfirm">确定</Button>
    </div>
  </Modal>
</template>

<script>
/* eslint-disable */
import S from '@/utils/util' // Some commonly used tools
import io from "@/utils/request" // Http request
import * as runtime from '@/utils/runtime' // Runtime information
import {debounce} from "lodash"
import { $operator } from '@/utils/operation';
/* eslint-disable */

let init_query_from_data = {
  page: 1,
  pageSize: 10,
  date: '', // 日期
  type: '', // 主类型
  order_type: '', // 子类型
  order_code: '',
}

export default {
  name: "order",
  model: {
    prop: 'modalValue',
    event: 'input'
  },
  props: {
    modalValue: {
      type: Boolean,
      default: false
    },
    checkedList: {
      type: Array,
      default (){
        return []
      }
    },
    type: {
      type: String,
      default: ''
    },
    date: {
      type: [Object, String],
      default: ''
    }
  },
  data() {
    return {
      queryFormData: {...init_query_from_data},
      totalAmount:'',
      tableCols: [
        { type: 'selection', width: 60},
        { title: '订单编号', key: 'order_code', align: 'center'},
        { title: '订单类型', key: 'type_text', align: 'center'},
        { title: '可开票金额', slot: 'amount', align: 'center', width: 100},
        { title: '开票方', key: 'drawer', align: 'center'},
      ],
      tableLoading: false,

      list: [],
      total: 0,
      statusDesc: {},

      selected_items: {},
      clone_selected_items: {},
      clone_date: {},
      purchaseTypeDesc: [], // 订单主类型
      unionPurchaseTypeDesc: [], // 平台订单子类型
      typeDesc: [], // 线下订单得子类型
    }
  },

  computed: {
    // 根据不同得主类型展示相应得子类型
  },

  watch: {
    selected_items(oldVal){
      this.itemDebonce(this,oldVal)
    }
  },

  mounted () {
  },

  methods: {
    init () {
      // 此处会清空所有原始数据，如果你需要初始化数据，请在后面进行操作
      this.clearQuery()
      // 默认获取前一个月
      // 获取枚举
      this.queryFormData.date = this.date || this.$moment().subtract(1,'months').format('YYYY-MM')
      this.getInvoicePurOderOptions()
      this.queryFormData.type = this.type
      this.onSelectAll(this.checkedList)

      this.clone_selected_items = this._.cloneDeep(this.selected_items)
      this.clone_date = this._.cloneDeep(this.date)
    },

    itemDebonce:debounce((vm,val)=>{
      if(Object?.keys(val)?.length){
        const mountObj={cli_trade_flow_ids:'',joinin_trade_flow_ids:''}
        for(let item in val) {
          if(val[item].plat=='CLINIC'){
            mountObj.cli_trade_flow_ids+=val[item].trade_flow_ids+','
          }else{
            mountObj.joinin_trade_flow_ids+=val[item].trade_flow_ids+','
          }
        }
        mountObj.cli_trade_flow_ids=mountObj.cli_trade_flow_ids.substring(0,mountObj.cli_trade_flow_ids.length-1)
        mountObj.joinin_trade_flow_ids=mountObj.joinin_trade_flow_ids.substring(0,mountObj.joinin_trade_flow_ids.length-1)
        vm.$api.getInvoiceAmount(mountObj).then(res=>{
          let mount=''
          console.log(Math.sign(Number(res.red_amount)),res,'res.red_amount');
          if(Math.sign(Number(res.red_amount))<0){
            mount=`${res.amount}(<span style='color:red'>¥${res.red_amount}</span>)`
          }else{
            mount=res.amount
          }
          vm.totalAmount= mount
        })
      }
    },500),

    dateChange (val) {
      this.queryFormData.date = val
      this.queryFormData.page = 1
      // 当日期发生变化时，清除已经选中的单子
      if ( val ) {
        this.selected_items = {}
      }
      // 如果当前选中月份数据 已经抛出去，此处切回原月份时,将外部选择的订单回显上
      if (this.clone_date == val) {
        this.selected_items = this._.cloneDeep(this.clone_selected_items)
      }
      this.get()
    },

    selectChange () {
      this.queryFormData.page = 1
      this.get()
    },

    typeList () {
      console.log(this.unionPurchaseTypeDesc);
      if ( this.queryFormData.type == 'CLINIC' ) {
        return this.unionPurchaseTypeDesc
      }else{
        return this.typeDesc
      }
    },

    // 获取采购订单枚举
    getInvoicePurOderOptions ( ) {
      this.$api.getInvoicePurOderOptions().then( res => {
        // 订单主类型
        this.purchaseTypeDesc = S.descToArrHandle(res.purchaseTypeDesc)
        if ( !this.queryFormData.type ) {
          this.queryFormData.type = this.purchaseTypeDesc[0] && this.purchaseTypeDesc[0].id
        }
        // 线下订单得子类型
        this.typeDesc = S.descToArrHandle(res.typeDesc)
        // 平台订单得子类型
        this.unionPurchaseTypeDesc = S.descToArrHandle(res.unionPurchaseTypeDesc)
        console.log(this.unionPurchaseTypeDesc,'2222222222');
        this.get()
      }, rej => {
        this.$Message.error( rej.errmsg )
      })
    },

    close () {
      this.$emit('input', false)
    },

    changeVisible (show) {
      this.scrollTop()
      if ( show ) {
        this.init()
      }else {
        this.close()
      }
    },

    onSearch: function () {
      this.queryFormData.page = 1
      this.get()
    },

    handleSizeChange( val ) {
      console.log( '-> val', val )
      this.queryFormData.page = 1
      this.queryFormData.pageSize = val
      this.get()
      this.scrollTop()
    },
    handleCurrentChange( val ) {
      console.log( '-> val', val )
      this.queryFormData.page = val
      this.get()
      this.scrollTop()
    },

    scrollTop(){
      let overflowY = this.$el.getElementsByClassName("ivu-table-body")[0];
      if (!overflowY) {
        return;
      }
      overflowY.scrollTop = 0;
    },

    onRefresh: function () {
      this.get()
    },

    onSelect: function (selection, row) {
      this.$set(this.selected_items, row.trade_flow_ids, row)
    },

    onSelectAll: function (selection) {
      selection.forEach(item => {
        this.$set(this.selected_items, item.trade_flow_ids, item)
      })
    },

    onSelectCancel: function (selection, row) {
      this.$delete(this.selected_items, row.trade_flow_ids)
    },

    onSelectAllCancel: function (selection) {
      for(let k in this.list) {
        this.$delete(this.selected_items, this.list[k].trade_flow_ids)
      }
    },

    onConfirm: function () {
      let onlinePlat = false
      let offPlat = false
      
      let items = []
      for (let key in this.selected_items) {
        if ( this.selected_items[key].plat == 'CLINIC' ) {
          onlinePlat = true
        }
        if ( this.selected_items[key].plat == 'COM' ) {
          offPlat = true
        }
        items.push(this.selected_items[key])
      }
      if ( items.length == 0 ) {
        this.$Message.error('请先选择订单')
        return false
      }
      if ( onlinePlat && offPlat ) {
        this.$Message.error('已选中的订单类型无法合并开票，请分开选中提交开票')
        return false
      }
      this.$emit('on-selected', items, this.queryFormData.type, this.queryFormData.date)
      this.close()
    },

    get: function () {
      this.tableLoading = true
      this.$api.getInvoicePurchaseorder(this.queryFormData).then(data => {
        this.list = this.handler(data.list)
        this.total = data.total
        this.tableLoading = false
      }, rej => {
        this.$Message.error(rej.errmsg)
      })
    },

    handler: function (list) {
      for(let k in list) {
        list[k].plat = this.queryFormData.type
        for(let j in this.selected_items) {
          if (list[k].trade_flow_ids== this.selected_items[j].trade_flow_ids) {
            list[k]['_checked'] = true // 选中已选项
          }
        }
      }
      return list
    },

    clearQuery: function () {
      this.queryFormData = {...init_query_from_data}
      this.queryFormData.page = 1
      this.list = []
      this.total = 0
      this.selected_items = {}
    }
  },
}
</script>

<style lang="less" scoped>
::v-deep .ivu-modal{
  top: 20%;
}
.mb10 {
  margin-bottom: 10px;
}
</style>
