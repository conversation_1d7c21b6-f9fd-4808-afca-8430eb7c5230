<template>
  <Modal
    :value="value"
    :title="title"
    :mask-closable="false"
    width="900px"
    @on-visible-change="changeVisible"
    footer-hide
    lock-scroll
    :styles="{ minHeight: '200px' }"
    class="goods-modal"
  >
    <div class="content">
      <div class="invoice-container">
        <Form :model="formData" ref="formData" label-position="right" :label-width="100" :rules="ruleValidate">
          <div class="block-header">申请方信息</div>
          <Row>
            <Col :span="22">
              <FormItem label="申请方:" class="mb10">
                {{ opcName }}
              </FormItem>
            </Col>
            <Col :span="22">
              <FormItem label="收票人姓名:" prop="name">
                <Input v-model="formData.name"></Input>
              </FormItem>
            </Col>
            <Col :span="22">
              <FormItem label="收票人手机:" prop="mobile">
                <Input v-model="formData.mobile"></Input>
              </FormItem>
            </Col>
            <Col :span="22">
              <FormItem label="收票邮箱:" prop="email">
                <Input v-model="formData.email"></Input>
              </FormItem>
            </Col>
            <Col :span="22">
              <FormItem label="收票地址:" prop="address" class="mb10">
                <div class="flex">
                  <div class="addressBox" style="width: 55%; padding-top: 1px">
                    <el-cascader
                      v-model="selectedAddress"
                      :options="options"
                      clearable
                      popper-class="address-com-small address-zIndex"
                      placeholder="请选择收件地址"
                      size="small"
                      style="width: 100%"
                      @change="regionChange"
                    >
                    </el-cascader>
                  </div>
                  <div class="ml10" style="width: 45%">
                    <Input v-model="formData.address.detail" placeholder="详细地址"></Input>
                  </div>
                </div>
              </FormItem>
            </Col>
          </Row>

          <div class="block-header">发票内容</div>
          <Row>
            <Col :span="22">
              <FormItem label="发票类型:" class="mb10" prop="type">
                <RadioGroup v-model="formData.type">
                  <Radio :label="item.id" v-for="item in invoiceType" :key="item.id">{{ item.desc }}</Radio>
                </RadioGroup>
              </FormItem>
            </Col>
            <Col :span="22">
              <FormItem label="发票金额:" required class="mb10">
                ￥{{ totalAmount }}
                <a @click="goodsDetail()">商品明细</a>
              </FormItem>
            </Col>
          </Row>
          <div class="block-header flex flex-align-center">
            发票信息
            <k-link class="ml10" :to="{ path: '/setting/invoice/list' }" target="_blank">修改发票信息</k-link>
            <Icon type="md-refresh" size="18" class="ml10 cursor" @click="getInvoiceInfo()" />
          </div>

          <div class="invoice-block">
            <Spin v-if="spinLoading"></Spin>
            <Row v-else>
              <Col :span="22">
                <FormItem label="发票抬头:" class="mb10">
                  {{ this.formData.invoice.organization_name || '-' }}
                </FormItem>
              </Col>
              <Col :span="22">
                <FormItem label="公司税号:" class="mb10">
                  {{ this.formData.invoice.organization_code || '-' }}
                </FormItem>
              </Col>
              <Col :span="22">
                <FormItem label="注册地址:" class="mb10">
                  {{ this.formData.invoice.reg_address_text || '-' }}
                </FormItem>
              </Col>
              <Col :span="22">
                <FormItem label="注册电话:" class="mb10">
                  {{ this.formData.invoice.reg_mobile || '-' }}
                </FormItem>
              </Col>
              <Col :span="22">
                <FormItem label="开户银行:" class="mb10">
                  {{ this.formData.invoice.bank || '-' }}
                </FormItem>
              </Col>
              <Col :span="22">
                <FormItem label="银行账户:" class="mb10">
                  {{ this.formData.invoice.bank_account || '-' }}
                </FormItem>
              </Col>
            </Row>
          </div>
        </Form>
        <div class="btn-wrapper">
          <Button @click="back">返回</Button>
          <dvd />
          <dvd />
          <dvd />
          <Button type="primary" @click="submit()" :loading="saveBtnLoading">确定</Button>
        </div>

        <!-- 商品明细 -->
        <goods-modal :goods-visible.sync="goodsVisible" :goodsDetails="goodsDetails"></goods-modal>
      </div>
    </div>
  </Modal>
</template>

<script>
import renderHeader from '@/mixins/renderHeader';
import { CodeToText, regionData } from '@/utils/chinaMap';
import { getOpcName } from 'utils/runtime';
import { $operator } from '@/utils/operation';

export default {
  name: 'applyInvoiceModal',
  mixins: [renderHeader],

  components: {
    GoodsModal: () => import('./GoodsDetail')
  },

  props: {
    title: {
      type: String,
      default: '申请开票'
    },
    value: {
      type: Boolean,
      default: false
    },

    pt_id: {
      type: String,
      default: ''
    },

    // 商品明细
    goodsDetails: {
      type: Array,
      default: () => []
    }
  },

  data() {
    const validateMobile = (rule, value, callback) => {
      if (value === '') {
        callback(new Error('请输入正确的手机号码'));
      } else {
        const reg = /^1[3456789]\d{9}$/;
        if (!reg.test(value)) {
          callback(new Error('请输入正确的手机号码'));
        }
        callback();
      }
    };

    const validateEmail = (rule, value, callback) => {
      let reg = /^\w+((-\w+)|(\.\w+))*\@[A-Za-z0-9]+((\.|-)[A-Za-z0-9]+)*\.[A-Za-z0-9]+$/;
      if (!reg.test(value)) {
        callback(new Error('请输入正确的邮箱'));
      } else {
        callback();
      }
    };

    return {
      saveBtnLoading: false,
      // 发票类型(1：普通电子发票，2:普通纸质发票,3：增值税专用电子发票)
      invoiceType: [
        { id: '1', desc: '普通电子发票' },
        { id: '2', desc: '普通纸质发票' },
        { id: '3', desc: '增值税专用电子发票' }
      ],
      // invoice里面统一放置发票信息
      formData: {
        invoice: {
          organization_name: '', // 发票抬头
          organization_code: '', // 公司税号
          reg_address_text: '', // 注册地址
          reg_mobile: '', // 注册电话
          bank: '', // 开户银行
          bank_account: '' // 银行账号
        },
        name: '',
        mobile: '',
        email: '',
        type: '1', // 发票类型
        //收票地址
        address: {
          prov: {
            name: '',
            code: ''
          },
          city: {
            name: '',
            code: ''
          },
          county: {
            name: '',
            code: ''
          },
          detail: ''
        }
      },
      ruleValidate: {
        name: [{ required: true, message: '请输入收票人姓名', trigger: 'blur' }],
        mobile: [
          { required: true, message: '请输入收票人电话', trigger: 'change' },
          { required: true, validator: validateMobile }
        ],
        email: [
          { required: true, message: '请输入正确的邮箱', trigger: 'change' },
          { required: true, validator: validateEmail }
        ],
        type: [{ required: true, message: '请选择发票类型', trigger: 'change' }]
      },
      options: regionData,
      selectedAddress: [],
      orderVisible: false,

      orderColumns: [
        { title: '订单编号', key: 'order_code', align: 'center' },
        { title: '订单类型', key: 'type_text', align: 'center' },
        { title: '金额', slot: 'amount', align: 'center' },
        { title: '开票方', key: 'drawer', align: 'center' },
        { title: '操作', slot: 'action', algin: 'center', width: 60 }
      ], // 选中订单columns
      checked_type: '', // 选中订单的主类型

      // 商品明细
      goodsVisible: false,
      order_codes: {}, // 采购订单codes合集
      spinLoading: false,

      // 失败发票数据
      fail_invoice: []
    };
  },

  computed: {
    opcName() {
      return getOpcName();
    },

    // 发票金额
    totalAmount() {
      let amount = 0;
      this.fail_invoice.forEach(item => {
        amount = $operator.add(Number(item.amount), Number(amount));
      });
      return amount || 0;
    }
  },

  watch: {},

  created() {},

  mounted() {},

  destroyed() {},

  methods: {
    // 关闭弹窗
    closeModal() {
      this.$emit('input', false);
    },
    changeVisible(flag) {
      if (flag) {
        this.init();
      } else {
        this.closeModal();
      }
    },

    // 初始化数据
    init() {
      let id = this.$route.query.id;
      if (id) {
        this.getInvoiceDetails(id);
      } else {
        // 初始化创建时，通过回显接口回显上次开票得用户
        this.echoInvoiceApplicant();
      }
      // 获取发票信息
      this.getInvoiceInfo();
    },
    // 发票详情
    getInvoiceDetails(id) {
      let params = {
        id: this.$route.query.id
      };
      this.$api.getInvoiceDetails(params).then(res => {
        this.formData.name = res.applicant.name;
        this.formData.mobile = res.applicant.mobile;
        this.formData.email = res.applicant.email;
        if (res.applicant.address.county && res.applicant.address.county.code) {
          this.selectedAddress = [
            res.applicant.address.prov.code,
            res.applicant.address.city.code,
            res.applicant.address.county.code
          ];
        } else {
          this.selectedAddress = [res.applicant.address.prov.code, res.applicant.address.city.code];
        }
        this.formData.address.detail = res.applicant.address.detail;
        this.formData.address = res.applicant.address;
        this.formData.type = res.type;

        this.fail_invoice = res.fail_invoice;
      });
    },
    //地区选择
    regionChange(address) {
      if (address.length) {
        const province = {
          name: CodeToText[address[0]],
          code: address[0]
        };
        const city = {
          name: CodeToText[address[1]],
          code: address[1]
        };
        const area = {
          name: CodeToText[address[2]],
          code: address[2]
        };
        this.formData.address.prov = province;
        this.formData.address.city = city;
        this.formData.address.county = area;
      } else {
        this.formData.address.prov = { name: '', code: '' };
        this.formData.address.city = { name: '', code: '' };
        this.formData.address.county = { name: '', code: '' };
      }
    },

    showOrderVisible() {
      this.orderVisible = true;
    },

    goodsDetail() {
      this.goodsVisible = true;
    },

    back() {
      this.closeModal();
    },

    // 专票时，字段是否填写完整
    validFormData() {
      /**
       * @note: 普通纸质发票,地址必填，发票抬头和税号必填
       */
      if (this.formData.type == '2') {
        if (!this.formData.address.city.code || !this.formData.address.detail) {
          this.$Message.error('请完善地址信息');
          return false;
        }

        if (!this.formData.invoice.organization_name) {
          this.$Message.error('请完善发票抬头');
          return false;
        }

        if (!this.formData.invoice.organization_code) {
          this.$Message.error('请完善发票税号');
          return false;
        }
      }

      /**
       * @note: 增值税专用纸质发票,地址喝开票信息必填
       */
      if (this.formData.type == '3') {
        if (!this.formData.address.city.code || !this.formData.address.detail) {
          this.$Message.error('请完善地址信息');
          return false;
        }

        for (let key in this.formData.invoice) {
          if (!this.formData.invoice[key]) {
            this.$Message.error('请完善发票信息');
            return false;
          }
        }
      }

      return true;
    },

    submit() {
      this.$refs['formData'].validate(valid => {
        if (valid) {
          if (!this.validFormData()) {
            return false;
          }
          this.invoiceResubmit();
        }
      });
    },

    // 获取开票信息
    getInvoiceInfo() {
      this.spinLoading = true;
      this.$api
        .getInvoiceInfo()
        .then(res => {
          this.formData.invoice.organization_name = res.organization_name;
          this.formData.invoice.organization_code = res.organization_code;
          this.formData.invoice.reg_address_text = res.reg_address_text;
          this.formData.invoice.reg_mobile = res.reg_mobile;
          this.formData.invoice.bank = res.bank;
          this.formData.invoice.bank_account = res.bank_account;
        })
        .finally(() => (this.spinLoading = false));
    },

    // 回显开票人信息
    echoInvoiceApplicant() {
      this.$api.echoInvoiceApplicant().then(
        res => {
          this.formData.name = res.applicant.name;
          this.formData.mobile = res.applicant.mobile;
          this.formData.email = res.applicant.email;
          if (res.applicant.address.county && res.applicant.address.county.code) {
            this.selectedAddress = [
              res.applicant.address.prov.code,
              res.applicant.address.city.code,
              res.applicant.address.county.code
            ];
          } else {
            this.selectedAddress = [res.applicant.address.prov.code, res.applicant.address.city.code];
          }
          this.formData.address.detail = res.applicant.address.detail;
          this.formData.address = res.applicant.address;
        },
        err => this.$Message.error(err.errmsg)
      );
    },

    // 获取发票申请的参数
    handleParams() {
      let params = {
        pt_id: this.pt_id,
        type: this.formData.type,
        name: this.formData.name,
        mobile: this.formData.mobile,
        email: this.formData.email,
        address: { ...this.formData.address }
      };
      return params;
    },

    // 发票申请
    invoiceResubmit() {
      this.saveBtnLoading = true;
      let params = this.handleParams();
      this.$api
        .invoiceResubmit(params)
        .then(
          res => {
            this.$Message.success('重新开票成功');
            this.closeModal();
            this.$emit('refresh');
          },
          rej => {
            this.$Message.error(rej.errmsg);
          }
        )
        .finally(() => (this.saveBtnLoading = false));
    }
  }
};
</script>

<style scoped lang="less">
::v-deep .ivu-modal-body {
  padding: 0px;
}
.content {
  padding: 16px 16px 0;
  height: 600px;
  overflow-y: scroll;
  .btn-wrapper {
    position: sticky;
    bottom: 0px;
    border-top: 1px solid #e8eaec;
    background-color: #fff;
    padding: 10px;
    text-align: center;
  }
}
.block-header {
  background-color: #efefef;
  padding: 10px;
  margin: 20px 0px;
  position: relative;
}

::v-deep .invoice-container {
  .page-title {
    font-size: 16px;
    font-weight: 700;
    padding-bottom: 10px;
    margin-bottom: 20px;
  }
}

::v-deep .el-cascader {
  .el-cascader-panel {
    font-size: 12px;
  }
  .el-input {
    .el-input__inner {
      border: 1px solid #bcc3d7;
      border-radius: 2px;
      padding-left: 7px;
      font-size: 12px;
    }
  }
}

.mb10 {
  margin-bottom: 10px;
}
.ml10 {
  margin-left: 10px;
}
.cursor {
  cursor: pointer;
}
.mr10 {
  margin-right: 10px;
}
.invoice-block {
  position: relative;
  min-height: 260px;
}
.ivu-spin {
  height: 300px;
  width: 100%;
  position: absolute;
  display: flex;
  justify-content: center;
  align-items: center;
}
</style>

<style lang="less">
// 当前页面地址下拉框层级加大,防止用户频繁点击弹窗，导致弹窗的层级盖过下拉框
.address-zIndex {
  z-index: 999999 !important;
}
</style>
