<template>
  <Modal
    :value="value"
    :title="title"
    :mask-closable="false"
    width="900px"
    @on-visible-change="changeVisible"
    footer-hide
    lock-scroll
    :styles="{ minHeight: '200px' }"
    class="goods-modal"
  >
    <div class="content">
      <div class="invoice-container">
        <Form :model="formData" ref="formData" label-position="right" :label-width="100" :rules="ruleValidate">
          <Row>
            <Col :span="22">
              <FormItem label="开票时间:">
                {{ invoice_time }}
              </FormItem>
            </Col>
            <Col :span="22">
              <FormItem label="发票金额:">
                {{ amount ? `￥${amount}` : '-' }}
              </FormItem>
            </Col>
            <Col :span="22">
              <FormItem label="发票编号:" prop="invoice_no">
                <Input v-model="formData.invoice_no"></Input>
              </FormItem>
            </Col>
            <Col :span="22">
              <FormItem label="发票附件:" prop="c_url">
                <Picture
                  v-model="formData.c_url"
                  :limit="9"
                  :format="['jpg', 'jpeg', 'png', 'pdf', 'ofd']"
                  accept=".pdf,.ofd,image/jpg,image/jpeg,image/png"
                />
              </FormItem>
            </Col>
          </Row>
        </Form>
        <div class="btn-wrapper">
          <Button @click="back">返回</Button>
          <dvd />
          <dvd />
          <dvd />
          <Button type="primary" @click="submit()" :loading="saveBtnLoading">确定</Button>
        </div>
      </div>
    </div>
  </Modal>
</template>

<script>
import renderHeader from '@/mixins/renderHeader';
// import {getOpcName} from 'utils/runtime'
import io from '@/utils/request'; // Http request
import Picture from '@/components/upload/picture';
import S from '@/utils/util'; // Some commonly used tools

export default {
  name: 'applyInvoiceModal',
  mixins: [renderHeader],

  components: {
    GoodsModal: () => import('./GoodsDetail'),
    Picture
  },

  props: {
    title: {
      type: String,
      default: '标记开票'
    },
    value: {
      type: Boolean,
      default: false
    },
    invoice_id: {
      type: String,
      default: ''
    },
    detail_id: {
      type: String,
      default: ''
    },
    amount: {
      type: String,
      default: null
    },
    // 发票类型 BLUE|RED
    invoiceType: {
      type: String,
      default: ''
    },

    // 为 1 时表示诊所/养疗馆新增的开票流程
    is_cli_joinin: {
      type: String,
      default: ''
    }
  },

  data() {
    const validateMobile = (rule, value, callback) => {
      if (value === '') {
        callback(new Error('请输入发票编号'));
      } else {
        callback();
      }
    };

    return {
      saveBtnLoading: false,
      invoice_time: S.moment().format('YYYY-MM-DD'), // 开票时间
      // invoice里面统一放置发票信息
      formData: { invoice_no: '', c_url: [] },
      ruleValidate: {
        invoice_no: [
          { required: true, message: '请输入发票编号', trigger: 'change' },
          { required: true, validator: validateMobile }
        ],
        type: [{ required: true, message: '请选择发票类型', trigger: 'change' }]
      },
      // 商品明细
      // 失败发票数据
      fail_invoice: []
    };
  },

  computed: {},

  watch: {},

  created() {},

  mounted() {},

  destroyed() {},

  methods: {
    // 关闭弹窗
    closeModal() {
      this.$refs['formData'].resetFields();
      this.$emit('input', false);
    },
    changeVisible(flag) {
      if (flag) {
      } else {
        this.closeModal();
      }
    },

    back() {
      this.closeModal();
    },

    // 专票时，字段是否填写完整
    submit() {
      this.$refs['formData'].validate(valid => {
        if (valid) {
          if (this.is_cli_joinin == 1) {
            this.setInvoiceFlagInvoice();
          } else {
            this.invoiceResubmit();
          }
        }
      });
    },

    handleParams() {
      let params = {
        invoice_id: this.invoice_id,
        detail_id: this.detail_id,
        invoice_no: this.formData.invoice_no,
        c_url: this.formData.c_url
      };
      return params;
    },

    // 发票申请
    invoiceResubmit() {
      this.saveBtnLoading = true;
      let params = this.handleParams();
      this.$http
        .post('/pms_opc/finance.invoice.flagred', params)
        .then(
          res => {
            this.$Message.success('标记开票成功');
            this.closeModal();
            this.$emit('refresh');
          },
          rej => {
            this.$Message.error(rej.errmsg);
          }
        )
        .finally(() => (this.saveBtnLoading = false));
    },

    /**
     * @description: 红票 ｜ 蓝票  标记开票
     * @note 2023.05.29 新增诊所，养疗馆的开票标记
     * @params { type } BLUE | RED 标记类型 必填
     * @params { invoice_id } 发票id 必填
     * @params { detail_id } 发票详情id 必填
     * @params { invoice_no } 发票号码 必填
     * @params { c_url } 发票地址 可选
     * */
    setInvoiceFlagInvoice() {
      this.saveBtnLoading = true;
      let params = {
        type: this.invoiceType,
        invoice_id: this.invoice_id,
        detail_id: this.detail_id,
        invoice_no: this.formData.invoice_no,
        c_url: this.formData.c_url
      };
      this.$api
        .setInvoiceFlagInvoice(params)
        .then(
          res => {
            this.$Message.success('标记开票成功');
            this.closeModal();
            this.$emit('refresh');
          },
          rej => this.$Message.error(rej.errmsg)
        )
        .finally(() => (this.saveBtnLoading = false));
    }
  }
};
</script>

<style scoped lang="less">
::v-deep .ivu-modal-body {
  padding: 0px;
}

.content {
  padding: 16px 16px 0;
  height: 360px;
  overflow-y: auto;

  .btn-wrapper {
    position: sticky;
    bottom: 0px;
    border-top: 1px solid #e8eaec;
    background-color: #fff;
    padding: 10px;
    text-align: center;
  }
}

.block-header {
  background-color: #efefef;
  padding: 10px;
  margin: 20px 0px;
  position: relative;
}

::v-deep .invoice-container {
  .page-title {
    font-size: 16px;
    font-weight: 700;
    padding-bottom: 10px;
    margin-bottom: 20px;
  }
}

::v-deep .el-cascader {
  .el-cascader-panel {
    font-size: 12px;
  }

  .el-input {
    .el-input__inner {
      border: 1px solid #bcc3d7;
      border-radius: 2px;
      padding-left: 7px;
      font-size: 12px;
    }
  }
}

.invoice-block {
  position: relative;
  min-height: 260px;
}

.ivu-spin {
  height: 300px;
  width: 100%;
  position: absolute;
  display: flex;
  justify-content: center;
  align-items: center;
}
</style>
