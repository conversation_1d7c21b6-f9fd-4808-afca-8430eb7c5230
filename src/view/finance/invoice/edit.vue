<template>
  <div class="invoice-container">
    <Form :model="formData" ref="formData" label-position="right" :label-width="100" :rules="ruleValidate">
      <div class="block-header">申请方信息</div>
      <Row>
        <Col :span="14">
          <FormItem label="申请方:" class="mb10">
            {{ opcName }}
          </FormItem>
        </Col>
        <Col :span="14">
          <FormItem label="收票人姓名:" prop="name">
            <Input v-model="formData.name"></Input>
          </FormItem>
        </Col>
        <Col :span="14">
          <FormItem label="收票人手机:" prop="mobile">
            <Input v-model="formData.mobile"></Input>
          </FormItem>
        </Col>
        <Col :span="14">
          <FormItem label="收票邮箱:" prop="email">
            <Input v-model="formData.email"></Input>
          </FormItem>
        </Col>
        <Col :span="14">
          <FormItem label="收票地址:" prop="address" class="mb10">
            <div class="flex">
              <div class="addressBox" style="width: 55%; padding-top: 1px">
                <el-cascader
                  v-model="selectedAddress"
                  :options="options"
                  clearable
                  popper-class="address-com-small"
                  placeholder="请选择收件地址"
                  size="small"
                  style="width: 100%"
                  @change="regionChange"
                >
                </el-cascader>
              </div>
              <div class="ml10" style="width: 45%">
                <Input v-model="formData.address.detail" placeholder="详细地址"></Input>
              </div>
            </div>
          </FormItem>
        </Col>
      </Row>

      <div class="block-header">发票内容</div>
      <Row>
        <Col :span="14">
          <FormItem label="发票类型:" class="mb10" prop="type">
            <RadioGroup v-model="formData.type">
              <Radio :label="item.id" v-for="item in invoiceType" :key="item.id">{{ item.desc }}</Radio>
            </RadioGroup>
          </FormItem>
        </Col>
        <Col :span="14">
          <FormItem label="发票金额:" required class="mb0">
            <span class="mr10" v-if="order_list.length"><span v-html="totalAmount"></span></span>
            <a @click="showOrderVisible" v-if="order_list.length">重选订单</a>
            <a @click="showOrderVisible" v-else>选择订单</a>

            <Table ref="selection" :columns="orderColumns" :data="show_order_list" v-if="order_list.length">
              <!-- 金额 -->
              <template slot-scope="{ row }" slot="amount">
                <span> {{ row.amount ? `￥${row.amount}` : '' }} </span>
              </template>

              <template slot-scope="{ row, index }" slot="action">
                <a @click="deleteOrder(index)">删除</a>
              </template>
            </Table>
          </FormItem>
        </Col>

        <Col :span="14">
          <FormItem required class="mb10">
            <div v-if="order_list.length > show_order_num" class="border-arrow" style="text-align: center">
              <a
                v-if="order_list.length > show_order_num && show_order_list.length <= show_order_num"
                @click="expand()"
              >
                展开
                <Icon type="ios-arrow-down" />
              </a>
              <a v-if="order_list.length > show_order_num && show_order_list.length > show_order_num" @click="fold()">
                收起
                <Icon type="ios-arrow-up" />
              </a>
            </div>
          </FormItem>
        </Col>
        <Col :span="14">
          <FormItem label="发票内容:" class="mb10">
            <a v-if="order_list.length" @click="goodsDetail()">商品明细</a>
            <p v-else>-</p>
          </FormItem>
        </Col>
      </Row>
      <div class="block-header flex flex-align-center">
        发票信息
        <k-link class="ml10" :to="{ path: '/setting/Invoice' }" target="_blank">修改发票信息</k-link>
        <Icon type="md-refresh" size="18" class="ml10 cursor" @click="getInvoiceInfo()" />
      </div>

      <div class="block">
        <Spin v-if="spinLoading"></Spin>
        <Row v-else>
          <Col :span="14">
            <FormItem label="发票抬头:" class="mb10">
              {{ this.formData.invoice.organization_name || '-' }}
            </FormItem>
          </Col>
          <Col :span="14">
            <FormItem label="公司税号:" class="mb10">
              {{ this.formData.invoice.organization_code || '-' }}
            </FormItem>
          </Col>
          <Col :span="14">
            <FormItem label="注册地址:" class="mb10">
              {{ this.formData.invoice.reg_address_text || '-' }}
            </FormItem>
          </Col>
          <Col :span="14">
            <FormItem label="注册电话:" class="mb10">
              {{ this.formData.invoice.reg_mobile || '-' }}
            </FormItem>
          </Col>
          <Col :span="14">
            <FormItem label="开户银行:" class="mb10">
              {{ this.formData.invoice.bank || '-' }}
            </FormItem>
          </Col>
          <Col :span="14">
            <FormItem label="银行账户:" class="mb10">
              {{ this.formData.invoice.bank_account || '-' }}
            </FormItem>
          </Col>
        </Row>
      </div>
    </Form>
    <div class="block_40"></div>

    <div class="fixed-bottom-wrapper">
      <back-button></back-button>
      <dvd />
      <dvd />
      <dvd />
      <Button type="primary" @click="submit()" :loading="saveBtnLoading">提交</Button>
    </div>

    <!-- 商品明细 -->
    <goods-modal :goods-visible.sync="goodsVisible" :order-codes="order_codes" :orderType="orderType"></goods-modal>

    <select-order-modal
      v-model="orderVisible"
      :date="checked_date"
      @on-selected="onSelected"
      :checkedList="order_list"
      :type="checked_type"
      @setStr="comDetailFunc"
    ></select-order-modal>
  </div>
</template>

<script>
import { debounce } from 'lodash';
import { CodeToText, regionData } from '@/utils/chinaMap';
import selectOrderModal from './components/SelectOrderModal';

import { $operator } from '@/utils/operation';
import { getOpcName } from 'utils/runtime';
import S from '@/utils/util';

export default {
  name: 'edit',
  components: {
    selectOrderModal,
    GoodsModal: () => import('./components/GoodsDetail')
  },

  props: {},

  data() {
    const validateMobile = (rule, value, callback) => {
      if (value === '') {
        callback(new Error('请输入正确的手机号码'));
      } else {
        const reg = /^1[3456789]\d{9}$/;
        if (!reg.test(value)) {
          callback(new Error('请输入正确的手机号码'));
        }
        callback();
      }
    };

    const validateEmail = (rule, value, callback) => {
      let reg = /^\w+((-\w+)|(\.\w+))*\@[A-Za-z0-9]+((\.|-)[A-Za-z0-9]+)*\.[A-Za-z0-9]+$/;
      if (!reg.test(value)) {
        callback(new Error('请输入正确的邮箱'));
      } else {
        callback();
      }
    };

    return {
      saveBtnLoading: false,
      // 发票类型(1：普通电子发票，2:普通纸质发票,3：增值税专用纸质发票)
      invoiceType: [],
      totalAmount: '',
      // invoice里面统一放置发票信息
      formData: {
        invoice: {
          organization_name: '', // 发票抬头
          organization_code: '', // 公司税号
          reg_address_text: '', // 注册地址
          reg_mobile: '', // 注册电话
          bank: '', // 开户银行
          bank_account: '' // 银行账号
        },
        name: '',
        mobile: '',
        email: '',
        type: '4', // 发票类型
        //收票地址
        address: {
          prov: {
            name: '',
            code: ''
          },
          city: {
            name: '',
            code: ''
          },
          county: {
            name: '',
            code: ''
          },
          detail: ''
        }
      },
      ruleValidate: {
        name: [{ required: true, message: '请输入收票人姓名', trigger: 'blur' }],
        mobile: [
          { required: true, message: '请输入收票人电话', trigger: 'change' },
          { required: true, validator: validateMobile }
        ],
        email: [
          { required: true, message: '请输入正确的邮箱', trigger: 'change' },
          { required: true, validator: validateEmail }
        ],
        type: [{ required: true, message: '请选择发票类型', trigger: 'change' }]
      },
      options: regionData,
      selectedAddress: [],
      orderVisible: false,
      orderType: '',
      orderColumns: [
        { title: '订单编号', key: 'order_code', align: 'center' },
        { title: '订单类型', key: 'type_text', align: 'center' },
        { title: '金额', slot: 'amount', align: 'center' },
        { title: '开票方', key: 'drawer', align: 'center' },
        { title: '操作', slot: 'action', algin: 'center', width: 60 }
      ], // 选中订单columns
      order_list: [], // 选中订单数据
      show_order_list: [],
      show_order_num: 5,
      checked_type: '', // 选中订单的主类型
      checked_date: '',

      // 商品明细
      goodsVisible: false,
      order_codes: {}, // 采购订单codes合集
      spinLoading: false
    };
  },

  computed: {
    opcName() {
      return getOpcName();
    }
  },
  watch: {
    order_list(oldVal) {
      this.itemDebonce(this, oldVal);
    }
  },

  created() {},
  mounted() {
    this.init();
    this.getInvoiceTypeList();
  },
  methods: {
    // 初始化数据
    init() {
      let id = this.$route.query.id;
      if (id) {
        this.getInvoiceDetails(id);
      } else {
        // 初始化创建时，通过回显接口回显上次开票得用户
        this.echoInvoiceApplicant();
      }
      // 获取发票信息
      this.getInvoiceInfo();
    },
    itemDebonce: debounce((vm, val) => {
      console.log(vm, val, 'vm,val');
      const orderTypeMap = {
        COM: 'joinin_trade_flow_ids',
        CLINIC: 'cli_trade_flow_ids'
      };
      const mountObj = { cli_trade_flow_ids: '', joinin_trade_flow_ids: '' };
      console.log(vm.orderType);
      const orderTypeKey = orderTypeMap[vm.orderType];
      console.log(orderTypeKey, 'orderTypeKey');
      if (Object?.keys(val)?.length) {
        for (let item in val) {
          mountObj[orderTypeKey] += val[item].trade_flow_ids + ',';
        }
        mountObj[orderTypeKey] = mountObj[orderTypeKey].substring(0, mountObj[orderTypeKey].length - 1);
        console.log(mountObj, 'mountObj.debouce');
        vm.$api.getInvoiceAmount(mountObj).then(res => {
          console.log(res, 'res');
          let mount = '';
          if (Math.sign(Number(res.red_amount)) < 0) {
            mount = `¥${res.amount}(<span style='color:red'>￥${res.red_amount}</span>)`;
          } else {
            mount = `¥${res.amount}`;
          }
          console.log(mount, 'amount');
          vm.totalAmount = mount;
        });
      }
    }, 500),
    // 发票详情
    getInvoiceDetails(id) {
      let params = {
        id: this.$route.query.id
      };
      this.$api.getInvoiceDetails(params).then(res => {
        this.getOrderType(res.order_from);
        this.order_list = res.order_details;
        // 处理下拉显示逻辑
        if (this.order_list.length > this.show_order_num) {
          this.show_order_list = this.$lodash.cloneDeep(this.order_list).slice(0, this.show_order_num);
        } else {
          this.show_order_list = this.$lodash.cloneDeep(this.order_list);
        }
        this.formData.name = res.applicant.name;
        this.formData.mobile = res.applicant.mobile;
        this.formData.email = res.applicant.email;
        this.checked_date = res.date;
        if (res.applicant.address.county && res.applicant.address.county.code) {
          this.selectedAddress = [
            res.applicant.address.prov.code,
            res.applicant.address.city.code,
            res.applicant.address.county.code
          ];
        } else {
          this.selectedAddress = [res.applicant.address.prov.code, res.applicant.address.city.code];
        }
        console.log(this.orderType);
        this.formData.address.detail = res.applicant.address.detail;
        this.formData.address = res.applicant.address;
        this.formData.type = res.type;
      });
    },
    //地区选择
    regionChange(address) {
      if (address.length) {
        const province = {
          name: CodeToText[address[0]],
          code: address[0]
        };
        const city = {
          name: CodeToText[address[1]],
          code: address[1]
        };
        const area = {
          name: CodeToText[address[2]],
          code: address[2]
        };
        this.formData.address.prov = province;
        this.formData.address.city = city;
        this.formData.address.county = area;
      } else {
        this.formData.address.prov = { name: '', code: '' };
        this.formData.address.city = { name: '', code: '' };
        this.formData.address.county = { name: '', code: '' };
      }
    },

    showOrderVisible() {
      this.orderVisible = true;
    },

    // 获取商品类型
    getOrderType(type) {
      this.orderType = type;
    },

    onSelected(item, type, date) {
      console.log(item, 'item');
      this.getOrderType(item[0].plat || this.orderType);
      this.order_list = item;
      this.show_order_list = item;
      this.checked_type = type;
      this.checked_date = date;
      this.fold();
    },

    deleteOrder(index) {
      this.$delete(this.order_list, index);
      if (this.show_order_list.length <= this.show_order_num) {
        this.fold();
      } else {
        this.expand();
      }
    },

    goodsDetail() {
      this.order_codes = this.getShipOrderCodeIds();
      this.goodsVisible = true;
    },

    back() {
      this.$router.push('/finance/invoice/list');
    },

    // 专票时，字段是否填写完整
    validFormData() {
      /**
       * @note: 普通纸质发票,地址必填，发票抬头和税号必填
       */
      if (this.formData.type == '2') {
        if (!this.formData.address.city.code || !this.formData.address.detail) {
          this.$Message.error('请完善地址信息');
          return false;
        }

        if (!this.formData.invoice.organization_name) {
          this.$Message.error('请完善发票抬头');
          return false;
        }

        if (!this.formData.invoice.organization_code) {
          this.$Message.error('请完善发票税号');
          return false;
        }
      }

      /**
       * @note: 增值税专用纸质发票,地址喝开票信息必填
       */
      if (this.formData.type == '3') {
        if (!this.formData.address.city.code || !this.formData.address.detail) {
          this.$Message.error('请完善地址信息');
          return false;
        }

        for (let key in this.formData.invoice) {
          if (!this.formData.invoice[key]) {
            this.$Message.error('请完善发票信息');
            return false;
          }
        }
      }

      return true;
    },

    submit() {
      this.$refs['formData'].validate(valid => {
        if (valid) {
          if (!this.order_list.length) {
            this.$Message.error('请选择订单');
            return;
          }
          if (!this.validFormData()) {
            return false;
          }
          this.createInvoiceGenerate();
        }
      });
    },

    // 获取选中订单的ship_code_ids
    getShipOrderCodeIds() {
      let codes = {};
      console.log(this.order_list, 'this.order_list');
      this.order_list.forEach(item => {
        this.$set(codes, item.trade_flow_ids, item.order_code);
      });
      console.log(codes, 'codes');
      return codes || {};
    },

    // 获取选中订单的codes合集
    getOrderCodes() {
      let codes = [];
      console.log(JSON.parse(JSON.stringify(this.order_list)), 'this.order_list');
      this.order_list.forEach(item => {
        let obj = {
          [item.order_code]: item.trade_flow_ids
        };
        codes.push(obj);
      });
      return codes || {};
    },

    // 获取开票信息
    getInvoiceInfo() {
      this.spinLoading = true;
      this.$api
        .getInvoiceInfo()
        .then(res => {
          this.formData.invoice.organization_name = res.organization_name;
          this.formData.invoice.organization_code = res.organization_code;
          this.formData.invoice.reg_address_text = res.reg_address_text;
          this.formData.invoice.reg_mobile = res.reg_mobile;
          this.formData.invoice.bank = res.bank;
          this.formData.invoice.bank_account = res.bank_account;
        })
        .finally(() => (this.spinLoading = false));
    },

    // 回显开票人信息
    echoInvoiceApplicant() {
      this.$api.echoInvoiceApplicant().then(
        res => {
          this.formData.name = res.applicant.name;
          this.formData.mobile = res.applicant.mobile;
          this.formData.email = res.applicant.email;
          if (res.applicant.address.county && res.applicant.address.county.code) {
            this.selectedAddress = [
              res.applicant.address.prov.code,
              res.applicant.address.city.code,
              res.applicant.address.county.code
            ];
          } else {
            this.selectedAddress = [res.applicant.address.prov.code, res.applicant.address.city.code];
          }
          this.formData.address.detail = res.applicant.address.detail;
          this.formData.address = res.applicant.address;
        },
        err => this.$Message.error(err.errmsg)
      );
    },

    // 获取发票申请的参数
    handleParams() {
      let params = {
        id: this.$route.query.id || '',
        type: this.formData.type,
        name: this.formData.name,
        mobile: this.formData.mobile,
        email: this.formData.email,
        date: this.checked_date,
        address: { ...this.formData.address },
        order_code: this.getOrderCodes()
      };

      console.log(params);
      return params;
    },

    // 发票申请
    createInvoiceGenerate() {
      // this.saveBtnLoading = true
      let params = this.handleParams();
      console.log(params);
      this.$api
        .createInvoiceGenerate(params)
        .then(
          res => {
            this.$Message.success('申请开票成功');
            this.$router.push('/finance/invoice/list');
          },
          rej => {
            this.$Message.error(rej.errmsg);
          }
        )
        .finally(() => (this.saveBtnLoading = false));
    },

    comDetailFunc(str) {
      console.log(str, 'str');
      this.sendStr = str;
    },
    // 展开
    expand() {
      this.show_order_list = this.$lodash.cloneDeep(this.order_list);
    },

    // 折叠
    fold() {
      this.show_order_list = this.$lodash.cloneDeep(this.order_list).slice(0, this.show_order_num);
    },

    // api - 获取发票类型列表
    getInvoiceTypeList() {
      let params = {};
      console.log('=>(detail.vue:241) params', params);
      this.$api
        .getInvoiceTypeList(params)
        .then(res => {
          this.invoiceType = S.descToArrHandle(res.typeDesc);
          console.log('=>(edit.vue:615) this.invoiceType', this.invoiceType);
        })
        .catch(err => this.$Message.error(err.errmsg));
    }
  }
};
</script>

<style lang="less" scoped>
::v-deep .invoice-container {
  .page-title {
    font-size: 16px;
    font-weight: 700;
    padding-bottom: 10px;
    margin-bottom: 20px;
  }
}

::v-deep .el-cascader {
  .el-cascader-panel {
    font-size: 12px;
  }

  .el-input {
    .el-input__inner {
      border: 1px solid #bcc3d7;
      border-radius: 2px;
      padding-left: 7px;
      font-size: 12px;
    }
  }
}

.mb10 {
  margin-bottom: 10px;
}

.mb0 {
  margin-bottom: 0px;
}

.ml10 {
  margin-left: 10px;
}

.cursor {
  cursor: pointer;
}

.mr10 {
  margin-right: 10px;
}

.block {
  position: relative;
}

.border-arrow {
  // border: 1px solid #d7d9de;
  background: #fafafa;

  a {
    display: inline-block;
    width: 100%;
    height: 100%;
  }
}

.ivu-spin {
  height: 300px;
  width: 50%;
  position: absolute;
  display: flex;
  justify-content: center;
  align-items: center;
}
</style>
