<template>
  <div class="search-box">
    <Form inline :label-width="0" @submit.native.prevent @keyup.enter.native="onSearch" label-colon>
      <Row>
        <Col>
          <FormItem>
            <invoice-apply-search
              ref="apply-search"
              v-model="queryFormData.applicant_id"
              :isClearable="true"
            ></invoice-apply-search>
          </FormItem>
        </Col>

        <Col>
          <FormItem>
            <Select filterable placeholder="输入或选择开票方" v-model="queryFormData.drawer_id" clearable>
              <Option v-for="item in drawerList" :key="item.id" :value="item.id" :label="item.desc"> </Option>
            </Select>
          </FormItem>
        </Col>

        <Col>
          <FormItem>
            <Input placeholder="关联订单号" v-model="queryFormData.order_code" clearable />
          </FormItem>
        </Col>
        <Col>
          <FormItem>
            <Input placeholder="申请单号" v-model="queryFormData.apply_code" clearable />
          </FormItem>
        </Col>

        <Col>
          <FormItem>
            <DatePicker
              type="daterange"
              placement="bottom-end"
              @on-change="times => handleTimeChange(times)"
              placeholder="申请时间"
              v-model="timeRange"
              clearable
              style="width: 200px"
            ></DatePicker>
          </FormItem>
        </Col>
      </Row>
      <Row>
        <Col>
          <FormItem>
            <DatePicker
              v-model="applyTimeRange"
              type="daterange"
              placement="bottom-end"
              placeholder="开票时间"
              clearable
              @on-change="times => handleTimeChange(times, 'finished_st', 'finished_et')"
            />
          </FormItem>
        </Col>

        <Col>
          <FormItem style="text-align: left">
            <Button type="primary" class="mr10" @click="onSearch">筛选</Button>
            <Button type="default" @click="onResetSearch">重置</Button>
          </FormItem>
        </Col>
      </Row>
    </Form>
    <div class="table-wrapper">
      <div class="table-func flex flex-item-between">
        <div class="panel-nav">
          <a
            class="nav"
            :class="{ active: $route.query.status == '' || !$route.query.status }"
            @click.prevent.capture="onStatusChange('')"
          >
            全部
          </a>
          <a
            class="nav"
            :class="{ active: $route.query.status == 'WAIT' }"
            @click.prevent.capture="onStatusChange('WAIT')"
          >
            待开票
          </a>
          <a
            class="nav"
            :class="{ active: $route.query.status == 'FINISHED' }"
            @click.prevent.capture="onStatusChange('FINISHED')"
          >
            已开票
          </a>
          <a
            class="nav"
            :class="{ active: $route.query.status == 'REJECTED' }"
            @click.prevent.capture="onStatusChange('REJECTED')"
          >
            已驳回
          </a>
        </div>
        <div class="table-btn flex">
          <Button :loading="downloadLoading" @click="exportInvoiceDetai" class="mr-12">导出明细</Button>
          <Button type="primary" @click="goCreate()">申请开票</Button>
        </div>
      </div>
      <Table :loading="tableLoading" :columns="tableCols" :data="list" :height="$store.state.app.clientHeight - 333">
        <!-- 申请方 -->
        <template slot-scope="{ row }" slot="apply">
          <div>
            <div class="flex table-slot">
              <span class="label">申请方:</span>
              <span class="value">{{ row.applicant.applicant || '-' }}</span>
            </div>

            <div class="flex table-slot">
              <span class="label">申请人:</span>
              <span class="value">{{ row.applicant.name || '-' }}</span>
            </div>

            <div class="flex table-slot">
              <span class="label">手机号:</span>
              <span class="value">{{ row.applicant.mobile || '-' }}</span>
            </div>

            <div class="flex table-slot">
              <span class="label">接收邮箱:</span>
              <span class="value">{{ row.applicant.email || '-' }}</span>
            </div>
          </div>
        </template>

        <!-- 开票方 -->
        <template slot-scope="{ row }" slot="drawer">
          <div>{{ row.drawer || '-' }}</div>
        </template>

        <!-- 发票信息 -->
        <template slot-scope="{ row }" slot="invoiceInfo">
          <div>
            <div class="flex table-slot">
              <span class="label">发票类型:</span>
              <span class="value">{{ row.type || '-' }}</span>
            </div>

            <div class="flex table-slot">
              <span class="label">发票内容:</span>
              <span class="value" v-if="row.drawer_type === '3'">{{ row.content || '-' }}</span>
              <a v-else @click="checkGoodsDetail(row.id)">{{ row.content }}</a>
            </div>

            <div class="flex table-slot">
              <span class="label">申请金额:</span>
              <span class="value"
                >{{ row.amount ? `￥${row.amount}` : '-'
                }}<span v-if="Math.sign(row.red_amount) === -1"
                  >(<span style="color: red">¥{{ getRedMount(row, 'red_amount') }}</span
                  >)</span
                ></span
              >
            </div>

            <div class="flex table-slot">
              <span class="label">开票成功:</span>
              <!-- <span class="value">{{ row.amount_invoiced ? `￥${row.amount_invoiced}` : '-' }}</span> -->
              <span class="value"
                >{{ row.amount_invoiced ? `￥${row.amount_invoiced}` : '-'
                }}<span v-if="Math.sign(row.red_amount_invoiced) === -1"
                  >(<span style="color: red">¥{{ getRedMount(row, 'red_amount_invoiced') }}</span
                  >)</span
                ></span
              >
            </div>

            <div class="flex table-slot error" v-if="Number(row.fail_amount) > 0">
              <span class="label">开票失败:</span>
              <span class="value">{{ row.fail_amount ? `￥${row.fail_amount}` : '-' }}</span>
            </div>
          </div>
        </template>

        <template slot-scope="{ row }" slot="content">
          <div>
            <span v-if="row.drawer_type === '3'">
              {{ row.content }}
            </span>
            <span v-else>
              <a @click="checkGoodsDetail(row.id)">{{ row.content }}</a>
            </span>
          </div>
        </template>

        <!-- 开票信息 -->
        <template slot-scope="{ row }" slot="billingInfo">
          <div>
            <div class="flex table-slot">
              <span class="label">发票抬头:</span>
              <span class="value">{{ row.invoice.organization_name || '-' }}</span>
            </div>
            <div class="flex table-slot">
              <span class="label">公司税号:</span>
              <span class="value">{{ row.invoice.organization_code || '-' }}</span>
            </div>
            <div class="flex table-slot">
              <span class="label">注册地址:</span>
              <span class="value">{{ (row.invoice && row.invoice.reg_address_text) || '-' }}</span>
            </div>
            <div class="flex table-slot">
              <span class="label">注册电话:</span>
              <span class="value">{{ (row.invoice && row.invoice.reg_mobile) || '-' }}</span>
            </div>
            <div class="flex table-slot">
              <span class="label">开户银行:</span>
              <span class="value">{{ (row.invoice && row.invoice.bank) || '-' }}</span>
            </div>
            <div class="flex table-slot">
              <span class="label">银行账号:</span>
              <span class="value">{{ (row.invoice && row.invoice.bank_account) || '-' }}</span>
            </div>
          </div>
        </template>

        <!-- 蓝票状态 -->
        <template slot-scope="{ row }" slot="blue_status_desc">
          {{ row.blue_status_desc || '-' }}
        </template>

        <!-- 红票状态 -->
        <template slot-scope="{ row }" slot="red_status_desc">
          {{ row.red_status_desc || '-' }}
        </template>

        <!-- 申请时间 -->
        <template slot="apply_time" slot-scope="{ row }">
          <div>{{ row.apply_time | date_format('YYYY-MM-DD HH:mm:ss') }}</div>
        </template>

        <template slot-scope="{ row }" slot="action">
          <!-- 1:诊所/rxj，2：省公司， 3：供应商 -->
          <div class="flex flex-item-center">
            <!--            <div v-if="row.drawer_type == 1 && row.status == 'WAIT' && row.is_cli_joinin != 1">-->
            <!--              <Poptip-->
            <!--                confirm-->
            <!--                title="确定通过开票申请?"-->
            <!--                @on-ok="updateApplyStatus({ apply_codes: [row.apply_code], status: 'FINISHED' })"-->
            <!--              >-->
            <!--                <a style="margin-right: 6px">标记开票</a>-->
            <!--              </Poptip>-->
            <!--              <a @click="markRejected(row)">驳回申请</a>-->
            <!--            </div>-->

            <a v-if="row.ent_type === 'OPC' && row.status == 'REJECTED'" @click="goCreate(row.id)">修改</a>
            <!--            <a v-if="row.status === 'AUDIT_WAIT'" @click="toDetail(row.id, row.status)">审核</a>-->

            <!-- 线下开票 -->
            <!--            <div v-if="row.status == 'WAIT' && row.is_cli_joinin === '1'">-->
            <!--              <a style="margin-right: 6px" @click="toDetail(row.id, 'mark')">标记开票</a>-->
            <!--              <a-->
            <!--                v-if="row.blue_status !== 'FINISHED' && row.red_status !== 'FINISHED'"-->
            <!--                @click="toDetail(row.id, 'reject')"-->
            <!--                >驳回申请</a-->
            <!--              >-->
            <!--            </div>-->

            <a style="margin-left: 6px" @click="toDetail(row.id)">详情</a>
          </div>
        </template>
      </Table>
      <div class="block_20"></div>
      <KPage
        :total="total"
        :page-size="+queryFormData.pageSize"
        :current="+queryFormData.page"
        @on-change="handleCurrentChange"
        @on-page-size-change="handleSizeChange"
        style="text-align: center"
      />
    </div>

    <!-- 商品明细 -->
    <goods-modal :goods-visible.sync="goodsVisible" :id="checkedId" />

    <ReasonModal :reason-visible.sync="reasonVisible" :reject-invoice="rejectInvoice"></ReasonModal>
  </div>
</template>

<script>
import S from 'utils/util';
import search from '@/mixins/search';
import downloadExcel from '@/mixins/downloadExcel';
import invoiceApplySearch from '@/components/invoice-apply-search/invoice-apply-search';

const init_query_form_data = {
  page: 1,
  pageSize: 20,
  st: '',
  et: '',
  status: '',
  order_code: '', // 关联订单号
  apply_code: '', // 申请单号
  drawer_id: '', // 开票方id
  applicant_id: '', // 申请方id
  r: ''
};
export default {
  name: 'list',
  mixins: [search, downloadExcel],
  components: {
    invoiceApplySearch,
    ReasonModal: () => import('./components/ReasonModal'),
    GoodsModal: () => import('./components/GoodsDetail')
  },
  data() {
    return {
      apiName: 'getInvoiceList',
      downloadApiName: 'exportInvoiceList', // 导出列表
      timeRange: '',
      reasonVisible: false,
      queryFormData: {
        ...init_query_form_data
      },
      tableCols: [
        { title: '申请单号', key: 'apply_code', align: 'center', minWidth: 150 },
        { title: '申请方', slot: 'apply', align: 'center', minWidth: 200 },
        { title: '开票方', slot: 'drawer', align: 'center', minWidth: 100 },
        { title: '发票信息', slot: 'invoiceInfo', align: 'center', minWidth: 190 },
        { title: '开票信息', slot: 'billingInfo', align: 'center', minWidth: 240 },
        { title: '蓝票状态', slot: 'blue_status_desc', align: 'center', minWidth: 80 },
        { title: '红票状态', slot: 'red_status_desc', align: 'center', minWidth: 80 },
        { title: '申请时间', slot: 'apply_time', align: 'center', minWidth: 120 },
        { title: '开票时间', key: 'finished_at', align: 'center', minWidth: 120 },
        { title: '操作', slot: 'action', align: 'center', width: 150 }
      ],
      list: [],
      drawerList: [], // 开票方list
      applicantList: [], // 申请方list
      sendStr: '',
      // 商品明细
      goodsVisible: false,
      checkedId: '',
      rejectId: '',
      applyTimeRange: []
    };
  },
  computed: {
    getRedMount() {
      return (row, key) => {
        console.log(row);
        if (Math.sign(row[key]) === -1) {
          return row[key];
        }
      };
    }
  },
  watch: {},
  created() {
    this.queryFormData = S.merge(this.queryFormData, this.$route.query);
    this.submitQueryForm(true);
  },
  mounted() {
    this.init();
  },
  methods: {
    // 初始化数据
    init() {
      // 获取开票方枚举数据
      this.getInvoiceDrawerList();
    },

    // 导出明细
    exportInvoiceDetai() {
      const diff = this.$moment(this.queryFormData.et).diff(this.$moment(this.queryFormData.st), 'days');
      if (!this.queryFormData.st || diff > 62) {
        this.$Message.error('每次最多导出发票申请时间段为两个月');
        return false;
      }
      this.downloadExcel(this.queryFormData);
    },

    rejectInvoice(rejected_desc) {
      this.updateApplyStatus({ apply_codes: [this.rejectId], status: 'REJECTED', rejected_desc });
    },

    //列表内 标记开票
    updateApplyStatus({ apply_codes, status = this.statusDesc.success, rejected_desc }) {
      console.log('-> %c status  === %o ', 'font-size: 15px', status);
      const params = {
        apply_codes,
        status,
        rejected_desc
      };
      this.$api.UpdateInvoiceUpdateflag(params).then(
        res => {
          this.reasonVisible = false;
          this.submitQueryForm(true);
        },
        err => this.$Message.error(err.errmsg)
      );
    },

    checkGoodsDetail(id) {
      this.checkedId = id;
      this.goodsVisible = true;
    },

    markRejected(row) {
      console.log('-> %c row  === %o ', 'font-size: 15px', row);
      this.rejectId = row.apply_code;
      this.reasonVisible = true;
    },

    onStatusChange(status) {
      this.queryFormData.page = 1;
      this.queryFormData.status = status;
      this.submitQueryForm();
    },

    onResetSearch() {
      this.queryFormData = { ...init_query_form_data };
      this.timeRange = [];
      this.submitQueryForm();
      this.$refs['apply-search'].clear();
    },

    // *api 获取申请方枚举数据
    getInvoiceDrawerList() {
      this.$api.getInvoiceDrawerList().then(res => {
        this.drawerList = S.descToArrHandle(res.drawer, 'name');
      });
    },

    // 申请开票
    goCreate(id = '') {
      this.$router.push({
        path: '/finance/invoice/edit',
        query: {
          id
        }
      });
    },

    // 详情
    toDetail(id, type = '') {
      this.$router.push({
        path: '/finance/invoice/detail',
        query: {
          id: id,
          type
        }
      });
    }
  },
  beforeRouteUpdate(to, from, next) {
    this.queryFormData = S.merge(init_query_form_data, to.query);
    this.getTimeRange('finished_st', 'finished_et', 'applyTimeRange');
    this.getTimeRange();
    this.loadList();
    next();
  }
};
</script>

<style lang="less" scoped>
.table-slot {
  font-size: 12px;

  .label {
    width: 64px;
    text-align: right;
    margin-right: 4px;
  }

  .value {
    text-align: left;
    flex: 1;
  }
}

.error {
  color: red;
}
</style>
