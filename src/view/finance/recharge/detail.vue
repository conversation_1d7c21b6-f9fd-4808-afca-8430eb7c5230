<template>
  <div>
    <div>
      <div class="block-header header">充值状态</div>
      <div class="block-box">
        <div class="block-item">
          <div class="block-item-label">充值单号:</div>
          <div class="block-item-value">{{ detailInfo.code || '-' }}</div>
        </div>

        <div class="block-item">
          <div class="block-item-label">状态:</div>
          <div class="block-item-value">{{ detailInfo.status_desc || '-' }}</div>
        </div>

        <div class="block-item" v-if="detailInfo.status === 'REJECT'">
          <div class="block-item-label">驳回原因:</div>
          <div class="block-item-value" style="color: red">{{ detailInfo.reject_reason || '-' }}</div>
        </div>
      </div>
    </div>

    <div>
      <div class="block-header header">主体信息</div>
      <div class="block-box">
        <div class="block-item">
          <div class="block-item-label">付款主体:</div>
          <div class="block-item-value">{{ detailInfo.payer_name || '-' }}</div>
        </div>

        <div class="block-item">
          <div class="block-item-label">付款主体类型:</div>
          <div class="block-item-value">{{ detailInfo.payer_type_desc || '-' }}</div>
        </div>

        <div class="block-item" v-if="detailInfo.payer_type === 'CLI'">
          <div class="block-item-label">诊所类型:</div>
          <div class="block-item-value">{{ detailInfo.payer_clinic_type || '-' }}</div>
        </div>

        <div class="block-item" v-if="detailInfo.payer_type !== 'PERSON_C'">
          <div class="block-item-label">营业执照名称:</div>
          <div class="block-item-value">{{ detailInfo.payer_organization_name || '-' }}</div>
        </div>

        <div class="block-item" v-if="detailInfo.payer_type === 'PERSON_C'">
          <div class="block-item-label">个人ID:</div>
          <div class="block-item-value">{{ detailInfo.payer_id || '-' }}</div>
        </div>

        <div class="block-item" v-if="detailInfo.payer_type === 'PERSON_C'">
          <div class="block-item-label">个人客户类型:</div>
          <div class="block-item-value">
            <div v-if="detailInfo.custom_type_desc?.length">{{ detailInfo?.custom_type_desc?.join('、') }}</div>
            <div v-else>-</div>
          </div>
        </div>

        <div class="block-item">
          <div class="block-item-label">收款主体:</div>
          <div class="block-item-value">{{ detailInfo.payee_name || '-' }}</div>
        </div>
      </div>
    </div>

    <div>
      <div class="block-header header">支付信息</div>
      <div class="block-box">
        <div class="block-item">
          <div class="block-item-label">充值类型:</div>
          <div class="block-item-value">{{ detailInfo.recharge_type_desc || '-' }}</div>
        </div>

        <div class="block-item">
          <div class="block-item-label">支付方式:</div>
          <div class="block-item-value">{{ detailInfo.pay_type_desc || '-' }}</div>
        </div>

        <div class="block-item">
          <div class="block-item-label">金额:</div>
          <div class="block-item-value">{{ detailInfo.amount ? `${detailInfo.amount}元` : '-' }}</div>
        </div>

        <div class="block-item">
          <div class="block-item-label">收付款时间:</div>
          <div class="block-item-value">{{ detailInfo.account_time | date_format }}</div>
        </div>

        <div class="block-item">
          <div class="block-item-label">收付款凭证:</div>
          <div class="block-item-value">
            <material-picture
              v-if="detailInfo?.voucher_imgs?.length"
              v-model="detailInfo.voucher_imgs"
              :limit="9"
              :disabled="true"
            />
            <div v-else>-</div>
          </div>
        </div>

        <div class="block-item">
          <div class="block-item-label">备注:</div>
          <div class="block-item-value">{{ detailInfo.remark || '-' }}</div>
        </div>
      </div>
    </div>

    <div style="margin-bottom: 80px">
      <div class="block-header header">操作记录</div>
      <Table :loading="tableLoading" :columns="tableCols" :data="operator_list">
        <template slot-scope="{ row }" slot="create_time">
          <div>{{ row.create_time | date_format }}</div>
        </template>

        <template slot-scope="{ row }" slot="operator_name">
          <div>{{ row.operator_name || '-' }}</div>
        </template>
      </Table>
    </div>

    <div class="fixed-bottom-wrapper">
      <back-button class="mr10"></back-button>
      <Button
        class="mr10"
        v-if="$route.query.type !== 'detail' && detailInfo.status === 'WAIT_AUDIT'"
        type="error"
        @click="showRefuseModal"
        >审核驳回
      </Button>
      <Button
        v-if="$route.query.type !== 'detail' && detailInfo.status === 'WAIT_AUDIT'"
        type="primary"
        @click="passCheck"
        >审核通过
      </Button>
    </div>
    <refuse-reason-modal :visible.sync="refuseModalVisible" :auditSalesOrder="submitRefuseReason"></refuse-reason-modal>
  </div>
</template>

<script>
import MaterialPicture from '@/components/MaterialCenter/MaterialPicture.vue';
import RefuseReasonModal from '@/components/RefuseReasonModal';

export default {
  name: 'detail',
  components: { MaterialPicture, RefuseReasonModal },
  mixins: [],
  data() {
    return {
      tableLoading: false,
      tableCols: [
        { title: '时间', slot: 'create_time', align: 'center' },
        { title: '操作主体', key: 'source', align: 'center' },
        { title: '操作人', slot: 'operator_name', align: 'center' },
        { title: '操作记录', key: 'remark', align: 'center' }
      ],
      detailInfo: {
        voucher_imgs: []
      },
      operator_list: [],
      refuseModalVisible: false
    };
  },
  computed: {},
  watch: {},
  created() {},
  mounted() {
    this.init();
  },
  methods: {
    init() {
      this.getARtDetail();
      this.getARGetLog();
    },
    submitRefuseReason(val, reason) {
      this.review('0', reason);
    },
    // 审核驳回
    showRefuseModal() {
      this.refuseModalVisible = true;
    },
    // 审核按钮
    passCheck() {
      this.$Modal.confirm({
        title: '通过审核',
        content: '您确定要通过该审核吗？',
        onOk: () => {
          this.review('1');
        }
      });
    },

    /**
     * @description 审核/驳回的接口
     * @param { action } 审核的状态
     * @param { reason } 驳回的原因
     * */
    review(status, reason) {
      const params = {
        id: this.$route.query.id,
        status,
        reject_reason: ''
      };
      let isPass = true;
      if (reason) {
        params.reject_reason = reason;
        isPass = false;
      }
      this.$api.getARGetAudit(params).then(
        res => {
          this.$Message.success(`${isPass ? '审核通过成功' : '审核驳回成功'}`);
          this.init();
        },
        err => this.$Message.error(err.errmsg)
      );
    },

    getARtDetail() {
      let params = {
        id: this.$route.query.id
      };
      this.$api.getARtDetail(params).then(
        res => {
          this.detailInfo = res.detail;
        },
        err => this.$Message.error(err.errmsg)
      );
    },
    getARGetLog() {
      this.tableLoading = true;
      let params = {
        id: this.$route.query.id,
        page: 1,
        pageSize: 1000
      };
      this.$api
        .getARGetLog(params)
        .then(
          res => {
            this.tableLoading = false;
            this.operator_list = res.list;
          },
          err => this.$Message.error(err.errmsg)
        )
        .finally(() => (this.tableLoading = false));
    }
  }
};
</script>

<style lang="less" scoped>
.header {
  font-weight: 600;
  margin-bottom: 30px;
}

.block-box {
  margin-left: 20px;
  display: flex;
  flex-wrap: wrap;

  .block-item {
    display: flex;
    align-items: flex-start;
    width: 30%;
    margin-bottom: 30px;

    &:nth-child(3n) {
      width: 40%;
    }

    .block-item-label {
      font-weight: 500;
      font-size: 12px;
      color: #999999;
      line-height: 17px;
      min-width: fit-content;
      margin-left: 10px;
    }

    .block-item-value {
      font-weight: 500;
      font-size: 12px;
      color: #333333;
      line-height: 17px;
      margin-left: 6px;
    }
  }
}

.mr10 {
  margin-right: 10px;
}
</style>
