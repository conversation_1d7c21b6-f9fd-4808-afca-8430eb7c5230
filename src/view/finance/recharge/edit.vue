<template>
  <div class="company-detail-wrapper">
    <div class="block-header mt15">主体信息</div>
    <div class="company-form">
      <Form ref="expensesForm" :model="formValidate" :rules="ruleValidate" :label-width="140" label-colon>
        <FormItem label="充值单号" v-if="$route.query.id" class="mb6">
          <span>{{ detailInfo.code }}</span>
        </FormItem>

        <FormItem label="状态" v-if="$route.query.id" class="mb6">
          <span>{{ detailInfo.status_desc }}</span>
          <span style="color: red; margin-left: 6px" v-if="detailInfo.status === 'REJECT'"
            >( 驳回原因：{{ detailInfo.reject_reason || '-' }} )</span
          >
        </FormItem>

        <FormItem label="选择付款主体类型" class="mb6">
          <RadioGroup v-model="formValidate.payer_type" @on-change="payerTypeChange">
            <Radio
              v-for="(item, index) in payer_type_list"
              :key="index"
              :label="item.value"
              :disabled="!!$route.query.id"
              >{{ item.label }}
            </Radio>
          </RadioGroup>
        </FormItem>

        <FormItem label="付款主体" class="mb6" prop="payer_id">
          <Select
            v-model="formValidate.payer_id"
            filterable
            placeholder="请选择付款主体"
            :disabled="!!$route.query.id"
            @on-change="payerChange"
          >
            <Option
              v-for="(item, index) in current_payer_list"
              :key="index"
              label-in-value
              :value="item.apply.id"
              :label="item.apply.name"
              >{{ item.apply.name }}
            </Option>
          </Select>
        </FormItem>

        <FormItem label="营业执照名称" class="mb6" v-if="formValidate.payer_type !== 'PERSON_C'">
          <div>{{ organization_name || '-' }}</div>
        </FormItem>
        <FormItem label="诊所类型" class="mb6" v-if="formValidate.payer_type === 'CLI'">
          <div>{{ clinic_type || '-' }}</div>
        </FormItem>
        <FormItem label="个人ID" class="mb6" v-if="formValidate.payer_type === 'PERSON_C'">
          <div>{{ personal_id || '-' }}</div>
        </FormItem>
        <FormItem label="个人客户类型" class="mb6" v-if="formValidate.payer_type === 'PERSON_C'">
          <div v-if="custom_type_desc.length">{{ custom_type_desc.join('、') }}</div>
          <div v-else>-</div>
        </FormItem>

        <FormItem label="收款主体" required>
          <Input v-model="payee_name" type="text" disabled placeholder="请选择收款主体" />
        </FormItem>

        <div class="block-header">支付信息</div>

        <FormItem label="支付方式" required>
          <Select v-model="pay_type" filterable placeholder="请选择支付方式" disabled>
            <Option v-for="(item, key) in payTypeDesc" :key="key" :value="item.id">{{ item.desc }}</Option>
          </Select>
        </FormItem>

        <FormItem label="金额" prop="amount">
          <InputNumber
            v-model="formValidate.amount"
            :active-change="false"
            :min="0"
            :max="********.99"
            :precision="2"
            placeholder="请输入金额"
            style="width: 100%"
          ></InputNumber>
        </FormItem>
        <FormItem label="收付款时间" prop="account_time">
          <DatePicker
            type="datetime"
            clearable
            format="yyyy-MM-dd HH:mm:ss"
            :options="disabledTime"
            placeholder="请选择实际的收款时间或付款时间"
            style="width: 100%"
            v-model="formValidate.account_time"
            @on-change="formValidate.account_time = $event"
            class="time-range"
          ></DatePicker>
        </FormItem>

        <FormItem label="收付款凭证" prop="voucher_imgs">
          <Picture v-model="formValidate.voucher_imgs" :limit="9" />
          <span style="display: block; margin-top: -10px"
            >(请上传收款凭证或付款凭证，例如银行到账截图或打款截图等)</span
          >
        </FormItem>

        <FormItem label="备注">
          <Input
            v-model="formValidate.remark"
            type="textarea"
            :autosize="{ minRows: 2, maxRows: 5 }"
            placeholder="请输入备注"
          />
        </FormItem>
      </Form>

      <div class="fixed-bottom-wrapper">
        <back-button></back-button>
        <Button
          style="margin-left: 8px"
          type="primary"
          @click="handleSubmit()"
          v-if="$route.query.type != 'detail'"
          :loading="saveLoading"
          >保存
        </Button>
      </div>
    </div>
  </div>
</template>

<script>
import Picture from '@/components/upload/picture';
import S from '@/utils/util';
import moment from 'moment';

export default {
  name: 'earnings-edit',
  components: { Picture },
  data() {
    const fee_validator = (rule, value, callback) => {
      if (value === '' || value === null) {
        callback(new Error('请输入金额'));
      } else {
        if (value <= 0) {
          callback(new Error('金额不能小于等于0'));
        }
        callback();
      }
    };

    const time_validator = (rule, value, callback) => {
      const stamp = moment(value).valueOf();
      const current_stamp = moment().valueOf();
      if (value == '' || value == null) {
        callback(new Error('请选择收付款时间'));
      } else if (stamp > current_stamp) {
        callback(new Error('收付款时间不能大于当前时间'));
      } else {
        callback();
      }
    };

    return {
      disabledTime: {
        disabledDate(date) {
          // 获取当前日期并向前推7天
          const date7DaysAgo = moment().subtract(15, 'days').format('YYYY-MM-DD');
          return (date && date.valueOf() > Date.now()) || (date && date.valueOf() < moment(date7DaysAgo).valueOf());
        }
      },

      formValidate: {
        payer_type: 'CLI', // 付款主体类型
        payer_id: '', // 付款主体id
        amount: null, // 金额
        account_time: '', // 到账时间，时间戳
        voucher_imgs: [], // 上传凭证
        remark: '' // 备注
      },
      ruleValidate: {
        payer_id: [{ required: true, message: '请选择付款主体', trigger: 'change' }],
        amount: [{ required: true, validator: fee_validator, trigger: 'blur' }],
        account_time: [{ required: true, validator: time_validator, trigger: 'change', type: 'date' }],
        voucher_imgs: [{ required: true, message: '请上传收付款凭证', trigger: 'change', type: 'array' }]
      },

      payer_name: '', // 跟id捆绑，需要传给后端
      clinic_type: '', // 诊所类型
      organization_name: '', // 营业执照名称
      partner_name: '', // 所属城市合伙人
      personal_id: '', // 个人id

      payer_com: [],

      payee_name: '',
      pay_type: 'BANK',
      payTypeDesc: [],
      saveLoading: false,
      detailInfo: {},

      payer_type_list: [
        { label: '诊所', value: 'CLI' },
        { label: '个人客户', value: 'PERSON_C' },
        { label: '外部公司', value: 'OUTSIDE' },
        { label: '直营运营中心', value: 'OPC' }
      ],
      payer_list: {}, // 付款主体数据
      custom_type_desc: [] // 个人客户类型
    };
  },
  computed: {
    current_payer_list() {
      if (this.formValidate.payer_type) {
        return this.payer_list[this.formValidate.payer_type] || [];
      } else {
        return [];
      }
    }
  },
  watch: {},
  created() {},
  mounted() {
    this.init();
  },
  methods: {
    handleQuickFill() {
      const payer_type = this.$route.query.payer_type;
      const payer_id = this.$route.query.payer_id;
      if (payer_type && payer_id) {
        this.formValidate.payer_type = payer_type;
        this.formValidate.payer_id = payer_id;
        this.payerChange(payer_id);
        if (['PERSON_C', 'OUTSIDE'].includes(this.formValidate.payer_type)) {
          let apply = this.payer_com.apply || {};
          this.payee_name = apply.name;
        } else {
          let parent = this.payer_com.parent || {};
          this.payee_name = parent.name;
        }
      }
    },
    payerTypeChange() {
      if (['PERSON_C', 'OUTSIDE'].includes(this.formValidate.payer_type)) {
        let apply = this.payer_com.apply || {};
        this.payee_name = apply.name;
      } else {
        let parent = this.payer_com.parent || {};
        this.payee_name = parent.name;
      }

      this.formValidate.payer_id = '';
      this.payer_name = '';
      this.clinic_type = '';
      this.organization_name = '';
      this.partner_name = '';
      this.personal_id = '';
      this.custom_type_desc = [];
    },
    // 选择付款主体
    payerChange(val) {
      let c_item = this.current_payer_list.filter(item => item?.apply?.id == val);
      let apply = c_item[0]?.apply || {};
      this.payer_name = apply.name;
      const payer_type = this.formValidate.payer_type;
      this.clinic_type = apply['clinic_type_desc'];
      this.organization_name = apply['organization_name'];
      this.partner_name = apply['partner_name'];

      if (payer_type === 'PERSON_C') {
        this.personal_id = apply['id'];
        this.custom_type_desc = apply['custom_type_desc'] || [];
      }
    },
    init() {
      // 获取枚举
      this.getAROptions();
      if (this.$route.query.id) {
        this.getARtDetail();
      }
    },
    // 保存事件
    handleSubmit() {
      this.$refs['expensesForm'].validate(valid => {
        if (valid) {
          this.getARInEdit();
        } else {
          this.$Message.error('请完善表单信息');
        }
      });
    },

    getARInEdit() {
      this.saveLoading = true;
      let params = {
        id: this.$route.query.id,
        payer_name: this.payer_name,
        ...this.formValidate,
        account_time: moment(this.formValidate.account_time).format('yyyy-MM-DD HH:mm:ss')
      };
      this.$api
        .getARInEdit(params)
        .then(
          res => {
            this.saveLoading = false;
            this.$Message.success(this.$route.query.id ? '编辑成功' : '创建成功');
            this.$router.push('/finance/recharge/list');
          },
          err => this.$Message.error(err.errmsg)
        )
        .finally(() => (this.saveLoading = false));
    },

    getARtDetail() {
      let params = {
        id: this.$route.query.id
      };
      this.$api.getARtDetail(params).then(
        res => {
          this.detailInfo = res.detail;
          this.formValidate.payer_type = res.detail.payer_type;
          this.formValidate.amount = Number(res.detail.amount || 0);
          this.formValidate.account_time = moment(res.detail.account_time * 1000 || '').format('yyyy-MM-DD HH:mm:ss');
          this.formValidate.voucher_imgs = res.detail.voucher_imgs;
          this.formValidate.remark = res.detail.remark;

          this.organization_name = res.detail.payer_organization_name;
          this.clinic_type = res.detail.payer_clinic_type;
          this.partner_name = res.detail.partner_name;

          this.personal_id = res.detail.id;
          this.formValidate.payer_id = res.detail.payer_id;
          this.payer_name = res.detail.payer_name;
          this.custom_type_desc = res.detail.custom_type_desc || [];
        },
        err => this.$Message.error(err.errmsg)
      );
    },

    getAROptions() {
      this.$api.getAROptions().then(
        res => {
          this.payee_name = res.payer_com?.parent?.name;

          this.payer_com = res.payer_com;

          this.payTypeDesc = S.descToArrHandle(res.payTypeDesc);
          this.payer_list = res.payer_list;
          this.handleQuickFill();
        },
        err => this.$Message.error(err.errmsg)
      );
    }
  }
};
</script>

<style scoped lang="less">
.company-detail-wrapper {
  margin-bottom: 100px;

  .company-form {
    margin-top: 20px;
  }
}

::v-deep .ivu-form-item {
  width: 50%;
}

.mt15 {
  margin-top: 15px;
}

.mb6 {
  margin-bottom: 6px;
}
</style>
