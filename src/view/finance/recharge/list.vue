<template>
  <div class="f_order-wrapper">
    <Form inline @submit.native.prevent @keyup.enter.native="onSearch">
      <div class="flex flex-item-between">
        <Row>
          <FormItem>
            <Input v-model="queryFormData.code" placeholder="请输入单号" clearable />
          </FormItem>

          <FormItem>
            <surning-search
              :p_ent_type="queryFormData.payer_type"
              routerKey="payer_id"
              :extra-params="{
                fill_opc: 1
              }"
              :showType="true"
              :typeList="typeList"
              :showOutside="true"
              ref="nursing-search"
              v-model="queryFormData.payer_id"
              @getType="type => (queryFormData.payer_type = type)"
              placeholder="输入付款方"
            ></surning-search>
          </FormItem>

          <FormItem>
            <DatePicker
              type="daterange"
              clearable
              :options="disabledTime"
              format="yyyy-MM-dd"
              placeholder="请选择创建时间"
              v-model="timeRange"
              @on-change="times => handleTimeChange(times)"
              class="time-range"
            ></DatePicker>
          </FormItem>
        </Row>
      </div>

      <Row>
        <FormItem>
          <payment-license-search
            ref="license-search"
            v-model="queryFormData.organ_payer_id"
            :default_params="{
              fill_outside: 1,
              fill_opc: 1
            }"
            placeholder="营业执照名称(付款方)"
            @getOrganEntType="val => (queryFormData.organ_ent_type = val)"
          >
          </payment-license-search>
        </FormItem>

        <FormItem>
          <clinic-type-search
            v-model="queryFormData.clinic_type"
            :clinic_direct_type="queryFormData.clinic_direct_type"
            :directTypeDesc="directTypeDesc"
            :typeDesc="clinicTypeDesc"
            @directChange="directChange"
          ></clinic-type-search>
        </FormItem>
        <Col>
          <FormItem style="text-align: left">
            <Button type="primary" class="mr10" @click="onSearch">筛选</Button>
            <Button type="default" class="mr10" @click="onResetSearch">重置</Button>
            <Button :loading="downloadLoading" type="default" @click="downloadExcel(queryFormData)">导出</Button>
          </FormItem>
        </Col>
      </Row>
    </Form>
    <div class="table-wrapper">
      <div class="panel-nav flex flex-item-between">
        <div>
          <a class="nav" :class="{ active: !queryFormData.status }" @click.prevent.capture="onStatusChange('')">
            全部
          </a>
          <a
            v-for="(item, index) in statusDesc"
            :key="index"
            class="nav"
            :class="{ active: $route.query.status === item.id }"
            @click.prevent.capture="onStatusChange(item.id)"
          >
            {{ item.desc }}
            <Tag :color="getTagColor(item.id)">{{ list_count[item.id]?.num }}</Tag>
          </a>
        </div>
        <Button type="primary" @click="onCreated"> 创建充值单 </Button>
      </div>
      <Table :loading="tableLoading" :columns="tableCols" :data="list" :height="$store.state.app.clientHeight - 330">
        <template slot="payer_organization_name" slot-scope="{ row }">
          {{ row.payer_organization_name || '-' }}
        </template>

        <template slot="payer_type_desc" slot-scope="{ row }">
          <div>{{ row.payer_type_desc || '-' }}</div>
          <div v-if="row.payer_type === 'CLI' && row.payer_clinic_type">{{ row.payer_clinic_type || '-' }}</div>
        </template>

        <template slot-scope="{ row }" slot="account_time">
          {{ row.account_time | date_format }}
        </template>

        <template slot-scope="{ row }" slot="create_time">
          {{ row.create_time | date_format }}
        </template>

        <template slot-scope="{ row }" slot="action">
          <a class="mr10" v-if="row.status === 'REJECT'" @click="updateInfo(row, true)">修改</a>
          <a @click="jumpToDetail(row, 'detail')">详情</a>
        </template>
      </Table>
      <div class="block_20"></div>
      <KPage
        :total="total"
        :page-size="+queryFormData.pageSize"
        :current="+queryFormData.page"
        @on-change="handleCurrentChange"
        @on-page-size-change="handleSizeChange"
        style="text-align: center"
      />
    </div>
  </div>
</template>

<script>
import S from 'utils/util';
import downloadExcel from '@/mixins/downloadExcel';
import search from '@/mixins/search';
import surningSearch from '@/components/surning-search/surning-search';
import clinicTypeSearch from '@/components/remote-search/clinic-type-search.vue';
import paymentLicenseSearch from '@/components/remote-search/payment-license-search.vue';

const init_query_form_data = {
  page: 1,
  pageSize: 20,
  code: '', // 单号
  payer_type: '', // 付款方的类型
  payer_id: '', // 付款方id
  st: '',
  et: '',
  organ_payer_id: '', // 营业执照id
  organ_ent_type: '', // 营业执照类型
  clinic_type: '', // 诊所类型
  clinic_direct_type: '', // 诊所子类型
  status: '', // 审核类型
  r: ''
};
export default {
  name: 'list',
  mixins: [search, downloadExcel],
  components: { surningSearch, clinicTypeSearch, paymentLicenseSearch },
  data() {
    return {
      downloadApiName: 'getARExportList',
      apiName: 'getARList', // 获取明细列表
      queryFormData: { ...init_query_form_data },
      timeRange: [],
      disabledTime: {
        disabledDate(date) {
          return date && date.valueOf() > Date.now();
        }
      },
      tableCols: [
        { title: '充值单号', key: 'code', align: 'center', minWidth: 100 },
        { title: '付款方', key: 'payer_name', align: 'center', minWidth: 100 },
        { title: '营业执照名称(付款方)', slot: 'payer_organization_name', align: 'center', width: 150 },
        { title: '付款方类型', slot: 'payer_type_desc', align: 'center', minWidth: 100 },
        { title: '收款方', key: 'payee_name', align: 'center', minWidth: 100 },
        { title: '支付方式', key: 'pay_type_desc', align: 'center', minWidth: 100 },
        { title: '金额', key: 'amount', align: 'center', minWidth: 100 },
        { title: '收付款时间', slot: 'account_time', align: 'center', minWidth: 100 },
        { title: '创建时间', slot: 'create_time', align: 'center', minWidth: 100 },
        { title: '状态', key: 'status_desc', align: 'center', minWidth: 60 },
        { title: '操作', slot: 'action', align: 'center', minWidth: 100 }
      ],

      clinicTypeDesc: [],
      directTypeDesc: [],
      typeList: [
        { label: '诊所', value: 'CLI' },
        { label: '直营运营中心', value: 'OPC' },
        { label: '个人客户', value: 'PERSON_C' },
        { label: '外部公司', value: 'OUTSIDE' }
      ],
      statusDesc: [],
      typeDesc: [],
      // 付款方默认参数
      default_params: {
        fill_cp: 1,
        fill_com: 1,
        fill_person_c: 1
      },
      list_count: {}
    };
  },
  computed: {
    getTagColor(type) {
      return type => {
        switch (type) {
          case 'WAIT_AUDIT': // 待财务审核
            return 'warning';
          case 'REJECT': // 已驳回
            return 'error';
          case 'PASS': // 已通过
            return 'success';
          default: // 待平台审核
            return 'default';
        }
      };
    }
  },
  watch: {},
  created() {
    this.queryFormData = S.merge(this.queryFormData, this.$route.query);
    this.getAROptions();
    this.getClinicOptions();
    this.submitQueryForm(true);
  },
  mounted() {},
  methods: {
    handleList(data) {
      this.list_count = data.status_total;
    },
    jumpToDetail(row, type) {
      this.$router.push({
        path: '/finance/recharge/detail',
        query: {
          id: row.id,
          type
        }
      });
    },
    directChange(val) {
      this.$set(this.queryFormData, 'clinic_direct_type', val);
    },

    // 创建充值单
    onCreated() {
      this.$router.push('/finance/recharge/edit');
    },

    onStatusChange(status) {
      this.queryFormData.page = 1;
      this.queryFormData.status = status;
      this.submitQueryForm();
    },
    onResetSearch() {
      this.queryFormData = { ...init_query_form_data };
      this.timeRange = [];
      this.submitQueryForm();
      this.$refs['nursing-search'].clear();
    },

    updateInfo(row, isEdit) {
      // 支出
      if (row.type === 'OUT_OPC') {
        this.$router.push({
          path: '/finance/recharge/edit',
          query: {
            id: row.id
          }
        });
      }

      // 收入
      if (row.type === 'IN_OPC') {
        this.$router.push({
          path: '/finance/recharge/edit',
          query: {
            id: row.id
          }
        });
      }
    },

    getClinicOptions() {
      this.$api.getClinicOptions().then(res => {
        this.directTypeDesc = S.descToArrHandle(res.directTypeDesc);
        this.clinicTypeDesc = S.descToArrHandle(res.typeDesc);
      });
    },

    // *api 获取枚举类型
    getAROptions() {
      this.$api.getAROptions().then(res => {
        this.typeDesc = S.descToArrHandle(res.typeDesc);
        this.statusDesc = S.descToArrHandle(res.statusDesc);
      });
    }
  },
  beforeRouteUpdate(to, from, next) {
    // S.log(to.params['nav-stack-key-dir'], 'nav-stack-key-dir')
    this.queryFormData = S.merge(init_query_form_data, to.query);
    console.log('this.queryFormData', this.queryFormData);
    this.getTimeRange();
    this.loadList();
    next();
  }
};
</script>

<style scoped lang="less">
.mr10 {
  margin-right: 10px;
}
</style>
