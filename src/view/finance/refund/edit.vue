<template>
  <div class="company-detail-wrapper">
    <h3>申请预付款余额退款</h3>
    <div class="block-header mt15">主体信息</div>
    <div class="company-form">
      <Form ref="expensesForm" :model="formValidate" :rules="ruleValidate" :label-width="140" label-colon>
        <FormItem label="充值单号" v-if="$route.query.id" class="mb6">
          <span>{{ detailInfo.code }}</span>
        </FormItem>

        <FormItem label="状态" v-if="$route.query.id" class="mb6">
          <span>{{ detailInfo.status_desc }}</span>
          <span style="color: red; margin-left: 6px" v-if="detailInfo.status === 'REJECT'"
            >( 驳回原因：{{ detailInfo.reject_reason || '-' }} )</span
          >
        </FormItem>

        <FormItem label="选择退款主体类型" class="mb6">
          <RadioGroup v-model="formValidate.ent_type" @on-change="entTypeChange">
            <Radio v-for="(item, index) in ent_type_list" :key="index" :label="item.value" :disabled="!!$route.query.id"
              >{{ item.label }}
            </Radio>
          </RadioGroup>
        </FormItem>
        <FormItem label="申请退款主体" class="mb6" prop="ent_id">
          <Select
            v-model="formValidate.ent_id"
            filterable
            placeholder="请选择申请退款主体"
            @on-change="entChange"
            :disabled="!!$route.query.id"
          >
            <Option
              v-for="(item, index) in current_ent_list"
              :key="index"
              :value="item.apply.id"
              :label="item.apply.name"
              >{{ item.apply.name }}
            </Option>
          </Select>
        </FormItem>

        <FormItem label="营业执照名称" class="mb6" v-if="formValidate.ent_type !== 'PERSON_C'">
          <div>{{ organization_name || '-' }}</div>
        </FormItem>
        <FormItem label="诊所类型" class="mb6" v-if="formValidate.ent_type === 'CLI'">
          <div>{{ clinic_type || '-' }}</div>
        </FormItem>
        <FormItem label="个人ID" class="mb6" v-if="formValidate.ent_type === 'PERSON_C'">
          <div>{{ personal_id || '-' }}</div>
        </FormItem>
        <FormItem label="个人客户类型" class="mb6" v-if="formValidate.ent_type === 'PERSON_C'">
          <div v-if="custom_type_desc.length">{{ custom_type_desc.join('、') }}</div>
          <div v-else>-</div>
        </FormItem>

        <FormItem label="退款主体" required>
          <Input v-model="refund_name" type="text" disabled placeholder="请选择退款主体" />
        </FormItem>

        <div class="block-header">退款信息</div>

        <FormItem label="退款原因" prop="refund_reason">
          <Input
            v-model="formValidate.refund_reason"
            type="textarea"
            :autosize="{ minRows: 2, maxRows: 5 }"
            placeholder="请输入退款原因"
          />
        </FormItem>

        <FormItem label="退款方式" required>
          <Select v-model="pay_type" filterable placeholder="请选择退款方式" disabled>
            <Option v-for="(item, key) in payTypeDesc" :key="key" :value="item.id">{{ item.desc }}</Option>
          </Select>
        </FormItem>

        <FormItem label="收付款时间" prop="account_time">
          <DatePicker
            type="datetime"
            clearable
            format="yyyy-MM-dd HH:mm:ss"
            :options="disabledTime"
            placeholder="请选择实际的收款时间或付款时间"
            style="width: 100%"
            v-model="formValidate.account_time"
            @on-change="formValidate.account_time = $event"
            class="time-range"
          ></DatePicker>
        </FormItem>

        <FormItem label="退款金额" prop="amount">
          <div style="position: relative">
            <InputNumber
              v-model="formValidate.amount"
              :active-change="false"
              :min="0"
              :max="********.99"
              :precision="2"
              placeholder="请输入退款金额"
              style="width: 100%"
            ></InputNumber>
            <div class="money-text" v-if="!!formValidate.ent_id && !!formValidate.account_time">
              <div>收付款时间当时的预付款可用余额：</div>
              <Spin v-if="getBalanceLoading"></Spin>
              <template v-else>
                <k-link
                  v-if="isHasBalanceDetailPage"
                  :to="{ path: '/finance/balance/detail', query: { id: wallet_id } }"
                  target="_blank"
                  >{{ balance }}</k-link
                >
                <div v-else>{{ balance }}</div>
              </template>
            </div>
          </div>
        </FormItem>

        <FormItem label="收付款凭证" prop="voucher_imgs">
          <Picture v-model="formValidate.voucher_imgs" :limit="9" />
          <span style="display: block; margin-top: -10px"
            >(请上传收款凭证或付款凭证，例如银行到账截图或打款截图等)</span
          >
        </FormItem>

        <FormItem label="备注">
          <Input
            v-model="formValidate.remark"
            type="textarea"
            :autosize="{ minRows: 2, maxRows: 5 }"
            placeholder="请输入备注"
          />
        </FormItem>
      </Form>

      <div class="fixed-bottom-wrapper">
        <back-button></back-button>
        <Button
          style="margin-left: 8px"
          type="primary"
          @click="handleSubmit()"
          v-if="$route.query.type != 'detail'"
          :loading="saveLoading"
          >保存
        </Button>
      </div>
    </div>
  </div>
</template>

<script>
import Picture from '@/components/upload/picture';
import S from '@/utils/util';
import moment from 'moment';

export default {
  name: 'edit',
  components: { Picture },
  data() {
    const fee_validator = (rule, value, callback) => {
      if (value === '' || value === null) {
        callback(new Error('请输入金额'));
      } else {
        if (value <= 0) {
          callback(new Error('金额不能小于等于0'));
        }
        callback();
      }
    };

    const time_validator = (rule, value, callback) => {
      const stamp = moment(value).valueOf();
      const current_stamp = moment().valueOf();
      if (value == '' || value == null) {
        callback(new Error('请选择收付款时间'));
      } else if (stamp > current_stamp) {
        callback(new Error('收付款时间不能大于当前时间'));
      } else {
        callback();
      }
    };

    return {
      isHasBalanceDetailPage: S.rootPageCheck('/finance/balance/detail'),
      disabledTime: {
        disabledDate(date) {
          // 获取当前日期并向前推7天
          const date7DaysAgo = moment().subtract(15, 'days').format('YYYY-MM-DD');
          return (date && date.valueOf() > Date.now()) || (date && date.valueOf() < moment(date7DaysAgo).valueOf());
        }
      },

      formValidate: {
        ent_type: 'CLI', // 申请退款主体类型
        ent_id: '', // 退款主体id
        refund_reason: '', // 退款原因
        amount: null, // 金额
        account_time: '', // 到账时间，时间戳
        voucher_imgs: [], // 上传凭证
        remark: '' // 备注
      },
      ruleValidate: {
        ent_id: [{ required: true, message: '请选择退款主体', trigger: 'change' }],
        refund_reason: [{ required: true, message: '请输入退款原因', trigger: 'change' }],
        amount: [{ required: true, validator: fee_validator, trigger: 'change' }],
        account_time: [{ required: true, validator: time_validator, trigger: 'change', type: 'date' }],
        voucher_imgs: [{ required: true, message: '请上传收付款凭证', trigger: 'change', type: 'array' }]
      },

      ent_name: '', // 跟id捆绑，需要传给后端
      clinic_type: '', // 诊所类型
      organization_name: '', // 营业执照名称
      personal_id: '', // 个人id

      refund_name: '',
      pay_type: 'BANK',
      payTypeDesc: [],
      saveLoading: false,
      detailInfo: {},

      ent_type_list: [
        { label: '诊所', value: 'CLI' },
        { label: '个人客户', value: 'PERSON_C' },
        { label: '外部公司', value: 'OUTSIDE' },
        { label: '直营运营中心', value: 'OPC' }
      ],
      ent_list: {}, // 申请退款主体数据
      ent_com: {},

      getBalanceLoading: false, // 获取预付款余额的loading
      balance: '', // 预付款余额
      wallet_id: '', // 预付款余额id
      custom_type_desc: [] // 个人客户类型
    };
  },
  computed: {
    current_ent_list() {
      if (this.formValidate.ent_type) {
        return this.ent_list[this.formValidate.ent_type] || [];
      } else {
        return [];
      }
    }
  },
  watch: {
    'formValidate.ent_id': {
      handler() {
        this.getAWentBalance();
      }
    },
    'formValidate.account_time': {
      handler() {
        this.getAWentBalance();
      }
    }
  },
  created() {},
  mounted() {
    this.init();
  },
  methods: {
    handleQuickRefund() {
      const ent_type = this.$route.query.ent_type;
      const ent_id = this.$route.query.ent_id;
      if (ent_type && ent_id) {
        this.formValidate.ent_type = ent_type;
        this.formValidate.ent_id = ent_id;
        this.entChange(ent_id);
      }
    },
    entTypeChange(val) {
      this.formValidate.ent_id = '';
      this.ent_name = '';
      this.clinic_type = '';
      this.organization_name = '';
      this.personal_id = '';
      this.balance = '';
      this.custom_type_desc = [];

      this.getRefundName();
    },
    getRefundName() {
      if (this.formValidate.ent_type === 'OPC' || this.formValidate.ent_type == 'CLI') {
        this.refund_name = this.ent_com?.parent?.name;
      } else {
        this.refund_name = this.ent_com?.apply?.name;
      }
    },
    // 选择申请退款主体
    entChange(val) {
      let c_item = this.current_ent_list.filter(item => item?.apply?.id == val);
      let apply = c_item[0]?.apply || {};
      this.ent_name = apply.name;
      const ent_type = this.formValidate.ent_type;
      this.clinic_type = apply['clinic_type_desc'];
      this.organization_name = apply['organization_name'];

      if (ent_type === 'PERSON_C') {
        this.personal_id = apply['id'];
        this.custom_type_desc = apply['custom_type_desc'] || [];
      }
    },
    init() {
      // 获取枚举
      this.getARRefundOptions();
      if (this.$route.query.id) {
        this.getARRefundDetail();
      } else {
        this.getAWentBalance();
      }
    },
    // 保存事件
    handleSubmit() {
      this.$refs['expensesForm'].validate(valid => {
        if (valid) {
          this.getARRefundEdit();
        } else {
          this.$Message.error('请完善表单信息');
        }
      });
    },

    getARRefundEdit() {
      this.saveLoading = true;
      let params = {
        id: this.$route.query.id,
        ent_name: this.ent_name,
        ...this.formValidate,
        account_time: moment(this.formValidate.account_time).format('yyyy-MM-DD HH:mm:ss')
      };
      this.$api
        .getARRefundEdit(params)
        .then(
          res => {
            this.saveLoading = false;
            this.$Message.success(this.$route.query.id ? '编辑成功' : '创建成功');
            this.$router.push('/finance/refund/list');
          },
          err => this.$Message.error(err.errmsg)
        )
        .finally(() => (this.saveLoading = false));
    },

    getARRefundDetail() {
      let params = {
        id: this.$route.query.id
      };
      this.$api.getARRefundDetail(params).then(
        res => {
          this.detailInfo = res.detail;
          this.formValidate.refund_reason = res.detail.refund_reason;
          this.formValidate.amount = Number(res.detail.amount || 0);
          this.formValidate.account_time = moment(res.detail.account_time * 1000 || '').format('yyyy-MM-DD HH:mm:ss');
          this.formValidate.voucher_imgs = res.detail.voucher_imgs;
          this.formValidate.remark = res.detail.remark;
          this.formValidate.ent_type = res.detail.ent_type;
          this.formValidate.ent_id = res.detail.ent_id;

          this.ent_name = res.detail.ent_name;
          this.clinic_type = res.detail.clinic_type;
          this.organization_name = res.detail.organization_name;
          this.personal_id = res.detail.personal_id;

          this.getAWentBalance();
        },
        err => this.$Message.error(err.errmsg)
      );
    },

    getAWentBalance: S.debounce(function () {
      if (!this.formValidate.ent_type || !this.formValidate.account_time || !this.formValidate.ent_id) return;
      this.getBalanceLoading = true;
      let params = {
        ent_type: this.formValidate.ent_type,
        ent_id: this.formValidate.ent_id,
        account_time: moment(this.formValidate.account_time).format('yyyy-MM-DD HH:mm:ss')
      };
      this.$api
        .getAWentBalance(params)
        .then(
          res => {
            this.getBalanceLoading = false;
            this.balance = res.balance;
            this.wallet_id = res.id;
          },
          err => this.$Message.error(err.errmsg)
        )
        .finally(() => (this.getBalanceLoading = false));
    }, 400),

    getARRefundOptions() {
      this.$api.getARRefundOptions().then(
        res => {
          this.ent_list = res.ent_list;
          this.ent_com = res.ent_com;
          this.ent_name = res.ent_com?.apply?.name;
          this.payTypeDesc = S.descToArrHandle(res.payTypeDesc);
          this.getRefundName();
          this.handleQuickRefund();
        },
        err => this.$Message.error(err.errmsg)
      );
    }
  }
};
</script>

<style scoped lang="less">
.company-detail-wrapper {
  margin-bottom: 100px;
  .company-form {
    margin-top: 20px;
  }

  .money-text {
    position: absolute;
    top: 0px;
    left: 102%;
    min-width: 100%;
    display: flex;
    align-items: center;
  }
}

::v-deep .ivu-form-item {
  width: 50%;
}

.mt15 {
  margin-top: 15px;
}

.mb6 {
  margin-bottom: 6px;
}
</style>
