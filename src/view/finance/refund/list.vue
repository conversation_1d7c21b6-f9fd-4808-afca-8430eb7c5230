<template>
  <div class="f_order-wrapper">
    <Form inline @submit.native.prevent @keyup.enter.native="onSearch">
      <Row>
        <FormItem>
          <Input v-model="queryFormData.code" placeholder="请输入退款单号" clearable />
        </FormItem>

        <FormItem>
          <surning-search
            localStorageListName="entTypeList"
            :p_ent_type="queryFormData.ent_type"
            routerKey="ent_id"
            :extra-params="{
              fill_opc: 1
            }"
            :showType="true"
            :typeList="typeList"
            :showOutside="true"
            ref="nursing-search"
            v-model="queryFormData.ent_id"
            @getType="type => (queryFormData.ent_type = type)"
            placeholder="请输入申请退款方"
          ></surning-search>
        </FormItem>

        <FormItem>
          <payment-license-search
            inputWidth="182"
            routerKey="organ_ent_id"
            placeholder="营业执照名称（退款方）"
            ref="license-search"
            :default_params="{
              fill_opc: 1,
              fill_outside: 1
            }"
            v-model="queryFormData.organ_ent_id"
            @getOrganEntType="val => (queryFormData.organ_ent_type = val)"
          >
          </payment-license-search>
        </FormItem>

        <FormItem>
          <clinic-type-search
            v-model="queryFormData.clinic_type"
            :clinic_direct_type="queryFormData.clinic_direct_type"
            :directTypeDesc="directTypeDesc"
            :typeDesc="clinicTypeDesc"
            @directChange="directChange"
          ></clinic-type-search>
        </FormItem>
      </Row>

      <Row>
        <FormItem>
          <other-surning-search
            localStorageListName="sideEntList"
            :p_ent_type="queryFormData.ref_ent_type"
            routerKey="ref_ent_id"
            searchKeyword="name"
            :showType="true"
            ref="other-search"
            v-model="queryFormData.ref_ent_id"
            @getType="type => (queryFormData.ref_ent_type = type)"
            placeholder="请输入退款方"
          ></other-surning-search>
        </FormItem>
        <FormItem>
          <DatePicker
            type="daterange"
            clearable
            :options="disabledTime"
            format="yyyy-MM-dd"
            placeholder="请选择申请时间"
            v-model="applyTimeRange"
            @on-change="times => handleTimeChange(times, 'apply_st', 'apply_et')"
            class="time-range"
          ></DatePicker>
        </FormItem>

        <FormItem>
          <DatePicker
            type="daterange"
            clearable
            :options="disabledTime"
            format="yyyy-MM-dd"
            placeholder="请选择完成时间"
            v-model="passedTimeRange"
            @on-change="times => handleTimeChange(times, 'passed_st', 'passed_et')"
            class="time-range"
          ></DatePicker>
        </FormItem>

        <FormItem style="text-align: left">
          <Button type="primary" class="mr10" @click="onSearch">筛选</Button>
          <Button type="default" class="mr10" @click="onResetSearch">重置</Button>
          <Button :loading="exportLoading" type="default" @click="exportExcel">导出</Button>
        </FormItem>
      </Row>
    </Form>
    <div class="table-wrapper">
      <div class="panel-nav flex flex-item-between">
        <div>
          <a class="nav" :class="{ active: !queryFormData.status }" @click.prevent.capture="onStatusChange('')">
            全部
          </a>
          <a
            v-for="(item, index) in statusDesc"
            :key="index"
            class="nav"
            :class="{ active: $route.query.status === item.id }"
            @click.prevent.capture="onStatusChange(item.id)"
          >
            {{ item.desc }}
            <Tag :color="getTagColor(item.id)">{{ list_count[item.id]?.num }}</Tag>
          </a>
        </div>

        <div class="creat-button">
          <Button type="primary" @click="applyRefund">申请退款</Button>
        </div>
      </div>
      <Table :loading="tableLoading" :columns="tableCols" :data="list" :height="$store.state.app.clientHeight - 330">
        <template slot-scope="{ row }" slot="ent_organization_name">
          {{ row.ent_organization_name || '-' }}
        </template>

        <template slot-scope="{ row }" slot="company_name">
          {{ row.company_name || '-' }}
        </template>

        <template slot-scope="{ row }" slot="ent_type_desc">
          <div>{{ row.ent_type_desc || '-' }}</div>
          <div v-if="row.ent_type === 'CLI' && row.ent_clinic_type">{{ row.ent_clinic_type || '-' }}</div>
        </template>

        <template slot-scope="{ row }" slot="apply_time">
          {{ row.apply_time | date_format }}
        </template>

        <template slot-scope="{ row }" slot="passed_time">
          {{ row.passed_time | date_format }}
        </template>

        <template slot-scope="{ row }" slot="account_time">
          {{ row.account_time | date_format }}
        </template>

        <template slot-scope="{ row }" slot="action">
          <a @click="jumpToDetail(row, 'detail')">详情</a>
        </template>
      </Table>
      <div class="block_20"></div>
      <KPage
        :total="total"
        :page-size="+queryFormData.pageSize"
        :current="+queryFormData.page"
        @on-change="handleCurrentChange"
        @on-page-size-change="handleSizeChange"
        style="text-align: center"
      />
    </div>
  </div>
</template>

<script>
// import downloadCenter from '@/mixins/downloadCenter';
import downloadExcel from '@/mixins/downloadExcel';
import S from 'utils/util';
import search from '@/mixins/search';
import surningSearch from '@/components/surning-search/surning-search';
import clinicTypeSearch from '@/components/remote-search/clinic-type-search.vue';
import paymentLicenseSearch from '@/components/remote-search/payment-license-search.vue';
import otherSurningSearch from '@/components/other-surning-search/other-surning-search';

const init_query_form_data = {
  page: 1,
  pageSize: 20,
  code: '', // 退款单号
  ent_id: '', // 申请方id
  ent_type: '', // 申请方类型
  ref_ent_id: '', // 退款方id
  ref_ent_type: '', // 退款方类型
  organ_ent_type: '', // 营业执照类型
  organ_ent_id: '', // 营业执照id
  clinic_type: '', // 诊所子类型
  clinic_direct_type: '', // 诊所类型
  apply_st: '', // 申请开始时间
  apply_et: '', // 申请结束时间
  passed_st: '', // 完成开始时间
  passed_et: '', // 完成结束时间
  status: '', // 审核类型
  r: ''
};
export default {
  name: 'list',
  mixins: [search, downloadExcel],
  components: { surningSearch, otherSurningSearch, clinicTypeSearch, paymentLicenseSearch },
  data() {
    return {
      downloadApiName: 'getARRefundExportList',
      apiName: 'getARRefundList', // 获取明细列表
      queryFormData: { ...init_query_form_data },
      applyTimeRange: [],
      passedTimeRange: [],
      disabledTime: {
        disabledDate(date) {
          return date && date.valueOf() > Date.now();
        }
      },
      tableCols: [
        { title: '退款单号', key: 'code', align: 'center' },
        { title: '申请退款方', key: 'ent_name', align: 'center' },
        { title: '申请退款方类型', slot: 'ent_type_desc', align: 'center', width: 150 },
        { title: '营业执照名称（申请退款方）', slot: 'ent_organization_name', align: 'center' },
        { title: '退款金额', key: 'amount', align: 'center' },
        { title: '申请时间', slot: 'apply_time', align: 'center' },
        { title: '完成时间', slot: 'passed_time', align: 'center' },
        { title: '收付款时间', slot: 'account_time', align: 'center' },
        { title: '状态', key: 'status_desc', align: 'center' },
        { title: '操作', slot: 'action', align: 'center' }
      ],
      list_count: {},
      clinicTypeDesc: [],
      directTypeDesc: [],
      statusDesc: [],
      typeList: [
        { label: '诊所', value: 'CLI' },
        { label: '直营运营中心', value: 'OPC' },
        { label: '个人客户', value: 'PERSON_C' },
        { label: '外部公司', value: 'OUTSIDE' }
      ],
      // 付款方默认参数
      default_params: {
        fill_cp: 1,
        fill_com: 1,
        fill_person_c: 1,
        fill_opc: 1
      },
      exportLoading: false
    };
  },
  computed: {
    getTagColor(type) {
      return type => {
        switch (type) {
          case 'PLAT_AUDIT': // 待平台审核
          case 'FINA_AUDIT': // 待财务审核
            return 'warning';
          case 'REJECT': // 已驳回
            return 'error';
          case 'PASS': // 已通过
            return 'success';
          default: // 待平台审核
            return 'default';
        }
      };
    }
  },
  watch: {},
  created() {
    this.queryFormData = S.merge(this.queryFormData, this.$route.query);
    this.getARRefundOptions();
    this.getClinicOptions();
    this.submitQueryForm(true);
  },
  mounted() {},
  methods: {
    applyRefund() {
      this.$router.push('/finance/refund/edit');
    },

    exportExcel() {
      this.downloadExcel(this.queryFormData);
      // this.exportLoading = true;
      // let params = {
      //   ...this.queryFormData
      // };
      // this.createDownloadCenterMission('accountRefundList', params);
      // setTimeout(() => {
      //   this.exportLoading = false;
      // }, 1000);
    },
    directChange(val) {
      this.$set(this.queryFormData, 'clinic_direct_type', val);
    },
    onStatusChange(status) {
      this.queryFormData.page = 1;
      this.queryFormData.status = status;
      this.submitQueryForm();
    },
    onResetSearch() {
      this.queryFormData = { ...init_query_form_data };
      this.timeRange = [];
      this.submitQueryForm();
      this.$refs['nursing-search'].clear();
      this.$refs['other-search'].clear();
    },
    jumpToDetail(row, type) {
      this.$router.push({
        path: '/finance/refund/detail',
        query: {
          type,
          id: row.id
        }
      });
    },

    handleList(data) {
      this.list_count = data.status_total;
    },
    getARRefundOptions() {
      this.$api.getARRefundOptions().then(res => {
        this.statusDesc = S.descToArrHandle(res.statusDesc);
      });
    },
    getClinicOptions() {
      this.$api.getClinicOptions().then(res => {
        this.directTypeDesc = S.descToArrHandle(res.directTypeDesc);
        this.clinicTypeDesc = S.descToArrHandle(res.typeDesc);
      });
    }
  },
  beforeRouteUpdate(to, from, next) {
    this.queryFormData = S.merge(init_query_form_data, to.query);
    this.getTimeRange('apply_st', 'apply_et', 'applyTimeRange');
    this.getTimeRange('passed_st', 'passed_et', 'passedTimeRange');
    this.loadList();
    next();
  }
};
</script>

<style scoped lang="less"></style>
