<template>
  <Modal
      v-model="modelVisible"
      width="1000"
      :styles="{ top: '8vh' }"
      title="选择诊所"
      :mask-closable="false"
      :footer-hide="false"
      class="clinic_table"
      @on-visible-change="modelChange"
  >
    <div>
      <Table
          row-key="id"
          border
          :columns="tableCols"
          :data="list"
          :loading="tableLoading"
          :height="$store.state.app.clientHeight-247"
          @on-select-all="selectTableRowAll"
          @on-select-all-cancel="cancelTableRowAll"
          @on-select="selectTableRow"
          @on-select-cancel="cancelTableRow"
          @on-row-click="clickTableRow"
      >
        <template slot="leading_mobile" slot-scope="{row}">
          <div>{{ row.leading_person || '-' }} </div>
          <div>{{ row.leading_mobile || '-'}}</div>
        </template>
        <template slot="action" slot-scope="{row}">
          <a v-if="row.clinicId === 'custom'" @click="delRow(row)">统一设置</a>
          <a v-else>移除诊所</a>
        </template>
      </Table>
    </div>
    <div slot="footer">
      <Button class="mr10" @click="onCancel">取消</Button>
      <Button type="primary" @click="onOk">确定</Button>
    </div>
  </Modal>
</template>

<script>
import {isEmpty} from "../../../../utils/helper";
import {cloneDeep} from "lodash";

export default {
  name: "addClinic",
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    selectedClinic: {
      type: Array,
      default: () => []
    },
    selectedRow: {
      type: Object,
      default: () => ({})
    }
  },
  computed: {
    modelVisible: {
      get: function() {
        return this.visible
      },
      set: function(val){
        this.$emit('update:visible', val);
      }
    }
  },
  data() {
    return {
      tableCols: [
        {
          type: 'selection',
          width: 60,
          align: 'center',
        },
        { title: '诊所编号', key: 'clinic_code', width: 80 },
        { title: '诊所门店编号', key: 'offline_code', minWidth: 150 },
        { title: '诊所名称', key: 'name', minWidth: 100 },
        { title: '经营地址', key: 'address', minWidth: 100 },
        { title: '联系人/手机号', slot: 'leading_mobile', minWidth: 100 },
      ],
      list: [],
      selectedTableRows: [],
      tableLoading: false,
    }
  },
  methods: {
    modelChange(val) {
      if (val) {
        this.list = []
        this.selectedTableRows = this.selectedClinic
        this.selectedRow.id && this.getClinicList(this.selectedRow.id)
      }
    },
    onCancel() {
      this.selectedTableRows = []
      this.$emit('update:visible', false);
    },
    onOk() {
      if (isEmpty(this.selectedTableRows)) {
        this.$Message.error('请选择诊所!')
        return
      }
      const list = cloneDeep(this.selectedTableRows);
      this.$emit('on-ok', list)
      this.onCancel()
    },
    getClinicList(id) {
      this.tableLoading = true
      this.$api.changePriceSelectClinicList({id}).then(res => {
        this.list = res?.list?.map(item => {
          if (this.selectedClinic.some(row => row.id === item.id)) {
            return {
              ...item,
              _checked: true,
            }
          }
          return item
        }) || []
      }).finally(() => {
        this.tableLoading = false
      })
    },
    selectTableRowAll() {
      this.selectedTableRows = this.list
    },
    cancelTableRowAll() {
      this.selectedTableRows = []
    },
    selectTableRow(selection, row) {
      this.selectedTableRows.push(row)
    },
    cancelTableRow(selection, row) {
      this.selectedTableRows = this.selectedTableRows.filter(item => item.id !== row.id)
    },
    clickTableRow(row) {
      const isExists = this.selectedTableRows.some(item => item.id === row.id)
      if (isExists) {
        this.selectedTableRows = this.selectedTableRows.filter(item => item.id !== row.id)
        this.list = this.list?.map(item => {
          if (item.id === row.id) {
            return {
              ...item,
              _checked: false,
            }
          }
          return item
        })
      }
      if (!isExists) {
        this.selectedTableRows.push(row);
        this.list = this.list?.map(item => {
          if (item.id === row.id) {
            return {
              ...item,
              _checked: true,
            }
          }
          return item
        })
      }
    }
  }
}
</script>


<style scoped lang="less">
</style>