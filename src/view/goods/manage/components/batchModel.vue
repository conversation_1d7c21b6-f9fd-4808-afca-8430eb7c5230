<template>
  <Modal
      v-model="modelVisible"
      width="1000"
      :styles="{ top: '8vh' }"
      :title="`批量${type === 'up' ? '上架' : '下架'}`"
      :mask-closable="false"
      :footer-hide="false"
      @on-visible-change="modelChange"
  >
    <div v-if="step === '1'">
      <Table row-key="id" :columns="tableCols" :data="list" :height="$store.state.app.clientHeight-247">
        <template slot="name" slot-scope="{row}">
          <div v-empty="row.name"></div>
        </template>
        <template slot="create_time" slot-scope="{row}">
          <div v-empty.time="row.create_time" />
        </template>
        <template slot="status_desc" slot-scope="{row}">
          <div v-if="row.status_desc && row.status_desc.length > 0">
            <div class="flex" v-for="item in row.status_desc" :key="item.status">
              <div>{{ item.title }}：</div>
              <div>{{item.value || '-'}}</div>
            </div>
          </div>
          <div v-else>-</div>
        </template>
        <template slot="action" slot-scope="{row}">
          <a @click="delRow(row)">删除</a>
        </template>
      </Table>
    </div>
    <div v-if="step === '2'">
      <div class="tip">可售库存为 0 的商品，上架后状态为“已售罄”，待添加库存后才可以正常销售。</div>
      <Form :model="formSearch" :label-width="60" class="mt10 mb10 custom-goods-form" @submit.native.prevent>
        <FormItem label="选择诊所">
          <RadioGroup v-model="radio" @on-change="clickClinic">
            <Radio label="1">全部诊所</Radio>
            <Radio label="2">指定诊所</Radio>
          </RadioGroup>
        </FormItem>
        <FormItem label="">
          <div class="width100 flex flex-item-center" style="justify-content: space-between">
            <div style="color:#bababa;">未选择的诊所中商品销售状态不变，操作对开启所选诊所生效。</div>
          </div>
        </FormItem>
      </Form>
      <Table
          ref="clinicTable"
          class="clinicTable"
          row-key="id"
          :loading="clinicLoading"
          :columns="clinicTableCols"
          :data="clinicList"
          :height="$store.state.app.clientHeight-396"
          style="margin: 10px;"
          @on-select-all="selectedTableAll"
          @on-select-all-cancel="cancelTableAll"
          @on-select="selectedTableRow"
          @on-select-cancel="cancelTableRow"
          @on-row-click="clickTableRow"
      >
        <template slot="leading_mobile" slot-scope="{row}">
          <div>{{ row.leading_person }}</div>
          <div>{{ row.leading_mobile }}</div>
        </template>
        <template slot="action" slot-scope="{row}">
          <a @click="delRow(row)">删除</a>
        </template>
      </Table>
    </div>
    <div slot="footer">
<!--      <Button v-if="step === '1'" class="mr10" @click="onCancel">取消</Button>-->
      <Button class="mr10" @click="onCancel">取消</Button>
<!--      <Button v-if="step === '2'" class="mr10" @click="onPre">上一步</Button>-->
      <Button v-if="step === '1'" type="primary" @click="onNext">下一步</Button>
      <Button v-if="step === '2'" type="primary" @click="onOk" :loading="okLoading">确定</Button>
    </div>
  </Modal>
</template>
<script>
import {cloneDeep} from "lodash";
import {isEmpty} from "../../../../utils/helper";

const init_query_form = {
  keyword: '',
}
export default {
  name: "batchModel",
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    selectedTable: {
      type: Array,
      default: () => []
    },
    type: {
      type: String,
      default: 'up',
    }
  },
  data() {
    return {
      step: '2',
      clinicTableCols: [
        {
          type: 'selection',
          width: 60,
          align: 'center',
        },
        { title: '诊所编号', key: 'clinic_code', width: 80 },
        { title: '诊所门店编号', key: 'offline_code', minWidth: 150 },
        { title: '诊所名称', key: 'name', minWidth: 100 },
        { title: '经营地址', key: 'address', minWidth: 100 },
        { title: '联系人/手机号', slot: 'leading_mobile', minWidth: 100 },
      ],
      tableCols: [
        { title: '商品ID', key: 'id', width: 80 },
        { title: '商品名称', key: 'name', minWidth: 150 },
        { title: '商品类型', key: 'goods_type_text', minWidth: 100 },
        { title: '商品来源', key: 'source_platform_text', minWidth: 100 },
        { title: '创建时间', slot: 'create_time', minWidth: 100 },
        { title: '商品状态', slot: 'status_desc', minWidth: 100 },
        { title: '操作', slot: 'action', align: 'center', width: 60 },
      ],
      list: [],
      clinicList: [],
      clinicLoading: false,
      selectedClinicIds: [],
      radio: '1',
      formSearch: { ...init_query_form },
      okLoading: false
    }
  },
  computed: {
    modelVisible: {
      get: function() {
        return this.visible
      },
      set: function(val){
        this.$emit('update:visible', val);
      }
    }
  },
  methods: {
    modelChange(val) {
      if (val) {
        // this.step = '1'
        this.step = '2'
        this.radio = '1'
        this.list = cloneDeep(this.selectedTable)
        this.formSearch = cloneDeep(init_query_form)
        this.clinicList = []
        this.getClinicList()
      }
    },
    onPre() {
      this.step = '1';
    },
    onNext() {
      this.step = '2';
      this.getClinicList()
    },
    onOk() {
      if (isEmpty(this.selectedClinicIds)) {
        this.$Message.error('请选择诊所!')
        return
      }
      this.okLoading = true
      this.$api[`${this.type}ShelvesGoods`]({
        ids: this.list?.map(item => item.id),
        clinic_ids: this.selectedClinicIds,
      }).then(() => {
        this.$Message.success(`${this.type === 'up' ? '上架' : '下架'}成功！`)
        this.onCancel()
        this.$emit('onResetSearch')
      }).catch(err => {
        this.$Message.error(err.errmsg)
      })
      .finally(() => {
        this.okLoading = false
      })
    },
    onCancel() {
      this.$emit('update:visible', false);
    },
    delRow(row) {
      this.list = this.list.filter(item => item.id !== row.id);
    },
    onSearch() {
      this.getClinicList()
    },
    selectedTableAll() {
      this.selectedClinicIds = this.clinicList?.map(item => item.id)
    },
    cancelTableAll() {
      this.selectedClinicIds = []
    },
    clickClinic(val) {
      if (val === '1') {
        this.$refs.clinicTable.selectAll(true)
        this.clinicList = this.clinicList?.map(item => ({
          ...item,
          _checked: true,
          _disabled: true
        }))
        this.selectedClinicIds = this.clinicList?.map(item => item.id)
      } else {
        this.$refs.clinicTable.selectAll(false)
        this.clinicList = this.clinicList?.map(item => ({
          ...item,
          _checked: false,
          _disabled: false
        }))
        this.selectedClinicIds = []
      }
      this.$nextTick(() => {
        document.querySelector('.clinicTable .ivu-table-overflowY')?.scrollTo({
          top: 0,
          behavior: 'smooth'
        })
      })
    },
    selectedTableRow(selection, row) {
      this.selectedClinicIds = [...new Set([...this.selectedClinicIds, row.id])]
    },
    cancelTableRow(selection, row) {
      this.selectedClinicIds = this.selectedClinicIds.filter(key => key !== row.id)
    },
    clickTableRow(row) {
      if (this.radio === '1') return
      const isExists = this.selectedClinicIds.includes(row.id)
      if (isExists) {
        this.selectedClinicIds = this.selectedClinicIds.filter(key => key !== row.id)
        this.clinicList = this.clinicList?.map(item => {
          if (item.id === row.id) {
            return {
              ...item,
              _checked: false,
            }
          }
          return item
        })
      }
      if (!isExists) {
        this.selectedClinicIds.push(row.id);
        this.clinicList = this.clinicList?.map(item => {
          if (item.id === row.id) {
            return {
              ...item,
              _checked: true,
            }
          }
          return item
        })
      }
    },
    getClinicList() {
      this.clinicLoading = true
      this.$api.getGoodsClinicList({}).then(res => {
        this.clinicList = res?.list?.map(item => ({
          ...item,
          _checked: true,
          _disabled: true
        })) || [];
        this.selectedClinicIds = res?.list?.map(item => item.id)
      }).finally(() => {
        this.clinicLoading = false
      })
    }
  }
}
</script>
<style scoped lang="less">
.tip {
  background-color: rgba(241, 248, 251, 1);
  box-sizing: border-box;
  border-width: 1px;
  border-style: solid;
  border-color: rgba(22, 155, 213, 1);
  border-radius: 3px;
  padding: 5px;
}
:deep(.custom-goods-form .ivu-form-item) {
  margin-bottom: 0 !important;
}
:deep(.clinicTable .ivu-table-header .ivu-checkbox) {
  display: none !important;
}
</style>