<template>
  <Modal
      v-model="modelVisible"
      width="1000"
      :styles="{ top: '8vh' }"
      title="改价格"
      :mask-closable="false"
      :footer-hide="false"
      @on-visible-change="modelChange"
  >
    <div>
      <Form>
        <FormItem label="选择诊所:" :label-width="80" required style="margin-bottom: 0;">
          <Button @click="openClinicModel">选择应用诊所</Button>
          <span style="color: #2277ff; margin-left: 5px;" v-if="selectedClinic?.length > 0">已选择诊所: {{selectedClinic.length}}</span>
        </FormItem>
        <FormItem label="" :label-width="80" required style="margin-bottom: 0;">
          <span style="color:#999999;">选择批量修改商品价格后的生效诊所。例如，如果选择了A诊所，则提交后，仅修改这部分商品在A诊所的价格。</span>
        </FormItem>
      </Form>
      <Table row-key="clinicId" border class="clinic_table" :row-class-name="rowClassName" :columns="tableCols" :data="list" :loading="tableLoading" :height="$store.state.app.clientHeight-307">
        <template slot="attr" slot-scope="{row}">
          <div class="custom-row">
            <div class="custom-row-item" v-for="item in (row.attrs || [])" :key="item.id">
              {{ item.spec }}
            </div>
          </div>
        </template>
        <template slot="price" slot-scope="{row}">
          <div class="custom-row">
            <div class="custom-row-item" v-for="(item, index) in row.attrs" :key="item.id">
              <InputNumber
                  :min="0"
                  style="width: 90%;"
                  :precision="2"
                  v-model="row.attrs[index].price"
                  :formatter="value => `¥${value}`"
                  :parser="value => value.replace('¥', '')"
                  :active-change="false"
                  @on-change="val => changeInputNumber(val, row,index)"
                  @on-blur="blurInputNumber(row,index)"
                  @on-focus="e => e.currentTarget.select()"
              />
            </div>
          </div>
        </template>
        <template slot="action" slot-scope="{row}">
          <a v-if="row.clinicId === 'custom'" @click="resetPrice(row)">统一设置</a>
          <a v-else @click="removeClinic(row)">移除诊所</a>
        </template>
      </Table>
    </div>
    <div slot="footer">
      <Button class="mr10" @click="onCancel">取消</Button>
      <Button type="primary" @click="onOk">确定</Button>
    </div>
    <add-clinic
        :visible.sync="addClinicVisible"
        :selected-clinic="selectedClinic"
        :selected-row="selectedRow"
        @on-ok="handleSaveClinic"
    />
  </Modal>
</template>

<script>
import {isEmpty} from "../../../../utils/helper";
import AddClinic from "./addClinic.vue";
import {cloneDeep} from "lodash";

export default {
  name: "batchPriceModel",
  components: {AddClinic},
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    selectedRow: {
      type: Object,
      default: () => ({})
    },
  },
  computed: {
    modelVisible: {
      get: function() {
        return this.visible
      },
      set: function(val){
        this.$emit('update:visible', val);
      }
    }
  },
  data() {
    return {
      tableCols: [
        { title: '商品', key: 'name', align: 'center', width: 80 },
        { title: '所属诊所', key: 'clinicName', align: 'center', minWidth: 150 },
        { title: '商品类型', key: 'goods_type_text', align: 'center', minWidth: 100 },
        { title: '商品来源', key: 'source_platform_text', align: 'center', minWidth: 100 },
        { title: '规格', slot: 'attr', align: 'center', minWidth: 100 },
        { title: '价格(元)', slot: 'price', align: 'center', minWidth: 100 },
        { title: '操作', slot: 'action', align: 'center', minWidth: 100 },
      ],
      list: [],
      tableLoading: false,
      addClinicVisible: false,
      selectedClinic: [],
      tableTemplate: {}
    }
  },
  methods: {
    modelChange(val) {
      if (val) {
        const id = this.selectedRow.id
        this.selectedClinic = []
        this.$set(this, 'list', [])
        if (!isEmpty(id)) this.getGoodsLibInfo(id);
      }
    },
    onCancel() {
      this.$emit('update:visible', false);
    },
    onOk() {
      const list = this.list?.filter(item => item.clinicId !== 'custom')
      if (isEmpty(list)) {
        this.$Message.error('请选择改价诊所！')
        return
      }
      let flag = false
      const attrs = list?.map(item => ({
        clinic_id: item.clinicId,
        attrs: item.attrs?.map(attr => {
          if (isEmpty(attr.price)) {
            flag = true
          }
          return {
            id: attr.id,
            price: attr.price
          }
        }),
      }))
      if (flag) {
        this.$Message.error('请输入价格!')
        return;
      }
      const params = {
        goods_id: this.selectedRow.id,
        attrs
      }
      this.$api.updateGoodsPrice(params).then(() => {
        this.$Message.success('改价成功!')
        this.onCancel()
        this.$emit('onResetSearch')
      })
    },
    removeClinic(row) {
      this.selectedClinic = this.selectedClinic.filter(item => item.id !== row.clinicId);
      const list = this.list.filter(item => item.clinicId !== row.clinicId);
      this.$set(this, 'list', list);
    },
    openClinicModel() {
      this.addClinicVisible = true
    },
    changeInputNumber(val, row, i) {
      this.list.forEach((item, index) => {
        if (item.clinicId === row.clinicId) {
          this.$set(this.list[index].attrs[i], 'price', +val);
        }
      })
    },
    blurInputNumber(row, i) {
      this.list.forEach((item, index) => {
        if (item.clinicId === row.clinicId) {
          this.$set(this.list[index].attrs[i], 'price', +item.attrs[i].price);
        }
      })
    },
    resetPrice() {
      const list = this.list?.filter(item => item.clinicId !== 'custom')
      if (isEmpty(list)) {
        this.$Message.error('请选择改价诊所！')
        return
      }
      const tableTemplate = this.list.find(item => item.clinicId === 'custom')
      this.list = this.list.map((item) => {
        if (item.clinicId !== 'custom') {
          return {
            ...item,
            attrs: item.attrs?.map(attr => {
              const attrs = tableTemplate?.attrs?.find(att => attr.pms_lga_id === att.id)
              attr.price = +attrs.price
              return attr
            })
          }
        }
        return item
      })
    },
    handleSaveClinic(list) {
      this.selectedClinic = list
      list.forEach(item => {
        const isExist = this.list.some(row => row.clinicId === item.id)
        const tableTemplate = cloneDeep(this.tableTemplate)
        !isExist && this.list.push({
          clinicId: item.id,
          clinicName: item.name,
          ...tableTemplate,
          attrs: item?.goods?.attrs || []
        })
      })
      this.list.forEach((item, index) => {
        item.attrs?.forEach((attr, i) => {
          this.$set(this.list[index].attrs[i], 'price', +attr.price);
        })
      })
    },
    getGoodsLibInfo(id) {
      this.tableLoading = true
      this.$api.getTemplatePriceInfo({
        id,
      }).then(res => {
        this.tableTemplate = res || {}
        this.tableTemplate.attrs = this.tableTemplate?.attrs?.map(item => ({ ...item, price: +item.price }))
        const tableTemplate = cloneDeep(this.tableTemplate)
        if (!isEmpty(res)) {
          this.list.push({
            clinicId: 'custom',
            clinicName: '-',
            ...tableTemplate,
          })
        }
      }).finally(() => {
        this.tableLoading = false
      })
    },
    rowClassName (row) {
      if (row.clinicId === 'custom') {
        return 'default-row';
      }
      return '';
    }
  }
}
</script>


<style scoped lang="less">
.custom-row .custom-row-item {
  height: 40px;
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  border-bottom: 1px solid #e8eaec;
}
.custom-row .custom-row-item:last-child {
  border-bottom: none;
}
:deep(.clinic_table .ivu-table-cell) {
  padding: 0;
}
:deep(.ivu-table .default-row td){
  background-color: #C7E3FA;
}
</style>