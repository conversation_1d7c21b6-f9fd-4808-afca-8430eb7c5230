<template>
  <Modal
      v-model="modelVisible"
      width="1000"
      :styles="{ top: '8vh' }"
      :title="model_title"
      :mask-closable="false"
      :footer-hide="false"
      class="clinic_table"
      @on-visible-change="modelChange"
  >
    <div>
      <Table
          row-key="id"
          border
          :columns="tableCols"
          :data="list"
          :loading="tableLoading"
          :height="$store.state.app.clientHeight-247"
      >
        <template slot="leading_mobile" slot-scope="{row}">
          <div>{{ row.leading_person || '-' }} </div>
          <div>{{ row.leading_mobile || '-'}}</div>
        </template>
      </Table>


    </div>
    <div slot="footer">
      <div style="width: 100%; display: flex; justify-content: space-between; align-items: center;">
        <Page
            :total="+page.total"
            size="small"
            :page-size-opts="[10, 20, 50, 80, 100, 200]"
            show-elevator
            show-sizer
            show-total
            @on-change="onChange"
            @on-page-size-change="onPageSizeChange"
        />
        <Button class="mr10" @click="onCancel">确定</Button>
      </div>
    </div>
  </Modal>
</template>

<script>

export default {
  name: "clinicListModel",
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    selectedRow: {
      type: Object,
      default: () => ({})
    },
  },
  computed: {
    modelVisible: {
      get: function() {
        return this.visible
      },
      set: function(val){
        this.$emit('update:visible', val);
      }
    },
    model_title() {
      const status = this.selectedRow.status
      if (status === '200') return '销售中的诊所'
      if (status === '209') return '已售罄的诊所'
      if (status === '800') return '已下架的诊所'
      return '销售中的诊所'
    }
  },
  data() {
    return {
      tableCols: [
        { title: '诊所编号', key: 'clinic_code', width: 80 },
        { title: '诊所门店编号', key: 'offline_code', minWidth: 120 },
        { title: '诊所名称', key: 'name', minWidth: 100 },
        { title: '经营地址', key: 'address', tooltip: true, minWidth: 150 },
        { title: '联系人/手机号', slot: 'leading_mobile', minWidth: 100 },
      ],
      list: [],
      tableLoading: false,
      page: {
        page: 1,
        pageSize: 10,
        total: 0,
      }
    }
  },
  methods: {
    modelChange(val) {
      if (val) {
        this.list = []
        this.page.page = 1
        this.page.pageSize = 10
        this.getClinicList()
      }
    },
    onCancel() {
      this.selectedTableRows = []
      this.$emit('update:visible', false);
    },
    getClinicList() {
      this.tableLoading = true
      this.$api.getClinicListByStatus({
        id: this.selectedRow.id,
        status: this.selectedRow.status,
        ...this.page
      }).then(res => {
        this.list = res?.list || []
        this.page.total = res.total || 0
      }).finally(() => {
        this.tableLoading = false
      })
    },
    onPageSizeChange(val) {
      this.page.pageSize = val
      this.getClinicList()
    },
    onChange(val) {
      this.page.page = val
      this.getClinicList()
    }
  }
}
</script>


<style scoped lang="less">
</style>