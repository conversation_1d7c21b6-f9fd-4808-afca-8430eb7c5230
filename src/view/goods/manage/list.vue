<template>
  <div>
    <Form inline :label-width="0" @submit.native.prevent @keyup.enter.native="onSearch">
      <Row>
        <FormItem>
          <Input type="text" placeholder="请输入商品名称" v-model="queryFormData.name" />
        </FormItem>
        <FormItem>
          <Select v-model="queryFormData.goods_type" placeholder="请选择商品类型" clear>
            <Option v-for="desc in typeDesc" :key="desc.id" :value="desc.id">{{ desc.desc }}</Option>
          </Select>
        </FormItem>
        <FormItem>
          <Select v-model="queryFormData.source_platform" placeholder="请选择商品来源" clear>
            <Option v-for="desc in sourcePlatformDesc" :key="desc.id" :value="desc.id">{{ desc.desc }}</Option>
          </Select>
        </FormItem>
        <Col>
          <FormItem>
            <Button class="mr10" type="primary" @click="onSearch">筛选</Button>
            <Button @click="onResetSearch">重置</Button>
          </FormItem>
        </Col>
      </Row>
    </Form>

    <div class="table-wrapper">
<!--      <div class="table-fun">-->
<!--        <Dropdown @on-click="clickBatchOpt">-->
<!--          <Button type="primary"> 批量操作 </Button>-->
<!--          <template #list>-->
<!--            <DropdownMenu>-->
<!--              <DropdownItem name="up">上架</DropdownItem>-->
<!--              <DropdownItem name="down">下架</DropdownItem>-->
<!--            </DropdownMenu>-->
<!--          </template>-->
<!--        </Dropdown>-->
<!--      </div>-->
      <Table
        :loading="tableLoading"
        row-key="id"
        :columns="tableCols"
        :data="list"
        @on-select="selectedTableRow"
        @on-select-cancel="cancelTableRow"
        @on-select-all="selectedTableAll"
        @on-select-all-cancel="cancelTableAll"
        @on-row-click="clickTableRow"
        :height="$store.state.app.clientHeight - 220"
      >
        <template slot="name" slot-scope="{ row }">
          <div class="flex flex-item-v-center" style="padding: 5px 0">
            <div @click.prevent.stop v-if="row.main_img" v-viewer="[row.main_img]" style="cursor: pointer">
              <img style="width: 60px; height: 60px; object-fit: cover" :src="row.main_img | imageStyle('B.w300')" />
            </div>
            <div class="ml-5">{{ row.name || '-' }}</div>
          </div>
        </template>
        <template slot="create_time" slot-scope="{ row }">
          <div v-empty.time="row.create_time" />
        </template>
        <template slot="status_desc" slot-scope="{ row }">
          <div v-if="row.status_desc && row.status_desc.length > 0">
            <div class="flex" v-for="item in row.status_desc" :key="item.status">
              <div>{{ item.title }}：</div>
              <a v-if="item.value > 0" @click.stop.prevent="openClinicModel(row, item.status)">{{ item.value }}</a>
              <div v-else>{{ item.value || '-' }}</div>
            </div>
          </div>
          <div v-else>-</div>
        </template>
        <template slot="action" slot-scope="{ row }">
<!--          <a v-if="!['30', '35', '40'].includes(row.goods_type)" @click="openPriceModel(row)">改价格</a>-->
          <a @click="toDetail(row)">详情</a>
        </template>
      </Table>

      <div class="block_20"></div>
      <KPage
        :current="+queryFormData.page"
        :page-size="+queryFormData.pageSize"
        :total="total"
        style="text-align: center"
        @on-change="handleCurrentChange"
        @on-page-size-change="handleSizeChange"
      />
    </div>
    <batch-model
      :visible.sync="batchVisible"
      :type="batchType"
      :selected-table="selectedTable"
      @onResetSearch="onResetSearch"
    />
    <batch-price-model :visible.sync="batchPriceVisible" :selected-row="selectedRow" @onResetSearch="onResetSearch" />
    <clinic-list-model :visible.sync="clinicModel" :selected-row="selectedRow" />
  </div>
</template>

<script>
import search from '@/mixins/search';
import S from 'utils/util';
import BatchModel from './components/batchModel.vue';
import { cloneDeep } from 'lodash';
import BatchPriceModel from './components/batchPriceModel.vue';
import ClinicListModel from './components/clinicListModel.vue';
let init_query_form_data = {
  page: 1,
  pageSize: 20,
  name: '',
  source_platform: '',
  goods_type: '', //商品类型
  r: ''
};
export default {
  name: 'list',
  components: { ClinicListModel, BatchPriceModel, BatchModel },
  mixins: [search],
  data() {
    return {
      apiName: 'getGoodsManageList',
      queryFormData: { ...init_query_form_data },
      tableCols: [
        // {
        //   type: 'selection',
        //   width: 60,
        //   align: 'center'
        // },
        { title: '商品ID', key: 'id', width: 80 },
        { title: '商品', slot: 'name', minWidth: 150 },
        { title: '商品类型', key: 'goods_type_text', minWidth: 100 },
        { title: '商品来源', key: 'source_platform_text', minWidth: 100 },
        { title: '创建时间', slot: 'create_time', minWidth: 100 },
        { title: '商品状态', slot: 'status_desc', minWidth: 100 },
        { title: '操作', slot: 'action', align: 'center', width: 100 }
      ],
      tableLoading: false,
      list: [],
      typeDesc: [],
      sourcePlatformDesc: [],
      total: 0,
      batchVisible: false,
      batchType: 'up',
      selectedTable: [],
      batchPriceVisible: false,
      clinicModel: false,
      selectedRow: {}
    };
  },
  computed: {},
  watch: {},
  created() {
    this.queryFormData = S.merge(this.queryFormData, this.$route.query);
    this.submitQueryForm(true);

    // 获取服务类型
    this.getGoodsOptions();
  },
  mounted() {},
  methods: {
    toDetail(row) {
      this.$router.push({
        path: '/goods/warehouse/detail',
        query: { id: row.id }
      })
    },
    clickBatchOpt(val) {
      if (this.selectedTable.length === 0) {
        this.$Message.error('请选择批量操作的商品!');
        return;
      }
      this.batchType = val;
      this.batchVisible = true;
    },
    openPriceModel(row) {
      this.batchPriceVisible = true;
      this.selectedRow = row || {};
    },
    openClinicModel(row, status) {
      row.status = status;
      this.clinicModel = true;
      this.selectedRow = row || {};
    },
    onResetSearch() {
      this.queryFormData = { ...init_query_form_data };
      this.selectedTable = [];
      this.submitQueryForm();
    },
    handlerListData(data) {
      this.list =
        data?.list
          ?.map(item => {
            const isExists = this.selectedTable.some(row => row.id === item.id);
            if (isExists) return { ...item, _checked: true };
            return item;
          })
          ?.map(item => ({ ...item, _disabled: ['30', '35', '40'].includes(item.goods_type) })) || [];
    },
    //诊所选项列表
    getGoodsOptions() {
      this.$api.getGoodsLibOptions().then(res => {
        this.typeDesc = S.descToArrHandle(res.typeDesc);
        this.sourcePlatformDesc = S.descToArrHandle(res.sourcePlatformDesc);
      });
    },
    selectedTableRow(selection, row) {
      const isExists = this.selectedTable.some(item => item.id === row.id);
      !isExists && this.selectedTable.push(row);
      this.$nextTick(() => {
        this.list = this.list?.map(item => {
          if (this.selectedTable?.map(itm => itm.id).includes(item.id)) {
            return {
              ...item,
              _checked: true
            };
          }
          return {
            ...item,
            _checked: false
          };
        });
      });
    },
    cancelTableRow(selection, row) {
      this.selectedTable = this.selectedTable.filter(item => item.id !== row.id);
      this.$nextTick(() => {
        this.list = this.list?.map(item => {
          if (this.selectedTable?.map(itm => itm.id).includes(item.id)) {
            return {
              ...item,
              _checked: true
            };
          }
          return {
            ...item,
            _checked: false
          };
        });
      });
    },
    selectedTableAll(selection) {
      const selectedTable = cloneDeep(this.selectedTable);
      selection.forEach(item => {
        if (selectedTable.every(row => row.id !== item.id)) {
          this.selectedTable.push(item);
        }
      });
    },
    cancelTableAll() {
      this.selectedTable = this.selectedTable.filter(item => this.list.every(row => item.id !== row.id));
    },
    clickTableRow(row) {
      if (row._disabled) return;
      const isExists = this.selectedTable.some(item => item.id === row.id);
      if (isExists) {
        this.selectedTable = this.selectedTable.filter(item => item.id !== row.id);
        this.list = this.list?.map(item => {
          if (item.id === row.id) {
            return {
              ...item,
              _checked: false
            };
          }
          return item;
        });
      }
      if (!isExists) {
        this.selectedTable.push(row);
        this.list = this.list?.map(item => {
          if (item.id === row.id) {
            return {
              ...item,
              _checked: true
            };
          }
          return item;
        });
      }
    }
  },
  beforeRouteUpdate(to, from, next) {
    this.queryFormData = S.merge(init_query_form_data, to.query);
    this.loadList();
    next();
  }
};
</script>

<style lang="less" scoped>
::v-deep .ivu-form-item {
  margin-bottom: 16px;
  label {
    vertical-align: middle;
  }
}
.table-wrapper {
  .table-fun {
    padding-bottom: 10px;
  }
}
</style>
