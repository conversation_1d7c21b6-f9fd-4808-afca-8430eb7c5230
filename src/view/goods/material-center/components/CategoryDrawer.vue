<template>
  <Drawer
    :value="visible"
    title="分组管理"
    width="45%"
    class-name="category-drawer"
    @on-visible-change="visibleChange"
    @on-close="closeDrawer"
  >
    <div class="breadcrumb-box">
      <Breadcrumb>
        <BreadcrumbItem
          v-for="(bread, index) in breadcrumbs"
          :key="bread.id + index"
          :class="{ active: index < breadcrumbs.length - 1 }"
          @click.native="breadClick(bread, index)"
        >
          {{ bread.name }}
        </BreadcrumbItem>
      </Breadcrumb>
    </div>
    <div class="action-bar">
      <Checkbox v-model="isCurPageCheckAll" @on-change="SelectAllChange">当页全选</Checkbox>
      <Divider type="vertical"></Divider>
      <span>已选{{ getSelectInfo() }}项</span>
      <Divider type="vertical"></Divider>
      <span class="mr-12">批量操作</span>
      <Button :disabled="!getSelectInfo()" @click="editCategory">修改分组</Button>
    </div>
    <div class="table-box">
      <Table
        ref="categoryTable"
        :columns="columns"
        @on-select="handleSelect"
        @on-select-cancel="handleSelectCancel"
        :data="data"
        :height="$store.state.app.clientHeight - 206"
        :loading="searchLoading"
      >
        <template v-slot:name="{ row }">
          <div class="flex flex-item-align">
            <svg-icon class="mr-4" style="font-size: 24px" name="folder"></svg-icon>
            <a v-if="row.sub_count > 0" @click="checkChildren(row)">{{ row.name }}</a>
            <span v-else>{{ row.name }}</span>
          </div>
        </template>
      </Table>
    </div>
    <div class="drawer-footer">
      <div class="action-bar flex-1">
        <Checkbox v-model="isCurPageCheckAll" @on-change="SelectAllChange">当页全选</Checkbox>
        <Divider type="vertical"></Divider>
        <span>已选{{ getSelectInfo() }}项</span>
        <Divider type="vertical"></Divider>
        <span class="mr-12">批量操作</span>
        <Button :disabled="!getSelectInfo()" @click="editCategory">修改分组</Button>
      </div>
      <Page
        style="padding-bottom: 12px"
        :current="pagination.page"
        :page-size="pagination.page_size"
        @on-change="onPageChange"
        :total="total"
        simple
      />
    </div>
    <edit-category
      v-model="editCategoryVisible"
      :edit-ids="getSelectInfo(false)"
      :asset-type="assetType"
      :is-batch-edit="true"
      :default-id="parentCategoryId"
      @success="handleSuccess"
    />
  </Drawer>
</template>

<script>
import EditCategory from './EditCategory.vue';
export default {
  name: 'CategoryDrawer',
  components: {
    EditCategory,
  },
  model: {
    prop: 'visible',
    event: 'changeVisible',
  },
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    assetType: {
      type: [String, Number],
      default: '',
    },
  },
  data() {
    return {
      columns: [
        { title: '1212', type: 'selection', width: 60 },
        { title: '分组名', slot: 'name' },
      ],
      data: [],
      total: 49,
      pagination: {
        page: 1,
        page_size: 5,
      },
      parentCategoryId: 0,
      breadcrumbs: [],
      initBreadcrumbs: [{ name: '全部分组', id: 0, parent_id: '' }],
      categoryState: {},
      isCurPageCheckAll: false,
      editCategoryVisible: false,
      treeMap: null,
      categoryNodes: [],
      searchLoading: false,
    };
  },
  computed: {
    getSelectInfo() {
      return (len = true) => {
        const values = Object.keys(this.categoryState);
        if (len) return values.length;
        return values;
      };
    },
  },
  created() {},
  methods: {
    handleCheckboxStatus() {
      const isAllPageDisabled = this.data.every(item => {
        return item._disabled;
      });
      if (isAllPageDisabled) {
        this.isCurPageCheckAll = false;
      } else {
        this.isCurPageCheckAll = this.data.every(item => item._disabled || item.id in this.categoryState);
      }
    },
    handleSelect(selection, row) {
      this.$set(this.categoryState, row.id, row);
      this.handleCheckboxStatus();
    },
    handleSelectCancel(selection, row) {
      this.$delete(this.categoryState, row.id);
      this.handleCheckboxStatus();
    },
    //点击全选时触发
    handleSelectAll(selection) {
      selection.forEach(item => {
        if (item._disabled === false) {
          this.$set(this.categoryState, item.id, item);
        }
      });
    },
    //点击全选时触发
    handleCancelAll() {
      for (let k in this.list) {
        if (this.list[k].id in this.categoryState) {
          this.$delete(this.categoryState, this.list[k].id);
        }
      }
    },
    SelectAllChange(v) {
      console.log('%c [ v ]-131', 'font-size:13px; background:#c4ea49; color:#ffff8d;', v);
      if (v) {
        this.$refs.categoryTable.selectAll(true);
        this.handleSelectAll(this.data);
      } else {
        this.$refs.categoryTable.selectAll(false);
        this.categoryState = {};
      }
    },
    editCategory() {
      this.editCategoryVisible = true;
    },
    visibleChange(v) {
      if (v) {
        this.parentCategoryId = 0;
        this.getCategoryList();
        this.breadcrumbs = [...this.initBreadcrumbs];
      }
    },
    closeDrawer() {
      console.log(1111);
      this.$emit('changeVisible', false);
    },
    breadClick(bread, index) {
      if (index === this.breadcrumbs.length - 1) return;
      this.breadcrumbs.splice(index + 1, this.breadcrumbs.length - index - 1);
      this.parentCategoryId = bread.id;
      this.getCategoryList();
    },
    checkChildren(row) {
      this.parentCategoryId = row.id;
      this.getCategoryList();
      this.breadcrumbs.push(row);
    },
    getCategoryList() {
      this.searchLoading = true;
      this.$api
        .getMtCategoryList({
          asset_type: this.assetType,
          parent_id: this.parentCategoryId,
          ...this.pagination,
        })
        .then(res => {
          this.data = this.handleList(res.list);
          this.handleCheckboxStatus();
          this.total = res.total;
        })
        .finally(() => {
          this.searchLoading = false;
        });
    },
    handleList(list) {
      const map = new Map();
      list.map(item => {
        map.set(item.id, item);
        item._disabled = !item.parent_id;
      });
      for (let k in list) {
        for (let j in this.categoryState) {
          if (list[k].id == this.categoryState[j].id) {
            list[k]['_checked'] = true; // 选中已选项
          }
        }
      }
      this.treeMap = map;
      return list;
    },
    updateBreadcrumbs() {},
    onPageChange(page) {
      this.pagination.page = page;
      this.getCategoryList();
    },
    handleSuccess() {
      this.getCategoryList();
    },
  },
};
</script>

<style lang="less" scoped>
.breadcrumb-box {
  padding-bottom: 16px;
  :deep(.ivu-breadcrumb) {
    .active {
      cursor: pointer;
      &:hover {
        color: #115bd4;
      }
    }
  }
}

.action-bar {
  padding-bottom: 12px;
  padding-left: 12px;
  white-space: nowrap;
}

.table-box {
  :deep(.ivu-table) {
    .ivu-table-header .ivu-table-cell-with-selection {
      visibility: hidden;
    }
  }
}

.drawer-footer {
  position: relative;
  margin-top: 0;
  padding-top: 16px;
  box-shadow: 0 -2px 8px 0 rgba(199, 200, 204, 0.5);
  margin-left: -16px;
  margin-right: -16px;
  padding-left: 16px;
  padding-right: 16px;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
}
</style>

<style lang="less">
.category-drawer {
  .ivu-drawer-body {
    overflow-y: hidden;
  }
}
</style>
