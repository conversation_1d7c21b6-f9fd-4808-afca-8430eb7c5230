<template>
  <Modal
    :title="getTitle"
    :value="visible"
    class-name="vertical-center-modal"
    :mask-closable="false"
    footer-hide
    width="520px"
    @on-visible-change="visibleChange"
  >
    <Form :model="formData" :rules="rules" :label-width="80" ref="categoryForm" class="category-form">
      <FormItem label="所在分组" required prop="parent_id">
        <Select ref="categorySelect" v-model="formData.parent_id" style="width: 100%" placeholder="请输入分组名称">
          <Option :value="formData.parent_id || ''" :label="getNodesPath" v-show="false"></Option>
          <category-tree
            :category-tree="categoryListTree"
            :current-category-id="formData.parent_id"
            :category-select="selectCategory"
            :is-upload="isEditAsset"
          />
        </Select>
        <div class="tips">支持建立四级分组；列表仅展示可操作的分组。</div>
      </FormItem>
      <FormItem v-if="!isBatchEdit && !isEditAsset" label="分组名称" prop="name">
        <Input v-model="formData.name" show-word-limit :maxlength="20" placeholder="20个字符以内" />
      </FormItem>
      <FormItem v-if="isEditAsset && !isBatchEdit" :label="(assetType === 1 ? '图片' : '视频') + '标题'" prop="name">
        <Input v-model="formData.name" :maxlength="50" placeholder="50个字以内" />
      </FormItem>
    </Form>
    <div class="submit-box">
      <Button @click="closeModal"> 取消</Button>
      <Button class="ml-12" type="primary" @click="submitAdd"> 确定</Button>
    </div>
  </Modal>
</template>

<script>
import CategoryTree from '@/components/MaterialCenter/CategoryTree.vue';

export default {
  name: 'EditCategory',
  components: { CategoryTree },
  model: {
    prop: 'visible',
    event: 'visibleChange',
  },
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    editCategoryInfo: {
      type: Object,
      default: () => ({
        name: '',
        id: '',
        level: '',
        type: '',
        parent_id: '',
      }),
    },
    defaultId: {
      type: [Number, String],
      default: null,
    },
    assetType: {
      type: Number,
      default: null,
    },
    categoryTree: {
      type: Array,
      default: () => [],
    },
    isBatchEdit: {
      type: Boolean,
      default: false,
    },
    editIds: {
      type: Array,
      default: () => [],
    },
    isEditAsset: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      key: '',
      rules: {
        parent_id: [{ required: false, message: '请选择所在分组', trigger: ['change'] }],
        name: [{ required: true, message: '请输入分组名称', trigger: ['change'] }],
      },
      formData: {
        name: '',
        id: '',
        parent_id: '',
        asset_type: '',
      },
      categoryNodes: [],
      treeMap: null,
      categoryListTree: [],
    };
  },
  computed: {
    getTitle() {
      if (this.isEditAsset && !this.isBatchEdit) {
        return '编辑' + (this.assetType === 1 ? '图片' : '视频');
      }
      return (this.editCategoryInfo.id ? '修改' : '新增') + '分组';
    },
    getNodesPath() {
      return this.categoryNodes.map(node => node.name).join('/');
    },
  },
  methods: {
    initCategoryData() {
      return this.$api
        .getMtCategoryAllList({
          asset_type: this.assetType,
        })
        .then(res => {
          let list = res.list;
          this.flatTreeHandler(list);
          this.formData.parent_id && this.initCategoryStatus(list, this.formData.parent_id);
          this.categoryListTree = this.buildTreeAndSearch(res.list);
        });
    },
    initCategoryStatus(data, categoryId) {
      data.forEach(item => {
        if (item.id === categoryId) {
          item.isOpen = true;
          if (item.parent_id) {
            this.initCategoryStatus(data, item.parent_id);
          }
        }
      });
    },
    buildTreeAndSearch(data, rootName = null) {
      const map = new Map();
      let rootNode = null; // 用于存储搜索到的根节点
      // 初始化 Map，并附加 children 属性
      data.forEach(item => {
        if (item.type === 2 && item.level === 0) {
          this.defaultEditId = item.id;
        }
        item.isOpen = item.isOpen || false;
        map.set(item.id, { ...item, children: [] });
      });
      // 构建树结构
      data.forEach(item => {
        if (map.has(item.parent_id)) {
          const parent = map.get(item.parent_id);
          parent.children.push(map.get(item.id));
        }
      });
      // 如果指定了 rootName，搜索该节点
      if (rootName) {
        rootNode = Array.from(map.values()).filter(node => node.name.includes(rootName));
      }

      // 如果未指定 rootName 或未找到目标节点，返回完整的树
      if (!rootName || !rootNode) {
        return Array.from(map.values()).filter(node => !map.has(node.parent_id));
      }
      // 返回以指定节点为根的子树
      return rootNode;
    },
    async visibleChange(v) {
      if (v) {
        if (!this.editCategoryInfo.parent_id) {
          this.formData.parent_id = this.defaultId;
        } else {
          this.formData.parent_id = this.editCategoryInfo.parent_id;
          this.formData.id = this.editCategoryInfo.id;
        }
        this.formData.name = this.editCategoryInfo.name ?? '';
        await this.initCategoryData();
        this.categoryNodes = this.findPathToRootById(this.formData.parent_id);
      } else {
        this.closeModal();
      }
    },
    closeModal() {
      this.$refs.categoryForm.resetFields();
      this.$emit('visibleChange', false);
    },
    selectCategory({ id }) {
      this.$refs.categorySelect.visible = false;
      this.categoryNodes = this.findPathToRootById(id);
      this.formData.parent_id = id;
      this.$refs.categoryForm.validateField('parent_id');
    },
    findPathToRootById(id) {
      const path = [];
      // 通过 id 和 parentId 构建路径
      let currentId = id;
      // let currentParentId = parentId;
      while (currentId) {
        const node = this.treeMap.get(currentId);
        if (!node) break; // 如果找不到，结束查找
        path.unshift(node); // 将当前节点添加到路径前面
        currentId = node.parent_id; // 更新为父节点的 ID
      }
      console.log('%c=>(EditCategory.vue:113) path', 'font-size: 18px;color: #FF7043;', path);
      return path;
    },
    // 将树结构平铺成 Map，方便查找
    flatTreeHandler(data) {
      const map = new Map();
      data.map(item => {
        map.set(item.id, item);
      });
      this.treeMap = map;
    },
    submitAdd() {
      this.$refs.categoryForm.validate(valid => {
        console.log('%c=>(EditCategory.vue:219) valid', 'color: #ECA233;font-size: 16px;', valid);
        if (valid) {
          let params = {};
          let apiName = '';
          if (this.isEditAsset) {
            if (this.isBatchEdit) {
              apiName = 'updateMaterialCategory';
              params = {
                category_id: this.formData.parent_id,
                ids: this.editIds,
              };
            } else {
              apiName = 'updateMaterial';
              params = {
                category_id: this.formData.parent_id,
                name: this.formData.name,
                id: this.editCategoryInfo.id,
              };
            }
          } else if (this.isBatchEdit) {
            apiName = 'batchEditMtCategory';
            params = {
              parent_id: this.formData.parent_id,
              ids: this.editIds,
            };
          } else {
            apiName = this.editCategoryInfo.id ? 'updateMtCategory' : 'createMtCategory';
            params = {
              name: this.formData.name,
              parent_id: this.formData.parent_id,
              asset_type: this.assetType,
            };
            this.editCategoryInfo.id && (params.id = this.editCategoryInfo.id);
          }
          console.log('%c=>(EditCategory.vue:252) apiName', 'font-size: 18px;color: #FF7043;', apiName);
          this.$api[apiName](params)
            .then(res => {
              console.log('%c=>(EditCategory.vue:159) res', 'font-size: 18px;color: #FF7043;', res);
              if (this.isEditAsset) {
                this.$Message.success('编辑成功');
              } else {
                this.$Message.success(this.isBatchEdit ? '移动成功' : '保存成功');
              }
              this.$emit('success');
              this.closeModal();
            })
            .catch(e => {
              this.$Message.error(e.message);
            });
        }
      });
    },
  },
};
</script>

<style lang="less" scoped>
.category-form {
  :deep(.ivu-form-item-content) {
    line-height: normal;
  }
}

.submit-box {
  text-align: right;
  margin-top: 28px;
}

.tips {
  color: #969799;
  margin-top: 8px;
  line-height: 14px;
}
</style>
