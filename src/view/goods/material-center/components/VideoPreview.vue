<template>
  <Modal title="视频预览" :value="value" width="720px" footer-hide @on-visible-change="handleVisibleChange">
    <div v-if="value" class="video-container hidden-scroll" >
      <video ref="video" controls :src="videoUrl" style="width: 100%; height: auto"></video>
    </div>
  </Modal>
</template>

<script>
export default {
  name: 'VideoPreview',
  model: {
    prop: 'value',
    event: 'changeVisible',
  },
  props: {
    videoUrl: {
      type: String,
      required: true,
    },
    value: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {};
  },
  methods: {
    handleVisibleChange(v) {
      if (v) {
        // this.$nextTick(() => {
        //   this.playVideo();
        // });
      } else {
        this.$emit('changeVisible', false);
      }
    },
    playVideo() {
      this.$refs.video.play();
    },
    pauseVideo() {
      this.$refs.video.pause();
    },
  },
};
</script>

<style lang="less" scoped>
.video-container {
  min-height: 158px;
  max-height: ~'calc(100vh - 204px)';
  overflow-y: auto;
}
</style>
