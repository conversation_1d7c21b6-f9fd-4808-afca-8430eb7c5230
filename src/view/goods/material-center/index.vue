<template>
  <div class="material-wrap">
    <!--    <header class="section-block">-->
    <!--      <h2 class="page-title">-->
    <!--        素材中心-->
    <!--        <Tooltip placement="bottom-start" theme="light">-->
    <!--          <Icon type="ios-arrow-dropdown" />-->
    <!--          <div slot="content" class="tooltip-content">-->
    <!--            <div>图片/音频/文档空间：{{ getSize(memoryInfo.image_size) }}</div>-->
    <!--            <div>视频空间：{{ getSize(memoryInfo.video_size) }}</div>-->
    <!--          </div>-->
    <!--        </Tooltip>-->
    <!--      </h2>-->
    <!--      &lt;!&ndash;      <p class="mt-4">总容量：3000G（包含图片/音频/文档1000G、视频2000G）</p>&ndash;&gt;-->
    <!--    </header>-->
    <input type="text" name="username" style="position: fixed; left: -2000px" />
    <div class="section-block">
      <Tabs v-model="activeTabName" class="material-tabs" :animated="false" @on-click="handleTabClick">
        <TabPane v-for="item in materialTabs" :key="item.name" :label="item.label" :name="item.name"></TabPane>
      </Tabs>
      <div class="material-content" ref="materialContent">
        <div class="header-box">
          <Button type="primary" @click="toUpload">{{ activeTabName === 'image' ? '上传图片' : '上传视频' }}</Button>
        </div>
        <div
          class="content-main"
          v-loading="tableLoading"
          element-loading-text="加载中..."
          element-loading-spinner="el-icon-loading"
        >
          <category-manager ref="categoryManager" :category-type="activeTabName" @getCategoryInfo="getCategoryInfo" />
          <div class="app-media-list">
            <h1 class="category-name">{{ selectCategory.name }}</h1>
            <div class="action-bar">
              <Checkbox :value="isCurPageCheckAll" :disabled="!mediaList.length" @on-change="selectAllHandler">
                当页全选
              </Checkbox>
              <Divider type="vertical"></Divider>
              <span>已选{{ getAssetsCount }}项</span>
              <Divider type="vertical"></Divider>
              <span class="mr-12">批量操作</span>
              <Button :disabled="!getAssetsCount" @click="batchEditCategory">修改分组</Button>
              <Button :disabled="!getAssetsCount" class="ml-16" @click="batchDownload">批量下载</Button>
              <Button :disabled="!getAssetsCount" class="ml-16" @click.stop="batchDelete">批量删除</Button>
            </div>
            <template v-if="activeTabName === 'image'">
              <div class="items-container" v-if="mediaList.length > 0">
                <div v-for="(item, index) in mediaList" :key="item.id" class="image-item">
                  <div
                    class="image-box"
                    @click="previewImage(index)"
                    :style="{ backgroundImage: `url(${item.url}-B.450)` }"
                  >
                    <div class="ckt-image-box">
                      <a
                        @click.stop
                        :href="`${item.url}?attname=${encodeURIComponent(item.name)}.${item.format}`"
                        target="_blank"
                        rel="noopener noreferrer"
                      >
                        <Icon type="md-cloud-download" color="#e5e5e5" size="20" />
                      </a>
                    </div>
                  </div>
                  <div class="image-title flex flex-item-align">
                    <Checkbox size="large" v-model="item._checked" @on-change="v => handleItemCheck(v, item)">
                      <Tooltip theme="light" :disabled="item.name.length < 10" :content="item.name" placement="top">
                        <span style="width: 88px; display: inline-block" class="single-line-ellipsis">
                          {{ item.name }}</span
                        >
                      </Tooltip>
                    </Checkbox>
                  </div>
                  <div class="image-opt">
                    <a @click="handleItemAction(item)">编辑</a>
                    <Divider type="vertical"></Divider>
                    <a @click.stop="handleItemAction(item, 'link')">链接</a>

                    <Divider type="vertical"></Divider>
                    <a href="javascript:;" @click.stop="e => handleDeleteItem(e, item)">删除</a>
                  </div>
                </div>
                <div class="image-item"></div>
                <div class="image-item"></div>
                <div class="image-item"></div>
                <div class="image-item"></div>
                <div class="image-item"></div>
                <div class="image-item"></div>
                <div class="image-item"></div>
                <div class="image-item"></div>
                <div class="image-item"></div>
                <div class="image-item"></div>
              </div>
              <div class="empty-tip" v-if="!mediaList.length">暂无数据，可点击左上角“上传”按钮添加</div>
            </template>
            <template v-if="activeTabName === 'video'">
              <Table
                :data="mediaList"
                :columns="videoColumns"
                :border="false"
                @on-select="handleSelect"
                @on-select-cancel="handleSelectCancel"
                @on-select-all="handleSelectAll"
                @on-select-all-cancel="handleSelectAllCancel"
              >
                <template v-slot:video="{ row }">
                  <div class="video-item">
                    <div
                      class="left"
                      @click="previewVideo(row.url)"
                      :style="{
                        cursor: 'pointer',
                        backgroundImage: `url(${row.cover_url}-B.450)`,
                      }"
                    >
                      <Icon class="play-icon" size="24" type="md-arrow-dropright-circle" />
                    </div>
                    <div class="right">
                      <a>{{ row.name }}</a>
                      <div class="video-duration">{{ formatDuration(row.meta_info?.duration) }}</div>
                    </div>
                  </div>
                </template>
                <template v-slot:size="{ row }">
                  {{ getSize(row.size) }}
                </template>
                <template v-slot:action="{ row }">
                  <a @click="handleItemAction(row)">编辑</a>
                  <Divider type="vertical"></Divider>
                  <a @click.stop="downloadVideo(row)">下载</a>
                  <Divider type="vertical"></Divider>
                  <a href="javascript:;" @click.stop="e => handleDeleteItem(e, row)">删除</a>
                </template>
              </Table>
            </template>
            <div v-if="mediaList.length > 0" class="pagination-box">
              <div class="action-bar">
                <Checkbox :value="isCurPageCheckAll" :disabled="!mediaList.length" @on-change="selectAllHandler">
                  当页全选
                </Checkbox>
                <Divider type="vertical"></Divider>
                <span>已选{{ getAssetsCount }}项</span>
                <Divider type="vertical"></Divider>
                <span class="mr-12">批量操作</span>
                <Button :disabled="!getAssetsCount" @click="batchEditCategory">修改分组</Button>
                <Button :disabled="!getAssetsCount" class="ml-16" @click="batchDownload">批量下载</Button>
                <Button :disabled="!getAssetsCount" class="ml-16" @click.stop="batchDelete">批量删除</Button>
              </div>
              <Page
                style="text-align: right"
                :page-size-opts="[5, 10, 20, 50]"
                :total="total"
                :page-size="queryFormData.page_size"
                :current="queryFormData.page"
                show-total
                show-sizer
                show-elevator
                @on-change="handlePageChange"
                @on-page-size-change="handlePageSizeChange"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
    <upload-modal
      v-model="uploadVisible"
      :current-category-id="selectCategory.id"
      :asset-type="mediaTypeMap[activeTabName]"
      @uploadSuccess="refreshMediaList"
      :limit="30"
    />
    <edit-category
      v-model="editCategoryVisible"
      :edit-category-info="editItem"
      :asset-type="mediaTypeMap[activeTabName]"
      :edit-ids="getBatchIds"
      :default-id="selectCategory.id"
      is-edit-asset
      :is-batch-edit="isBatchEdit"
      @success="refreshMediaList"
    />
    <Poptip
      ref="indexDelPop"
      :reference="popReference"
      placement="bottom-start"
      style="height: 0; width: 0; display: none"
    >
      <div slot="content" class="del-pop">
        <div class="title">确认删除所选{{ getAssetName }}吗？</div>
        <div class="desc">删除{{ getAssetName }}不会对已使用{{ getAssetName }}的业务造成影响。</div>
        <div class="action-btn">
          <Button class="mr-12" size="small" @click="hidePop">取消</Button>
          <Button type="primary" size="small" :loading="deleteLoading" @click="confirmDelItem">删除</Button>
        </div>
      </div>
    </Poptip>
    <Poptip ref="linkPop" :reference="popReference" :width="350" placement="bottom-start">
      <div slot="content" class="link-pop">
        <Input v-model="editItem.url" disabled />
        <Button class="ml-12" type="primary" @click="confirmCopy">复制</Button>
      </div>
    </Poptip>
    <video-preview v-model="previewVideoVisible" :video-url="previewVideoUrl"></video-preview>
  </div>
</template>

<script>
import CategoryManager from './components/CategoryManager.vue';
import UploadModal from '@/components/MaterialCenter/UploadModal.vue';
import EditCategory from './components/EditCategory.vue';
import { downloadImagesAsZip } from '@/utils/packageDownload';
import VideoPreview from '@/components/MaterialCenter/VideoPreview.vue';

export default {
  name: 'MaterialCenter',
  components: {
    VideoPreview,
    CategoryManager,
    UploadModal,
    EditCategory,
  },

  data() {
    return {
      activeTabName: 'image',
      mediaTypeMap: {
        image: 1,
        video: 2,
      },
      materialTabs: [
        {
          label: '图片',
          name: 'image',
        },
        {
          label: '视频',
          name: 'video',
        },
      ],
      mediaList: [],
      selectCategory: {
        id: '',
        name: '',
      },
      uploadVisible: false,
      stateManager: {},
      queryFormData: {
        page: 1,
        page_size: 20,
      },
      total: 0,
      tableLoading: false,
      popReference: null,
      linkRefer: null,
      editItem: {},
      editCategoryVisible: false,
      deleteLoading: false,
      isBatchEdit: false, // 批量删除
      videoColumns: [
        { title: '', type: 'selection', width: 60 },
        { title: '视频', slot: 'video', width: 300 },
        { title: '大小', slot: 'size' },
        { title: '上传时间', key: 'created_at' },
        // { title: '播放次数', key: 'name' },
        // { title: '状态', key: 'name' },
        { title: '操作', slot: 'action' },
      ],
      previewVideoUrl: '',
      previewVideoVisible: false,
      memoryInfo: {
        image_size: 0,
        video_size: 0,
      },
    };
  },
  computed: {
    formatDuration() {
      return seconds => {
        seconds = Math.floor(seconds);
        // 计算分钟和剩余秒数
        const minutes = Math.floor(seconds / 60);
        const remainingSeconds = seconds % 60;

        // 格式化为两位数
        const formattedMinutes = String(minutes).padStart(2, '0');
        const formattedSeconds = String(remainingSeconds).padStart(2, '0');

        return `${formattedMinutes}:${formattedSeconds}`;
      };
    },
    getAssetName() {
      return this.activeTabName === 'image' ? '图片' : '视频';
    },
    getSize() {
      return size => {
        return (size / 1024 / 1024).toFixed(2) + 'MB';
      };
    },
    getAssetsCount() {
      return this.getBatchIds.length;
    },
    getBatchIds() {
      return Object.keys(this.stateManager);
    },
    isCurPageCheckAll() {
      return !!this.mediaList.length && this.mediaList.every(item => item._checked);
    },
    getImageUrls() {
      return this.mediaList.map(item => item.url);
    },
  },
  mounted() {
    // if (this.$route.query.createCategory) {
    //   this.activeTabName = this.$route.query.type;
    // this.$nextTick(() => {
    //   this.$refs.categoryManager.addCategory();
    // });
    // }
    this.getOptions();
  },

  methods: {
    async downloadVideo(item) {
      try {
        const response = await fetch(item.url);
        if (!response.ok) {
          throw new Error('网络错误，无法下载视频。');
        }
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const extention = item.url.split('.').pop();
        // 创建一个链接元素并触发下载
        const a = document.createElement('a');
        a.style.display = 'none';
        a.href = url;
        a.download = item.name + '.' + extention; // 设置下载的文件名
        document.body.appendChild(a);
        a.click(); // 触发下载
        window.URL.revokeObjectURL(url); // 释放 URL 对象
      } catch (error) {
        console.error('下载视频失败:', error);
      }
    },
    previewVideo(url) {
      this.previewVideoUrl = url;
      this.previewVideoVisible = true;
    },
    handleTabClick(name) {
      console.log(
        '%c=>(index.vue:347) name',
        'background-color: green; color: #fff; padding: 4px 20px; border-radius: 4px;',
        name
      );
      // this.$router.replace({
      //   path: `/goods/material-center?type=${this.activeTabName}`,
      // });
      console.log(this.activeTabName);
      this.activeTabName = name;
      this.mediaList = [];
      this.$nextTick(() => {
        this.refreshMediaList();
      });
      this.stateManager = {};
    },
    // 全选反选
    selectAllHandler(v) {
      this.mediaList.map(item => {
        item._checked = v;
        if (v) {
          this.$set(this.stateManager, item.id, item);
        } else {
          this.$delete(this.stateManager, item.id);
        }
      });
    },
    previewImage(index) {
      this.$viewerApi({
        images: this.getImageUrls,
        options: {
          initialViewIndex: index,
        },
      });
    },
    refreshMediaList() {
      console.log(2222);
      this.isBatchEdit = false;
      console.log(this.$refs.categoryManager);
      this.$refs.categoryManager.initCategoryData();
      // this.getMediaList();
    },
    handleDeleteItem(e, item) {
      this.isBatchEdit = false;
      this.editItem = { ...item, parent_id: item.category_id };
      this.showPop('indexDelPop', e);
    },
    showPop(ref, e) {
      this.hidePop();
      const newDom = e.target;
      let popStone = this.$refs[ref];
      popStone.visible = false;
      popStone.doDestroy();
      this.popReference = newDom;
      popStone.visible = true;
    },
    batchEditCategory() {
      this.isBatchEdit = true;
      this.editCategoryVisible = true;
    },
    handleItemCheck(v, item) {
      console.log('%c=>(index.vue:243) item', 'font-size: 18px;color: #FF7043;', item);
      if (v) {
        this.$set(this.stateManager, item.id, item);
      } else {
        this.$delete(this.stateManager, item.id);
      }
    },
    handleItemAction(item, action = 'edit') {
      this.isBatchEdit = false;
      this.editItem = { ...item };
      switch (action) {
        case 'edit':
          this.editCategoryVisible = true;
          break;
        case 'link':
          this.showPop('linkPop', event);
          break;
        default:
          return;
      }
    },
    hidePop() {
      this.$refs.materialContent.click();
    },
    batchDownload() {
      const items = Object.values(this.stateManager);
      downloadImagesAsZip(items, this.activeTabName === 'image' ? '图片下载.zip' : '视频下载.zip');
      this.refreshMediaList();
    },
    batchDelete(e) {
      this.isBatchEdit = true;
      this.showPop('indexDelPop', e);
    },
    changeCategory() {},
    confirmDelItem() {
      let ids = [];
      if (this.isBatchEdit) {
        ids = Object.values(this.stateManager).map(item => item.id);
        this.deleteMaterial(ids);
      } else {
        ids = [this.editItem.id];
        this.deleteMaterial(ids);
      }
    },
    deleteMaterial(ids) {
      this.deleteLoading = true;
      this.$api.deleteMaterial({ ids }).then(res => {
        this.$Message.success('删除成功');
        if (ids.length > 1) {
          this.queryFormData.page = 1;
          this.stateManager = {};
        } else {
          if (ids[0] in this.stateManager) {
            this.$delete(this.stateManager, ids[0]);
          }
        }

        this.isBatchEdit = false;
        this.refreshMediaList();
        this.hidePop();
        this.deleteLoading = false;
      });
    },

    confirmCopy() {
      navigator.clipboard.writeText(this.editItem.url).then(res => {
        this.$Message.success('复制成功');
      });
    },
    toUpload() {
      this.uploadVisible = true;
    },
    getCategoryInfo(category) {
      console.log(
        '%c=>(index.vue:469) category',
        'background-color: green; color: #fff; padding: 4px 20px; border-radius: 4px;',
        category
      );
      this.queryFormData.page = 1;
      this.queryFormData.page_size = 20;
      this.selectCategory.id = category.id;
      this.selectCategory.name = category.name;
      this.stateManager = {};
      this.getMediaList();
    },
    getOptions() {
      this.$api.getMaterialOptions().then(res => {
        console.log('%c=>(index.vue:382) res', 'font-size: 18px;color: #FF7043;', res);
        this.memoryInfo = {
          image_size: res.image_size,
          video_size: res.video_size,
        };
      });
    },
    getMediaList() {
      this.tableLoading = true;
      this.$api
        .getMaterialList({
          category_id: this.selectCategory.id,
          type: this.activeTabName === 'image' ? 1 : 2,
          ...this.queryFormData,
        })
        .then(res => {
          this.mediaList = this.handleList(res.list);
          this.total = res.total;
        })
        .finally(() => {
          this.tableLoading = false;
        });
    },
    handleList(list) {
      list.map(item => {
        item._checked = item.id in this.stateManager;
      });
      return list;
    },
    handlePageChange(page) {
      this.queryFormData.page = page;
      this.getMediaList();
    },
    handlePageSizeChange(pageSize) {
      this.queryFormData.page_size = pageSize;
      this.stateManager = {};
      this.getMediaList();
    },
    handleSelect(selections, row) {
      console.log(selections, row);
      this.handleItemCheck(true, row);
    },
    handleSelectCancel(selections, row) {
      this.handleItemCheck(false, row);
    },
    handleSelectAll(selection) {
      console.log(selection);
      this.selectAllHandler(true);
    },
    handleSelectAllCancel() {
      this.selectAllHandler(false);
    },
  },
};
</script>

<style lang="less" scoped>
.material-wrap {
  :deep(.material-tabs .ivu-tabs-nav-container) {
    font-size: 14px;
  }

  .material-content {
    .header-box {
      margin: 12px 0;
    }

    .content-main {
      display: flex;
      height: calc(~'max(100vh - 260px, 300px)');
      min-height: 400px;

      .app-media-list {
        flex: auto;
        box-sizing: border-box;
        border: 1px solid #dcdee0;
        padding: 12px;
        overflow-y: auto;

        &::-webkit-scrollbar {
          display: none;
        }

        .category-name {
          font-size: 16px;
          line-height: 24px;
          font-weight: 700;
          color: #323233;
        }

        .action-bar {
          font-size: 14px;
          margin: 12px 0;
          line-height: 20px;
        }
      }
    }
  }
}

.empty-tip {
  text-align: center;
  font-size: 14px;
  margin: 60px 0;
}

.tooltip-content {
  color: #39393a;
  font-size: 13px;
  line-height: 20px;
}

.items-container {
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  overflow: hidden;
  margin: -6px;
  font-size: 14px;

  .image-item {
    flex: 1 0 0;
    position: relative;
    min-width: 112px;
    max-width: 130px;
    margin: 6px;

    .image-box {
      box-sizing: content-box;
      position: relative;
      height: 0;
      width: 100%;
      padding-bottom: 100%;
      cursor: pointer;
      background-color: #f7f8fa;
      background-position: 50%;
      background-size: cover;
      background-repeat: no-repeat;
      overflow: hidden;

      .ckt-image-box {
        display: none;
        position: absolute;
        right: 0;
        top: 0;
        margin: 5px;
      }

      &:hover {
        .ckt-image-box {
          display: unset !important;
        }
      }
    }

    .image-title {
      margin: 8px 0 4px;
      font-size: 14px;
      line-height: 20px;
      height: 20px;
      text-overflow: ellipsis;
      white-space: nowrap;
      overflow: hidden;
    }

    .image-opt {
    }
  }
}

.video-item {
  display: flex;
  align-items: center;
  height: 60px;
  padding: 4px 12px;

  .left {
    flex: none;
    width: 60px;
    background-color: #f2f3f5;
    background-size: cover;
    background-repeat: no-repeat;
    background-position: 50%;
    margin-right: 10px;
    color: #646566;
    box-sizing: content-box;
    height: 100%;
    position: relative;

    .play-icon {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      font-size: 24px;
      color: #fff;
    }
  }

  .right {
    display: flex;
    flex-direction: column;
    flex: auto;
    justify-content: center;

    .video-duration {
      color: #969799;
      font-size: 12px;
      margin-top: 2px;
    }
  }
}

.section-block {
  background-color: #fff;
  padding: 24px 16px;
  //margin-bottom: 8px;
  height: 100%;
}

.link-pop {
  display: flex;
  align-items: center;
  padding: 6px 12px;
}

.del-pop {
  padding: 13px 12px;
  color: #323233;
  font-size: 14px;

  .desc {
    color: #969799;
    margin-bottom: 12px;
  }

  .action-btn {
    text-align: right;
  }
}
</style>
<style lang="less">
.app-inner:has(.material-wrap) {
  background: #f2f2f2 !important;
  padding: 0 !important;
  margin-bottom: 0 !important;
}
</style>
