<template>
	<div>
		<Form inline :label-width="0" @submit.native.prevent @keyup.enter.native="onSearch">
      <Row>
          <FormItem>
            <Input type="text" placeholder="请输入商品名称" v-model="queryFormData.name" />
          </FormItem>

          <FormItem>
            <Select v-model="queryFormData.prod_type" placeholder="请选择商品类型" clear>
              <Option v-for="(desc, level) in typeOptions" :key="desc.id" :value="level">{{ desc.name }}</Option>
            </Select>
          </FormItem>

<!--        <Col>-->
<!--          <FormItem label="商品状态:">-->
<!--            <Select v-model="queryFormData.status" placeholder="全部">-->
<!--              <Option value="">全部</Option>-->
<!--              <Option v-for="(desc, level) in statusDescList" :key="desc.kw" :value="level">{{ desc.desc }}</Option>-->
<!--            </Select>-->
<!--          </FormItem>-->
<!--        </Col>-->

        <Col>
          <FormItem>
            <Button class="mr-10" type="primary" @click="onSearch">筛选</Button>
            <Button @click="onResetSearch">重置</Button>
          </FormItem>
        </Col>
      </Row>
    </Form>

		<div class="table-wrapper">
<!--			<div class="table-fun">-->
<!--        <Button type="primary" @click="createProduct">添加货品</Button>-->
<!--      </div>-->
      <Table :loading="tableLoading" row-key="_key" :columns="tableCols" :data="list" :height="$store.state.app.clientHeight-240">
        <template slot="tree" slot-scope="{ row }"></template>
        <template slot="id" slot-scope="{ row }">
          <div v-if="row._type === 'first'">{{ row.children?.length === 0 ? row.id : '-' }}</div>
          <div v-else style="margin-left: 20px;">{{ row.id }}</div>
        </template>
        <template slot="grade_desc" slot-scope="{ row }">
          <span v-if="row._type === 'first' && row.children?.length">-</span>
          <span v-else>{{ row.grade_desc || '-' }}</span>
        </template>
        <template slot="prod_spec" slot-scope="{row}">
          <span>{{row.prod_spec||'-'}}</span>
        </template>
        <template slot="manufacturer" slot-scope="{ row }">
          <span v-if="row._type === 'first' && row.children?.length">-</span>
          <span v-else>{{ row.manufacturer || '-' }}</span>
        </template>
        <!-- 创建时间 -->
        <template slot="create_time" slot-scope="{row}">
          <span>{{row.create_time | date_format}}</span>
        </template>

        <template slot="retail_price" slot-scope="{row}">
          <span>{{row.retail_price?'￥'+row.retail_price:'-'}}</span>
        </template>
        <template slot="split_info" slot-scope="{row}">
          <span v-if="row.is_split ==='1'">{{ row.is_split?'是':'否' }} / {{ row.split_num }} / {{ row.split_price }}</span>
          <span v-else>-</span>
        </template>
        <template slot="prod_unit" slot-scope="{ row }">
          <span v-if="row._type === 'first' && row.children?.length">-</span>
          <span v-else>
            <span v-if="!row.prod_unit && !row.split_prod_unit">-</span>
            <span v-else>
              <span>{{ row.prod_unit }}</span>
              <span v-if="row.split_prod_unit"> / {{ row.split_prod_unit }}</span>
            </span>
          </span>
        </template>
        <template slot="relate_num" slot-scope="{ row }">
          <span v-if="row._type === 'first' && row.children?.length">{{ row.children.length }}</span>
          <span v-else>-</span>
        </template>
        <template slot="sync_cli_num" slot-scope="{ row }">
          <span v-if="row._type === 'first' && row.children?.length">-</span>
          <span v-else>{{ row.sync_cli_num || '-' }}</span>
        </template>
        <template slot="pms_status_text" slot-scope="{ row }">
          <span v-if="row._type === 'first' && row.children?.length">-</span>
          <span v-else>{{ row.pms_status_text || '-' }}</span>
        </template>
        <template slot="cf_status_text" slot-scope="{ row }">
          <span v-if="row._type === 'first' && row.children?.length">-</span>
          <span v-else>{{ row.cf_status_text || '-' }}</span>
        </template>
        <template slot="action" slot-scope="{row}">
           <k-link :to="{path: '/goods/products/product-detail', query: {id: row.id}}" class="space6">详情</k-link>
        </template>
      </Table>
      
			<div class="block_20"></div>
			<KPage :current="+queryFormData.page"
        :page-size="+queryFormData.pageSize"
        :total="total"
        style="text-align:center"
        @on-change="handleCurrentChange"
        @on-page-size-change="handleSizeChange"
			/>
		</div>

	</div>
</template>

<script>
import cloneDeep from 'lodash.clonedeep'
import search from '@/mixins/search'
import S from 'utils/util'
let init_query_form_data = {
  page: 1,
  pageSize: 20,
  name: '',
  status: '',
  prod_type: '',//商品类型
  r: '',
}
let init_add_form_data = {
  id: '',
  name: '',
  price: null,
  serv_type: '', // 服务类型
}
export default {
	name: 'list',
	mixins: [search],
	data() {
		return {
			apiName: 'getProductsList',
			queryFormData: {...init_query_form_data},
      tableCols: [
        { title: ' ', slot: 'tree', align: 'right', tree: true, width: 40 },
        {title: '编码', slot: 'id',align: 'left',width: 80},
				{title: '名称', key: 'generic_name', align: 'center',width: 120},
				{title: '类型', key: 'prod_type_text', align: 'center',width: 80},
        {title: '品级', slot: 'grade_desc', align: 'center', width: 80 },
				{title: '规格', slot: 'prod_spec', align: 'center',width: 120},
        { title: '厂家', slot: 'manufacturer', align: 'center', width: 120 },
        {title: '建议零售价', slot: 'retail_price', align: 'center',width: 80},
        { title: '是否拆零', slot: 'split_info', align: 'center', width: 200 },
        { title: '零售/拆零单位', slot: 'prod_unit', align: 'center', width: 120 },
        { title: '关联货品', slot: 'relate_num', align: 'center', width: 80 },
        { title: '同步门店数', slot: 'sync_cli_num', align: 'center', width: 80 },
        { title: '平台售卖状态', slot: 'pms_status_text', align: 'center', width: 100 },
        { title: '常繁售卖状态', slot: 'cf_status_text', align: 'center', width: 100 },
				{title: '创建日期', slot: 'create_time', align: 'center',width: 130},
				{title: '操作', slot: 'action',fixed: 'right', align: 'center', width: 80},
				// {title: '批号', key: 'batch_code', align: 'center'},
				// {title: '过期时间', slot: 'expire_date', align: 'center'},
        // {title: '当前库存', key: 'stock_num_text', align: 'center'},
      ],
      tableLoading: false,
      list: [],
      total: 0,
      statusDescList: {},
      addModal: false,
      addModalTitle: '添加货品',
      isEditName: false, // 默认可编辑名称
      addModalLoading: false,
      addFormData: {...init_add_form_data},

      typeOptions: {}, // 服务类型数据
		}
	},
	computed: {},
	watch: {},
	created() {
		this.queryFormData = S.merge(this.queryFormData, this.$route.query)
		this.submitQueryForm(true)

    // 获取服务类型
    this.getGoodsOptions()
	},
	mounted() {},
	methods: {
    handleList () {
      this.list?.forEach( (item, index) => {
        item.children = item.group_items || []
        item._showChildren = true
        item._key= index
        item._type = 'first'
        item.group_items.forEach(c_item => c_item._key = c_item.id)
      })
    },
    onOpenAddModal: function (id, type) {
      if (!id) {
        this.addModalTitle = '添加货品'
        this.addFormData = {...init_add_form_data}
      } else {
        this.addModalTitle = '编辑货品'

        let service = {}
        this.list.forEach(tmpService => {
          if (tmpService.id == id) {
            service = tmpService
          }
        })

        this.addFormData.id = service.id
        this.addFormData.name = service.name
        this.addFormData.serv_type = service.serv_type
        this.addFormData.price = Number(service.price)

        if ( type == 'HIS' ) {
          this.isEditName = true
        }
      }
      this.addModal = true
    },

    cancel: function () {
      this.addModal = false
    },

    onAdd: function () {
      let formData = {...this.addFormData}
      if (!formData.name.trim()) {
        this.$Message.error('请填写服务名称')
        return
      }

      if (!formData.serv_type.trim()) {
        this.$Message.error('请选择服务类型')
        return
      }

      if ( Number(formData.price) == 0 ) {
        this.$Message.error('服务价格不能为0')
        return
      }
        
      this.addModalLoading = true
      io.post('clinic/goods.goodsservice.edit', formData).then(() => {
        this.$Message.success('保存成功')
        this.addModal = false
        this.submitQueryForm(true)
      }).catch(error => {
        this.addModalLoading = false
        this.$Message.error(error.errmsg)
      })
    },

		onResetSearch() {
			this.queryFormData = {...init_query_form_data}
			this.submitQueryForm()
		},

		//诊所选项列表
    getGoodsOptions(){
			this.$api.getGoodsListOptions().then(res=>{
        console.log("-> %c res  === %o", "font-size: 15px;color: green;", res)
        this.typeOptions = res.prodTypes
        // this.typeList = S.descToArrHandle(res.prodTypes)
        // this.statusDescList = res.prodStatusDesc
      })
		},

    createProduct() {
      this.$router.push({
        path: '/goods/products/edit',
        query: {}
      })
    }
	},
	beforeRouteUpdate(to, from, next) {
		this.queryFormData = S.merge(init_query_form_data, to.query)
		this.loadList()
		next()
	},
}
</script>

<style lang="less" scoped>
::v-deep .ivu-form-item {
	margin-bottom: 16px;
	label {
		vertical-align: middle;
	}
}
.table-wrapper{
	.table-fun{
    padding-bottom: 10px;
	}
}
</style>
