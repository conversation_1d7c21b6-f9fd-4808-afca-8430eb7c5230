<template>
  <div>
    <div class="product-info-box">
      <div class="block-header">商品基本信息</div>
      <Row>
        <Col span="8" class="common-detail-item">
          <k-widget label="通用名" text>
            <span class="item-value">{{ echoData.prod_name||'-' }}</span>
          </k-widget>
        </Col>
        <Col span="8" class="common-detail-item">
          <k-widget label="商品名" text>
            <span class="item-value">{{ echoData.generic_name||'-' }}</span>
          </k-widget>
        </Col>
        <Col span="8" class="common-detail-item">
          <k-widget label="英文名" text>
            <span class="item-value">{{ echoData.english_name||'-' }}</span>
          </k-widget>
        </Col>
        <Col span="8" class="common-detail-item">
          <k-widget label="生产厂家" text>
            <span class="item-value">{{ echoData.manufacturer||'-' }}</span>
          </k-widget>
        </Col>
        <Col span="8" class="common-detail-item">
          <k-widget label="药品条形码" text>
            <span class="item-value">{{ echoData.bar_code||'-' }}</span>
          </k-widget>
        </Col>
        <Col span="8" class="common-detail-item">
          <k-widget label="批准文号" text>
            <span class="item-value">{{ echoData.approval_number||'-' }}</span>
          </k-widget>
        </Col>
        <Col span="8" class="common-detail-item">
          <k-widget label="商品类型" text>
            <span class="item-value">{{ echoData.prod_type_text||'-' }}</span>
          </k-widget>
        </Col>
        <Col span="8" class="common-detail-item">
          <k-widget label="助记码" text>
            <span class="item-value">{{ echoData.phonetic_code||'-' }}</span>
          </k-widget>
        </Col>
        <Col span="8" class="common-detail-item">
          <k-widget label="药品使用年龄" text>
            <span class="item-value">{{ usageAgeDesc[echoData.usage_age]||'-' }}</span>
          </k-widget>
        </Col>
        <Col v-if="col_class_show" span="8" class="common-detail-item">
          <k-widget label="药品分类" text>
            <span class="item-value">{{ echoData.one_level_classid_text }}/{{ echoData.two_level_classid_text }}/{{ echoData.three_level_classid_text }}</span>
          </k-widget>
        </Col>
        <Col v-if="col_allergen_show" span="8" class="common-detail-item">
          <k-widget label="过敏禁忌" text>
            <span class="item-value">{{ echoData.allergen_taboo.length?echoData.allergen_taboo.join(''):'-' }}</span>
          </k-widget>
        </Col>
      </Row>

      <div class="block-header">商品规格信息</div>
      <div>
        <Row>
          <Col span="8" class="common-detail-item">
            <k-widget label="规格" text>
              <span class="item-value">{{ echoData.prod_spec||'-' }}</span>
            </k-widget>
          </Col>
          <Col span="8" class="common-detail-item">
            <k-widget label="包装单位" text>
              <span class="item-value">{{ echoData.prod_unit||'-' }}</span>
            </k-widget>
          </Col>
          <!-- <Col span="8"></Col> -->
          <Col span="8" class="common-detail-item">
            <k-widget label="零售价" text>
              <span class="item-value">￥{{ echoData.retail_price|number_format('￥') }}</span>
            </k-widget>
          </Col>
          <Col span="8" class="common-detail-item">
            <k-widget label="是否拆零" text>
              <span class="item-value">{{ echoData.is_split_text||'-' }}</span>
            </k-widget>
          </Col>
          <Col v-if="echoData.is_split == '1'" span="8" class="common-detail-item">
            <k-widget label="拆零比" text>
              <span class="item-value">{{ echoData.split_num }}{{ echoData.split_prod_unit }}</span>
            </k-widget>
          </Col>
          <Col v-if="echoData.is_split == '1'" span="8" class="common-detail-item">
            <k-widget label="拆零价" text>
              <span class="item-value">￥{{ echoData.split_price }}</span>
            </k-widget>
          </Col>
        </Row>
      </div>

      <div class="block-header">商品用法</div>
      <Row>
        <Col span="8" class="common-detail-item">
          <k-widget label="默认用法" text>
            <span class="item-value">{{ echoData.usage_type_text||'-' }}</span>
          </k-widget>
        </Col>
        <Col span="8" class="common-detail-item">
          <k-widget label="单次剂量" text>
            <span class="item-value" v-if="echoData.single_dose">{{ echoData.single_dose }}{{ echoData.single_unit }}</span>
            <span class="item-value" v-else>-</span>
          </k-widget>
        </Col>
        <Col span="8" class="common-detail-item">
          <k-widget label="默认频次" text>
            <span class="item-value">{{ echoData.frequency||'-' }}</span>
          </k-widget>
        </Col>
        <Col span="16" class="common-detail-item">
          <k-widget label="备注" text>
            <span class="item-value">{{ echoData.remark||'-' }}</span>
          </k-widget>
        </Col>
        <Col span="16" class="common-detail-item">
          <k-widget label="说明书" text>
            <span class="item-value">{{ echoData.instructions_txt||'-' }}</span>
          </k-widget>
        </Col>
        <Col span="16" class="common-detail-item">
          <k-widget label="说明图片" text>
            <Picture v-if="echoData.instructions_imgs.length" v-model="echoData.instructions_imgs" isQueryDetail></Picture>
            <span v-else>-</span>
          </k-widget>
        </Col>
      </Row>
    </div>

    <div class="fixed-bottom-wrapper">
      <back-button></back-button>
    </div>

    <div class="block_45"></div>
  </div>
</template>

<script>
import Picture from '@/components/upload/picture'
export default {
  name: 'edit',
  components: {Picture},
  data() {
    return {
      usageAgeDesc: {
        '0': '不限',
        '1': '儿童',
        '2': '成人',
      },
      is_allergen: 0,
      echoData: {
        instructions_imgs: []
      },
      col_allergen_show: false,
      col_class_show: false
    }
  },

  created() {

    // this.getsOptions().then( () => {
    //   if ( this.formEditData.id ) {
    //     this.get()
    //   }
    //   this.handlerProdType( this.echoData.prod_type )
    //   this.getsClass()
    // } )
    this.getProductDetail()
  },

  methods: {
    getProductDetail () {
      const id = this.$route.query.id
      this.$api.getProductsInfo(  { id } ).then( data => {
        console.log("-> %c data  === %o", "font-size: 15px;color: green;", data)
        let product = data.product
        //通用名
        this.echoData.prod_name = product.prod_name
        //商品名
        this.echoData.generic_name = product.generic_name
        //英文名
        this.echoData.english_name = product.english_name
        //助记码code
        this.echoData.phonetic_code = product.phonetic_code
        //生产厂家
        this.echoData.manufacturer = product.manufacturer
        this.echoData.approval_number = product.approval_number
        //药品条形码
        this.echoData.bar_code = product.bar_code
        //商品类型
        this.echoData.prod_type = product.prod_type
        this.echoData.prod_type_text = product.prod_type_text
        this.handlerProdType( this.echoData.prod_type )
        // 一级药品分类
        this.echoData.one_level_classid_text = product.one_level_classid_text
        //二级药品分类
        this.echoData.two_level_classid_text = product.two_level_classid_text
        //三级药品分类
        this.echoData.three_level_classid_text = product.three_level_classid_text
        //过敏禁忌
        this.echoData.allergen_taboo = product.allergen_taboo
        this.is_allergen = this.echoData.allergen_taboo.length > 0 ? 1 : 0
        //使用方法
        this.echoData.usage_age = product.usage_age
        //规格
        this.echoData.prod_spec = product.prod_spec
        // 商品单位
        this.echoData.prod_unit = product.prod_unit
        //商品金额
        this.echoData.retail_price =  product.retail_price
        //成本价
        this.echoData.cost_price = product.cost_price
        //使用方法
        this.echoData.usage_type_text = product.usage_type_text
        //单次剂量
        this.echoData.single_dose = product.single_dose
        //单次单位
        this.echoData.single_unit = product.single_unit
        //使用频次
        this.echoData.frequency = product.frequency
        // 说明文案、说明图片
        this.echoData.instructions_txt = product.instructions_txt
        this.echoData.instructions_imgs = product.instructions_imgs
        //备注
        this.echoData.remark = product.remark
        //是否拆零
        this.echoData.is_split = product.is_split
        this.echoData.is_split_text = product.is_split_text
        //拆零数量
        this.echoData.split_num =  product.split_num
        // 拆零单位
        this.echoData.split_prod_unit = product.split_prod_unit
        //拆零价格
        this.echoData.split_price =  product.split_price
        //来源
        this.source = product.source

      } ).catch( error => {
        this.$Message.error( error.errmsg )
      } )
    },

    handlerProdType: function ( prod_type ) {
      switch ( prod_type ) {
        case '1': // 西药
          this.col_class_show = true
          this.col_allergen_show = true
          break
        case '2': // 中草药
          this.col_class_show = false
          this.col_allergen_show = true
          break
        case '3': // 中成药
          this.col_class_show = true
          this.col_allergen_show = false
          break
        default:
          this.col_class_show = false
          this.col_allergen_show = false
      }
    },
  },
  computed: {
    // 是否是系统自建
    isSystemCreate () {
      return this.source == 9 ? true : false
    }
  },
  watch: {
    // 'echoData.split_num': {
    //   handler( newVal, oldVal ) {
    //     console.log( '-> newVal,oldVal', newVal, oldVal )
    //     const { retail_price } = this.echoData
    //     if ( !retail_price ) this.echoData.split_price = 0
    //     if ( !newVal ) {
    //       this.echoData.split_num = this.echoData.split_price = 0
    //       return
    //     }
    //     this.echoData.split_price = Number( retail_price / newVal )
    //   }, immediate: true,
    // },
    // 'echoData.retail_price': {
    //   handler( newVal, oldVal ) {
    //     console.log( '-> newVal,oldVal', newVal, oldVal )
    //     const { split_num } = this.echoData
    //     if ( !split_num ) {
    //       this.echoData.split_price = 0
    //     } else {
    //       this.echoData.split_price = Number( newVal / split_num )
    //     }
    //   }, immediate: true,
    // },
  },
}
</script>

<style scoped lang="less">
.common-detail-item{
  .label{

  }
}
</style>
