<template>
  <div class="detail-box">
    <div class="form-box">
      <Form
        :label-width="100"
        ref="queryFormData"
        :disabled="isDetail"
        :label-colon="true"
        :model="queryFormData"
        :rules="ruleValidate"
      >
        <FormItem label="服务名称" prop="name">
          <Input v-model="queryFormData.name" placeholder="请输入服务名称" show-word-limit maxlength="20" />
        </FormItem>

        <FormItem label="服务类型" prop="serv_type">
          <Select v-model="queryFormData.serv_type" placeholder="请选择服务类型">
            <Option v-for="item in typeList" :value="item.id" :key="item.id">{{ item.desc }}</Option>
          </Select>
        </FormItem>

        <FormItem label="图片" prop="img_list">
          <div v-if="isDetail && queryFormData.img_list.length === 0">-</div>
          <MaterialPicture v-else v-model="queryFormData.img_list" :limit="5" :disabled="isDetail" />
          <div class="form-item-tip" style="margin-top: -13px">
            建议尺寸：750*350像素，你可以拖拽图片调整顺序，最多上传5张
          </div>
        </FormItem>

        <FormItem label="视频">
          <div v-if="isDetail && !queryFormData.video">-</div>
          <MaterialVideo v-else v-model="queryFormData.video" :limit="1" :disabled="isDetail" />
          <div class="form-item-tip" style="margin-top: -16px">建议上传视频时长为9-30秒，视频宽高比为16:9</div>
        </FormItem>

        <FormItem label="服务描述">
          <Input
            v-model="queryFormData.desc"
            type="textarea"
            :autosize="{ minRows: 3, maxRows: 6 }"
            maxlength="200"
            show-word-limit
            placeholder="请输入服务描述"
          />
        </FormItem>

        <FormItem label="服务售价" prop="price">
          <div class="money-prefix-box">
            <div class="money-prefix">¥</div>
            <InputNumber
              v-model="queryFormData.price"
              :active-change="false"
              :precision="2"
              :min="0"
              style="width: 100%"
            ></InputNumber>
          </div>
        </FormItem>

        <FormItem label="服务时长" prop="duration">
          <div class="suffix-box">
            <div class="suffix">分钟</div>
            <InputNumber
              :active-change="false"
              v-model="queryFormData.duration"
              :min="15"
              style="width: 100%"
            ></InputNumber>
          </div>
        </FormItem>
      </Form>
    </div>

    <div style="height: 35px"></div>
    <div class="fixed-bottom-wrapper">
      <back-button></back-button>
      <Button class="ml10" type="primary" :loading="loading" @click="save" v-if="!isDetail">保存</Button>
    </div>
  </div>
</template>

<script>
import S from 'utils/util'; // Some commonly used tools

const validPrice = (rule, value, callback) => {
  if (Number(value || 0) === 0) {
    callback(new Error('请输入大于0的服务售价'));
  } else {
    callback();
  }
};

const validDuration = (rule, value, callback) => {
  if (Number(value || 0) < 15) {
    callback(new Error('服务时长最少为15分钟'));
  } else {
    callback();
  }
};

export default {
  name: 'detail',
  components: {},
  mixins: [],
  data() {
    return {
      queryFormData: {
        name: '',
        serv_type: '',
        img_list: [],
        video: '',
        desc: '',
        price: null,
        duration: null
      },
      ruleValidate: {
        name: [{ required: true, message: '该填写服务名称', trigger: 'change' }],
        serv_type: [{ required: true, message: '请选择服务类型', trigger: 'change' }],
        price: [{ required: true, type: 'number', validator: validPrice, trigger: 'change' }],
        duration: [{ required: true, type: 'number', validator: validDuration, trigger: 'change' }]
      },
      typeList: [],
      loading: false,
      detailInfo: {} // 详情信息
    };
  },
  computed: {
    isDetail() {
      let query = this.$route.query;
      return !!query.id && query.type === 'detail';
    }
  },
  watch: {},
  created() {
    // 获取服务类型
    this.getServiceType();
    let id = this.$route.query.id;
    if (id) {
      this.getgoodsServiceInfo(id);
    }
  },
  mounted() {},
  methods: {
    save() {
      this.$refs.queryFormData.validate(valid => {
        if (valid) {
          this.goodsServiceEdit();
        }
      });
    },
    // 获取服务类型
    getServiceType() {
      this.$api.getGoodsservicelibOptions().then(res => {
        this.typeList = S.descToArrHandle(res.servTypeDesc || {});
      });
    },

    // 保存
    goodsServiceEdit() {
      let params = {
        ...this.queryFormData,
        id: this.$route.query.id
      };
      this.loading = true;
      this.$api
        .goodsServiceEdit(params)
        .then(res => {
          this.$router.push('/goods/service/list');
        })
        .finally(() => (this.loading = false));
    },

    // 获取详情
    getgoodsServiceInfo(id) {
      let params = {
        id
      };
      this.$api.getgoodsServiceInfo(params).then(res => {
        this.detailInfo = res;
        this.handlerData(res);
      });
    },

    handlerData(data) {
      this.queryFormData.name = data.name;
      this.queryFormData.serv_type = data.serv_type;
      this.queryFormData.img_list = data.img_list;
      this.queryFormData.video = data.video;
      this.queryFormData.desc = data.desc;
      this.queryFormData.price = !data.price ? null : Number(data.price || 0);
      this.queryFormData.duration = !data.duration ? null : Number(data.duration || 0);
    }
  }
};
</script>

<style lang="less" scoped>
.detail-box {
  .form-box {
    margin-left: 100px;
    width: 50%;

    // prefix
    .money-prefix-box {
      display: flex;
      align-items: center;
      position: relative;

      :deep(.ivu-input-number-input) {
        margin-left: 35px;
      }

      .money-prefix {
        width: fit-content;
        padding: 0 10px;
        height: 30px;
        background: #eee;
        display: flex;
        justify-content: center;
        align-items: center;
        position: absolute;
        left: 1px;
        top: 1px;
        z-index: 1;
        border-right: 1px solid #dcdee2;
      }
    }

    // suffix
    .suffix-box {
      display: flex;
      align-items: center;
      position: relative;

      .suffix {
        width: fit-content;
        padding: 0 10px;
        height: 30px;
        background: #eee;
        display: flex;
        justify-content: center;
        align-items: center;
        position: absolute;
        right: 1px;
        top: 1px;
        z-index: 1;
        border-left: 1px solid #dcdee2;
      }
    }

    .form-item-tip {
      display: flex;
      color: #ccc;
    }
  }
}
::v-deep .picture-upload-icon {
  position: relative;
  > span {
    position: absolute;
    display: inline-block;
    width: 100%;
    text-align: center;
    top: 46px;
    font-weight: bold;
    color: #bbbbbb;
  }
}
</style>
