<template>
  <div>
    <div class="table-fun">
      <Button type="primary" @click="addService()">添加服务</Button>
    </div>
    <Form inline :label-width="0" @submit.native.prevent @keyup.enter.native="onSearch">
      <FormItem>
        <Input type="text" placeholder="服务名称" v-model="queryFormData.keyword" />
      </FormItem>

      <FormItem>
        <Select v-model="queryFormData.serv_type" placeholder="类型" clearable>
          <Option v-for="item in typeList" :key="item.id" :value="item.id">{{ item.desc }}</Option>
        </Select>
      </FormItem>

      <FormItem>
        <Button class="mr-10" type="primary" @click="onSearch">筛选</Button>
        <Button @click="onResetSearch">重置</Button>
      </FormItem>
    </Form>

    <div class="table-wrapper">
      <Table :loading="tableLoading" :columns="tableCols" :data="list" :height="$store.state.app.clientHeight - 262">
        <template slot-scope="{ row }" slot="info">
          {{ row.name }}
        </template>
        <template slot-scope="{ row }" slot="price"> {{ row.price | number_format }}元 </template>

        <template slot-scope="{ row }" slot="operate">
          <div class="flex">
            <a class="mr10" @click="toDetail(row)" v-if="row.source_platform == 'CP'">编辑</a>
            <a @click="toDetail(row, 'detail')">详情</a>
          </div>
        </template>

        <!-- 创建时间 -->
        <template slot="create_time" slot-scope="{ row }">
          <span>{{ row.create_time | date_format }}</span>
        </template>
      </Table>

      <div class="block_20"></div>
      <KPage
        :current="+queryFormData.page"
        :page-size="+queryFormData.pageSize"
        :total="total"
        style="text-align: center"
        @on-change="handleCurrentChange"
        @on-page-size-change="handleSizeChange"
      />
    </div>
  </div>
</template>

<script>
import search from '@/mixins/search';
import S from 'utils/util';
let init_query_form_data = {
  page: 1,
  pageSize: 20,
  keyword: '', // 服务名称
  serv_type: '', // 服务类型
  r: ''
};

export default {
  name: 'list',
  mixins: [search],
  data() {
    return {
      apiName: 'getGoodsservicelibList',
      queryFormData: { ...init_query_form_data },
      tableCols: [
        { title: 'ID', key: 'id', width: '150' },
        { title: '服务', slot: 'info' },
        { title: '价格', slot: 'price' },
        { title: '类型', key: 'serv_type_text' },
        { title: '来源', key: 'source_platform_text' },
        // {title: '状态', slot: 'status_text'},
        { title: '创建时间', slot: 'create_time' },
        { title: '操作', slot: 'operate', width: 100 }
      ],
      tableLoading: false,

      list: [],
      total: 0,

      typeList: [] // 服务类型数据
    };
  },
  computed: {},
  watch: {},
  created() {
    this.queryFormData = S.merge(this.queryFormData, this.$route.query);
    this.submitQueryForm(true);

    // 获取服务类型
    this.getServiceType();
  },
  mounted() {},
  methods: {
    toDetail(row, type) {
      let query = {
        id: row.id
      };
      if (type) {
        query.type = type;
      }
      this.$router.push({
        path: '/goods/service/detail',
        query: query
      });
    },
    addService() {
      this.$router.push('/goods/service/detail');
    },

    onResetSearch() {
      this.queryFormData = { ...init_query_form_data };
      this.submitQueryForm();
    },

    // 获取服务类型数据
    getServiceType() {
      this.$api.getGoodsservicelibOptions().then(res => {
        // .获取服务类型
        this.typeList = S.descToArrHandle(res.servTypeDesc || {});
      });
    }
  },

  beforeRouteUpdate(to, from, next) {
    this.queryFormData = S.merge(init_query_form_data, to.query);
    this.loadList();
    next();
  }
};
</script>

<style lang="less" scoped>
::v-deep .ivu-form-item {
  margin-bottom: 16px;
  label {
    vertical-align: middle;
  }
}
.table-fun {
  margin-bottom: 10px;
}
</style>
