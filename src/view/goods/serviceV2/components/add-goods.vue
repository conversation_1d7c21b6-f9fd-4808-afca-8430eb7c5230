<template>
  <Modal
    :value="visible"
    :mask-closable="false"
    :before-close="cancel"
    :styles="styles"
    lock-scroll
    width="960"
    :title="title"
    @on-cancel="cancel"
  >
    <div class="goods-content">
      <div class="top">
        <div class="flex flex-item-between">
          <div>
            <Button to="/goods/products/list" target="_blank" type="default">货品列表</Button>
            <span class="margin-left10 cursor aStyle" @click="reload">刷新</span>
          </div>
          <div>
            <Select
              v-model="queryFormData.prod_type"
              style="width: 180px; margin-right: 10px"
              placeholder="请选择商品类型"
              clearable
              @on-change="onSearch"
            >
              <Option v-for="item in drugProdTypeDesc" :key="item.id" :value="item.id">{{ item.name }}</Option>
            </Select>
            <Input
              v-model="queryFormData.name"
              class="cursor-wrapper"
              icon="ios-search"
              placeholder="输入货品名称"
              style="width: 180px"
              @keyup.enter.native="onSearch"
              @on-click="onSearch"
            />
          </div>
        </div>

        <!-- table -->
        <Table
          :loading="tableLoading"
          stripe
          :columns="goodsColumns"
          :data="list"
          class="margin-top16"
          height="424"
          @on-row-click="clickRow"
        >
          <template slot="box" slot-scope="{ row }">
            <Checkbox v-model="row.checked" :disabled="row.disabled" @on-change="checkboxEvent(row)" />
          </template>

          <template slot="is_split" slot-scope="{ row }">
            <span v-if="row.is_split == '1'">是</span>
            <span v-else>否</span>
          </template>

          <!--      拆零单位-->
          <template slot="split_unit" slot-scope="{ row }">
            {{ row.split_prod_unit || '-' }}
          </template>

          <template slot="split_price" slot-scope="{ row }">
            <span v-if="row.is_split == '1'">{{ row.split_price }}</span>
            <span v-else>-</span>
          </template>
        </Table>

        <KPage
          :total="total"
          class="margin-top16"
          :page-size="queryFormData.pageSize"
          :current="queryFormData.page"
          :page-size-opts="[5, 8, 20]"
          style="text-align: center"
          @on-change="handleCurrentChange"
          @on-page-size-change="handleSizeChange"
        />
      </div>
    </div>

    <div slot="footer" class="goods-footer">
      已选: 货品({{ checkedList.length || 0 }})
      <Button type="default" class="margin-left10" @click="cancel">取消</Button>
      <Button type="primary" :loading="confirmLoading" @click="confirm">确定</Button>
    </div>
  </Modal>
</template>

<script>
import S from 'utils/util';
let init_query_form_data = {
  page: 1,
  pageSize: 8,
  name: '',
  prod_type: '',
  status: 1,
  filter_drug: 1,
};
export default {
  name: 'AddGoods',
  components: {},
  filters: {},
  mixins: [],
  props: {
    title: {
      type: String,
      default: '选择耗材',
    },
    visible: {
      type: Boolean,
      default: false,
    },
    checkedGoods: {
      type: Array,
      default: () => [],
    },
    styles: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      // 分页
      queryFormData: { ...init_query_form_data },
      total: 0,
      tableLoading: false,

      confirmLoading: false, // 确定加载loading

      goodsColumns: [
        { title: ' ', slot: 'box', align: 'center', width: 50 },
        { title: '货品', key: 'generic_name', align: 'center' },
        { title: '类型', key: 'prod_type_text', align: 'center' },
        { title: '来源', key: 'from_text', align: 'center' },
        { title: '单位', key: 'prod_unit', align: 'center' },
        { title: '单价', key: 'retail_price', align: 'center' },
        { title: '是否拆零', slot: 'is_split', align: 'center' },
        { title: '拆零单位', slot: 'split_unit', align: 'center' },
        { title: '拆零价', slot: 'split_price', align: 'center' },
      ],
      list: [],
      checkedIdList: [],
      checkedList: [],
      drugProdTypeDesc: [],
    };
  },
  computed: {},
  watch: {
    visible(val) {
      if (!val) {
        // Modal关闭,做一些你希望的操作
        this.resetPageData();
      } else {
        this.checkedIdList = this.checkedGoods?.map(item => item.id) || [];
        this.checkedList = this.checkedGoods || [];
        this.getList();
      }
    },
  },
  created() {
    this.getGoodsOptions();
  },
  mounted() {},
  methods: {
    // 属性
    reload() {
      this.resetPageData();
      this.getList();
    },

    resetPageData() {
      this.queryFormData = { ...init_query_form_data };
      this.checkedIdList = [];
      this.checkedList = [];
      this.list = [];
    },

    handleSizeChange(val) {
      this.queryFormData.page = 1;
      this.queryFormData.pageSize = val;
      this.getList();
    },

    handleCurrentChange(val) {
      this.queryFormData.page = val;
      this.getList();
    },

    // 勾选事件
    checkboxEvent(row) {
      let index = this.checkedIdList.indexOf(row.id);
      if (row.checked) {
        if (index > -1) return;
        this.checkedIdList.push(row.id);
        this.checkedList.push(row);
      } else {
        if (index < 0) return;
        this.checkedIdList.splice(index, 1);
        this.checkedList.splice(index, 1);
      }
    },

    clickRow(row) {
      const index = this.checkedIdList.indexOf(row.id);
      const listIndex = this.list.findIndex(item => item.id === row.id);
      this.$set(this.list[listIndex], 'checked', !row.checked);
      if (index < 0) {
        this.checkedIdList.push(row.id);
        this.checkedList.push(row);
        return;
      }
      this.checkedIdList.splice(index, 1);
      this.checkedList.splice(index, 1);
    },
    // 已勾选的数据回显
    echoChecked() {
      this.checkedList.forEach(checked_item => {
        this.list.forEach((item, index) => {
          if (checked_item.id === item.id) {
            this.list[index].checked = true;
            this.list[index].use_num = checked_item.use_num;
          }
        });
      });
    },

    // 如果外部已经选择了货品,并且手动设置了,那么弹窗里面对应的货品不可再被选中,处于禁选状态
    isDisabledGoodsItem() {
      let goodsIdList = [];
      this.checkedGoods.forEach(item => goodsIdList.push(item.prod_id));

      goodsIdList.forEach(id_item => {
        this.list.forEach(list_item => {
          if (id_item === list_item.id) {
            list_item.checked = true;
          }
        });
      });
    },

    closeDia() {
      this.$emit('update:visible', false);
    },
    cancel() {
      this.closeDia();
    },
    confirm() {
      this.$emit('selectGoods', this.$lodash.cloneDeep(this.checkedList));
      this.closeDia();
    },

    onSearch() {
      this.queryFormData.page = 1;
      this.getList();
    },

    handleList(list) {
      list.map(item => {
        item.checked = false;
        item.disabled = false;
      });
      return list;
    },

    getList() {
      this.tableLoading = true;
      let params = { ...this.queryFormData };
      this.$api
        .getProdLibList(params)
        .then(data => {
          this.resetScroll();
          this.list = this.handleList(data.list);
          this.total = data.total;
          this.statusDesc = data.statusDesc;
          this.typeDesc = data.typeDesc;

          this.tableLoading = false;
          setTimeout(() => {
            this.echoChecked();
          }, 3);
          this.isDisabledGoodsItem();
        })
        .catch(error => {
          this.$Message.error(error.errmsg);
        })
        .finally(() => {
          this.tableLoading = false;
        });
    },

    //诊所选项列表
    getGoodsOptions() {
      this.$api.getGoodsListOptions().then(res => {
        this.drugProdTypeDesc = S.descToArrHandle(res.drugProdTypeDesc);
      });
    },

    resetScroll() {
      this.$nextTick(() => {
        let overflowY = this.$el.getElementsByClassName('ivu-table-body')[0];
        if (!overflowY) {
          return;
        }
        overflowY.scrollTop = 0;
      });
    },
  },
};
</script>
<style lang="less" scoped>
.cursor {
  cursor: pointer;
}

.aStyle {
  color: #155bd4;
}

.margin-left10 {
  margin-left: 10px;
}

.margin-top16 {
  margin-top: 16px;
}
:deep(.cursor-wrapper .ivu-icon-ios-search)  {
  cursor: pointer;
}
</style>
