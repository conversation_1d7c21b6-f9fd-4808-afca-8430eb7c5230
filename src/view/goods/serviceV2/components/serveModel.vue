<template>
  <Modal
    v-model="modalVisible"
    width="750"
    title="可服务理疗师"
    footer-hide
    class-name="serve-model"
    @on-visible-change="modalVisibleChange"
  >
    <div>
      <Table
        class="goods-table"
        :loading="tableLoading"
        :columns="tableCols"
        :data="list"
      />
      <div class="block_20" />
      <KPage
        :total="total"
        :page-size.sync="page.pageSize"
        :current.sync="page.page"
        style="text-align: center"
        @on-change="onPageChange"
        @on-page-size-change="onPageSizeChange"
      />
    </div>
    <template #footer>
      <Button type="primary" @click="closeModal">确定</Button>
    </template>
  </Modal>
</template>

<script>
export default {
  name: 'ServeModel',
  props: {
    visible: Boolean,
    id: {
      type: String,
      required: true,
      default: null,
    },
  },
  data() {
    return {
      list: [],
      tableLoading: false,
      tableCols: [
        { title: '理疗师姓名', key: 'name', align: 'center' },
        { title: '所属门店', key: 'clinic_name', align: 'center' },
        { title: '所属合伙人', key: 'cp_name', align: 'center' },
        { title: '所属导师', key: 'advisor_name', align: 'center' },
      ],
      total: 0,
      page: {
        page: 1,
        pageSize: 10,
      },
    };
  },
  computed: {
    modalVisible: {
      get() {
        return this.visible;
      },
      set(val) {
        this.$emit('update:visible', val);
      },
    },
  },
  methods: {
    closeModal() {
      this.list = [];
      this.$emit('update:visible', false);
    },
    modalVisibleChange(visible) {
      if (visible) {
        this.handleQueryList();
        return;
      }
      this.closeModal();
    },
    onPageChange(page) {
      this.page.page = page;
      this.handleQueryList();
    },
    onPageSizeChange(pageSize) {
      this.page.page = 1;
      this.page.pageSize = pageSize;
      this.handleQueryList();
    },
    handleQueryList() {
      this.$api
        .getCanPhysioList({
          id: this.id,
          dispatch_status: '1',
          ...this.page,
        })
        .then(res => {
          this.list = res?.list || [];
          this.total = res?.total || 0;
        });
    },
  },
};
</script>

<style scoped lang="less">
:deep(.serve-model .ivu-modal-body) {
  max-height: 75vh;
}
</style>
