<template>
  <div style="padding-bottom: 20px">
    <Form
      ref="form"
      :model="detail"
      :rules="rules"
      :disabled="isDetail"
      style="max-width: 750px"
      label-colon
      :label-width="120"
    >
      <FormItem label="服务名称" prop="name">
        <Input
          v-model="detail.name"
          :disabled="isDetail || (detail.dispatch_status === '1' && $route.query.type !== 'copy')"
          maxlength="20"
          show-word-limit
          placeholder="长度最多20个字"
        />
      </FormItem>
      <FormItem label="服务类型" prop="serv_type">
        <Select
          v-model="detail.serv_type"
          :disabled="isDetail || (detail.dispatch_status === '1' && $route.query.type !== 'copy')"
          placeholder="请选择服务类型"
        >
          <Option v-for="item in servTypeDesc" :key="item.id" :value="item.id">{{ item.desc }}</Option>
        </Select>
      </FormItem>
      <FormItem label="服务描述" prop="desc">
        <Input
          v-model="detail.desc"
          :autosize="{ minRows: 3, maxRows: 6 }"
          maxlength="200"
          show-word-limit
          :disabled="isDetail"
          type="textarea"
          placeholder="请输入服务描述"
        />
      </FormItem>
      <FormItem label="服务视频" prop="video">
        <div v-if="isDetail && !detail.video">暂无视频</div>
        <template v-else>
          <material-video v-model="detail.video" :disabled="isDetail" />
          <div v-if="!isDetail" class="note">建议上传视频时长为9-30秒</div>
        </template>
      </FormItem>
      <FormItem label="服务图片" prop="img_list">
        <div v-if="isDetail && detail.img_list.length === 0">暂无图片</div>
        <template v-else>
          <material-picture v-model="detail.img_list" :limit="5" :disabled="isDetail" />
          <div v-if="!isDetail" class="note">建议尺寸：750*350像素，你可以拖拽图片调整顺序，最多上传5张</div>
        </template>
      </FormItem>
      <price-set ref="priceSet" v-model="price_set_data" :disabled="isDetail"></price-set>

      <FormItem label="直营中心售价" prop="opc_price">
        <kOpcPriceD :data="detail?.opc_price" />
      </FormItem>

      <!--      <FormItem label="服务售价" prop="price">-->
      <!--        <custom-input-number-->
      <!--          v-model="detail.price"-->
      <!--          :min="0.01"-->
      <!--          :precision="2"-->
      <!--          :disabled="isDetail"-->
      <!--          prefix="¥"-->
      <!--          placeholder="请输入服务售价"-->
      <!--        />-->
      <!--      </FormItem>-->
      <FormItem label="服务时长" prop="duration">
        <custom-input-number
          v-model="detail.duration"
          :disabled="isDetail"
          :min="15"
          :precision="0"
          suffix="分钟"
          placeholder="请输入服务时长"
        />
      </FormItem>
      <FormItem label="服务准备时长" prop="prepare_duration">
        <custom-input-number
          v-model="detail.prepare_duration"
          :disabled="isDetail"
          :min="5"
          :precision="0"
          suffix="分钟"
          placeholder="请输入服务准备时长"
        />
        <div v-if="!isDetail" class="note">服务准备时长将计入服务预约时间</div>
      </FormItem>

      <FormItem label="可核销人员" prop="can_use_scope">
        <div class="flex">
          <Select
            v-model="detail.can_use_scope"
            placeholder="请选择可核销人员"
            @on-change="() => (detail.mdp_level = '')"
          >
            <Option v-for="item in canUseScopeDesc" :key="item.kw" :value="item.kw">{{ item.desc }}</Option>
          </Select>

          <Select
            style="margin-left: 20px"
            v-model="detail.mdp_level"
            placeholder="请选择"
            v-if="detail.can_use_scope === '2'"
          >
            <Option v-for="item in physioMdpLevelList" :key="item.kw" :value="item.kw">{{ item.desc }}</Option>
          </Select>
        </div>
      </FormItem>

      <FormItem label="关联服务耗材" prop="product_list">
        <div v-if="detail.product_list?.length === 0" class="flex">
          <div>尚未关联</div>
          <a v-if="!isDetail" class="ml10" @click="openConsumeModal"> 添加耗材 </a>
        </div>
        <div v-else class="flex">
          <div>
            所需耗材
            <a style="text-decoration: underline" @click="openConsumeModal">
              {{ detail.product_list?.length || 0 }}
            </a>
          </div>
          <a v-if="!isDetail" class="ml10" @click="openConsumeModal"> 修改耗材 </a>
        </div>
        <Table
          class="goods-table"
          :columns="tableCols"
          border
          :data="detail.product_list"
          :height="detail.product_list.length > 5 ? 300 : ''"
          style="width: 900px"
        >
          <template #number="{ row, index }">
            <InputNumber
              v-model="row.use_num"
              :disabled="isDetail"
              :min="1"
              :max="50"
              :precision="0"
              :active-change="false"
              style="width: 100%"
              @on-change="val => changeUseNumber(val, index, 'use_num')"
              @on-focus="e => e.currentTarget.select()"
            />
          </template>

          <template #can_use_nums="{ row, index }">
            <InputNumber
              v-model="row.can_use_nums"
              :disabled="isDetail"
              :min="1"
              :max="50"
              :precision="0"
              :active-change="false"
              style="width: 100%"
              @on-change="val => changeUseNumber(val, index, 'can_use_nums')"
              @on-focus="e => e.currentTarget.select()"
            />
          </template>

          <template #single_use_nums="{ row, index }">
            <InputNumber
              v-model="row.single_use_nums"
              :disabled="isDetail"
              :min="1"
              :max="50"
              :precision="0"
              :active-change="false"
              style="width: 100%"
              @on-change="val => changeUseNumber(val, index, 'single_use_nums')"
              @on-focus="e => e.currentTarget.select()"
            />
          </template>

          <template #prod_spec="{ row, index }">
            <div>{{ row.prod_spec || '-' }}</div>
          </template>
          <template #action="{ row, index }">
            <Poptip confirm title="确认删除该该耗材?" @on-ok="handleDeleteRow(index)">
              <a>删除</a>
            </Poptip>
          </template>
        </Table>
      </FormItem>
    </Form>
    <div class="fixed-bottom-wrapper">
      <back-button />
      <Button v-if="!isDetail" class="ml10" type="primary" :loading="loading" @click="save"> 保存 </Button>
      <Poptip v-if="$route.query.type === 'dispatch'" confirm title="确定分发给所有合伙人和诊所?" @on-ok="save">
        <Button class="ml10" type="primary" :loading="loading"> 确认分发 </Button>
      </Poptip>
      <KLink
        v-if="$route.query.type === 'edit'"
        class="ml10"
        :to="{ path: '/rst/manage/detail', query: { id: $route.query.id, type: 'copy' } }"
        target="_blank"
      >
        <Button type="primary"> 复制 </Button>
      </KLink>
    </div>

    <add-goods
      :visible.sync="consumeVisible"
      :checked-goods="detail.product_list"
      :styles="{ top: '3vh' }"
      @selectGoods="saveProductList"
    />
  </div>
</template>
<script>
import S from 'utils/util';
import MaterialPicture from '../../../components/MaterialCenter/MaterialPicture.vue';
import CustomInputNumber from '../../../components/CustomInputNumber/index.vue';
import AddGoods from './components/add-goods.vue';
import priceSet from './components/price-set.vue';
import kOpcPriceD from '@/components/k-goods-opc/k-opc-price-d.vue';
const validPrice = (rule, value, callback) => {
  if (Number(value || 0) === 0) {
    callback(new Error('请输入大于0的服务售价'));
  } else {
    callback();
  }
};

const validDuration = (rule, value, callback) => {
  if (Number(value || 0) < 15) {
    callback(new Error('服务时长最少为15分钟'));
  } else {
    callback();
  }
};
export default {
  name: 'Detail',
  components: { AddGoods, CustomInputNumber, MaterialPicture, priceSet, kOpcPriceD },
  data() {
    const validScope = (rule, value, callback) => {
      if (!this.detail.can_use_scope) {
        callback(new Error('请选择可核销人员'));
      } else if (this.detail.can_use_scope === '2' && !this.detail.mdp_level) {
        callback(new Error('请选择'));
      } else {
        callback();
      }
    };
    return {
      consumeVisible: false,
      servTypeDesc: [],
      detail: {
        name: '',
        serv_type: '',
        img_list: [],
        // skill_list: [],
        product_list: [],
        video: '',
        desc: '',
        dispatch_status: '2',
        // price: null,
        duration: null,
        prepare_duration: null,
        can_use_scope: '',
        mdp_level: '',
        opc_price: []
      },
      price_set_data: {
        first_price: null,
        first_vip_price: null,
        other_price: null,
        other_vip_price: null,
        single_price: null,
        more_service_type: 'radio',
        more_service_value: null,
        more_sale_type: 'radio',
        more_sale_value: null,
        less_service_type: 'fixed',
        less_service_value: null
      },
      rules: {
        name: [{ required: true, message: '该填写服务名称', trigger: 'change' }],
        serv_type: [{ required: true, message: '请选择服务类型', trigger: 'change' }],
        // price: [{ required: true, type: 'number', validator: validPrice, trigger: 'change' }],
        duration: [{ required: true, type: 'number', validator: validDuration, trigger: 'change' }],
        can_use_scope: [{ required: true, validator: validScope, trigger: 'change' }]
      },
      loading: false,
      isEdit: false,

      canUseScopeDesc: [],
      physioMdpLevelList: []
    };
  },
  computed: {
    isDetail() {
      const { type } = this.$route.query;
      return type !== 'add' && type !== 'edit' && type !== 'copy';
    },
    tableCols() {
      const cols = [
        { title: '分类', key: 'prod_type_text', align: 'center', minWidth: 100 },
        { title: '名称', key: 'generic_name', align: 'center', minWidth: 120 },
        { title: '包装规格', slot: 'prod_spec', align: 'center', minWidth: 120 },
        { title: '消耗数量', slot: 'number', align: 'center', minWidth: 120 },
        { title: '单位', key: 'prod_unit', align: 'center', minWidth: 120 },
        { title: '耗材可提供服务次数', slot: 'can_use_nums', align: 'center', minWidth: 130 },
        { title: '每次消耗数量', slot: 'single_use_nums', align: 'center', minWidth: 130 }
      ];
      if (!this.isDetail) cols.push({ title: '操作', slot: 'action', align: 'center', width: '70px' });
      return cols;
    }
  },
  mounted() {
    this.getGoodsServiceOptions();
    if (this.$route.query.id) this.getgoodsServiceInfo();
  },
  methods: {
    openConsumeModal() {
      if (this.isDetail) return;
      this.consumeVisible = true;
    },
    getGoodsServiceOptions() {
      this.$api.getGoodsservicelibOptions().then(res => {
        this.servTypeDesc = S.descToArrHandle(res?.servTypeDesc);
        this.canUseScopeDesc = res.canUseScopeDesc || [];
        this.physioMdpLevelList = S.descToArrHandle(res?.physioMdpLevelDesc || {});
      });
    },
    saveProductList(list) {
      this.detail.product_list = list?.map(item => ({
        ...item,
        prod_id: item.id,
        use_num: +item.use_num || 1,
        can_use_nums: +item.can_use_nums || 1
      }));
    },
    handleParamsPrice() {
      let price_set_data = this.price_set_data;
      let price_obj = {
        first_tier_price: price_set_data.first_price,
        first_tier_vip_price: price_set_data.first_vip_price,
        not_first_tier_price: price_set_data.other_price,
        not_first_tier_vip_price: price_set_data.other_vip_price,
        divide_rules: {
          real_price: price_set_data.single_price,
          gte: {
            server: {
              divide_type: price_set_data.more_service_type,
              divide_value: price_set_data.more_service_value
            },
            sales: {
              divide_type: price_set_data.more_sale_type,
              divide_value: price_set_data.more_sale_value
            }
          },
          lt: {
            server: {
              divide_type: price_set_data.less_service_type,
              divide_value: price_set_data.less_service_value
            },
            sales: null
          }
        }
      };
      return price_obj;
    },
    save() {
      this.$refs.form.validate(async valid => {
        const price_set_valid = await this.$refs.priceSet.validPriceSet();
        console.log('=>(detail.vue:372) price_set_valid', price_set_valid);
        if (valid && price_set_valid) {
          if (this.detail.prepare_duration && this.detail.prepare_duration > this.detail.duration) {
            this.$Message.error('服务准备时长不能大于服务时长');
            return;
          }
          const type = this.$route.query.type;
          const params = {
            dispatch_type: 'RST',
            ...this.detail,
            product_list: this.detail.product_list?.map(item => ({
              ...item,
              id: item.prod_id,
              num: item.use_num,
              use_nums: item.can_use_nums
            })),
            id: this.$route.query.id,
            ...this.handleParamsPrice()
          };
          delete params.dispatch_status;

          if (type === 'copy') delete params.id;
          if (type === 'dispatch') params.dispatch_status = '1';
          this.loading = true;
          this.$api
            .goodsServiceEdit(params)
            .then(() => {
              type === 'add' && this.$Message.success('新增成功!');
              type === 'copy' && this.$Message.success('新增成功!');
              type === 'edit' && this.$Message.success('修改成功!');
              type === 'dispatch' && this.$Message.success('分发成功!');
              this.$router.push('/rst/manage/list');
            })
            .catch(err => {
              this.$Message.error(err.errmsg);
            })
            .finally(() => (this.loading = false));
        }
      });
    },
    changeUseNumber(val, index, field) {
      this.$set(this.detail.product_list[index], field, val);
    },
    handleDeleteRow(index) {
      this.detail.product_list.splice(index, 1);
    },
    // 获取详情
    getgoodsServiceInfo() {
      let params = {
        id: this.$route.query.id,
        dispatch_type: 'RST'
      };
      this.$api.getgoodsServiceInfo(params).then(res => {
        this.detail = res || {};
        this.handlerData(res);
      });
    },
    handlerData(data) {
      this.detail.name = data.name;
      this.detail.serv_type = data.serv_type;
      this.detail.img_list = data.img_list;
      this.detail.video = data.video;
      this.detail.desc = data.desc;
      this.detail.opc_price = data.opc_price;
      this.detail.dispatch_status = data.dispatch_status || '2';
      // this.detail.price = !data.price ? null : Number(data.price || 0);
      this.detail.duration = !data.duration ? null : Number(data.duration || 0);
      this.detail.prepare_duration = !data.prepare_duration ? null : Number(data.prepare_duration || 0);
      // this.detail.skill_list = data?.skill_list || [];
      this.detail.product_list =
        data.product_list?.map(item => {
          return {
            ...item,
            prod_id: item.id,
            use_num: +item.num || null,
            can_use_nums: +item.can_use_nums || null,
            single_use_nums: +item.single_use_nums
          };
        }) || [];
      // 分账策略
      let divide_rules = data?.divide_rules || {};
      const gte = divide_rules?.gte || {};
      const lt = divide_rules?.lt || {};
      this.price_set_data = {
        first_price: Number(data.first_tier_price || 0),
        first_vip_price: Number(data.first_tier_vip_price || 0),
        other_price: Number(data.not_first_tier_price || 0),
        other_vip_price: Number(data.not_first_tier_vip_price || 0),
        single_price: Number(divide_rules?.real_price || 0),
        more_service_type: gte.server?.divide_type || 'ratio',
        more_service_value: Number(gte.server?.divide_value || 0),
        more_sale_type: gte?.sales?.divide_type || 'ratio',
        more_sale_value: Number(gte?.sales?.divide_value || 0),
        less_service_type: lt.server?.divide_type || 'ratio',
        less_service_value: Number(lt.server?.divide_value || 0)
      };
    }
  }
};
</script>

<style scoped lang="less">
.ml10 {
  margin-left: 10px;
}
.mr10 {
  margin-right: 10px;
}
</style>
