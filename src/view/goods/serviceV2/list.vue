<template>
  <div class="list-wrapper">
    <Form inline :label-width="0" class="search-form" @submit.native.prevent @keyup.enter.native="onSearch">
      <FormItem label="">
        <Input v-model="queryFormData.keyword" type="text" placeholder="服务名称" />
      </FormItem>
      <FormItem label="">
        <Select v-model="queryFormData.serv_type" placeholder="服务类型" clearable>
          <Option value="">全部</Option>
          <Option v-for="type in typeList" :key="type.id" :value="type.id">{{ type.desc }}</Option>
        </Select>
      </FormItem>
      <FormItem>
        <Button class="mr10" type="primary" @click="onSearch">筛选</Button>
        <Button class="mr10" @click="onResetSearch">重置</Button>
      </FormItem>
    </Form>

    <!-- table -->
    <div class="goods-table-wrapper">
      <Table
        class="goods-table"
        :loading="tableLoading"
        :columns="tableCols"
        border
        :data="list"
        :height="$store.state.app.clientHeight - 236"
      >
        <template #name="{ row }">
          <div>{{ row.name }}</div>
        </template>

        <template #can_use_scope_desc="{ row }">
          <div>{{ row.can_use_scope_desc || '-' }}</div>
        </template>

        <template #physio_count="{ row }">
          <a v-if="+row.physio_count > 0" @click="openServeModel(row)">{{ row.physio_count }}</a>
          <span v-else>-</span>
        </template>
        <template #sales="{ row }">
          <span>{{ row.sales || '-' }}</span>
        </template>

        <template #first_tier_price="{ row }">
          <div class="lv-box">
            <div class="label">单次价:</div>
            <div class="value">
              {{ row.first_tier_price ? `¥ ${Number(row.first_tier_price || 0).toFixed(2)}` : '-' }}
            </div>
          </div>
          <div class="lv-box">
            <div class="label">会员价:</div>
            <div class="value">
              {{ row.first_tier_vip_price ? `¥ ${Number(row.first_tier_vip_price || 0).toFixed(2)}` : '-' }}
            </div>
          </div>
        </template>

        <template #not_first_tier_price="{ row }">
          <div class="lv-box">
            <div class="label">单次价:</div>
            <div class="value">
              {{ row.not_first_tier_price ? `¥ ${Number(row.not_first_tier_price || 0).toFixed(2)}` : '-' }}
            </div>
          </div>
          <div class="lv-box">
            <div class="label">会员价:</div>
            <div class="value">
              {{ row.not_first_tier_vip_price ? `¥ ${Number(row.not_first_tier_vip_price || 0).toFixed(2)}` : '-' }}
            </div>
          </div>
        </template>

        <template #opc_price="{ row }">
          <div class="lv-box">
            <div class="label">单次价:</div>
            <div class="value" v-if="row.opc_price">
              {{ row.opc_price[0] ? `¥ ${Number(row.opc_price[0].price || 0).toFixed(2)}` : '-' }}
            </div>
            <div v-else>-</div>
          </div>
          <div class="lv-box">
            <div class="label">会员价:</div>
            <div class="value" v-if="row.opc_price">
              {{ row.opc_price[0] ? `¥ ${Number(row.opc_price[0].vip_price || 0).toFixed(2)}` : '-' }}
            </div>
            <div v-else>-</div>
          </div>
        </template>

        <template #price="{ row }">
          <span v-if="row.price">¥ {{ row.price }}</span>
          <span v-else>-</span>
        </template>
        <template slot="operate" slot-scope="{ row }">
          <KLink class="mr10" :to="{ path: '/goods/serviceV2/detail', query: { id: row.id, type: 'detail' } }">
            详情
          </KLink>
        </template>
      </Table>
    </div>

    <div class="block_20" />

    <KPage
      :total="total"
      :page-size.sync="+queryFormData.pageSize"
      :current.sync="+queryFormData.page"
      style="text-align: center"
      @on-change="onPageChange"
      @on-page-size-change="onPageSizeChange"
    />
    <serve-model :id="selectedRow.id || ''" :visible.sync="addServeVisible" />
  </div>
</template>

<script>
import S from 'utils/util';
import renderHeader from '@/mixins/renderHeader';
import search from '@/mixins/search';
import ServeModel from './components/serveModel.vue';

let init_query_form_data = {
  page: 1,
  pageSize: 20,
  name: '',
  goods_type: '',
  dispatch_status: '1',
  r: '',
  source_platform: '',
  dispatch_type: 'RST'
};
export default {
  name: 'List',
  components: { ServeModel },
  mixins: [renderHeader, search],
  data() {
    return {
      queryFormData: { ...init_query_form_data },
      apiName: 'getGoodsservicelibList',
      tableCols: [
        { title: '服务', slot: 'name', minWidth: 100 },
        { title: '服务类型', key: 'serv_type_text', minWidth: 80 },
        { title: '服务时长(分钟)', key: 'duration', minWidth: 120 },
        { title: '可核销人员', slot: 'can_use_scope_desc', minWidth: 120 },
        { title: '一线售价', slot: 'first_tier_price', minWidth: 120 },
        { title: '非一线售价', slot: 'not_first_tier_price', minWidth: 120 },
        { title: '直营中心售价', slot: 'opc_price', minWidth: 120, renderHeader: (h, params) => this._renderHeader(h, params, '有直营中心售价则以展示直营中心售价为准') },
        { title: '总销量', slot: 'sales', minWidth: 80 },
        // {
        //   title: '可服务理疗师',
        //   slot: 'physio_count',
        //   minWidth: 80,
        //   renderHeader: (h, params) => this._renderHeader(h, params, '平台/合伙人已经分发到具体门店并且理疗师是启用状态'),
        // },
        // { title: '启用状态', key: 'status_text', minWidth: 80 },
        { title: '状态', key: 'dispatch_status_text', minWidth: 80 },
        { title: '操作', slot: 'operate', width: 80 }
      ],
      tableLoading: false,
      selectedRow: {},
      addServeVisible: false,
      typeList: [],
      dispatchStatusDesc: [],
      list: [],
      total: 0
    };
  },
  created() {
    this.queryFormData = S.merge(this.queryFormData, this.$route.query);
    this.submitQueryForm(true);

    // 获取服务类型
    this.getServiceType();
  },
  methods: {
    onSearch: function () {
      this.queryFormData.page = 1;
      this.submitQueryForm();
    },
    onResetSearch: function () {
      this.queryFormData = { ...init_query_form_data };
      this.submitQueryForm();
    },
    onPageChange: function (page) {
      this.queryFormData.page = page;
      this.submitQueryForm();
    },
    onPageSizeChange(pageSize) {
      this.queryFormData.page = 1;
      this.queryFormData.pageSize = pageSize;
      this.submitQueryForm();
    },
    openServeModel(row) {
      this.selectedRow = row;
      this.addServeVisible = true;
    },
    toEdit() {
      this.$router.push({
        path: '/reserve/manage/detail',
        query: {
          type: 'add'
        }
      });
    },
    // 获取服务类型数据
    getServiceType() {
      this.$api.getGoodsservicelibOptions().then(res => {
        // .获取服务类型
        this.typeList = S.descToArrHandle(res.servTypeDesc || {});
        this.dispatchStatusDesc = S.descToArrHandle(res.dispatchStatusDesc || {})?.reverse() || [];
      });
    },
    onStatusChange(status) {
      this.queryFormData.page = 1;
      this.queryFormData.dispatch_status = status;
      this.submitQueryForm();
    }
  },
  beforeRouteUpdate: function (to, from, next) {
    this.queryFormData = S.merge(init_query_form_data, to.query);
    this.loadList();
    next();
  }
};
</script>

<style scoped lang="less">
.panel-nav a {
  border-bottom: unset;
}
.lv-box {
  display: flex;
  align-items: center;

  .label {
    width: 40px;
    min-width: 40px;
    text-align: right;
  }

  .value {
    text-align: left;
    margin-left: 4px;
  }
}
</style>
