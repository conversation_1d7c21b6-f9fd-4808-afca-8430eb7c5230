<template>
  <div>
    <div class="info-item">
      <div class="item-label">兑换次数：</div>
      <div :class="['item-value', hasModifiedItem('exchange_num') ? 'base-changed' : '']">
        <span>{{ getGoodsInfo.exchange_num }}</span>
      </div>
    </div>
    <div class="info-item mb-20 mt-20">
      <div class="item-label align-start">服务范围：</div>
      <div :class="['item-value', hasModifiedItem('name') ? 'base-changed' : '']">
        <Table
          :columns="serviceCols"
          :data="getGoodsInfo.services"
          style="width: 100%; max-width: 1000px"
          disabled-hover
        >
          <template v-slot:price="{ row }">{{ row.price | number_format }}</template>
        </Table>
      </div>
    </div>
    <div class="info-item">
      <div class="item-label">备注：</div>
      <div :class="['item-value', hasModifiedItem('remark') ? 'base-changed' : '']">
        <span>{{ getGoodsInfo.remark }}</span>
      </div>
    </div>
    <div class="block-title">价格库存</div>
    <div class="info-item">
      <div class="item-label">价格：</div>
      <div :class="['item-value', hasModifiedItem('price') ? 'base-changed' : '']">
        <span>￥ {{ getGoodsInfo.price }}</span>
      </div>
    </div>
    <div class="info-item">
      <div class="item-label">储值价：</div>
      <div :class="['item-value', hasModifiedItem('stored_price') ? 'base-changed' : '']">
        <span v-if="getGoodsInfo.stored_price">¥ {{ getGoodsInfo.stored_price | number_format }}</span>
        <span v-else>-</span>
      </div>
    </div>
    <div class="block-title">服务设置</div>
    <div class="info-item">
      <div class="item-label">商品有效期: ：</div>
      <div :class="['item-value', hasModifiedItem('service_info.expiration.days') ? 'base-changed' : '']">
        <span>
          {{
            getGoodsInfo.service_info.expiration.type == '1'
                ? `购买后${getGoodsInfo.service_info.expiration.days}天内有效`
                : `截止到${getGoodsInfo.service_info.expiration.date}日有效`
          }}
        </span>
      </div>
    </div>
  </div>
</template>
<script>

export default {
  name: 'CouponsTemp',
  methods: {  },
  props: {
    hasModifiedItem: {
      type: Function,
      required: true,
    },
    getGoodsInfo: {
      type: Object,
      required: true,
    },
  },
  data() {
    return {
      serviceCols: [
        { title: '已选服务', key: 'name', align: 'center' },
        { title: '价格', slot: 'price', align: 'center' },
        { title: '可用次数', key: 'times', align: 'center' },
      ],
    };
  },
};
</script>

<style scoped lang="less">
@import '../../common/style/common.less';
</style>
price
