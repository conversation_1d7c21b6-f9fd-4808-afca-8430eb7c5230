<template>
  <div :class="['goods-box', getBoxClass]">
    <div v-if="hasChanged" :class="['flex', isBasic ? 'flex-item-end' : '', 'mb-10']">
      <div class="status-title" v-if="isBasic">变更前</div>
      <div class="status-title" v-else>
        <span>变更后</span>
        <span class="update-tips">有更新</span>
      </div>
    </div>
    <div class="block-title">基本信息</div>
    <div class="info-item">
      <div class="item-label">商品名：</div>
      <div :class="['item-value', hasModifiedItem('name') ? 'base-changed' : '']">
        <span>{{ getGoodsInfo.name }}</span>
      </div>
    </div>
    <div class="info-item">
      <div class="item-label">商品类型：</div>
      <div :class="['item-value', hasModifiedItem('goods_type') ? 'base-changed' : '']">
        <span>{{ goodsOptions.typeDesc[goodsType]?.desc }}</span>
      </div>
    </div>
    <div class="info-item">
      <div class="item-label align-start">商品图：</div>
      <div :class="['item-value', hasModifiedItem('slide_imgs') ? 'base-changed' : '']">
        <div style="align-self: flex-start" :class="['img-box']">
          <Picture :value="getGoodsInfo.slide_imgs" isQueryDetail></Picture>
        </div>
      </div>
    </div>
    <div class="info-item">
      <div class="item-label align-start">商品详情：</div>
      <div :class="['item-value', hasModifiedItem('detail_imgs') ? 'base-changed' : '']">
        <div style="align-self: flex-start" :class="['img-box']">
          <Picture :value="getGoodsInfo.detail_imgs" isQueryDetail></Picture>
        </div>
      </div>
    </div>
    <component
      :is="goodsTypeMap[goodsType]"
      :isBasic="isBasic"
      :hasModifiedItem="hasModifiedItem"
      :getGoodsInfo="getGoodsInfo"
    ></component>
    <div class="info-item">
      <div class="item-label">售卖主体：</div>
      <div :class="['item-value', hasModifiedItem('sell_ent_type') ? 'base-changed' : '']">
        <span>{{ getGoodsInfo.sell_ent_type_text }}</span>
      </div>
    </div>
    <div class="info-item" v-if="getGoodsInfo.goods_type !== '10'">
      <div class="item-label">上架范围：</div>
      <div :class="['item-value', hasModifiedItem('xn_scope') ? 'base-changed' : '']">
        <span>
          <span v-if="getGoodsInfo.xn_scope !== '9'">
            {{ getGoodsInfo.xn_scope == '1' ? '零售服务' : getGoodsInfo.xn_scope == '2' ? '问诊治疗' : '-' }}
          </span>
          <span v-else>零售服务 问诊治疗</span></span
        >
      </div>
    </div>
    <div class="info-item" v-else>
      <div class="item-label">上架范围：</div>
      <div :class="['item-value', hasModifiedItem('shelf_scope') ? 'base-changed' : '']">
        <span>
          <span v-if="getGoodsInfo.shelf_scope !== '9'">
            {{ getGoodsInfo.shelf_scope == '1' ? '零售服务' : getGoodsInfo.shelf_scope == '2' ? '问诊治疗' : '-' }}
          </span>
          <span v-else>零售服务 问诊治疗</span>
        </span>
      </div>
    </div>
    <div class="info-item">
      <div class="item-label">储值购买：</div>
      <div :class="['item-value', hasModifiedItem('is_recharge_buy') ? 'base-changed' : '']">
        <span>{{ goodsOptions.isRechargeBuyDesc[getGoodsInfo.is_recharge_buy]?.desc }}</span>
      </div>
    </div>
  </div>
</template>

<script>
import Picture from '@/components/upload/picture';
import PhysicalTemp from './PhysicalTemp.vue';
import VirtualTemp from './VirtualTemp.vue';
import CouponsTemp from './CouponsTemp.vue';
import PackageTemp from './PackageTemp.vue';
import S from '@/utils/util';
/*
  商品类型枚举
  10 实物
  15 虚拟
  20 套餐
  25 通兑券
* */
export default {
  name: 'GoodsInfo',
  components: {
    Picture,
    PhysicalTemp,
    VirtualTemp,
    CouponsTemp,
    PackageTemp
  },
  mixins: [],
  props: {
    isBasic: {
      type: Boolean,
      default: true
    },
    modifyContent: {
      type: Object,
      default: () => {}
    },
    basicInfo: {
      type: Object,
      default: () => {}
    },
    goodsType: {
      type: String,
      default: ''
    },
    afterInfo: {
      type: Object,
      default: () => {}
    },
    goodsOptions: {
      type: Object,
      default: () => ({
        typeDesc: [],
        xnScopeDesc: [],
        storedList: [],
        isRechargeBuyDesc: [],
        sellEntType: []
      })
    }
  },
  data() {
    return {
      key: 'value',
      tableData: [],
      goodsTypeMap: {
        10: 'PhysicalTemp',
        15: 'VirtualTemp',
        20: 'PackageTemp',
        25: 'CouponsTemp'
      }
    };
  },
  created() {
    console.log(this.goodsType);
  },
  computed: {
    getBoxClass() {
      return this.isBasic ? 'goods-box-basic' : 'goods-box-after';
    },
    hasModifiedItem() {
      return key => {
        const modifyKeys = Object.keys(this.modifyContent);
        return modifyKeys.includes(key);
      };
    },
    hasChanged() {
      return Object.keys(this.modifyContent).length > 0;
    },
    getGoodsInfo() {
      return this.isBasic ? this.basicInfo : this.afterInfo;
    },
    getSellScopes() {
      const { xnScopeDesc } = this.goodsOptions;
      const { shelf_scope } = this.getGoodsInfo;
      return xnScopeDesc[shelf_scope]?.desc;
    }
  },
  methods: {}
};
</script>

<style lang="less" scoped>
@import '../../common/style/common.less';

.goods-box {
  background: #fafbfd;
  padding: 16px 10px;
  box-sizing: border-box;
}

.goods-box-after {
  .block-title {
    background: #f3f5f8;
    color: #000;
  }

  .info-item {
    .base-changed {
      color: #000;
    }
  }

  ::v-deep .block-title {
    background: #f3f5f8;
    color: #000;
  }

  ::v-deep .info-item {
    .base-changed {
      color: #000;
    }
  }
}

.goods-box-basic {
  background: rgba(248, 248, 248, 0.28);
}
</style>
