<template>
  <div>
    <div class="info-item mb-20 mt-20">
      <div class="item-label align-start">设置套餐：</div>
      <div :class="['item-value', hasModifiedItem('name') ? 'base-changed' : '']">
        <Table
          :columns="serviceCols"
          :data="getGoodsInfo.tc_infos"
          style="width: 100%; max-width: 1000px"
          disabled-hover
        >
          <template v-slot:goodsId="{ row }">{{ row.goods?.id }}</template>
          <template v-slot:spec="{ row }">{{ row.goods_type === '10' ? row.spec : '-' }}</template>
        </Table>
      </div>
    </div>
    <div class="block-title">价格库存</div>
    <div class="info-item">
      <div class="item-label">价格：</div>
      <div :class="['item-value', hasModifiedItem('price') ? 'base-changed' : '']">
        <span>￥ {{ getGoodsInfo.price }}</span>
      </div>
    </div>
    <div class="info-item">
      <div class="item-label">储值价：</div>
      <div :class="['item-value', hasModifiedItem('stored_price') ? 'base-changed' : '']">
        <span v-if="getGoodsInfo.stored_price">¥ {{ getGoodsInfo.stored_price | number_format }}</span>
        <span v-else>-</span>
      </div>
    </div>
    <template>
      <div class="block-title">关联赠送服务卡券</div>
      <div class="info-item">
        <div class="item-label">是否关联：</div>
        <div :class="['item-value', hasModifiedItem('relation_card') ? 'base-changed' : '']">
          <span>{{ getGoodsInfo.relation_card == '1' ? '关联' : '不关联'}}</span>
        </div>
      </div>
      <div class="info-item" v-if="getGoodsInfo.relation_card == '1'">
        <div class="item-label">发放方式：</div>
        <div :class="['item-value', hasModifiedItem('grant_type') ? 'base-changed' : '']">
          <span>{{ getGoodsInfo.grant_type == '2' ? '订单交易后按需发放' : '购买时发放' }}</span>
        </div>
      </div>
      <div class="info-item mb-20 mt-20" v-if="getGoodsInfo.relation_card == '1'">
        <div class="item-label align-start"></div>
        <div :class="['item-value', hasModifiedItem('services') ? 'base-changed' : '']">
          <Table
            :columns="serviceTcCols"
            :data="getGoodsInfo.services"
            style="width: 100%; max-width: 1000px"
            disabled-hover
          >
            <template slot-scope="{ row }" slot="times">
              <div v-if="getGoodsInfo.grant_type == '2'">-</div>
              <div v-else>{{ row.times || '-' }}</div>
            </template>
          </Table>
        </div>
      </div>
    </template>
    <div class="block-title">服务设置</div>
  </div>
</template>
<script>
export default {
  name: 'PackageTemp',
  props: {
    hasModifiedItem: {
      type: Function,
      required: true,
    },
    getGoodsInfo: {
      type: Object,
      required: true,
    },
  },
  data() {
    return {
      serviceCols: [
        { title: 'ID', slot: 'goodsId', align: 'center' },
        { title: '商品名', key: 'name', align: 'center' },
        { title: '类型', key: 'goods_type_text', align: 'center' },
        { title: '规格', slot: 'spec', align: 'center' },
        { title: '价格', key: 'price', align: 'center' },
        { title: '数量', key: 'num', align: 'center' },
      ],
      serviceTcCols: [
        { title: '服务', key: 'name', align: 'center', minWidth: 50 },
        { title: '来源', key: 'source_platform_text', align: 'center', minWidth: 50 },
        { title: '类型', key: 'serv_type_text', align: 'center', minWidth: 50 },
        { title: '发放数量', slot: 'times', align: 'center', minWidth: 50 },
      ]
    };
  },
};
</script>

<style scoped lang="less">
@import '../../common/style/common.less';
</style>
price
