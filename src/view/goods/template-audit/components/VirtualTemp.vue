<template>
  <div>
    <div class="info-item mb-20 mt-20">
      <div class="item-label align-start">服务权益：</div>
      <div :class="['item-value', hasModifiedItem('name') ? 'base-changed' : '']">
        <Table
          :columns="serviceCols"
          :data="getGoodsInfo.services"
          style="width: 100%; max-width: 1000px"
          disabled-hover
        ></Table>
      </div>
    </div>
    <div class="block-title">价格库存</div>
    <div class="info-item">
      <div class="item-label">价格：</div>
      <div :class="['item-value', hasModifiedItem('price') ? 'base-changed' : '']">
        <span>¥ {{ getGoodsInfo.price | number_format }}</span>
      </div>
    </div>
    <div class="info-item">
      <div class="item-label">储值价：</div>
      <div :class="['item-value', hasModifiedItem('stored_price') ? 'base-changed' : '']">
        <span v-if="getGoodsInfo.stored_price">¥ {{ getGoodsInfo.stored_price | number_format }}</span>
        <span v-else>-</span>
      </div>
    </div>
    <div class="block-title">服务设置</div>
    <div class="info-item">
      <div class="item-label">商品有效期: ：</div>
      <div :class="['item-value', hasModifiedItem('service_info.expiration.days') ? 'base-changed' : '']">
        <span>
          {{
            echoData.service_info.expiration.type == '1'
              ? `购买后${getGoodsInfo.service_info.expiration.days}天内有效`
              : `截止到${getGoodsInfo.service_info.expiration.date}日有效`
          }}
        </span>
      </div>
    </div>
    <div class="info-item">
      <div class="item-label">是否可约: ：</div>
      <div :class="['item-value', hasModifiedItem('service_info.is_appointment') ? 'base-changed' : '']">
        <span>{{ getGoodsInfo.service_info.is_appointment == '1' ? '需要预约' : '免预约' }}</span>
      </div>
    </div>
    <div class="info-item">
      <div class="item-label">是否可退：</div>
      <div :class="['item-value', hasModifiedItem('service_info.is_can_refund') ? 'base-changed' : '']">
        <span>{{ getGoodsInfo.service_info.is_can_refund == '1' ? '可退' : '不可退' }}</span>
      </div>
    </div>
    <div class="info-item">
      <div class="item-label">使用时间：</div>
      <div :class="['item-value', hasModifiedItem('service_info.working_time') ? 'base-changed' : '']">
        <span>{{ getGoodsInfo.service_info.working_time }}</span>
      </div>
    </div>
    <div class="info-item">
      <div class="item-label">不可使用日期：</div>
      <div :class="['item-value', hasModifiedItem('service_info.not_working_date') ? 'base-changed' : '']">
        <span>{{ getGoodsInfo.service_info.not_working_date }}</span>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'VirtualGoodsTemp',
  props: {
    hasModifiedItem: {
      type: Function,
      required: true,
    },
    getGoodsInfo: {
      type: Object,
      required: true,
    },
    isBasic: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      serviceCols: [
        { title: '已选服务', key: 'name', align: 'center', minWidth: 50 },
        { title: '价格', key: 'price', align: 'center', minWidth: 50 },
        { title: '可用次数', key: 'times', align: 'center', minWidth: 50 },
      ],
    };
  },
  computed: {
    echoData() {
      return this.getGoodsInfo;
    },
  },
  watch: {
    echoData: {
      handler(val) {},
      immediate: true,
    },
  },
};
</script>

<style scoped lang="less">
@import '../../common/style/common.less';
</style>
