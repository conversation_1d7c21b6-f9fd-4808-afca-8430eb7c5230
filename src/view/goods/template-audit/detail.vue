<!--  -->
<template>
  <div class="goods-template-wrap">
    <div class="goods-info-box flex" v-show="!pageLoading">
      <div class="flex-1">
        <goods-info
          :isBasic="true"
          :modify-content="modifyContent"
          :afterInfo="afterInfo"
          :basicInfo="basicInfo"
          :goods-type="goodsType"
          :goodsOptions="{
            storedList,
            isRechargeBuyDesc,
            sellEntType,
            typeDesc,
            xnScopeDesc
          }"
        ></goods-info>
      </div>
      <div class="flex-1 ml-20" v-if="hasChanged">
        <goods-info
          :isBasic="false"
          :modify-content="modifyContent"
          :afterInfo="afterInfo"
          :basicInfo="basicInfo"
          :goods-type="goodsType"
          :goodsOptions="{
            storedList,
            isRechargeBuyDesc,
            sellEntType,
            typeDesc,
            xnScopeDesc
          }"
        ></goods-info>
      </div>
    </div>
    <div style="height: 100; width: 100%" v-if="pageLoading">
      <Spin fix>
        <div>加载中...</div>
      </Spin>
    </div>

    <div class="fixed-bottom-wrapper">
      <back-button></back-button>
      <div class="flex-inline" v-if="auditStatus === '1' && $route.query.isAudit">
        <Button class="ml-12 mr-12" @click="confirmVisible = true" type="primary">审核通过</Button>
        <audit-refuse @ok="rejectConfirm"></audit-refuse>
      </div>
    </div>
    <k-confirm-modal v-model="confirmVisible" title="" isCustomContent @on-confirm="passConfirm">
      <div slot="custom-content" class="modal-content">
        <div class="title">温馨提示</div>
        <div class="content-text">确认审核通过模板变更，并将变更分发更新至所有{{ getSellEntDesc }}吗？</div>
      </div>
    </k-confirm-modal>
  </div>
</template>

<script type="text/javascript">
import GoodsInfo from './components/GoodsInfo.vue';
import S from '@/utils/util';
import KConfirmModal from '@/components/k-confirm-modal';

export default {
  name: 'TemplateAuditDetail',
  components: {
    GoodsInfo,
    KConfirmModal
  },
  mixins: [],
  props: {},
  data() {
    return {
      submitLoading: false,
      goodsStatus: 'INIT',
      goodsId: '',
      auditStatus: '',
      modifyContent: {},
      basicInfo: {},
      afterInfo: {},
      goodsType: '',
      typeDesc: [],
      xnScopeDesc: [],
      storedList: [],
      isRechargeBuyDesc: [],
      sellEntType: [],
      confirmVisible: false,
      pageLoading: true
    };
  },
  computed: {
    hasChanged() {
      return Object.keys(this.modifyContent).length > 0;
    },
    getSellEntDesc() {
      return S.isEmptyObject(this.afterInfo) ? this.basicInfo.sell_ent_type_text : this.afterInfo.sell_ent_type_text;
    }
  },
  watch: {},
  created() {
    this.$router.onReady(() => {
      this.goodsId = this.$route.query.id;
      this.getGoodsOptions();
    });
  },
  mounted() {},
  methods: {
    getDetail() {
      this.$api.getGoodsAuditInfo({ id: this.goodsId }).then(res => {
        console.log('-> %c res  ===    %o', 'font-size: 15px;color: #fa8c16 ;', res);
        this.afterInfo = res.audit_data.after_info;
        this.basicInfo = res.audit_data.basic_info;
        this.modifyContent = res.audit_data.modify_content;
        this.goodsType = res.goods_type;
        this.auditStatus = res.audit_status;
        this.pageLoading = false;
      });
    },
    getGoodsOptions() {
      this.pageLoading = true;
      this.$api.getGoodsLibOptions().then(res => {
        this.storedList = S.descToArrHandle(res.isRechargeBuyDesc);
        this.isRechargeBuyDesc = res.isRechargeBuyDesc;
        this.sellEntType = res.sellEntType;
        this.typeDesc = res.typeDesc;
        console.log('-> %c this.typeDesc  ===    %o', 'font-size: 15px;color: #fa8c16 ;', this.typeDesc);
        this.xnScopeDesc = res.xnScopeDesc;
        this.getDetail();
      });
    },
    updateAuditStatus(params) {
      this.$api.auditGoodsTemplate(params).then(
        res => {
          this.$Message.success(`审核${params.act === 'PASS' ? '通过' : '驳回'}成功`);
          this.confirmVisible = false;
          this.$router.back();
        },
        err => {
          this.$Message.error(err.errmsg);
        }
      );
    },
    rejectConfirm(reason) {
      const params = {
        id: this.goodsId,
        act: 'REJECT',
        reason
      };
      this.updateAuditStatus(params);
    },
    passConfirm() {
      const params = {
        id: this.goodsId,
        act: 'PASS'
      };
      this.updateAuditStatus(params);
    }
  },
  filters: {}
};
</script>
<style lang="less" scoped>
@import '../common/style/common.less';

.goods-template-wrap {
  padding: 10px;
  margin-bottom: 60px;
}

.modal-content {
  padding: 10px;

  .title {
    margin-top: 12px;
    font-size: 15px;
  }

  .content-text {
    font-size: 13px;
    color: #666666;
    line-height: 20px;
    margin-top: 10px;
  }
}
</style>
