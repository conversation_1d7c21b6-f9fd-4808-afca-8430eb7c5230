<template>
  <div class="goods-wrapper">
    <Form inline :label-width="0" @submit.native.prevent @keyup.enter.native="onSearch">
      <div class="no-wrap">
        <!-- <Col span="6"> -->
        <FormItem label="">
          <Input v-model="queryFormData.goods_name" placeholder="请输入商品名" clearable />
        </FormItem>
        <!-- <Col span="6"> -->
        <FormItem label="">
          <Select v-model="queryFormData.goods_type" style="width: 200px" placeholder="请选择商品类型" clearable>
            <Option v-for="item in prodTypeDesc" :value="item.id" :key="item.id">{{ item.desc }}</Option>
          </Select>
        </FormItem>
        <!-- </Col> -->
        <FormItem style="text-align: left">
          <Button type="primary" class="mr10" @click="onSearch">筛选</Button>
          <Button type="default" @click="onResetSearch">重置</Button>
        </FormItem>
      </div>
    </Form>

    <div class="table-wrapper">
      <div class="panel-nav">
        <a class="nav" :class="{ active: !$route.query.audit_status }" @click.prevent.capture="onStatusChange('')">
          全部
        </a>
        <a
          class="nav"
          v-for="item in auditStatusDesc"
          :key="item.id"
          :class="{ active: $route.query.audit_status == item.id }"
          @click.prevent.capture="onStatusChange(item.id)"
        >
          {{ item.desc }}
          <Tag :color="getTagColor(item.id)">{{ listCount[item.id] }}</Tag>
        </a>
      </div>
      <Table
        ref="kls-b"
        class="kls"
        :loading="tableLoading"
        :columns="tableCols"
        :data="list"
        :height="$store.state.app.clientHeight - 272"
      >
        <template slot="audit_status" slot-scope="{ row }">
          <mark-status :type="getTagColor(row.audit_status)">{{ row.audit_status_text }}</mark-status>
        </template>
        <template slot-scope="{ row, index }" slot="goodsInfo">
          <div class="flex flex-item-align">
            <viewer :images="[row.img]" v-if="row.img" class="flex flex-item-center">
              <img
                style="width: 60px; height: auto; margin: 10px 0"
                :src="row.img | imageStyle('B.w300')"
                class="image"
              />
            </viewer>
            <span>{{ row.goods_name }}</span>
          </div>
        </template>
        <template slot-scope="{ row }" slot="action">
          <a @click="toDetail(row.id, true)" v-if="row.audit_status === '1'" style="margin-left: 10px">审核</a>
          <a @click="toDetail(row.id)" style="margin-left: 10px">详情</a>
        </template>
      </Table>
      <div class="block_20"></div>
      <KPage
        :total="total"
        :page-size="+queryFormData.pageSize"
        :current="+queryFormData.page"
        @on-change="handleCurrentChange"
        @on-page-size-change="handleSizeChange"
        style="text-align: center"
      />
    </div>
  </div>
</template>

<script>
import search from '@/mixins/search';
import S from 'utils/util';

const init_query_form_data = {
  page: 1,
  pageSize: 20,
  goods_name: '', // 商品名称
  goods_type: '', //商品类型
  audit_status: '', // 审核状态
  r: ''
};
export default {
  name: 'list',
  mixins: [search],
  components: {},
  data() {
    return {
      apiName: 'getGoodsAuditList',
      queryFormData: {
        ...init_query_form_data
      },
      supplierList: [], // 供应商下拉枚举
      prodTypeDesc: [], // 商品类型下拉枚举
      auditStatusDesc: [], // 商品类型下拉枚举
      tableCols: [
        { title: 'ID', key: 'id', align: 'center', width: 120 },
        { title: '商品名', slot: 'goodsInfo', align: 'center' },
        { title: '商品类型', key: 'goods_type_text', align: 'center' },
        { title: '类型', key: 'audit_type_desc', align: 'center' },
        { title: '审核状态', slot: 'audit_status', align: 'center' },
        { title: '审核人', key: 'operator_name', align: 'center' },
        { title: '审核时间', key: 'operate_at', align: 'center' },
        {
          title: '审核意见',
          key: 'status_desc',
          align: 'center',
          render: (h, { row }) => h('span', {}, row.reason || '-')
        },
        { title: '操作', slot: 'action', align: 'center' }
      ],
      listCount: {}
    };
  },
  computed: {
    getTagColor() {
      return type => {
        switch (type) {
          case '1':
            return 'warning';
          case '2':
            return 'success';
          case '3':
            return 'error';
          default:
            return 'info';
        }
      };
    }
  },
  watch: {},
  created() {
    this.getOptions();

    // 获取枚举列表
  },
  mounted() {},
  methods: {
    // 状态改变调用接口
    onStatusChange(status) {
      this.queryFormData.page = 1;
      this.queryFormData.audit_status = status;
      this.submitQueryForm();
    },
    // 上下架获取数据
    changeGoodsStatus(id, status) {
      let params = {
        status: status === 'ON' ? 'OFF' : 'ON',
        id
      };
      this.$api.changeGoodsStatus(params).then(
        res => {
          this.$Message.success(`${params.status === 'ON' ? '上架' : '下架'}成功`);
          this.submitQueryForm(true);
        },
        err => this.$Message.error(err.errmsg)
      );
    },
    onResetSearch() {
      this.queryFormData = { ...init_query_form_data };
      this.submitQueryForm();
    },
    getOptions() {
      this.$api.getGoodsAuditOptions().then(res => {
        console.log('-> %c res  ===    %o', 'font-size: 15px;color: #fa8c16 ;', res);
        this.prodTypeDesc = S.descToArrHandle(res.typeDesc);
        this.auditStatusDesc = S.descToArrHandle(res.auditStatusDesc);
        this.queryFormData = S.merge(this.queryFormData, this.$route.query);
        this.submitQueryForm(true);
      });
    },
    toDetail(id, isAudit = false) {
      let query = {
        id
      };
      isAudit && (query.isAudit = '1');
      this.$router.push({
        path: '/goods/template-audit/detail',
        query
      });
    },
    handlerListData(data) {
      this.listCount = data.list_count;
    }
  },
  beforeRouteUpdate(to, from, next) {
    // S.log(to.params['nav-stack-key-dir'], 'nav-stack-key-dir')
    this.queryFormData = S.merge(init_query_form_data, to.query);
    this.loadList();
    next();
  }
};
</script>

<style scoped lang="less">
.goods-wrapper {
}
</style>
