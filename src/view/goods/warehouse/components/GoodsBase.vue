<template>
  <div class="goods-base-wrapper mt-20">
    <div class="block-header"><span>基本信息</span></div>
    <KWidget label="商品名：" required>
      <Input
        v-model="formData.name"
        style="max-width: 70%"
        autocomplete="off"
        maxlength="15"
        placeholder="输入商品名称，15字以内"
        show-word-limit
      />
    </KWidget>
    <KWidget label="商品图：" required>
      <MaterialPicture v-model="formData.slide_imgs" :limit="9" />
      <div class="note">建议尺寸：800*800像素，图片大小不超过3.0M，最多9张，你可以拖拽图片调整顺序</div>
    </KWidget>
    <KWidget label="商品详情：" required>
      <MaterialPicture v-model="formData.detail_imgs" :limit="30" />
      <div class="note">图片大小不超过3.0M，最多30张，你可以拖拽图片调整顺序</div>
    </KWidget>
  </div>
</template>

<script>
import Picture from '@/components/upload/picture';
import goodsDiff from '../mixins/goodsDiff';

export default {
  name: 'GoodsBase',
  components: { Picture },

  mixins: [],

  props: {
    baseInfo: {
      type: Object,
      default: () => ({})
    }
  },
  watch: {
    baseInfo: {
      handler(newValue) {
        this.formData = this.$lodash.cloneDeep(newValue);
        this.originFormData = this.$lodash.cloneDeep(newValue);
      },
      deep: true,
      immediate: true
    }
  },
  data() {
    return {
      formData: {
        name: '',
        slide_imgs: [],
        detail_imgs: []
      },
      formDataKeyMap: {
        detail_imgs: { sort: 2, name: '商品详情' },
        name: { sort: 0, name: '商品名' },
        slide_imgs: { sort: 1, name: '商品图' }
      }
    };
  },

  computed: {},
  created() {},

  mounted() {},

  destroyed() {},

  methods: {
    validateForm() {
      const data = this.formData;
      if (data.name.trim() == '') {
        this.$Message.error('请填写商品标题');
        return false;
      }

      if (data.slide_imgs.length <= 0) {
        this.$Message.error('至少上传一张商品图');
        return false;
      }

      if (data.detail_imgs.length <= 0) {
        this.$Message.error('至少上传一张商品详情图');
        return false;
      }
      return true;
    },
    getFormDataDiff(obj1, obj2) {
      console.log('-> %c obj1, obj2  ===    %o', 'font-size: 15px;color: #fa8c16 ;', obj1, obj2);
      if (this.$lodash.isEqual(obj1, obj2)) {
        return [];
      } else {
        let diff = [];
        for (let key in obj1) {
          if (typeof obj1[key] === 'object') {
            if (JSON.stringify(obj1[key]) !== JSON.stringify(obj2[key])) {
              diff.push(this.formDataKeyMap[key]);
            }
          } else {
            if (obj1[key] !== obj2[key]) {
              diff.push(this.formDataKeyMap[key]);
            }
          }
        }
        return diff.sort((a, b) => a.sort - b.sort).map(item => item.name);
      }
    },
    getDiffData() {
      return this.getFormDataDiff(this.formData, this.originFormData);
    }
  }
};
</script>

<style scoped lang="scss"></style>
