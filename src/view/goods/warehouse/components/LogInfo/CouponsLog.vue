<template>
  <div>
    <div class="info-item" v-if="hasModifiedItem('exchange_num')">
      <div class="item-label">兑换次数：</div>
      <div class="item-value">
        <span>{{ getGoodsInfo.exchange_num }}</span>
      </div>
    </div>
    <div class="info-item mb-20 mt-20" v-if="hasModifiedItem('services')">
      <div class="item-label align-start">服务范围：</div>
      <div class="item-value">
        <Table
          :columns="serviceCols"
          :data="getGoodsInfo.services"
          style="width: 100%; max-width: 1000px"
          disabled-hover
        >
          <template v-slot:price="{ row }">{{ row.price | number_format }}</template>
          <template slot-scope="{ row }" slot="first_tier_price">
            <div>销售价：¥ {{ row.first_tier_price | number_format }}</div>
            <div>会员价：¥ {{ row.first_tier_vip_price | number_format }}</div>
          </template>
          <template slot-scope="{ row }" slot="not_first_tier_price">
            <div>销售价：¥ {{ row.not_first_tier_price | number_format }}</div>
            <div>会员价：¥ {{ row.not_first_tier_vip_price | number_format }}</div>
          </template>
        </Table>
      </div>
    </div>
    <div class="info-item" v-if="hasModifiedItem('remark')">
      <div class="item-label align-start">备注：</div>
      <div class="item-value">
        <span>{{ getGoodsInfo.remark }}</span>
      </div>
    </div>
    <!--    <div class="block-title">价格库存</div>-->
    <div class="info-item" v-if="hasModifiedItem('price')">
      <div class="item-label">价格：</div>
      <div class="item-value">
        <span>￥ {{ getGoodsInfo.price | number_format }}</span>
      </div>
    </div>

    <div class="info-item" v-if="hasModifiedItem('first_tier_price')">
      <div class="item-label">一线城市：</div>
      <div class="item-value">
        <div>
          <span> 销售价：</span> <span>￥ {{ getGoodsInfo.first_tier_price | number_format }}</span>
        </div>
      </div>
    </div>

    <div class="info-item" v-if="hasModifiedItem('first_tier_vip_price')">
      <div class="item-label">一线城市：</div>
      <div class="item-value">
        <div>
          <span> 会员价：</span> <span>￥ {{ getGoodsInfo.first_tier_vip_price | number_format }}</span>
        </div>
      </div>
    </div>
    <div class="info-item" v-if="hasModifiedItem('not_first_tier_price')">
      <div class="item-label">非一线城市：</div>
      <div class="item-value">
        <div>
          <span>销售价：</span> <span>￥ {{ getGoodsInfo.not_first_tier_price | number_format }}</span>
        </div>
      </div>
    </div>
    <div class="info-item" v-if="hasModifiedItem('not_first_tier_vip_price')">
      <div class="item-label">非一线城市：</div>
      <div class="item-value">
        <div>
          <span>会员价：</span><span>￥ {{ getGoodsInfo.not_first_tier_vip_price | number_format }}</span>
        </div>
      </div>
    </div>

    <div class="info-item" v-if="hasModifiedItem('stored_price')">
      <div class="item-label">储值价：</div>
      <div class="item-value">
        <span v-if="getGoodsInfo.stored_price">¥ {{ getGoodsInfo.stored_price | number_format }}</span>
        <span v-else>-</span>
      </div>
    </div>
    <!--    <div class="block-title" v-if="hasModifiedItem('service_info')">服务设置</div>-->
    <div class="info-item" v-if="showExpirationItem">
      <div class="item-label">商品有效期: ：</div>
      <div class="item-value">
        <span>
          {{
            getGoodsInfo.service_info.expiration.type == '1'
              ? `购买后${getGoodsInfo.service_info.expiration.days}天内有效`
              : `截止到${getGoodsInfo.service_info.expiration.date}日有效`
          }}
        </span>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  name: 'CouponsLog',
  props: {
    hasModifiedItem: {
      type: Function,
      required: true
    },
    getGoodsInfo: {
      type: Object,
      required: true
    },
    goods_type: String
  },
  data() {
    return {
      serviceCols: [
        { title: '已选服务', key: 'name', align: 'center' },
        { title: '价格', slot: 'price', key: 'price', align: 'center' },
        { title: '可用次数', key: 'times', align: 'center' }
      ]
    };
  },
  computed: {
    showExpirationItem() {
      return (
        this.hasModifiedItem('service_info.expiration.type') ||
        this.hasModifiedItem('service_info.expiration.date') ||
        this.hasModifiedItem('service_info.expiration.days')
      );
    }
  },

  watch: {
    getGoodsInfo: {
      handler(val) {
        console.log('🚀 ~ handler ~ val=>', val);
        this.tableSpecs();
      },
      immediate: true
    }
  },
  methods: {
    // 处理表格规格
    tableSpecs() {
      if (this.goods_type == '45') {
        this.serviceCols = this.serviceCols.filter(item => item.key != 'price');
        const newColumns = [
          { title: '一线城市', slot: 'first_tier_price', align: 'center', minWidth: 50 },
          { title: '非一线城市', slot: 'not_first_tier_price', align: 'center', minWidth: 50 }
        ];
        this.serviceCols = newColumns.concat(this.serviceCols);
      }
    }
  }
};
</script>

<style scoped lang="less">
@import '../../../common/style/common.less';
</style>
