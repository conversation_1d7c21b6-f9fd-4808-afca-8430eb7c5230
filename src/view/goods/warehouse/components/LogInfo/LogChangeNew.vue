<template>
  <Modal
    :value="value"
    width="950"
    footer-hide
    lock-scroll
    title="修改日志记录"
    :mask-closable="false"
    @on-visible-change="visibleChange"
  >
    <div v-if="value">
      <div class="mb-20 before-box">
        <log-info
          :get-goods-info="getGoodsInfo('before')"
          :has-modified-item="hasModifyItem"
          :sell-ent-type="sellEntType"
          :is-recharge-buy-desc="isRechargeBuyDesc"
        ></log-info>
      </div>
      <div class="after-box">
        <log-info
          :is-before="false"
          :get-goods-info="getGoodsInfo('after')"
          :has-modified-item="hasModifyItem"
          :sell-ent-type="sellEntType"
          :is-recharge-buy-desc="isRechargeBuyDesc"
        ></log-info>
      </div>
    </div>
  </Modal>
</template>

<script>
import LogInfo from './index.vue';

export default {
  name: 'logChangeModal',
  components: { LogInfo },
  mixins: [],
  props: {
    value: {
      type: Boolean,
      default: () => false,
    },
    afterInfo: {
      type: Object,
      default: () => {},
    },
    beforeInfo: {
      type: Object,
      default: () => {},
    },
    diffColumns: {
      type: Array,
      default: () => [],
    },
    sellEntType: {
      type: Object,
      default: () => {},
    },
    isRechargeBuyDesc: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      express_name: '',
      express_no: '',
      beforeData: [],
      afterData: [],
      beforeSpecsData: [],
      afterSpecsData: [],
      diffData: [],
      list: [],
      tableLoading: false,
    };
  },
  computed: {
    hasModifyItem() {
      return itemKey => {
        return this.diffColumns && this.diffColumns.length && this.diffColumns.includes(itemKey);
      };
    },
    getGoodsInfo() {
      return type => {
        return type === 'before' ? this.beforeInfo : this.afterInfo;
      };
    },
  },
  watch: {},
  created() {},
  mounted() {},
  methods: {
    /* 弹窗 */
    closeable() {
      this.$emit('input', false);
    },
    visibleChange(val) {
      if (val) {
      } else {
        this.closeable();
      }
    },

    /* api */
  },
  filters: {},
};
</script>
<style lang="less" scoped>
::v-deep .ivu-modal-body {
  padding: 20px 30px;
  height: calc(~'100% - 110px');
  overflow-y: auto;
}

::v-deep .ivu-modal {
  height: calc(~'100% - 100px') !important;
}

::v-deep .ivu-modal-content {
  height: calc(~'100% - 100px');
}

::v-deep .after-box {
  .block-title {
    color: #000;
    background: #f5f5f5;
  }
}
</style>
