<template>
  <div>
    <div class="info-item mb-20 mt-20" v-if="hasModifiedItem('tc_infos')">
      <div class="item-label align-start">设置套餐：</div>
      <div class="item-value">
        <Table
          :columns="serviceCols"
          :data="getGoodsInfo.tc_infos"
          style="width: 100%; max-width: 1000px"
          disabled-hover
        >
          <template v-slot:goodsId="{ row }">{{ row.goods?.id }}</template>
          <template v-slot:spec="{ row }">{{ ['10', '40'].includes(row.goods_type) ? row.spec : '-' }}</template>

          <template v-slot:price="{ row }">
            <span v-text-format.money="row.price" />
          </template>

          <template v-slot:first_tier_price="{ row }">
            <div v-if="row.goods_type === '40'">
              <div>销售价：¥ {{ getPyGoodsPrice(row).first_tier_price | number_format }}</div>
              <div>会员价：¥ {{ getPyGoodsPrice(row).first_tier_vip_price | number_format }}</div>
            </div>
            <div v-else>
              <div>销售价：¥ {{ row.first_tier_price | number_format }}</div>
              <div>会员价：¥ {{ row.first_tier_vip_price | number_format }}</div>
            </div>
          </template>
          <template v-slot:not_first_tier_price="{ row }">
            <div v-if="row.goods_type === '40'">
              <div>销售价：¥ {{ getPyGoodsPrice(row).not_first_tier_price | number_format }}</div>
              <div>会员价：¥ {{ getPyGoodsPrice(row).not_first_tier_vip_price | number_format }}</div>
            </div>
            <div v-else>
              <div>销售价：¥ {{ row.not_first_tier_price | number_format }}</div>
              <div>会员价：¥ {{ row.not_first_tier_vip_price | number_format }}</div>
            </div>
          </template>
        </Table>
      </div>
    </div>
    <!--    <div class="block-title" v-if="showPriceTitle">价格库存</div>-->
    <div class="info-item" v-if="hasModifiedItem('price')">
      <div class="item-label">价格：</div>
      <div class="item-value">
        <span>￥ {{ getGoodsInfo.price | number_format }}</span>
      </div>
    </div>

    <div class="info-item" v-if="hasModifiedItem('first_tier_price')">
      <div class="item-label">一线城市：</div>
      <div class="item-value">
        <div>
          <span> 销售价：</span> <span>￥ {{ getGoodsInfo.first_tier_price | number_format }}</span>
        </div>
      </div>
    </div>
    <div class="info-item" v-if="hasModifiedItem('first_tier_vip_price')">
      <div class="item-label">一线城市：</div>
      <div class="item-value">
        <div>
          <span> 会员价：</span> <span>￥ {{ getGoodsInfo.first_tier_vip_price | number_format }}</span>
        </div>
      </div>
    </div>
    <div class="info-item" v-if="hasModifiedItem('not_first_tier_price')">
      <div class="item-label">非一线城市：</div>
      <div class="item-value">
        <div>
          <span>销售价：</span> <span>￥ {{ getGoodsInfo.not_first_tier_price | number_format }}</span>
        </div>
      </div>
    </div>
    <div class="info-item" v-if="hasModifiedItem('not_first_tier_vip_price')">
      <div class="item-label">非一线城市：</div>
      <div class="item-value">
        <div>
          <span>会员价：</span><span>￥ {{ getGoodsInfo.not_first_tier_vip_price | number_format }}</span>
        </div>
      </div>
    </div>
    <div class="info-item" v-if="hasModifiedItem('stored_price')">
      <div class="item-label">储值价：</div>
      <div class="item-value">
        <span v-if="getGoodsInfo.stored_price">¥ {{ getGoodsInfo.stored_price | number_format }}</span>
        <span v-else>-</span>
      </div>
    </div>
    <div class="info-item" v-if="hasModifiedItem('relation_card')">
      <div class="item-label align-start">是否关联：</div>
      <div class="item-value">{{ getGoodsInfo.relation_card == 1 ? '关联' : '不关联' }}</div>
    </div>
    <div class="info-item" v-if="hasModifiedItem('grant_type') && getGoodsInfo.relation_card == 1">
      <div class="item-label align-start">发放方式：</div>
      <div class="item-value">{{ getGoodsInfo.grant_type == 1 ? '购买时发放' : '订单交易后按需发放' }}</div>
    </div>
    <div
      class="info-item"
      v-if="(hasModifiedItem('grant_type') || hasModifiedItem('services')) && getGoodsInfo.relation_card == 1"
    >
      <div class="item-label align-start"></div>
      <div class="item-value">
        <table class="table" style="width: 500px">
          <thead>
            <tr>
              <th>服务</th>
              <th>类型</th>
              <th>来源</th>
              <th>发放数量</th>
              <th>操作</th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="(item, key) in getGoodsInfo.services" :key="key">
              <td>{{ item.name }}</td>
              <td>{{ item.serv_type_text }}</td>
              <td>{{ item.source_platform_text }}</td>
              <td>
                <span style="width: 60px" v-if="getGoodsInfo.grant_type == 1">{{ item.times }}</span>
                <div v-else style="width: 60px">-</div>
              </td>
              <td>-</td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>

    <!--    <div class="block-title" v-if="hasModifiedItem('service_info')">服务设置</div>-->
  </div>
</template>
<script>
export default {
  name: 'PackageLog',
  props: {
    hasModifiedItem: {
      type: Function,
      required: true
    },
    getGoodsInfo: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      serviceCols: [
        { title: 'ID', slot: 'goodsId', align: 'center' },
        { title: '商品名', key: 'name', align: 'center' },
        { title: '类型', key: 'goods_type_text', align: 'center' },
        { title: '规格', slot: 'spec', align: 'center' },
        { title: '价格', slot: 'price', key: 'price', align: 'center' },
        { title: '数量', key: 'num', align: 'center' }
      ]
    };
  },
  computed: {
    echoData() {
      return this.getGoodsInfo;
    },
    showPriceTitle() {
      return this.hasModifiedItem('price') || this.hasModifiedItem('stored_price');
    }
  },
  watch: {
    echoData: {
      handler(val) {
        console.log('🚀 ~ handler ~ val=>', val);
        this.tableSpecs(val);
      },
      immediate: true
    }
  },
  methods: {
    getPyGoodsPrice(item) {
      return item?.goods.attrs?.[item.id] || {};
    },

    // 处理表格规格
    tableSpecs(val) {
      if (val.goods_type == '35') {
        this.serviceCols = this.serviceCols.filter(item => item.key != 'price');
        const newColumns = [
          { title: '一线城市', slot: 'first_tier_price', align: 'center', minWidth: 50 },
          { title: '非一线城市', slot: 'not_first_tier_price', align: 'center', minWidth: 50 }
        ];
        this.serviceCols = [...this.serviceCols, ...newColumns];
      }
    }
  }
};
</script>

<style scoped lang="less">
@import '../../../common/style/common.less';
</style>
