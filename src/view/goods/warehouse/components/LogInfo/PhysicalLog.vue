<template>
  <div>
    <div v-if="hasModifiedItem('attrs')">
      <!--      <div class="block-title">价格库存</div>-->
      <div class="info-item">
        <div class="item-label align-start">规格明细：</div>
        <div class="item-value">
          <Table
            :columns="goodsCols"
            :data="tableData"
            style="width: 100%; max-width: 1000px"
            :span-method="handleSpan"
            disabled-hover
            border
          >
            <template v-for="(item, index) in specsData" :slot="item.name + index" slot-scope="{ row }">
              {{ row.specs_atoms[item.name] }}
            </template>

            <template slot-scope="{ row }" slot="first_tier_price">
              <div>销售价：{{ row.first_tier_price | number_format }}</div>
              <div>会员价：{{ row.first_tier_vip_price | number_format }}</div>
            </template>
            <template slot-scope="{ row }" slot="not_first_tier_price">
              <div>销售价：{{ row.not_first_tier_price | number_format }}</div>
              <div>会员价：{{ row.not_first_tier_vip_price | number_format }}</div>
            </template>
            <template slot-scope="{ row }" slot="stored_price">
              <div>{{ Number(row.stored_price || '') > 0 ? row.stored_price : '-' }}</div>
            </template>

            <template slot-scope="{ row }" slot="sku">
              <div>{{ row.sku || '-' }}</div>
            </template>

            <!--                诊所货品-->
            <template slot-scope="{ row }" slot="generic_name">
              <div v-if="row.relate_his_prods?.length > 0">
                <div v-for="item in row.relate_his_prods" :key="item.prod_id">{{ item.generic_name }}</div>
              </div>
              <div v-else>-</div>
            </template>
            <!--                售卖单位-->
            <template slot-scope="{ row }" slot="sales_unit">
              <div v-if="row.relate_his_prods?.length > 0">
                <div v-for="item in row.relate_his_prods" :key="item.prod_id">{{ item._sales_unit }}</div>
              </div>
              <div v-else>-</div>
            </template>
            <template slot-scope="{ row }" slot="price">
              <div v-if="row.relate_his_prods?.length > 0">
                <div v-for="item in row.relate_his_prods" :key="item.prod_id">{{ item._price | number_format }}</div>
              </div>
              <div v-else>-</div>
            </template>
            <template slot-scope="{ row }" slot="buy_multiples">
              <div v-if="row.relate_his_prods?.length > 0">
                <div v-for="item in row.relate_his_prods" :key="item.prod_id">{{ item.buy_multiples }}</div>
              </div>
              <div v-else>-</div>
            </template>
          </Table>
        </div>
      </div>
      <div class="info-item" v-if="goods_type == '40'">
        <div class="item-label align-start">分账策略：</div>
        <div class="item-value">
          <Table :columns="fenzhangColumns" :data="tableData" :span-method="handleSpan" disabled-hover border>
            <template v-for="(item, index) in specsData" :slot="item.name + index" slot-scope="{ row }">
              {{ row.specs_atoms[item.name] || '-' }}
            </template>

            <template slot-scope="{ row }" slot="fenzhang">
              <div>
                <span>实付时≥单次成本价</span>
                <span style="margin: 0 4px">{{ row.divide_rules?.real_price || '-' }}</span>
                <span>元，销售提成按</span>
                <span style="margin: 0 4px">
                  {{ formatFenZhangText(row, 'gte') }}
                </span>
                <span>分账</span>
              </div>
              <div>
                <span>实付时&lt;单次成本价</span>
                <span style="margin: 0 4px">{{ row.divide_rules?.real_price || '-' }}</span>
                <span>元，销售提成按</span>
                <span style="margin: 0 4px">
                  {{ formatFenZhangText(row, 'lt') }}
                </span>
                <span>分账</span>
              </div>
            </template>
          </Table>
        </div>
      </div>
    </div>
    <div class="info-item" v-if="hasModifiedItem('relation_card')">
      <div class="item-label align-start">是否关联：</div>
      <div class="item-value">{{ getGoodsInfo.relation_card == 1 ? '关联' : '不关联' }}</div>
    </div>
    <div class="info-item" v-if="hasModifiedItem('grant_type') && getGoodsInfo.relation_card == 1">
      <div class="item-label align-start">发放方式：</div>
      <div class="item-value">{{ getGoodsInfo.grant_type == 1 ? '购买时发放' : '订单交易后按需发放' }}</div>
    </div>
    <div
      class="info-item"
      v-if="(hasModifiedItem('grant_type') || hasModifiedItem('services')) && getGoodsInfo.relation_card == 1"
    >
      <div class="item-label align-start"></div>
      <div class="item-value">
        <table class="table" style="width: 500px">
          <thead>
            <tr>
              <th>服务</th>
              <th>类型</th>
              <th>来源</th>
              <th>发放数量</th>
              <th>操作</th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="(item, key) in getGoodsInfo.services" :key="key">
              <td>{{ item.name }}</td>
              <td>{{ item.serv_type_text }}</td>
              <td>{{ item.source_platform_text }}</td>
              <td>
                <span style="width: 60px" v-if="getGoodsInfo.grant_type == 1">{{ item.times }}</span>
                <div v-else style="width: 60px">-</div>
              </td>
              <td>-</td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
    <div class="block-title" v-if="hasModifiedItem('service_info')">服务设置</div>
  </div>
</template>

<script>
import RenderHeader from '@/mixins/renderHeader';
import cloneDeep from 'lodash/cloneDeep';
import S from '@/utils/util';
import { number } from 'echarts';

export default {
  name: 'LogPhysical',
  props: {
    hasModifiedItem: {
      type: Function,
      required: true
    },
    getGoodsInfo: {
      type: Object,
      required: true
    },
    isBasic: {
      type: Boolean,
      default: true
    },
    goods_type: {
      type: String,
      default: '10'
    }
  },
  mixins: [RenderHeader],
  data() {
    return {
      goodsCols: [
        { title: '价格(元)', key: 'price', align: 'center', minWidth: 50 },
        {
          title: '储值价',
          slot: 'stored_price',
          align: 'center',
          minWidth: 80,
          renderHeader: (h, params) =>
            this._renderHeader(h, params, '商品储值价指使用储值余额购买时可享受的优惠价，不设置即无优惠价')
        },
        { title: '编码', slot: 'sku', align: 'center', minWidth: 50 },
        { title: '货品', slot: 'generic_name', align: 'center', minWidth: 50 },
        { title: '售卖单位', slot: 'sales_unit', align: 'center', minWidth: 50 },
        { title: '单位售价(元)', slot: 'price', align: 'center', minWidth: 50 },
        {
          title: '起购倍数',
          slot: 'buy_multiples',
          align: 'center',
          minWidth: 50,
          className: 'table-col-right-border'
        }
      ],
      fenzhangColumns: [{ title: '分账规则', slot: 'fenzhang', align: 'left', minWidth: '70%' }],
      tableData: [],
      specsData: []
    };
  },
  computed: {
    echoData() {
      return this.getGoodsInfo;
    },
    formatFenZhangText() {
      return (row, type) => {
        if (!row.divide_rules?.[type]) return '-';
        const { divide_type, divide_value } = row.divide_rules?.[type].sales || {};
        const text1 = `${divide_type === 'ratio' ? '分账比例' : '固定金额'}`;
        const text2 = `${divide_type === 'ratio' ? '%' : '元'}`;
        return ` ${text1 || '-'}   ${divide_value || '-'}${text2 || '-'} `;
      };
    }
  },
  watch: {
    echoData: {
      handler(val) {
        console.log('-> %c val  ===    %o', 'font-size: 15px;color: #fa8c16 ;', val);
        if (S.isEmptyObject(val)) return;
        this.tableData = cloneDeep(val.attrs || []);
        this.specsData = cloneDeep(val.specs_data || []);
        // 处理表格规格
        this.tableSpecs();
        // 处理关联货品数据
        this.handleRelateProd();

        if (val.is_recharge_buy === 'no') {
          this.goodsCols = this.goodsCols.filter(item => {
            return item.key !== 'stored_price';
          });
        }
      },
      immediate: true
    }
  },
  methods: {
    handleSpan({ row, column, rowIndex, columnIndex }) {
      if (columnIndex === 0) {
        let x = row.mergeFirstCol === 0 ? 0 : row.mergeFirstCol;
        let y = row.mergeFirstCol === 0 ? 0 : 1;
        return [x, y];
      }
      if (columnIndex === 1) {
        //计算合并的行数列数
        let x = row.mergeSecCol === 0 ? 0 : row.mergeSecCol;
        let y = row.mergeSecCol === 0 ? 0 : 1;
        return [x, y];
      }
    },

    // 处理表格规格
    tableSpecs() {
      this.tableData.forEach(item => {
        item.specsAtoms = Object.values(item.specs_atoms);
      });
      this.integratedData(this.tableData);
      let arr = this.$lodash.cloneDeep(this.specsData);
      arr &&
        arr.forEach((item, index) => {
          if (index === 0) {
            item.className = 'table-col-left-border';
          }
          if (index < arr.length - 1) {
            if (index === 0) {
              item.className = 'table-col-border';
            } else {
              item.className = 'table-col-right-border';
            }
          }
          item.title = item.name;
          item.slot = item.name + index;
          item.minWidth = 40;
          item.align = 'center';
        });
      if (this.goods_type == '40') {
        this.goodsCols = this.goodsCols.filter(item => item.slot != 'stored_price' && item.key != 'price');
        const newColumns = [
          { title: '一线城市', slot: 'first_tier_price', align: 'center', minWidth: 50 },
          { title: '非一线城市', slot: 'not_first_tier_price', align: 'center', minWidth: 50 }
        ];
        this.goodsCols = newColumns.concat(this.goodsCols);
      }
      let newArr = arr.concat(this.goodsCols);
      this.goodsCols = newArr;
      let newArrFenzhang = arr.concat(
        this.fenzhangColumns?.map(item => ({ ...item, minWidth: `${100 - 10 * arr.length}%` }))
      );
      this.fenzhangColumns = newArrFenzhang;
    },

    // 合并单元格
    integratedData(data) {
      // 处理第一列规格
      for (let index = 0; index < data.length; index++) {
        if (data[index].firstAlready !== 1) {
          if (index + 1) {
            data[index].mergeFirstCol = 1;
            for (let idx = index + 1; idx < data.length; idx++) {
              if (data[index].specs_atoms[this.specsData[0]?.name] === data[idx].specs_atoms[this.specsData[0]?.name]) {
                data[index].mergeFirstCol++;
                data[idx].mergeFirstCol = 0;
                data[idx].firstAlready = 1;
              } else {
                break;
              }
            }
          }
        }
      }

      // 处理第二列规格
      for (let index = 0; index < data.length; index++) {
        if (data[index].mergeFirstCol > 1) {
          for (let idx = 0; idx < data[index].mergeFirstCol; idx++) {
            if (data[index + idx].secondAlready !== 1) {
              if (idx + 1 < data[index].mergeFirstCol) {
                data[index + idx].mergeSecCol = 1;
                for (let i = idx + 1; i < data[index].mergeFirstCol; i++) {
                  if (
                    data[index + idx].specs_atoms[this.specsData[1]?.name] ===
                    data[index + i].specs_atoms[this.specsData[1]?.name]
                  ) {
                    data[index + idx].mergeSecCol++;
                    data[index + i].mergeSecCol = 0;
                    data[index + i].secondAlready = 1;
                  } else {
                    break;
                  }
                }
              }
            }
          }
        }
      }
      console.log('=>(detail.vue:450) data', data);

      this.tableData = data;
    },

    // 关联货品
    handleRelateProd() {
      console.log('->this.tableData ', this.tableData);
      this.tableData.forEach(item => {
        item.relate_his_prods.forEach(prods => {
          prods._sales_unit = this.setDefaultUnit(prods).unit || '';
          // prods._stock_num = this.setDefaultUnit(prods).stock_num || ''
          prods._price = this.setDefaultUnit(prods).price || '';
        });
      });
    },

    // 如果货单单位有选中的，默认带出单位，库存，单位售价
    setDefaultUnit(item) {
      let resultList = item.sales_units.filter(item => item.checked == '1');
      return resultList[0] || {};
    }
  }
};
</script>

<style scoped lang="less">
@import '../../../common/style/common.less';
</style>
