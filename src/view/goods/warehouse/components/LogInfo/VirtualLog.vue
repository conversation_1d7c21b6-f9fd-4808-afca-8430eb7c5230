<template>
  <div>
    <div class="info-item mb-20 mt-20" v-if="hasModifiedItem('services')">
      <div class="item-label align-start">服务权益：</div>
      <div class="item-value">
        <Table
          :columns="serviceCols"
          :data="getGoodsInfo.services"
          style="width: 100%; max-width: 1000px"
          disabled-hover
        >
          <template slot-scope="{ row }" slot="first_tier_price">
            <div>销售价：{{ row.first_tier_price | number_format }}</div>
            <div>会员价：{{ row.first_tier_vip_price | number_format }}</div>
          </template>
          <template slot-scope="{ row }" slot="not_first_tier_price">
            <div>销售价：{{ row.not_first_tier_price | number_format }}</div>
            <div>会员价：{{ row.not_first_tier_vip_price | number_format }}</div>
          </template>
        </Table>
      </div>
    </div>
    <!--    <div class="block-title mt-20" v-if="showPriceTitle">价格库存</div>-->
    <div class="info-item" v-if="hasModifiedItem('price')">
      <div class="item-label">价格：</div>
      <div class="item-value">
        <span>￥ {{ getGoodsInfo.price | number_format }}</span>
      </div>
    </div>

    <div class="info-item" v-if="hasModifiedItem('first_tier_price')">
      <div class="item-label">一线城市：</div>
      <div class="item-value">
        <div>
          <span> 销售价：</span> <span>￥ {{ getGoodsInfo.first_tier_price | number_format }}</span>
        </div>
      </div>
    </div>
    <div class="info-item" v-if="hasModifiedItem('first_tier_vip_price')">
      <div class="item-label">一线城市：</div>
      <div class="item-value">
        <div>
          <span> 会员价：</span> <span>￥ {{ getGoodsInfo.first_tier_vip_price | number_format }}</span>
        </div>
      </div>
    </div>
    <div class="info-item" v-if="hasModifiedItem('not_first_tier_price')">
      <div class="item-label">非一线城市：</div>
      <div class="item-value">
        <div>
          <span>销售价：</span> <span>￥ {{ getGoodsInfo.not_first_tier_price | number_format }}</span>
        </div>
      </div>
    </div>
    <div class="info-item" v-if="hasModifiedItem('not_first_tier_vip_price')">
      <div class="item-label">非一线城市：</div>
      <div class="item-value">
        <div>
          <span>会员价：</span><span>￥ {{ getGoodsInfo.not_first_tier_vip_price | number_format }}</span>
        </div>
      </div>
    </div>
    <div class="info-item" v-if="hasModifiedItem('stored_price')">
      <div class="item-label">储值价：</div>
      <div class="item-value">
        <span v-if="getGoodsInfo.stored_price">¥ {{ getGoodsInfo.stored_price | number_format }}</span>
        <span v-else>-</span>
      </div>
    </div>

    <!--    <div class="block-title" v-if="hasModifiedItem('service_info')">服务设置</div>-->
    <div class="info-item" v-if="showExpirationItem">
      <div class="item-label">商品有效期: ：</div>
      <div class="item-value">
        <span>
          {{
            echoData.service_info.expiration.type == '1'
              ? `购买后${getGoodsInfo.service_info?.expiration.days}天内有效`
              : `截止到${getGoodsInfo.service_info?.expiration.date}日有效`
          }}
        </span>
      </div>
    </div>
    <div class="info-item" v-if="hasModifiedItem('service_info.is_appointment')">
      <div class="item-label">是否可约：</div>
      <div class="item-value">
        <span>{{ getGoodsInfo.service_info?.is_appointment == '1' ? '需要预约' : '免预约' }}</span>
      </div>
    </div>
    <div class="info-item" v-if="hasModifiedItem('service_info.is_can_refund')">
      <div class="item-label">是否可退：</div>
      <div class="item-value">
        <span>{{ getGoodsInfo.service_info?.is_can_refund == '1' ? '可退' : '不可退' }}</span>
      </div>
    </div>
    <div class="info-item" v-if="hasModifiedItem('service_info.working_time')">
      <div class="item-label">使用时间：</div>
      <div class="item-value">
        <span>{{ getGoodsInfo.service_info?.working_time }}</span>
      </div>
    </div>
    <div class="info-item" v-if="hasModifiedItem('service_info.not_working_date')">
      <div class="item-label">不可使用日期：</div>
      <div class="item-value">
        <span>{{ getGoodsInfo.service_info?.not_working_date }}</span>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'VirtualLog',
  props: {
    hasModifiedItem: {
      type: Function,
      required: true
    },
    getGoodsInfo: {
      type: Object,
      required: true
    },
    goods_type: {
      type: String
    }
  },
  data() {
    return {
      serviceCols: [
        { title: '已选服务', key: 'name', align: 'center', minWidth: 50 },
        { title: '价格', key: 'price', align: 'center', minWidth: 50 },
        { title: '可用次数', key: 'times', align: 'center', minWidth: 50 }
      ]
    };
  },
  computed: {
    echoData() {
      return this.getGoodsInfo;
    },
    showPriceTitle() {
      return this.hasModifiedItem('price') || this.hasModifiedItem('stored_price');
    },
    showExpirationItem() {
      return (
        this.hasModifiedItem('service_info.expiration.type') ||
        this.hasModifiedItem('service_info.expiration.date') ||
        this.hasModifiedItem('service_info.expiration.days')
      );
    }
  },
  watch: {
    echoData: {
      handler(val) {
        console.log('🚀 ~ handler ~ val=>', val);
        this.tableSpecs();
      },
      immediate: true
    }
  },

  methods: {
    // 处理表格规格
    tableSpecs() {
      if (this.goods_type == '30') {
        this.serviceCols = this.serviceCols.filter(item => item.key != 'price');
        const newColumns = [
          { title: '一线城市', slot: 'first_tier_price', align: 'center', minWidth: 50 },
          { title: '非一线城市', slot: 'not_first_tier_price', align: 'center', minWidth: 50 }
        ];
        this.serviceCols = newColumns.concat(this.serviceCols);
      }
    }
  }
};
</script>

<style scoped lang="less">
@import '../../../common/style/common.less';
</style>
