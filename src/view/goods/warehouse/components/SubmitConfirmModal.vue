<template>
  <Modal
    :value="value"
    :mask-closable="false"
    class-name="vertical-center-modal"
    :title="title"
    @on-visible-change="changeVisible"
  >
    <div class="content">
      {{ getPlatDesc }}
    </div>
    <div slot="footer">
      <Button @click="cancel">取消</Button>
      <Button :loading="$parent.saveUpBtnLoading" type="primary" @click="confirm">确认提交</Button>
    </div>
  </Modal>
</template>

<script>
export default {
  name: 'SubmitConfirm',
  mixins: [],

  components: {},
  props: {
    value: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      default: '提示'
    },
    objSellEntTypes: {
      type: Object,
      default: () => {},
    },
    sell_ent_type: {
      type: Array,
      default: () => [],
    },
  },

  data() {
    return {
      confirmLoading: false
    };
  },

  computed: {
    getPlatDesc() {
      let base_desc = '模板信息审核通过后，模板商品将同步上架给所有';
      let plat_desc = ''
      this.sell_ent_type &&
      this.sell_ent_type.forEach((item, index) => {
        plat_desc = index === this.sell_ent_type.length - 1
          ? `${plat_desc}【${this.objSellEntTypes[item]?.desc}】`
          : `${plat_desc}【${this.objSellEntTypes[item]?.desc}】和`;

      });
      return `${base_desc}${plat_desc}`;
    },
  },

  watch: {},

  created() {},

  mounted() {},

  destroyed() {},

  methods: {
    cancel() {
      this.$emit('input', false);
    },

    confirm() {
      this.$emit('success')
      this.cancel();
    },

    changeVisible(visible) {
      if (!visible) {
        this.cancel();
      }
    }
  }
};
</script>

<style scoped lang="less">
.content {
  font-size: 14px;
  padding-top: 50px;
}
::v-deep .ivu-modal-body {
  max-height: 500px;
  min-height: 150px;
  overflow-y: auto;
}
::v-deep .ivu-modal-header{
  display: none;
}
::v-deep .ivu-modal-footer {
  border: none;
}
</style>
