<template>
  <Modal
    :value="value"
    :mask-closable="false"
    class-name="vertical-center-modal"
    :title="title"
    @on-visible-change="changeVisible"
  >
    <div class="content">
      <p>检测到本次修改更新了以下信息</p>
      <div class="diff-box">
        <div class="diff-item" v-for="(item, index) in diffData" :key="item + index">{{ index + 1 }}.{{ `修改了${item}` }}</div>
      </div>
      <p class="confirmTip">
        请确认要进行以上信息修改，确认后本次修改会进入到待审核阶段，审核通过后即会同步所有已分发的诊所和养疗馆
      </p>
    </div>
    <div slot="footer">
      <Button @click="cancel">取消</Button>
      <Button :loading="$parent.saveUpBtnLoading" type="primary" @click="confirm">确认提交</Button>
    </div>
  </Modal>
</template>

<script>
export default {
  name: 'UpdateConfirm',
  mixins: [],

  components: {},
  props: {
    value: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      default: '提示'
    },
    diffData: {
      type: Array,
      default: () => [],
    },
  },

  data() {
    return {
      confirmLoading: false
    };
  },

  computed: {},

  watch: {},

  created() {},

  mounted() {},

  destroyed() {},

  methods: {
    cancel() {
      this.$emit('input', false);
    },

    confirm() {
      this.$emit('success')
      this.cancel();
    },

    changeVisible(visible) {
      if (!visible) {
        this.cancel();
      }
    }
  }
};
</script>

<style scoped lang="less">
.content {
  font-size: 14px;
  padding-top: 30px;

  .confirmTip {
    margin-top: 10px;
  }
  .diff-box{
    padding: 12px;
    background: #f5f5f5;
    margin: 12px 0;
    .diff-item{
      line-height: 17px;
      font-size: 13px;
      margin-bottom: 6px;
      color: #ff0000;
    }
  }
}
::v-deep .ivu-modal-body {
  max-height: 500px;
  min-height: 150px;
  overflow-y: auto;
}
::v-deep .ivu-modal-header{
  display: none;
}
::v-deep .ivu-modal-footer {
  border: none;
}
</style>
