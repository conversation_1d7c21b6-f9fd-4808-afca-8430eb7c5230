<template>
  <div>
    <div>
      <KWidget label="兑换次数：" required>
        <InputNumber
          :disabled="isFromUpdateTemplate"
          v-model="formData.exchange_num"
          :max="100"
          :min="0"
          :precision="0"
          show-word-limit
          placeholder="请输入兑换次数"
        />
        <span class="note" style="display: inline-block">兑换次数最多不能超过100次</span>
      </KWidget>
      <KWidget label="服务范围：" text required>
        <span class="space6" v-if="isFromUpdateTemplate">添加服务</span>
        <a v-else class="space6" style="color: #155bd4" @click="addService">添加服务</a>
        <span class="note" style="display: inline-block">设置此商品支持兑换的服务范围</span>
        <table v-if="formData.services?.length > 0" class="table" style="width: 500px">
          <thead>
            <tr>
              <th>兑换服务范围</th>
              <!--              <th>来源</th>-->
              <th>价格</th>
              <th>最多可兑次数</th>
              <th>操作</th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="(item, key) in formData.services" :key="key">
              <td>{{ item.name }}</td>
              <!--              <td>{{ item.source_platform_text }}</td>-->
              <td>{{ item.price | number_format }}</td>
              <td>
                <InputNumber
                  :disabled="isFromUpdateTemplate"
                  v-model="item.times"
                  controls-outside
                  :min="1"
                  :max="50"
                  :precision="0"
                  style="width: 100px"
                />
              </td>
              <td>
                <span v-if="isFromUpdateTemplate">-</span>
                <a v-else @click="onDelService(key)" style="color: #155bd4">删除</a>
              </td>
            </tr>
          </tbody>
        </table>
      </KWidget>
      <KWidget label="备注：">
        <Input v-model="formData.remark" maxlength="20" show-word-limit placeholder="请输入备注" />
      </KWidget>
      <div class="block-header"><span>价格库存</span></div>

      <KWidget label="价格：" required>
        <InputNumber v-model="formData.price" :precision="2" :active-change="false" :min="0" />
        元
      </KWidget>
      <KWidget label="储值价：" v-show="formData.is_recharge_buy == 'yes'">
        <InputNumber
          v-model="formData.stored_price"
          ref="stored"
          placeholder="请输入储值价"
          :precision="2"
          :min="0"
          @on-change="storedPriceChange"
          :active-change="false"
        />
        元
      </KWidget>

      <div class="block-header"><span>服务设置</span></div>
      <KWidget label="商品有效期：" required>
        <RadioGroup v-model="formData.service_info.expiration.type">
          <Radio label="1"><span></span></Radio>
          <div style="display: inline-block; margin-left: -10px">
            购买后
            <InputNumber
              v-model="formData.service_info.expiration.days"
              :min="1"
              placeholder="自定义"
              :disabled="formData.service_info.expiration.type != 1"
            />
            天内有效
          </div>
          <div class="block_10"></div>
          <Radio label="2"><span></span></Radio>
          <div style="display: inline-block; margin-left: -10px">
            截止到
            <DatePicker
              type="date"
              format="yyyy-MM-dd"
              v-model="formData.service_info.expiration.date"
              @on-change="formData.service_info.expiration.date = arguments[0]"
              placeholder="请选择日期"
              :options="expiration_datepicker_options"
              :disabled="formData.service_info.expiration.type != 2"
            />
            日有效
          </div>
        </RadioGroup>
      </KWidget>
      <KWidget label="售卖主体：" required text>
        <CheckboxGroup v-model="formData.sell_ent_type">
          <Checkbox
              disabled
            :label="item.id"
            v-for="item in sellEntTypes.filter(item => item.id == 1)"
            :key="item.id"
            >{{ item.desc }}
          </Checkbox>
        </CheckboxGroup>
      </KWidget>
      <KWidget label="上架范围：" required text>
        <CheckboxGroup v-model="formData.xn_scope_tmp">
          <Checkbox label="1">零售服务</Checkbox>
          <Checkbox label="2">问诊治疗</Checkbox>
        </CheckboxGroup>
      </KWidget>

      <KWidget label="储值购买: " required text>
        <Radio-group v-model="formData.is_recharge_buy">
          <Radio :label="item.id" v-for="(item, index) in storedList" :key="'stored' + index">{{ item.desc }}</Radio>
        </Radio-group>
      </KWidget>
    </div>

    <k-goods-services
      v-model="serviceModalVisible"
      @on-selected="onSelectedService"
      :checked-service="formData.services"
    />
  </div>
</template>

<script>
import S from '@/utils/util';
import cloneDeep from 'lodash/cloneDeep';
import KGoodsServices from '@/components/k-goods-services';
import goodsDiff from '../../mixins/goodsDiff';

export default {
  name: 'ExchangeCoupon',
  mixins: [goodsDiff],
  components: {
    KGoodsServices
  },
  props: {
    // 储值购买枚举
    storedList: {
      type: Array,
      default: () => []
    },
    sellEntTypes: {
      type: Array,
      default: () => []
    },
    echoData: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      formData: {
        service_info: {
          expiration: {
            type: '1', // 1 购买后${days}天内有效；2 ${date}日期前有效；
            date: '', //
            days: 365 //
          }
        },
        exchange_num: null, // 兑换次数
        price: 0, // 价格
        stored_price: null, // 储值价
        services: [], //  服务
        remark: '', // 备注
        sell_ent_type: ['1'], // 售卖主体
        xn_scope_tmp: ['1'], // 上架范围
        is_recharge_buy: 'yes' // 储值购买
      },
      serviceModalVisible: false,
      // 截止日期
      expiration_datepicker_options: {
        disabledDate(date) {
          return S.moment(date).unix() <= S.moment(new Date()).unix();
        }
      },
      clone_sell_ent_type: [], //保存初始化显示的售卖主体
      originFormData: {},
      formDataKeyMap: {
        exchange_num: { sort: 0, name: '兑换次数' },
        remark: { sort: 1, name: '备注' },
        price: { sort: 2, name: '商品价格' },
        stored_price: { sort: 3, name: '商品储值价' },
        expiration: { sort: 4, name: '商品有效期' },
        sell_ent_type: { sort: 5, name: '售卖主体' },
        xn_scope: { sort: 6, name: '上架范围' },
        is_recharge_buy: { sort: 7, name: '储值购买' }
      }
    };
  },
  watch: {
    echoData: {
      handler(val) {
        if (S.isEmptyObject(val) || val.goods_type !== '25') return;
        this.handleEchoData(val);
      },
      immediate: true
    }
  },
  computed: {
    // 是否来源于修改模板页面
    isFromUpdateTemplate() {
      return this.$route.query.type === 'update';
    },
    // 已经选中的售卖主体不可再取消
    isChoosed() {
      return id => {
        return this.clone_sell_ent_type.indexOf(id) > -1;
      };
    }
  },
  mounted() {},
  methods: {
    // 通兑券校验
    validateForm() {
      const data = this.formData;

      if (data.exchange_num <= 0) {
        this.$Message.error('请填写兑换次数');
        return false;
      }

      if (data.services.length <= 0) {
        this.$Message.error('请添加服务权益');
        return false;
      }

      // 是否有空
      let isHasEmpty = false;
      // 是否有单个服务兑换次数大于总兑换次数
      let isSingleMore = false;

      const totalTimes = data.services.reduce((total, item) => {
        if (item.times == null) {
          isHasEmpty = true;
        }
        if (item.times > data.exchange_num) {
          isSingleMore = true;
          this.$Message.error(`【${item.name}】兑换次数不能大于服务权益总次数`);
          // item.times = data.exchange_num;
        }
        return total + item.times;
      }, 0);

      // 单个服务超出,拦截
      if (isSingleMore) {
        return false;
      }

      if (!isHasEmpty && totalTimes < data.exchange_num) {
        this.$Message.error('兑换次数不能大于服务权益总次数');
        return false;
      }

      if (!/^\d([\d\.]+)?/.test(data.price)) {
        this.$Message.error('请输入有效的商品价格');
        return false;
      }
      let price = parseFloat(data.price);
      if (price < 0.01) {
        this.$Message.error('商品价格不允许小于0.01元');
        return false;
      }
      if (price > 100000) {
        this.$Message.error('商品价格不允许大于100000.00元');
        return false;
      }

      // 储值价非必填
      // if ( data.is_recharge_buy == 'yes' && data.stored_price < 0.01) {
      //   this.$Message.error( '储值价不允许小于0.01元' )
      //   return false
      // }

      if (data.sell_ent_type.length == 0) {
        this.$Message.error('请选择售卖主体');
        return false;
      }

      if (data.xn_scope_tmp.length == 0) {
        this.$Message.error('请选择通兑券上架范围');
        return false;
      }

      if (!data.is_recharge_buy) {
        this.$Message.error('请选择储值购买');
        return false;
      }

      return true;
    },

    // 获取通兑券的数据
    getFormData() {
      let formData = {
        ...this.formData
      };
      formData.xn_scope = this.formData.xn_scope_tmp.length == 2 ? '9' : this.formData.xn_scope_tmp[0];
      delete formData.xn_scope_tmp;
      return formData;
    },

    handleEchoData(goods) {
      this.clone_sell_ent_type = cloneDeep(goods.sell_ent_type);
      this.formData.exchange_num = Number(goods.exchange_num);

      for (let key in goods.services ? goods.services : []) {
        goods.services[key].times =
          Number(goods.services[key].times || 0) === 0 ? null : Number(goods.services[key].times || 0);
      }
      this.formData.services = goods.services;

      this.formData.remark = goods.remark;

      this.formData.price = Number(goods.price);
      this.formData.stored_price = goods.stored_price == '' ? null : Number(goods.stored_price);

      this.formData.service_info = !S.isEmptyObject(goods.service_info)
        ? goods.service_info
        : this.formData.service_info;
      this.formData.service_info.expiration.days = Number(this.formData.service_info.expiration.days);
      this.formData.sell_ent_type = goods.sell_ent_type;
      this.formData.xn_scope_tmp = goods.xn_scope == '9' ? ['1', '2'] : [goods.xn_scope];
      this.formData.xn_scope = goods.xn_scope;
      this.formData.is_recharge_buy = goods.is_recharge_buy;
      this.originFormData = cloneDeep(this.formData);
      this.$delete(this.originFormData, 'xn_scope_tmp');
    },

    addService() {
      this.serviceModalVisible = true;
    },

    // 删除服务
    onDelService: function (index) {
      this.formData.services.splice(index, 1);
    },

    // 储值价
    storedPriceChange(val) {
      if (Number(val) == 0) {
        this.formData.stored_price = null;
        this.$refs.stored.currentValue = null;
      }
    },

    // 选中
    onSelectedService: function (items) {
      this.formData.services = items.map(item => {
        // 编辑时携带次数
        let isExist = this.formData.services.findIndex(service_item => service_item.id === item.id);
        return {
          id: item.id,
          name: item.name,
          price: item.price,
          times: isExist === -1 ? 1 : this.formData.services[isExist].times
        };
      });
      this.serviceModalVisible = false;
    },
    getFormDataDiff(obj1, obj2) {
      if (this.$lodash.isEqual(obj1, obj2)) {
        return [];
      } else {
        const res = this.deepDiff(obj1, obj2, ['services', 'expiration']);
        let diffDesc = [];
        let isExpirationDiff = this.getExpirationDiff(obj1.attrs, obj2.attrs);
        if (isExpirationDiff) {
          res.push('expiration');
        }
        if (res.length) {
          diffDesc = res.map(item => this.formDataKeyMap[item]);
        }
        return diffDesc.sort((a, b) => a.sort - b.sort).map(item => item.name) || [];
      }
    },
    getExpirationDiff() {
      const oldExpiration = this.getFormData().service_info.expiration;
      const newExpiration = this.originFormData.service_info.expiration;

      if (oldExpiration.type !== newExpiration.type) {
        return true;
      }

      if (oldExpiration.type === '1') {
        return oldExpiration.days !== newExpiration.days;
      }

      return oldExpiration.date !== newExpiration.date;
    },
  }
};
</script>

<style scoped lang="less"></style>
