<template>
  <div>
    <FormItem label="兑换次数">
      <p>{{ echoData.exchange_num }}</p>
    </FormItem>

    <FormItem label="服务范围">
      <div class="table-box">
        <table v-if="echoData.services.length > 0" class="table">
          <thead>
            <tr>
              <th style="min-width: 100px">已选服务</th>
              <th v-if="isRst" style="min-width: 180px">一线城市</th>
              <th v-if="isRst" style="min-width: 180px">非一线城市</th>
              <th v-if="!isRst" style="min-width: 180px">价格</th>
              <th v-if="isRst && isOpcPriceExist" style="min-width: 180px">价格</th>
              <th style="min-width: 100px">可用次数</th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="(item, key) in echoData.services" :key="key">
              <td>{{ item.name }}</td>
              <td v-if="!isRst">{{ item.price | number_format }}</td>
              <td v-if="isRst">
                <div>销售价：¥ {{ item.first_tier_price | number_format }}</div>
                <div>会员价：¥ {{ item.first_tier_vip_price | number_format }}</div>
              </td>
              <td v-if="isRst">
                <div>销售价：¥ {{ item.not_first_tier_price | number_format }}</div>
                <div>会员价：¥ {{ item.not_first_tier_vip_price | number_format }}</div>
              </td>

              <td v-if="isRst && isOpcPriceExist">
                <div>销售价： {{ formatOpcPrice(item, 'price') }}</div>
                <div>会员价： {{ formatOpcPrice(item, 'vip_price') }}</div>
              </td>
              <td>{{ item.times }}</td>
            </tr>
          </tbody>
        </table>
      </div>
    </FormItem>

    <FormItem label="备注">
      <p>{{ echoData.remark }}</p>
    </FormItem>

    <div class="block-header"><span>价格库存</span></div>

    <FormItem v-if="!isRst" label="价格">
      <p>¥ {{ echoData.price }}</p>
    </FormItem>

    <FormItem v-if="isRst" label="一线城市价格">
      <div class="flex gap-12">
        <div>
          销售价 <span>¥ {{ echoData.first_tier_price | number_format }}</span>
        </div>
        <div>
          会员价 <span>¥ {{ echoData.first_tier_vip_price | number_format }}</span>
        </div>
      </div>
    </FormItem>
    <FormItem v-if="isRst" label="非一线城市价格">
      <div class="flex gap-12">
        <div>
          销售价 <span>¥ {{ echoData.not_first_tier_price | number_format }}</span>
        </div>
        <div>
          会员价 <span>¥ {{ echoData.not_first_tier_vip_price | number_format }}</span>
        </div>
      </div>
    </FormItem>

    <FormItem v-if="isRst" label="直营中心价格">
      <kOpcPriceD :data="echoData?.opc_price_list" />
    </FormItem>

    <template v-if="!isRst">
      <FormItem v-show="echoData.is_recharge_buy == 'yes'" label="储值价">
        <p v-if="echoData.stored_price">¥ {{ echoData.stored_price }}</p>
        <p v-else>-</p>
      </FormItem>
    </template>

    <div class="block-header"><span>服务设置</span></div>
    <FormItem label="商品有效期">
      <p v-if="echoData.service_info.expiration.type == '1'">
        购买后{{ echoData.service_info.expiration.days }}天内有效
      </p>
      <p v-if="echoData.service_info.expiration.type == '2'">截止到{{ echoData.service_info.expiration.date }}日有效</p>
    </FormItem>
  </div>
</template>

<script>
import kOpcPriceD from '@/components/k-goods-opc/k-opc-price-d.vue';
export default {
  name: 'ExchangeCouponDetail',
  components: { kOpcPriceD },
  mixins: [],
  props: {
    isRst: {
      type: Boolean,
      default: false
    },
    echoData: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {};
  },
  computed: {
    // 判断服务中是否存在直营中心价格
    isOpcPriceExist() {
      return (
        this.echoData &&
        Array.isArray(this.echoData.services) &&
        this.echoData.services.some(item => Array.isArray(item.opc_price) && item.opc_price.length > 0)
      );
    }
  },

  mounted() {},
  methods: {
    formatOpcPrice(row, key) {
      if (row.opc_price.length === 0) {
        return '-';
      } else {
        const temp = row.opc_price[0];
        return temp ? '¥ ' + this.$options.filters.number_format(temp[key]) : '-';
      }
    }
  }
};
</script>

<style lang="less" scoped>
.table-box {
  width: 900px;
  overflow: auto;
}
</style>
