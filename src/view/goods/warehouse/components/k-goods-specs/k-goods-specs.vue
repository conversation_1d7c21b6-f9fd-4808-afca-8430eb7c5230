<template>
  <div class="specs-wrapper">
    <KWidget label="商品规格：">
      <div class="specs-container">
        <div class="specs-group">
          <div v-for="(specs, specskey) in specsData" :key="specskey" class="specs-group_item">
            <div class="specs-group_item-name media">
              <div class="media-left"><b>规格名：</b></div>
              <div class="media-body">
                <Input
                  class="input-mini1"
                  :disabled="isFromUpdateTemplate"
                  type="text"
                  v-model="specs.name"
                  :ref="'input-' + specskey"
                />
                <Dvd />
                <Dvd />
                <Dvd />
                <Dvd />
                <label v-if="specskey == 0" style="font-weight: 400">
                  <Checkbox :disabled="isFromUpdateTemplate" v-model="specs.isAddImg">添加规格图片</Checkbox>
                </label>
                <div class="specs-group_item-del btn-delete" v-if="!isFromUpdateTemplate" @click="onDelSpecs(specskey)">
                  ×
                </div>
              </div>
            </div>
            <div class="specs-group_item-value media">
              <div class="media-left">规格值：</div>
              <div class="media-body">
                <div class="atom-list" v-viewer="{ url: 'data-source' }">
                  <div
                    v-for="(atom, atomkey) in specs.specs_atoms"
                    :key="atomkey"
                    class="atom"
                    :class="{ active: specs.isAddImg }"
                  >
                    <Input
                      class="input-mini2"
                      :disabled="isFromUpdateTemplate"
                      v-model="atom[0]"
                      :ref="'input-' + specskey + '-' + atomkey"
                    />
                    <div v-if="specs.isAddImg" class="img-comp">
                      <div class="arrow"></div>
                      <div v-if="atom[1]" class="img-container">
                        <img :src="atom[1] | imageStyle('B.w300')" :data-source="atom[1]" />
                        <div class="img-del btn-delete" @click="onDelSpecsImg(atomkey)" v-if="!isFromUpdateTemplate">
                          ×
                        </div>
                      </div>
                      <div v-else class="img-container">
                        <Upload @on-success="onUploadSuccess(atomkey, arguments[0])">
                          <div class="img-upload">+</div>
                          <span class="img-note">添加规格图片</span>
                        </Upload>
                      </div>
                    </div>
                  </div>
                </div>
                <a class="atom-add" :disabled="isFromUpdateTemplate" @click="onAddAtom(specskey)">添加规格值</a>
                <div v-if="specskey == 0 && specs.isAddImg" class="note">
                  仅支持为第一组规格设置规格图片，买家选择不同规格会看到对应规格图片，建议尺寸：800 x 800像素
                </div>
              </div>
            </div>
          </div>
        </div>
        <div v-if="add_specs_btn_show" class="specs-op">
          <Button :disabled="isFromUpdateTemplate" @click="onAddSpecs">添加规格项目</Button>
        </div>
      </div>
    </KWidget>
    <KWidget label="规格明细：" :key="refreshStockTableKey" v-if="stockData.length > 0">
      <table class="table goods-specs-table ks-specs-table" style="border: 1px solid #ddd">
        <thead>
          <tr>
            <template v-for="(specs, specskey) in specsData">
              <th width="5%" :key="specskey">
                {{ specs.name }}
              </th>
            </template>
            <th width="8%' : '16%'"><span class="text-danger">*</span> 价格(元)</th>
            <th width="8%" v-show="give == 'yes'">
              <Tooltip
                maxWidth="200px"
                placement="top"
                content="商品储值价指使用储值余额购买时可享受的优惠价，不设置即无优惠价"
              >
                <svg-icon iconClass="help" class="helpIcon"></svg-icon>
              </Tooltip>
              储值价
            </th>
            <th width="8%">编码</th>
            <th width="14%">货品</th>
            <th width="8%">售卖单位</th>
            <!--          <th width="10%">剩余库存</th>-->
            <th width="10%">单位售价(元)</th>
            <th width="8%">起购倍数</th>
            <th width="8%"></th>
          </tr>
        </thead>
        <tbody class="ks-specs-table-body">
          <tr v-for="(stock, stockkey) in stockData" :key="stockkey">
            <td v-for="(item, index) in specsData" :key="index">
              {{ stock.specs_atoms[item.name] }}
            </td>
            <td>
              <InputNumber
                class="input-mini"
                :precision="2"
                :active-change="false"
                :min="0"
                v-model="stock.price"
                placeholder="请填写价格"
              ></InputNumber>
              <p class="recommendTip" v-if="stock.relate_his_prods.length">推荐价格：￥{{ recommendPrice(stock) }}</p>
            </td>

            <!-- 储值价 -->
            <td v-show="give == 'yes'">
              <InputNumber
                :ref="`sStored${stockkey}`"
                class="input-mini"
                :active-change="false"
                :precision="2"
                :min="0"
                @on-change="storedPriceChange($event, stockkey)"
                v-model="stock.stored_price"
                placeholder="请填写储值价"
              ></InputNumber>
            </td>

            <!-- 编码 -->
            <td>
              <Input class="input-mini2" :disabled="isFromUpdateTemplate" placeholder="选填" v-model="stock.sku" />
            </td>

            <!-- 诊所货品 -->
            <td>
              <template v-if="stock.relate_his_prods.length">
                <p
                  class="single-item"
                  v-for="(checked_item, currentGoodsIndex) in stock.relate_his_prods"
                  :key="'goodsName' + currentGoodsIndex"
                >
                  <span class="flex flex-item-align">
                    <span>{{ checked_item.generic_name }}</span>
                    <span
                      v-if="!isFromUpdateTemplate"
                      class="cursor margin-left10 flex flex-item-align"
                      @click="removeCheckedGoods(stockkey, currentGoodsIndex)"
                      style="width: 40px"
                      >移除</span
                    >
                  </span>
                </p>
              </template>
            </td>

            <!-- 售卖单位 -->
            <td>
              <template v-if="stock.relate_his_prods.length">
                <div
                  class="single-item"
                  v-for="(checked_item, index) in stock.relate_his_prods"
                  :key="'select' + index"
                >
                  <Select
                    :disabled="isFromUpdateTemplate"
                    v-model="checked_item._sales_unit"
                    @on-select="saleChange($event, stockkey, index, checked_item.sales_units)"
                  >
                    <Option
                      v-for="(select_item, select_index) in checked_item.sales_units"
                      :value="select_item.unit"
                      :key="select_index + 'select'"
                      >{{ select_item.unit }}
                    </Option>
                  </Select>
                </div>
              </template>
            </td>

            <!-- 剩余库存 -->
            <!--          <td>-->
            <!--            <template v-if="stock.relate_his_prods.length">-->
            <!--              <p class="single-item" v-for="( checked_item, index) in stock.relate_his_prods" :key="'price'+index">-->
            <!--                <Input class="input-mini" v-model="checked_item._stock_num" disabled/>-->
            <!--              </p>-->
            <!--            </template>-->
            <!--          </td>-->

            <!-- 单位售价 -->
            <td>
              <template v-if="stock.relate_his_prods.length">
                <p class="single-item" v-for="(checked_item, index) in stock.relate_his_prods" :key="'price' + index">
                  <Input class="input-mini" v-model="checked_item._price" disabled />
                </p>
              </template>
            </td>

            <!-- 起购倍数 -->
            <td>
              <template v-if="stock.relate_his_prods.length">
                <p
                  class="single-item"
                  v-for="(checked_item, index) in stock.relate_his_prods"
                  :key="'purchaseMultiple' + index"
                >
                  <InputNumber
                    :disabled="isFromUpdateTemplate"
                    class="input-mini"
                    :min="1"
                    v-model="checked_item.buy_multiples"
                    placeholder="请输入起购倍数"
                    :precision="0"
                    @on-change="multiplesChange($event, stockkey, index)"
                  ></InputNumber>
                </p>
              </template>
            </td>
            <td :class="{ height100: stock.relate_his_prods.length == 1 }">
              <span v-if="isFromUpdateTemplate">-</span>
              <span v-else style="color: #155bd4" class="cursor" @click="connectStock(stockkey)">关联货品</span>
            </td>
          </tr>
        </tbody>
        <tfoot>
          <tr>
            <td colspan="12">
              <label class="pull-left" style="line-height: 30px">批量设置：</label>
              <div v-show="batch_set_btn" class="pull-left" style="line-height: 30px">
                <a class="ks-batch-attr space6" @click="onBatchBtn('price')">价格</a>
                <a class="ks-batch-attr" @click="onBatchBtn('stored_price')" v-if="give == 'yes'">储值价</a>
                <!-- <a class="ks-batch-attr ml6" @click="onBatchBtn('stock')">库存</a> -->
              </div>
              <div v-show="!batch_set_btn" class="form-group pull-left">
                <InputNumber
                  element-id="batch-set-input"
                  class="input-mini"
                  :min="0"
                  v-model="batch_set_value"
                  :placeholder="batchPlaceholder"
                ></InputNumber>
                <!-- <Input ref="batch-set-input" type="number" v-model="batch_set_value" class="input-mini" /> -->
                <Dvd />
                <Button @click="onBatchSave">保存</Button>
                <Dvd />
                <Button @click="onBatchCancel">取消</Button>
              </div>
            </td>
          </tr>
        </tfoot>
      </table>
    </KWidget>

    <KGoodsChoose
      :visible.sync="goodDiaVisible"
      @selectGoods="selectGoods"
      :checkedGoods="modalCheckedGoods"
    ></KGoodsChoose>
  </div>
</template>

<script>
/* eslint-disable */
import S from 'utils/util'; // Some commonly used tools
/* eslint-disable */
import './index.less';
import Upload from '@/components/upload/qiniu-upload';
import Viewer from 'v-viewer';
import Vue from 'vue';
import KGoodsChoose from '@/components/k-goods-choose';
import { $operator } from 'utils/operation';

Vue.use(Viewer);

export default {
  name: 'k-goods-specs',
  components: {
    Upload,
    KGoodsChoose
  },
  props: {
    value: {
      type: Array,
      default() {
        return [];
      }
    },
    is_recharge_buy: {
      type: String,
      default: 'yes'
    },
    sell_ent_type: {
      type: String,
      default: 'CLI'
    },
    sw_scope_tmp: {
      type: Array,
      default: () => []
    },
    storedList: {
      type: Array,
      default: () => []
    }
  },

  data() {
    return {
      specsData: [],
      stockData: [],

      add_specs_btn_show: true,
      refreshStockTableKey: 0,
      batch_set_btn: true,
      batch_set_key: '',
      batch_set_value: null,

      goodDiaVisible: false, // 关联货品弹窗
      checkedIndex: null,
      modalCheckedGoods: [], // 当前组关联的诊所货品,用于弹窗回显

      give: 'yes', // 储值购买,
      scope_tmp: [], // 上架范围
      sellType: 'CLI',
      batchPlaceholder: '请输入批量价格'
    };
  },

  computed: {
    // 是否来源于修改模板页面
    isFromUpdateTemplate() {
      return this.$route.query.type === 'update';
    },
    recommendPrice() {
      return list => {
        let price = 0;
        if (list.relate_his_prods.length) {
          list.relate_his_prods.forEach(item => {
            let totol_price = $operator.multiply(Number(item.buy_multiples), Number(item._price));
            price = $operator.add(price, totol_price);
          });
        }
        return price;
      };
    }
  },

  created() {
    this.tableMergeCell();
    // 编辑回显
    if (this.$route.query.id) {
      this.stockData.forEach(item => {
        item.relate_his_prods.forEach(prods => {
          prods._sales_unit = this.setDefaultUnit(prods).unit || '';
          // prods._stock_num = this.setDefaultUnit(prods).stock_num || ''
          prods._price = this.setDefaultUnit(prods).price || '';
        });
      });
    }
  },

  methods: {
    // 如果储值价设置为小于0，置为空
    storedPriceChange(val, index) {
      if (Number(val) == 0) {
        this.stockData[index].stored_price = null;
        let customR = `sStored${index}`;
        this.$refs[customR][0].currentValue = null;
      }
    },
    onBatchBtn: function (key) {
      if (key === 'price') {
        this.batchPlaceholder = '请批量设置价格';
      }
      if (key === 'stock') {
        this.batchPlaceholder = '请批量设置可售库存';
      }
      if (key === 'stored_price') {
        this.batchPlaceholder = '请批量设置储值价';
      }
      this.batch_set_btn = false;
      this.batch_set_key = key;
      this.$nextTick(() => {
        document.getElementById('batch-set-input').focus();
      });
    },
    onBatchSave: function () {
      for (let key in this.stockData) {
        // 价格批量
        if (this.batch_set_key === 'price') {
          this.stockData[key][this.batch_set_key] = Number(this.batch_set_value);
        } else if (this.batch_set_key === 'stock') {
          if (!this.stockData[key].relate_his_prods.length) {
            this.stockData[key][this.batch_set_key] = Number(this.batch_set_value);
          }
        } else {
          this.stockData[key][this.batch_set_key] = Number(this.batch_set_value);
        }
      }
      this.onBatchCancel();
    },
    onBatchCancel: function () {
      this.batch_set_btn = true;
      this.batch_set_key = '';
      this.batch_set_value = null;
    },

    // 添加规格值
    onAddAtom: function (index) {
      this.specsData[index].specs_atoms.push(['']);
      this.tableMergeCell();

      let n = 'input-' + index + '-' + (this.specsData[index].specs_atoms.length - 1);
      this.$nextTick(() => this.$refs[n][0].focus());
    },

    // 删除规格值
    onDelAtom: function (index, atom_index) {
      this.specsData[index].specs_atoms.splice(atom_index, 1);
      this.tableMergeCell();
    },

    // 添加规格
    onAddSpecs: function () {
      this.specsData.push({
        name: '',
        isAddImg: false,
        specs_atoms: [['']]
      });

      if (this.specsData.length >= 3) {
        this.add_specs_btn_show = false;
      } else {
        this.add_specs_btn_show = true;
      }

      this.tableMergeCell();

      let n = 'input-' + (this.specsData.length - 1);
      this.$nextTick(() => this.$refs[n][0].focus());
    },

    // 删除规格
    onDelSpecs: function (index) {
      this.specsData.splice(index, 1);

      if (this.specsData.length >= 3) {
        this.add_specs_btn_show = false;
      } else {
        this.add_specs_btn_show = true;
      }

      this.tableMergeCell();
    },

    // 删除规格图片
    onDelSpecsImg: function (atom_index) {
      this.$set(this.specsData[0].specs_atoms[atom_index], 1, '');
    },

    onUploadSuccess: function (atom_index, img) {
      this.$set(this.specsData[0].specs_atoms[atom_index], 1, img);
    },

    //渲染商品库存模块
    render_goods_stock: function (goods_specs_data, goods_stock_data) {
      if (S.isUndefined(goods_specs_data)) {
        return true;
      }

      let wait_combination_arrs = [];

      goods_specs_data.forEach((item, index) => {
        let atoms = [];
        item.specs_atoms.forEach(atom_item => {
          atoms.push(atom_item[0]);
        });
        if (!S.isEmptyObject(atoms)) {
          wait_combination_arrs.push(atoms);
        }
      });

      let specs_combination = [];
      let tmp_specs_combination = this.bigcombination(wait_combination_arrs);
      let default_goods_attr_fields = {
        id: 0,
        price: null,
        stored_price: null,
        stock: 0,
        sku: '',
        cost_price: 0,

        // 每一款规格下关联的诊所货品
        relate_his_prods: []
      };

      let new_goods_stock_data = [];

      for (let i = 0; i < tmp_specs_combination.length; i++) {
        let tmp_comb = tmp_specs_combination[i];
        goods_specs_data.forEach((specs_item, k) => {
          if (S.isUndefined(specs_combination[i])) {
            specs_combination[i] = {};
          }
          specs_combination[i][specs_item.name] = tmp_comb[k];
        });

        let specs_atoms = specs_combination[i];

        new_goods_stock_data[i] = default_goods_attr_fields;
        new_goods_stock_data[i] = S.merge(new_goods_stock_data[i], { specs_atoms: specs_atoms });

        let match_goods_stock_item = [];
        goods_stock_data.forEach(goods_stock_item => {
          let is_match = true;
          for (let specs_name in goods_stock_item.specs_atoms) {
            if (goods_stock_item.specs_atoms[specs_name] != specs_atoms[specs_name]) {
              is_match = false;
            }
          }
          for (let specs_name in specs_atoms) {
            if (goods_stock_item.specs_atoms[specs_name] != specs_atoms[specs_name]) {
              is_match = false;
            }
          }
          if (is_match) {
            match_goods_stock_item = goods_stock_item;
          }
        });

        if (!S.isEmptyObject(match_goods_stock_item)) {
          new_goods_stock_data[i] = S.merge(new_goods_stock_data[i], match_goods_stock_item);
        }
      }

      this.stockData = new_goods_stock_data;
    },

    tableMergeCell: function () {
      this.$nextTick(() => {
        this.refreshStockTableKey += 1;
        // 合并单元格
        setTimeout(() => {
          let tb = document.getElementsByClassName('ks-specs-table-body')[0];
          for (let col = this.specsData.length - 2; col >= 0; col--) {
            this.mergeCell(tb, 0, this.stockData.length - 1, col);
          }
        }, 20);
      });
    },

    // 合并单元格
    // start_row 起始行 (从0开始)
    // end_row 结束行 (从0开始)
    // col 合并的列号，对第几列进行合并 (从0开始)
    mergeCell: function (tb, start_row, end_row, col) {
      for (let i = start_row; i < end_row; i++) {
        if (tb.rows[start_row].cells[col].innerHTML == tb.rows[i + 1].cells[col].innerHTML) {
          tb.rows[i + 1].removeChild(tb.rows[i + 1].cells[col]);
          tb.rows[start_row].cells[col].rowSpan = tb.rows[start_row].cells[col].rowSpan + 1;
        } else {
          this.mergeCell(tb, i + 1, end_row, col);
          break;
        }
      }
    },

    //多个一维数组所有可能的组合
    bigcombination: function (arrs) {
      if (arrs.length < 1) {
        return arrs;
      }

      let _comb = function (a, b) {
        let arr = Array();
        for (let i = 0; i < a.length; i++) {
          if (S.isUndefined(b)) {
            arr.push(a[i]);
          } else {
            for (let j = 0; j < b.length; j++) {
              arr.push(a[i] + '#@#' + b[j]);
            }
          }
        }
        return arr;
      };

      let combarrs = _comb(arrs[0], arrs[1]);
      let index = 2;
      while (true) {
        if (arrs[index]) {
          combarrs = _comb(combarrs, arrs[index]);
          index++;
        } else {
          break;
        }
      }

      for (let i = 0; i < combarrs.length; i++) {
        combarrs[i] = combarrs[i].split('#@#');
      }

      return combarrs;
    },

    // 关联的诊所货品数据
    selectGoods(selectGoodsList) {
      const { checkedIndex } = this;
      let relate_his_prods = [];
      selectGoodsList.forEach(item => {
        relate_his_prods.push({
          _sales_unit: this.setDefaultUnit(item).unit || '',
          // _stock_num: this.setDefaultUnit(item).stock_num || '',
          _price: this.setDefaultUnit(item).price || '',
          buy_multiples: 1,
          prod_id: item.id,
          sales_units: item.sales_units,
          generic_name: item.generic_name
        });
      });
      console.log('selectGoodsList', selectGoodsList);
      this.stockData[checkedIndex].relate_his_prods = [
        ...this.stockData[checkedIndex].relate_his_prods,
        ...relate_his_prods
      ];

      // this.stockData[checkedIndex].checkedGoodList = [...this.stockData[checkedIndex].checkedGoodList, ...selectGoodsList]

      this.calcSaleStock(checkedIndex);
    },

    // 计算可售库存
    calcSaleStock(index) {
      if (!this.stockData.length) return;
      let saleStockNum = 0;

      let multiplesList = [];
      let _wait_stock_list = this.stockData[index].relate_his_prods;

      _wait_stock_list.forEach((stock_item, stock_index) => {
        multiplesList.push(Math.floor(stock_item._stock_num / stock_item.buy_multiples));
      });

      saleStockNum = multiplesList.sort((a, b) => a - b)[0] || 0;

      this.stockData[index].stock = saleStockNum;
    },

    // 关联诊所货品点击
    connectStock(index) {
      this.goodDiaVisible = true;
      this.checkedIndex = index;
      this.modalCheckedGoods = this.stockData[index].relate_his_prods;
    },

    // 如果货单单位有选中的，默认带出单位，库存，单位售价
    setDefaultUnit(item) {
      // if ( !item.length ) return {}
      let resultList = item.sales_units.filter(item => item.checked == '1');
      return resultList[0] || {};
    },

    // 移除单个商品
    removeCheckedGoods(stockIndex, currentGoodsIndex) {
      this.stockData[stockIndex].relate_his_prods.splice(currentGoodsIndex, 1);
      this.calcSaleStock(stockIndex);
    },

    // 单位切换
    saleChange(val, stockKey, index, sales_units) {
      let unit_index = '';
      sales_units.forEach((item, index) => {
        if (val.value === item.unit) {
          unit_index = index;
        }
      });
      let _item = this.stockData[stockKey].relate_his_prods[index];
      _item._stock_num = sales_units[unit_index].stock_num;
      _item._price = sales_units[unit_index].price;
      _item._sales_unit = val.value;

      _item.sales_units.sort();
      this.calcSaleStock(stockKey);
    },

    // 起购倍数变化
    multiplesChange(e, stockKey, index) {
      if (e === null || e === 0) {
        let _header = this.stockData[stockKey].relate_his_prods;
        // _header[index].buy_multiples = 1
      } else {
        this.calcSaleStock(stockKey);
      }
    }
  },

  watch: {
    is_recharge_buy: {
      immediate: true,
      handler(val) {
        this.give = val;
      }
    },

    sw_scope_tmp: {
      immediate: true,
      handler(val) {
        this.scope_tmp = val;
      }
    },

    sell_ent_type: {
      immediate: true,
      handler(val) {
        this.sellType = val;
      }
    },

    specsData: {
      deep: true,
      handler: function (val) {
        this.render_goods_stock(val, this.stockData);
        let value = [...this.value];
        value[0] = val;
        this.$emit('input', value);
      }
    },

    stockData: {
      deep: true,
      handler: function (val) {
        let value = [...this.value];
        value[1] = val;
        this.$emit('input', value);
      }
    },

    value: {
      immediate: true,
      handler: function (val) {
        if (!S.isUndefined(val[0])) {
          this.specsData = val[0];
          if (this.specsData.length >= 3) {
            this.add_specs_btn_show = false;
          }
        }
        if (!S.isUndefined(val[1])) {
          // 价格，可售库存，起购倍数均转化为number类型
          val[1].map(item => {
            item.price = Number(item.price) == 0 ? null : Number(item.price);
            item.stored_price = Number(item.stored_price) == 0 ? null : Number(item.stored_price);
            // if ( !item.relate_his_prods.length ) {
            //   item.stock = Number(item.stock) == 0 ? null : Number(item.stock)
            // }else{
            //   item.stock = Number(item.stock)
            // }
            item.relate_his_prods.forEach(relate_item => {
              relate_item.buy_multiples = Number(relate_item.buy_multiples);
            });
          });
          this.stockData = val[1];
        }

        // isAddImg 转换成Bool类型
        for (let key in this.specsData) {
          if (!this.specsData[key]['isAddImg'] || this.specsData[key]['isAddImg'] == '0') {
            this.specsData[key]['isAddImg'] = false;
          } else {
            this.specsData[key]['isAddImg'] = true;
          }
        }
      }
    }
  }
};
</script>

<style lang="less">
.cursor {
  cursor: pointer;
  color: #155bd4;
}

.margin-left10 {
  margin-left: 10px;
}

.single-item {
  // height: 30px;
  margin-bottom: 20px;
  line-height: 30px;

  &:nth-last-child(1) {
    margin-bottom: 0;
  }
}

.recommendTip {
  color: #999;
  position: absolute;
  margin-top: 10px;
}

.height100 {
  height: 100px !important;
}

.helpIcon {
  padding-top: 2px;
  cursor: pointer;
  width: 16px !important;
  height: 16px !important;
}

.ml6 {
  margin-left: 6px;
}

.sw-wrapper {
  .ivu-checkbox {
    width: 18px;
  }
}
</style>
