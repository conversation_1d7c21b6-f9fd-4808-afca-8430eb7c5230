<template>
  <Modal
    :value="value"
    width="950"
    footer-hide
    lock-scroll
    title="修改日志记录"
    :mask-closable="false"
    @on-visible-change="visibleChange"
  >
    <div class="content" v-if="value">
      <div class="title">变更前</div>
      <Table
        :columns="beforeColumns"
        :data="beforeData"
        :span-method="handleSpan"
        :loading="tableLoading"
        disabled-hover
      >
        <template v-for="(item, index) in beforeSpecsData" :slot="item.name + index" slot-scope="{ row }">
          {{ row.specs_atoms[item.name] }}
        </template>
        <template slot="stored_price" slot-scope="{ row }">
          {{ row.stored_price || '-' }}
        </template>
        <template slot="sku" slot-scope="{ row }">
          {{ row.sku || '-' }}
        </template>

        <template slot-scope="{ row }" slot="buy_multiples">
          <div v-if="row.relate_his_prods.length === 0">-</div>
          <div v-for="item in row.relate_his_prods" :key="item.prod_id">起购倍数 {{ item.buy_multiples }}倍</div>
        </template>

        <template slot-scope="{ row }" slot="relate_prods">
          <div v-if="row.relate_his_prods.length === 0">-</div>
          <div v-for="item in row.relate_his_prods" :key="item.prod_id" class="flex">
            <div class="flex-1">{{ item.generic_name }}</div>
            <div class="flex-1">售卖单位: {{ item._sales_unit }}</div>
            <div class="flex-1">单位售价: {{ item._price }}</div>
          </div>
        </template>

        <!--                诊所货品-->
        <template slot-scope="{ row }" slot="generic_name">
          <div v-for="item in row.relate_his_prods" :key="item.prod_id">{{ item.generic_name }}</div>
        </template>
        <!--                售卖单位-->
        <template slot-scope="{ row }" slot="sales_unit">
          <div v-for="item in row.relate_his_prods" :key="item.prod_id">{{ item._sales_unit }}</div>
        </template>
        <template slot-scope="{ row }" slot="price">
          <div v-for="item in row.relate_his_prods" :key="item.prod_id">{{ item._price }}</div>
        </template>
      </Table>

      <Divider></Divider>

      <div class="title mt20">变更后</div>
      <Table :columns="afterColumns" :data="afterData" :span-method="handleSpan" disabled-hover>
        <template
          v-for="(item, spec_index) in afterSpecsData"
          :slot="item.name + spec_index"
          slot-scope="{ row, index }"
        >
          <div :key="item.name + spec_index" :class="{ diff: diffData[index].specs_atoms[item.name] === '1' }">
            {{ row.specs_atoms[item.name] }}
          </div>
        </template>

        <template slot="price" slot-scope="{ row, index }">
          <div :class="{ diff: diffData[index].price === '1' && row.price }">{{ row.price || '-' }}</div>
        </template>

        <template slot="stored_price" slot-scope="{ row, index }">
          <div :class="{ diff: diffData[index].stored_price === '1' && row.stored_price }">
            {{ row.stored_price || '-' }}
          </div>
        </template>

        <template slot="sku" slot-scope="{ row, index }">
          <div :class="{ diff: diffData[index].sku === '1' && row.sku }">{{ row.sku || '-' }}</div>
        </template>

        <template slot-scope="{ row, index }" slot="buy_multiples">
          <div v-if="row.relate_his_prods.length === 0">-</div>
          <div
            v-for="(item, prod_index) in row.relate_his_prods"
            :key="item.prod_id"
            :class="{ diff: diffData[index].relate_his_prods[prod_index].buy_multiples === '1' }"
          >
            起购倍数 {{ item.buy_multiples }}倍
          </div>
        </template>

        <template slot-scope="{ row, index }" slot="relate_prods">
          <div v-if="row.relate_his_prods.length === 0">-</div>
          <div v-for="(item, prod_index) in row.relate_his_prods" :key="item.prod_id" class="flex">
            <div class="flex-1" :class="{ diff: diffData[index].relate_his_prods[prod_index].generic_name === '1' }">
              {{ item.generic_name }}
            </div>
            <div class="flex-1" :class="{ diff: diffData[index].relate_his_prods[prod_index].sales_unit === '1' }">
              售卖单位: {{ item._sales_unit }}
            </div>
            <div class="flex-1" :class="{ diff: diffData[index].relate_his_prods[prod_index].sales_units === '1' }">
              单位售价: {{ item._price }}
            </div>
          </div>
        </template>
      </Table>
    </div>
  </Modal>
</template>

<script>
import renderHeader from '@/mixins/renderHeader';
export default {
  name: 'logChange',
  components: {},
  mixins: [renderHeader],
  props: {
    value: {
      type: Boolean,
      default: () => false
    },
    diffId: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      express_name: '',
      express_no: '',
      beforeColumns: [
        { title: '价格(元)', key: 'price', align: 'center', minWidth: 50 },
        { title: '储值价', slot: 'stored_price', align: 'center', minWidth: 80 },
        { title: '编码', slot: 'sku', align: 'center', minWidth: 50 },
        { title: '购买要求', slot: 'buy_multiples', align: 'center', minWidth: 50 },
        { title: '关联货品', slot: 'relate_prods', align: 'center', minWidth: 200, className: 'table-col-right-border' }
      ],
      afterColumns: [
        { title: '价格(元)', slot: 'price', align: 'center', minWidth: 50 },
        { title: '储值价', slot: 'stored_price', align: 'center', minWidth: 80 },
        { title: '编码', slot: 'sku', align: 'center', minWidth: 50 },
        { title: '购买要求', slot: 'buy_multiples', align: 'center', minWidth: 50 },
        { title: '关联货品', slot: 'relate_prods', align: 'center', minWidth: 200, className: 'table-col-right-border' }
      ],
      beforeData: [],
      afterData: [],
      beforeSpecsData: [],
      afterSpecsData: [],
      diffData: [],
      list: [],
      tableLoading: false
    };
  },
  computed: {},
  watch: {
    value: {
      immediate: true,
      handler(val) {
        if (val) {
          this.getLogSnapshot();
        }
      }
    }
  },
  created() {},
  mounted() {},
  methods: {
    /* 弹窗 */
    closeable() {
      this.$emit('input', false);
      this.beforeColumns = [
        { title: '价格(元)', key: 'price', align: 'center', minWidth: 50 },
        { title: '储值价', slot: 'stored_price', align: 'center', minWidth: 80 },
        { title: '编码', slot: 'sku', align: 'center', minWidth: 50 },
        { title: '购买要求', slot: 'buy_multiples', align: 'center', minWidth: 50 },
        { title: '关联货品', slot: 'relate_prods', align: 'center', minWidth: 200, className: 'table-col-right-border' }
      ];
      this.afterColumns = [
        { title: '价格(元)', slot: 'price', align: 'center', minWidth: 50 },
        { title: '储值价', slot: 'stored_price', align: 'center', minWidth: 80 },
        { title: '编码', slot: 'sku', align: 'center', minWidth: 50 },
        { title: '购买要求', slot: 'buy_multiples', align: 'center', minWidth: 50 },
        { title: '关联货品', slot: 'relate_prods', align: 'center', minWidth: 200, className: 'table-col-right-border' }
      ];
    },
    visibleChange(val) {
      if (val) {
        // this.getList()
      } else {
        this.closeable();
      }
    },

    /* api */
    getList() {
      this.tableLoading = true;
      let params = {
        order_code: this.logisticsData.order_code,
        pack_index: this.logisticsData.pack_index
      };
      this.$api
        .getPackinfo(params)
        .then(res => {
          this.list = res.info.list;
          this.express_name = res.info.express_name;
          this.express_no = res.info.express_no;
        })
        .catch(err => {
          this.$Message.error(err.errmsg);
        })
        .finally(() => {
          this.tableLoading = false;
        });
    },

    handleSpan({ row, column, rowIndex, columnIndex }) {
      if (columnIndex === 0) {
        let x = row.mergeFirstCol === 0 ? 0 : row.mergeFirstCol;
        let y = row.mergeFirstCol === 0 ? 0 : 1;
        return [x, y];
      }
      if (columnIndex === 1) {
        //计算合并的行数列数
        let x = row.mergeSecCol === 0 ? 0 : row.mergeSecCol;
        let y = row.mergeSecCol === 0 ? 0 : 1;
        return [x, y];
      }
    },

    // 合并单元格
    integratedData(data, specsData) {
      // 处理第一列规格
      for (let index = 0; index < data.length; index++) {
        if (data[index].firstAlready !== 1) {
          if (index + 1) {
            data[index].mergeFirstCol = 1;
            for (let idx = index + 1; idx < data.length; idx++) {
              if (data[index].specs_atoms[specsData[0].name] === data[idx].specs_atoms[specsData[0].name]) {
                data[index].mergeFirstCol++;
                data[idx].mergeFirstCol = 0;
                data[idx].firstAlready = 1;
              } else {
                break;
              }
            }
          }
        }
      }

      // 处理第二列规格
      for (let index = 0; index < data.length; index++) {
        if (data[index].mergeFirstCol > 1) {
          for (let idx = 0; idx < data[index].mergeFirstCol; idx++) {
            if (data[index + idx].secondAlready !== 1) {
              if (idx + 1 < data[index].mergeFirstCol) {
                data[index + idx].mergeSecCol = 1;
                for (let i = idx + 1; i < data[index].mergeFirstCol; i++) {
                  if (
                    data[index + idx].specs_atoms[specsData[1].name] === data[index + i].specs_atoms[specsData[1].name]
                  ) {
                    data[index + idx].mergeSecCol++;
                    data[index + i].mergeSecCol = 0;
                    data[index + i].secondAlready = 1;
                  } else {
                    break;
                  }
                }
              }
            }
          }
        }
      }
      console.log('=>(detail.vue:450) data', data);

      this.tableData = data;
    },

    getLogSnapshot() {
      let params = { id: this.diffId };
      this.$api.getLogSnapshot(params).then(res => {
        console.log('=>(log-change.vue:217) res', res);
        this.beforeData = res.before.attrs;
        this.afterData = res.after.attrs;
        this.beforeSpecsData = res.before.specs_data;
        this.afterSpecsData = res.after.specs_data;
        this.diffData = res.diff;
        this.beforeColumns = this.handleSpec(this.beforeSpecsData).concat(this.beforeColumns);
        this.afterColumns = this.handleSpec(this.afterSpecsData).concat(this.afterColumns);
        // 处理关联货品数据
        if (this.beforeData) {
          this.handleRelateProd(this.beforeData);
        }
        this.handleRelateProd(this.afterData);
        this.integratedData(this.beforeData, this.beforeSpecsData);
        this.integratedData(this.afterData, this.afterSpecsData);
      });
    },

    handleSpec(arr) {
      let newArr = this.$lodash.cloneDeep(arr);
      let specArr = newArr.map((item, index) => {
        if (index === 0) {
          item.className = 'table-col-left-border';
        }
        if (index < arr.length - 1) {
          if (index === 0) {
            item.className = 'table-col-border';
          } else {
            item.className = 'table-col-right-border';
          }
        }
        return { title: item.name, slot: item.name + index, minWidth: 40, align: 'center', className: item.className };
      });
      console.log(specArr);
      return specArr;
    },

    // 关联货品
    handleRelateProd(arr) {
      arr.forEach(item => {
        item.relate_his_prods.forEach(prods => {
          prods._sales_unit = this.setDefaultUnit(prods).unit || '';
          // prods._stock_num = this.setDefaultUnit(prods).stock_num || ''
          prods._price = this.setDefaultUnit(prods).price || '';
        });
      });
    },

    // 如果货单单位有选中的，默认带出单位，库存，单位售价
    setDefaultUnit(item) {
      // if ( !item.length ) return {}
      let resultList = item.sales_units.filter(item => item.checked == '1');
      return resultList[0] || {};
    }
  },
  filters: {}
};
</script>
<style lang="less" scoped>
p {
  margin: 0;
}
.auto-p {
  p {
    width: auto;
  }
}
.tip-text {
  color: #ccc;
  font-size: 14px;
}
.ml10 {
  margin-left: 10px;
}
.mt20 {
  margin-top: 20px;
}

::v-deep .ivu-modal-body {
  padding: 20px 30px;
  height: calc(~'100% - 110px');
  overflow-y: auto;
}
::v-deep .ivu-modal {
  height: calc(~'100% - 100px') !important;
}
::v-deep .ivu-modal-content {
  height: calc(~'100% - 100px');
}

.title {
  font-size: 20px;
  font-weight: bold;
}

.diff {
  color: red;
}
</style>
