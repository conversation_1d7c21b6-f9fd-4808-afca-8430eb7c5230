<template>
  <div class="mt-20">
    <div class="block-header"><span>价格库存</span></div>
    <k-goods-specs v-model="specs_attrs" :storedList="storedList" :is_recharge_buy="formData.is_recharge_buy" />
    <div>
      <div class="block-header"><span>关联赠送服务卡券</span></div>
      <KWidget label="是否关联：" required class="flex-item-align">
        <Radio-group v-model="formData.relation_card">
          <Radio :disabled="!isCanEdit" :label="item.id" v-for="(item, index) in relationList" :key="'stored' + index">{{ item.desc }}</Radio>
        </Radio-group>
        <Button type="default" :disabled="!isCanEdit" class="ml10" v-if="formData.relation_card == 1" @click="serviceModalVisible = true">添加关联服务</Button>
      </KWidget>

      <KWidget label="发放方式：" v-if="formData.relation_card == 1" required class="flex-item-align">
        <Radio-group v-model="formData.grant_type">
          <Radio :label="item.id" v-for="(item, index) in grantList" :key="'stored' + index">{{ item.desc }}</Radio>
        </Radio-group>
      </KWidget>

      <KWidget label="" v-if="formData.relation_card == 1">
        <table class="table" style="width: 500px">
          <thead>
          <tr>
            <th>服务</th>
            <th>类型</th>
            <th>来源</th>
            <th>发放数量</th>
            <th>操作</th>
          </tr>
          </thead>
          <tbody>
          <tr v-for="(item, key) in formData.services" :key="key">
            <td>{{ item.name }}</td>
            <td>{{ item.serv_type_text}}</td>
            <td>{{ item.source_platform_text }}</td>
            <td>
              <InputNumber
                v-model="item.times"
                controls-outside
                :min="1"
                :max="50"
                style="width: 100px"
                v-if="formData.grant_type == 1"
              />
              <div v-else>-</div>
            </td>
            <td>
              <a :disabled="!isCanEdit" @click="onDelService(key)">删除</a>
            </td>
          </tr>
          </tbody>
        </table>
      </KWidget>
    </div>

    <div class="block-header"><span>其他设置</span></div>
    <KWidget label="售卖主体：" required text>
      <CheckboxGroup v-model="formData.sell_ent_type">
        <Checkbox
            disabled
          :label="item.id"
          v-for="item in sellEntTypes.filter(item => item.id == 1)"
          :key="item.id"
          >{{ item.desc }}</Checkbox
        >
      </CheckboxGroup>
    </KWidget>

    <KWidget label="上架范围：" required text>
      <CheckboxGroup v-model="formData.sw_scope_tmp">
        <Checkbox label="1">零售服务</Checkbox>
        <Checkbox label="2">问诊治疗</Checkbox>
      </CheckboxGroup>
    </KWidget>

    <KWidget label="储值购买：" required text>
      <Radio-group v-model="formData.is_recharge_buy">
        <Radio :label="item.id" v-for="(item, index) in storedList" :key="'stored' + index">{{ item.desc }}</Radio>
      </Radio-group>
    </KWidget>

    <k-goods-services
      v-model="serviceModalVisible"
      @on-selected="onSelectedService"
      :checked-service="formData.services"
      :optionsList="optionsList"
    />
  </div>
</template>

<script>
import cloneDeep from 'lodash/cloneDeep';
import KGoodsSpecs from '../../components/k-goods-specs';
import S from 'utils/util';
import GoodsDiff from '../../mixins/goodsDiff';
import KGoodsServices from '@/components/k-goods-services';


export default {
  name: 'PhysicalGoods',
  mixins: [GoodsDiff],
  props: {
    storedList: {
      type: Array,
      default: () => [],
    },
    sellEntTypes: {
      type: Array,
      default: () => [],
    },
    // 平台代发
    isDropShoppingDesc: {
      type: Array,
      default: () => [],
    },
    echoData: {
      type: Object,
      default: () => ({}),
    },
    optionsList: {
      type: Object,
      default () {
        return {
          servTypeDesc: [],
          sourcePlatformDesc: [],
        }
      }
    }
  },
  components: {
    KGoodsSpecs,
    KGoodsServices
  },
  watch: {
    echoData: {
      handler(val) {
        console.log('-> %c val  ===    %o', 'font-size: 15px;color: #fa8c16 ;', val);
        if (S.isEmptyObject(val) || val.goods_type !== '10') return;
        this.handleEchoData(val);
      },
      immediate: true,
    },
  },
  computed: {
    // 实物中关联了服务，禁止勾选养疗馆，并且需要将勾选的养疗馆去除
    isGrantType () {
      return (item) => {
        if ( this.formData.relation_card == 1 && item.id == 2 ) {
          if ( this.formData?.sell_ent_type?.indexOf('2') > 0) {
            this.formData.sell_ent_type = this.formData?.sell_ent_type.filter( sell_item => sell_item != 2 )
          }
          return true
        }else{
          return false
        }
      }
    },
    // 关联服务是否可以编辑
    isCanEdit () {
      return !this.echoData.id || this.echoData.id && this.echoData.status !== '200' && this.echoData.is_audit != '1'
    },
    // 是否来源于修改模板页面
    isFromUpdateTemplate() {
      return this.$route.query.type === 'update';
    },
    // 已经选中的售卖主体不可再取消
    isChoosed() {
      return id => {
        return this.clone_sell_ent_type.indexOf(id) > -1;
      };
    },
  },
  data() {
    return {
      specs_attrs: [[], []],
      formData: {
        sell_ent_type: ['1'],
        is_recharge_buy: 'yes', // 储值购买
        sw_scope_tmp: ['1'],
        specs_data: [],
        attrs: [],
        shelf_scope: '',
        relation_card: '0',
        grant_type: '2',
        services: [],
      },
      clone_sell_ent_type: [], //保存初始化显示的售卖主体
      originFormData: {},
      formDataKeyMap: {
        attrs: { sort: 0, name: '商品规格' },
        grant_type: { sort: 1, name: '发放方式' },
        services: { sort: 2, name: '服务数据' },
        sell_ent_type: { sort: 1, name: '售卖主体' },
        is_recharge_buy: { sort: 2, name: '储值购买' },
        is_drop_shopping: { sort: 3, name: '平台一件代发' },
        shelf_scope: { sort: 4, name: '上架范围' },
      },
      relationList: [
        { id: '0', desc: '不关联' },
        { id: '1', desc: '关联' },
      ],
      grantList: [
        { id: '1', desc: '购买时发放' },
        { id: '2', desc: '订单交易后按需发放' },
      ],
      serviceModalVisible: false,
    };
  },
  methods: {
    onSelectedService: function (items) {
      this.formData.services = items.map(item => {
        // 编辑时携带次数
        let isExist = this.formData.services.findIndex(service_item => service_item.id === item.id);
        return {
          id: item.id,
          name: item.name,
          serv_type: item.serv_type,
          serv_type_text: item.serv_type_text,
          source_platform_text: item.source_platform_text,
          times: isExist === -1 ? 1 : Number(this.formData.services[isExist].times || 0)
        };
      });
      this.serviceModalVisible = false;
    },
    onDelService(index) {
      this.formData.services.splice(index, 1);
    },
    validateForm() {
      let data = {
        ...this.formData,
      };
      console.log('-> %c this.specs_attrs[0]  ===    %o', 'font-size: 15px;color: #fa8c16 ;', this.specs_attrs);
      data.specs_data = this.specs_attrs[0];
      // formData.attrs = this.specs_attrs[1]
      data.attrs = this.getAttrs(this.specs_attrs[1]);
      // 实物商品
      let specs_error_msg = '';
      data.specs_data.forEach(item => {
        if (item.name == '') {
          specs_error_msg = '规格名称不能为空';
          return false;
        }

        item.specs_atoms.forEach(item => {
          if (item[0] == '') {
            specs_error_msg = '规格值不能为空';
            return false;
          }
          if ((item.isAddImg && !item[1]) || item[1] == '') {
            specs_error_msg = '请选添加规格图片';
            return false;
          }
        });
      });
      if (specs_error_msg) {
        this.$Message.error(specs_error_msg);
        return false;
      }

      if (S.isEmptyObject(data.attrs)) {
        this.$Message.error('规格值不能为空');
        return false;
      }

      data.attrs.forEach(item => {
        let price = parseFloat(item.price);
        item.stored_price = item.stored_price == null ? '' : item.stored_price;
        // let stored_price = parseFloat( Number(item.stored_price) )
        if (!/^\d([\d\.]+)?/.test(price)) {
          specs_error_msg = '请输入有效的商品价格';
          return false;
        }

        if (price < 0.01) {
          specs_error_msg = '商品价格不允许小于0.01元';
          return false;
        }
        // 储值价非必填
        // if ( data.is_recharge_buy == 'yes' && stored_price < 0.01 ) {
        //   specs_error_msg = '储值价不允许小于0.01元'
        //   return false
        // }

        if (price > 50000) {
          specs_error_msg = '商品价格不允许大于50000.00元';
          return false;
        }
      });

      if (specs_error_msg) {
        this.$Message.error(specs_error_msg);
        return false;
      }

      if ( !this.formData.relation_card ) {
        this.$Message.error('请选择是否关联');
        return false;
      }

      if ( !this.formData.grant_type ) {
        this.$Message.error('请选择发放方式');
        return false;
      }

      if ( this.formData.relation_card == '1' && this.formData.services?.length == 0 ) {
        this.$Message.error('请最少添加一条关联服务');
        return false;
      }
      let is_has_empty = false;
      this.formData.services?.some(item => {
        if (Number(item.times || 0) == 0) {
          this.$Message.error(`【${item.name}】的发放数量不能小于1`);
          is_has_empty = true;
          return true;
        }
      });
      return !is_has_empty;

      if (data.sell_ent_type.length == 0) {
        this.$Message.error('请选择售卖主体');
        return false;
      }

      if (!this.formData.sw_scope_tmp.length) {
        this.$Message.error('请选择实物商品上架范围');
        return false;
      }
      return true;
    },
    getFormData() {
      let data = {
        ...this.formData,
      };
      data.specs_data = this.specs_attrs[0];
      // formData.attrs = this.specs_attrs[1]
      data.attrs = this.getAttrs(this.specs_attrs[1]);
      data.attrs.forEach(item => {
        if (this.formData.is_recharge_buy === 'no') {
          item.stored_price = '';
        }
      });
      data.shelf_scope = this.formData.sw_scope_tmp.length == 2 ? '9' : this.formData.sw_scope_tmp[0];
      delete data.sw_scope_tmp;
      return data;
    },
    getAttrs(val) {
      let attrs = this.$lodash.cloneDeep(val);
      console.log('-> %c attrs  === %o ', 'font-size: 15px', attrs);
      attrs.forEach((item, index) => {
        let _relate_his_prods = [];
        item.relate_his_prods &&
          item.relate_his_prods.length &&
          item.relate_his_prods.forEach(single_item => {
            console.log('single_item.buy_multiples', single_item.buy_multiples);
            _relate_his_prods.push({
              prod_id: single_item.prod_id,
              sales_unit: single_item._sales_unit,
              buy_multiples: single_item.buy_multiples,
            });
          });
        item.relate_his_prods = _relate_his_prods;
        // item.stock = item.stock;
        item.is_relate_his_prod = _relate_his_prods.length > 0 ? 1 : 0; // 是否有关联货品的标识
      });
      return attrs;
    },
    // 编辑回显
    handleEchoData(data) {
      this.clone_sell_ent_type = cloneDeep(data.sell_ent_type);
      this.specs_attrs = [data.specs_data, data.attrs];
      this.formData.attrs = data.attrs;
      this.formData.specs_data = data.specs_data;
      this.formData.sell_ent_type = data.sell_ent_type;
      this.formData.is_recharge_buy = data.is_recharge_buy;
      this.formData.is_drop_shopping = data.is_drop_shopping;
      this.formData.shelf_scope = data.shelf_scope;
      this.formData.relation_card = data.relation_card
      this.formData.grant_type = data.grant_type
      for (let key in data.services ? data.services : []) {
        data.services[key].times = Number(data.services[key].times);
      }
      this.formData.services = data.services
      this.formData.sw_scope_tmp = data.shelf_scope == '9' ? ['1', '2'] : [data.shelf_scope];
      this.originFormData = cloneDeep(this.formData);
      this.$delete(this.originFormData, 'sw_scope_tmp');
    },
    getFormDataDiff(obj1, obj2) {
      if (this.$lodash.isEqual(obj1, obj2)) {
        return [];
      } else {
        const res = this.deepDiff(obj1, obj2, ['specs_data', 'attrs', 'services']);
        let isAttrDiff = this.getAttrsDiff(obj1.attrs, obj2.attrs);
        if (isAttrDiff) {
          res.unshift('attrs');
        }

        if ( obj1.grant_type == 1 ) {
          let isServiceDiff = this.getServicesDiff(obj1.services, obj2.services);
          if (isServiceDiff) {
            res.unshift('services');
          }
        }

        let diffDesc = [];
        if (res.length) {
          diffDesc = res.map(item => this.formDataKeyMap[item]);
        }
        return diffDesc.sort((a, b) => a.sort - b.sort).map(item => item.name) || [];
      }
    },
    getAttrsDiff(attrs1, attrs2) {
      if (!Array.isArray(attrs1) || !Array.isArray(attrs2)) {
        return false;
      }
      if (attrs1.length !== attrs2.length) {
        return true;
      }
      for (let i = 0; i < attrs1.length; i++) {
        if (+attrs1[i].price !== +attrs2[i].price) {
          return true;
        }
        if (+attrs1[i].stored_price !== +attrs2[i].stored_price) {
          return true;
        }
      }
    },
    getServicesDiff(attrs1, attrs2) {
      if (!Array.isArray(attrs1) || !Array.isArray(attrs2)) {
        return false;
      }
      if (attrs1.length !== attrs2.length) {
        return true;
      }
      for (let i = 0; i < attrs1.length; i++) {
        if (+attrs1[i].times !== +attrs2[i].times) {
          return true;
        }
      }
    },
  },
};
</script>

<style lang="less" scoped></style>
