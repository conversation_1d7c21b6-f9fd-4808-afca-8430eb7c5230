<template>
  <div>
    <FormItem label="设置套餐">
      <div class="table-box">
        <table v-if="echoData.tc_infos.length > 0" class="table" style="width: 800px">
          <thead>
            <tr>
              <th style="min-width: 80px">ID</th>
              <th style="min-width: 140px">商品名</th>
              <th style="min-width: 100px">类型</th>
              <th style="min-width: 140px">规格</th>
              <th style="min-width: 180px">一线城市</th>
              <th style="min-width: 180px">非一线城市</th>
              <th v-if="isOpcPriceExist" style="min-width: 180px">价格</th>
              <!-- <template v-if="opcCols">
              <th v-for="item in opcCols" :key="item.opc_id" style="min-width: 180px">
                {{ item.opc_name }}
              </th>
            </template> -->
              <th style="min-width: 180px">数量</th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="(item, key) in echoData.tc_infos" :key="key">
              <td>{{ item.goods.id }}</td>
              <td>{{ item.name }}</td>
              <td>{{ item.goods_type_text }}</td>
              <td>{{ item.goods_type === '10' || item.goods_type == '40' ? item.spec : '-' }}</td>

              <td>
                <!-- 未选规格时，显示区间 -->
                <div v-if="item.goods_type === '40'">
                  <div>
                    <span>销售价：¥ {{ getPyGoodsPrice(item).first_tier_price | number_format }}</span>
                  </div>
                  <div>
                    <span> 会员价：¥ {{ getPyGoodsPrice(item).first_tier_vip_price | number_format }} </span>
                  </div>
                </div>
                <div v-else>
                  <div>
                    <span>销售价：￥ {{ item.first_tier_price | number_format }}</span>
                  </div>
                  <div>
                    <span>会员价：￥ {{ item.first_tier_vip_price | number_format }}</span>
                  </div>
                </div>
              </td>

              <td>
                <div v-if="item.goods_type === '40'">
                  <div>
                    <span>销售价：¥ {{ getPyGoodsPrice(item).not_first_tier_price | number_format }}</span>
                  </div>

                  <div>
                    <span> 会员价：¥ {{ getPyGoodsPrice(item).not_first_tier_vip_price | number_format }} </span>
                  </div>
                </div>
                <div v-else>
                  <div>
                    <span>销售价：￥ {{ item.not_first_tier_price | number_format }}</span>
                  </div>

                  <div>
                    <span>会员价：￥ {{ item.not_first_tier_vip_price | number_format }}</span>
                  </div>
                </div>
              </td>

              <td v-if="isOpcPriceExist">
                <div>销售价：{{ formatOpcPrice(item, 'price') }}</div>
                <div>会员价：{{ formatOpcPrice(item, 'vip_price') }}</div>
              </td>

              <!-- <template v-if="item.opc_price">
              <td v-for="(v, k) in opcCols" :key="v.opc_id + k">
                <div>销售价：{{ formatOpcPrice(v, item, 'price') }}</div>
                <div>会员价：{{ formatOpcPrice(v, item, 'vip_price') }}</div>
              </td>
            </template> -->

              <td>{{ item.num }}</td>
            </tr>
          </tbody>
        </table>
      </div>
    </FormItem>

    <div class="block-header"><span>价格库存</span></div>

    <FormItem label="一线城市">
      <div class="form-item-price">
        <div>
          销售价 <span>¥ {{ echoData.first_tier_price | number_format }}</span>
        </div>
        <div>
          会员价 <span>¥ {{ echoData.first_tier_vip_price | number_format }}</span>
        </div>
      </div>
    </FormItem>
    <FormItem label="非一线城市">
      <div class="form-item-price">
        <div>
          销售价 <span>¥ {{ echoData.not_first_tier_price | number_format }}</span>
        </div>
        <div>
          会员价 <span>¥ {{ echoData.not_first_tier_vip_price | number_format }}</span>
        </div>
      </div>
    </FormItem>

    <FormItem label="直营中心价格">
      <kOpcPriceD :data="echoData?.opc_price_list" />
    </FormItem>

    <div class="block-header"><span>服务设置</span></div>
  </div>
</template>

<script>
import S from 'utils/util';
import cloneDeep from 'lodash/cloneDeep';
import kOpcPriceD from '@/components/k-goods-opc/k-opc-price-d.vue';

export default {
  name: 'TaoCanGoodsDetail',
  components: { kOpcPriceD },
  mixins: [],
  props: {
    echoData: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      services: [],

      relation_card: '',
      grant_type: '',
      relationList: [
        { id: '0', desc: '不关联' },
        { id: '1', desc: '关联' }
      ],
      grantList: [
        { id: '1', desc: '购买时发放' },
        { id: '2', desc: '订单交易后按需发放' }
      ]
    };
  },
  watch: {
    echoData: {
      handler(val) {
        console.log('=>(TaoCanGoodsDetail.vue:112) val', val);
        if (S.isEmptyObject(val)) return;
        this.services = cloneDeep(val.services || []);
        this.relation_card = cloneDeep(val.relation_card || '');
        this.grant_type = cloneDeep(val.grant_type || '');
      },
      immediate: true
    }
  },
  computed: {
    getPyGoodsPrice() {
      return item => {
        return item?.goods.attrs?.[item.id] || {};
      };
    },

    // 判断服务中是否存在直营中心价格
    isOpcPriceExist() {
      return (
        this.echoData &&
        Array.isArray(this.echoData.tc_infos) &&
        this.echoData.tc_infos.some(item => Array.isArray(item.opc_price) && item.opc_price.length > 0)
      );
    }
  },
  mounted() {},
  methods: {
    formatOpcPrice(row, key) {
      if (row.opc_price.length === 0) {
        return '-';
      } else {
        const temp = row.opc_price[0];
        return temp ? '¥ ' + this.$options.filters.number_format(temp[key]) : '-';
      }
    }
  }
};
</script>

<style lang="less" scoped>
.form-item-price {
  display: flex;
  gap: 0.75rem;
}

.table-box {
  width: 1000px;
  overflow: auto;
}
</style>
