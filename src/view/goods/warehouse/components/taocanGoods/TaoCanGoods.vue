<template>
  <div>
    <div>
      <KWidget label="设置套餐:" text required>
        <span class="space6" v-if="isFromUpdateTemplate">添加产品</span>
        <a class="space6" v-else @click="mealModalVisible = true">添加产品</a>
        <span class="note" style="display: inline-block">支持添加实物、虚拟或通兑券商品组合售卖</span>
        <table v-if="formData.meal_lists.length > 0" class="table" style="maxwidth: 1000px">
          <thead>
            <tr>
              <th>ID</th>
              <th>商品名</th>
              <th>类型</th>
              <th>规格</th>
              <th style="width: 100px">价格</th>
              <th>数量</th>
              <th>操作</th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="(item, key) in formData.meal_lists" :key="key">
              <td>{{ item.goodsId }}</td>
              <td class="goodsName">{{ item.name }}</td>
              <td>{{ item.goods_type_text }}</td>
              <td class="custom-select">
                <template v-if="item.goods_type == '10'">
                  <Select
                    v-model="item.skuId"
                    :disabled="isFromUpdateTemplate"
                    style="width: 130px"
                    @on-change="specsChange($event, key)"
                  >
                    <Option
                      v-for="(attrs_item, index) in item.attrs"
                      :value="attrs_item.id"
                      :label="attrs_item.spec"
                      :key="'specs' + index"
                      :disabled="item.skuId == attrs_item.id ? false : isDisabled(item.goodsId, attrs_item.id)"
                    >
                      {{ attrs_item.spec }} <span>￥{{ attrs_item.price }}</span>
                    </Option>
                  </Select>
                </template>
                <p v-else>-</p>
              </td>
              <td>
                <!-- 未选规格时，显示区间 -->
                <template v-if="!item.chooseGoodsPrice && item.price_section">
                  <p v-if="!item.price_section.max">￥{{ item.price_section.min }}</p>
                  <p v-else>
                    ￥{{ item.price_section.min | number_format }} - ￥{{ item.price_section.max | number_format }}
                  </p>
                </template>
                <p v-else>￥{{ item.chooseGoodsPrice | number_format }}</p>
              </td>
              <td>
                <InputNumber
                  v-model="item.num"
                  :disabled="isFromUpdateTemplate"
                  controls-outside
                  :min="1"
                  style="width: 100px"
                />
              </td>
              <td>
                <span v-if="isFromUpdateTemplate">-</span>
                <template v-else>
                  <a v-if="!isLastPhysical(item)" @click="onDelMeal(key)">删除</a>
                  <Poptip
                    width="200"
                    :disabled="!isLastPhysical(item)"
                    v-else
                    confirm
                    title="套餐如果不关联实物商品，则关联赠送的服务卡券也将被清空"
                    @on-ok="onDelMeal(key)"
                  >
                    <a>删除</a>
                  </Poptip>
                </template>
              </td>
            </tr>
          </tbody>
        </table>
      </KWidget>

      <div class="block-header"><span>价格库存</span></div>

      <KWidget label="价格：" required>
        <InputNumber v-model="formData.price" :precision="2" :active-change="false" :min="0" />
        元
        <span style="marginleft: 20px; color: #999" v-if="suggestPrice">建议售价: ￥{{ suggestPrice }}</span>
      </KWidget>

      <KWidget label="储值价：" v-show="formData.is_recharge_buy == 'yes'">
        <InputNumber
          v-model="formData.stored_price"
          ref="stored"
          placeholder="请输入储值价"
          :precision="2"
          :min="0"
          @on-change="storedPriceChange"
          :active-change="false"
        />
        元
      </KWidget>

      <div style="margin-top: 20px">
        <div class="block-header">
          <span>关联赠送服务卡券</span>
          <Tooltip max-width="200" content="套餐商品中添加了实物商品，才可关联赠送卡券">
            <Icon type="md-help-circle" size="16" />
          </Tooltip>
        </div>
        <KWidget label="是否关联：" required class="flex-item-align">
          <Radio-group v-model="formData.relation_card">
            <Radio
              :disabled="!isCanEdit || !isTcHasPhysicalGoods"
              :label="item.id"
              v-for="(item, index) in relationList"
              :key="'stored' + index"
              >{{ item.desc }}
            </Radio>
          </Radio-group>
          <Button
            type="default"
            :disabled="!isCanEdit || !isTcHasPhysicalGoods"
            class="ml10"
            v-if="formData.relation_card == 1"
            @click="serviceModalVisible = true"
            >添加关联服务
          </Button>
        </KWidget>

        <KWidget label="发放方式：" v-if="formData.relation_card == 1" required class="flex-item-align">
          <Radio-group v-model="formData.grant_type">
            <Radio
              :disabled="!isTcHasPhysicalGoods"
              :label="item.id"
              v-for="(item, index) in grantList"
              :key="'stored' + index"
              >{{ item.desc }}
            </Radio>
          </Radio-group>
        </KWidget>

        <KWidget label="" v-if="formData.relation_card == 1">
          <table class="table" style="width: 500px">
            <thead>
              <tr>
                <th>服务</th>
                <th>类型</th>
                <th>来源</th>
                <th>发放数量</th>
                <th>操作</th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="(item, key) in formData.services" :key="key">
                <td>{{ item.name }}</td>
                <td>{{ item.serv_type_text }}</td>
                <td>{{ item.source_platform_text }}</td>
                <td>
                  <InputNumber
                    v-model="item.times"
                    controls-outside
                    :min="1"
                    :max="50"
                    style="width: 100px"
                    v-if="formData.grant_type == 1"
                  />
                  <div v-else>-</div>
                </td>
                <td>
                  <a :disabled="!isCanEdit" @click="onDelService(key)">删除</a>
                </td>
              </tr>
            </tbody>
          </table>
        </KWidget>
      </div>

      <div class="block-header"><span>服务设置</span></div>
      <KWidget label="售卖主体：" required text>
        <CheckboxGroup v-model="formData.sell_ent_type">
          <Checkbox disabled :label="item.id" v-for="item in sellEntTypes.filter(item => item.id == 1)" :key="item.id"
            >{{ item.desc }}
          </Checkbox>
        </CheckboxGroup>
      </KWidget>
      <KWidget label="上架范围：" required text>
        <CheckboxGroup v-model="formData.xn_scope_tmp">
          <Checkbox label="1">零售服务</Checkbox>
          <Checkbox label="2">问诊治疗</Checkbox>
        </CheckboxGroup>
      </KWidget>

      <KWidget label="储值购买: " required text>
        <Radio-group v-model="formData.is_recharge_buy">
          <Radio :label="item.id" v-for="(item, index) in storedList" :key="'stored' + index">{{ item.desc }}</Radio>
        </Radio-group>
      </KWidget>
    </div>

    <k-goods-meal v-model="mealModalVisible" @on-selected="onSelectedMeal" />

    <k-goods-services
      v-model="serviceModalVisible"
      @on-selected="onSelectedService"
      :checked-service="formData.services"
      :optionsList="optionsList"
    />
  </div>
</template>

<script>
import cloneDeep from 'lodash/cloneDeep';
import S from '@/utils/util';
import KGoodsMeal from '@/components/k-goods-meal';
import goodsDiff from '../../mixins/goodsDiff';
import KGoodsServices from '@/components/k-goods-services';

export default {
  name: 'TaoCanGoods',
  mixins: [goodsDiff],
  components: {
    KGoodsMeal,
    KGoodsServices
  },
  props: {
    // 储值购买枚举
    storedList: {
      type: Array,
      default: () => []
    },
    sellEntTypes: {
      type: Array,
      default: () => []
    },
    echoData: {
      type: Object,
      default: () => ({})
    },
    optionsList: {
      type: Object,
      default() {
        return {
          servTypeDesc: [],
          sourcePlatformDesc: []
        };
      }
    }
  },
  data() {
    return {
      formData: {
        meal_lists: [], // 套餐商品
        price: null, // 套餐商品价格
        stored_price: null, // 储值价
        sell_ent_type: ['1'], // 售卖主体
        xn_scope_tmp: ['1'], // 上架范围
        is_recharge_buy: 'yes', // 储值购买
        relation_card: '0',
        grant_type: '2',
        services: []
      },

      tc_infos: [], // 整合的套餐商品数据

      suggestPrice: null, // 建议零售价
      chooseSkuIdLists: {}, // 已经选中的skuId
      mealModalVisible: false,
      clone_sell_ent_type: [], //保存初始化显示的售卖主体
      originFormData: {},
      formDataKeyMap: {
        price: { sort: 1, name: '商品价格' },
        stored_price: { sort: 2, name: '商品储值价' },
        grant_type: { sort: 3, name: '发放方式' },
        services: { sort: 4, name: '服务数据' },
        sell_ent_type: { sort: 5, name: '售卖主体' },
        xn_scope: { sort: 6, name: '上架范围' },
        is_recharge_buy: { sort: 7, name: '储值购买' }
      },
      relationList: [
        { id: '0', desc: '不关联' },
        { id: '1', desc: '关联' }
      ],
      grantList: [
        { id: '1', desc: '购买时发放' },
        { id: '2', desc: '订单交易后按需发放' }
      ],
      serviceModalVisible: false
    };
  },
  watch: {
    // 套餐关联的商品里面不包含实物，初始化关联数据
    'formData.meal_lists'(list) {
      if (list.length) {
        if (list.every(item => item.goods_type !== '10')) {
          this.clearRelationData();
        }
      }
    },
    echoData: {
      handler(val) {
        if (S.isEmptyObject(val) || val.goods_type !== '20') return;
        this.handleEchoData(val);
      },
      immediate: true
    }
  },
  computed: {
    isLastPhysical() {
      return item => {
        let current_physical_list = this.formData.meal_lists.filter(item => item.goods_type == 10);
        if (current_physical_list.length == 1 && item.goods_type == '10' && this.formData.relation_card == 1) {
          return true;
        } else {
          return false;
        }
      };
    },
    isTcHasPhysicalGoods() {
      let list = this.formData.meal_lists || [];
      let isHas = list.some(item => item.goods_type == '10');
      if (!isHas) {
        this.clearRelationData();
      }
      return isHas;
    },
    // 套餐中存在通兑券，或者实物关联了服务，禁止勾选养疗馆，并且需要将勾选的养疗馆去除
    isTcGrantType() {
      return item => {
        let is_exist = this.formData.meal_lists.some(tc_item => {
          if ((tc_item.goods_type == 10 && tc_item.relation_card == '1') || tc_item.goods_type == 25) {
            if (this.formData?.sell_ent_type?.indexOf('2') > 0) {
              this.formData.sell_ent_type = this.formData?.sell_ent_type.filter(sell_item => sell_item != 2);
            }
            return true;
          }
        });
        return is_exist;
      };
    },
    // 关联服务是否可以编辑
    isCanEdit() {
      return !this.echoData.id || (this.echoData.id && this.echoData.status !== '200' && this.echoData.is_audit != '1');
    },
    // 是否来源于修改模板页面
    isFromUpdateTemplate() {
      return this.$route.query.type === 'update';
    },
    // 已经选中的售卖主体不可再取消
    isChoosed() {
      return id => {
        return this.clone_sell_ent_type.indexOf(id) > -1;
      };
    }
  },
  mounted() {},
  methods: {
    clearRelationData() {
      this.formData.relation_card = '0';
      this.formData.grant_type = '2';
      this.formData.services = [];
    },
    onSelectedService: function (items) {
      this.formData.services = items.map(item => {
        // 编辑时携带次数
        let isExist = this.formData.services.findIndex(service_item => service_item.id === item.id);
        return {
          id: item.id,
          name: item.name,
          serv_type: item.serv_type,
          serv_type_text: item.serv_type_text,
          source_platform_text: item.source_platform_text,
          times: isExist === -1 ? 1 : Number(this.formData.services[isExist].times || 0)
        };
      });
      this.serviceModalVisible = false;
    },
    onDelService(index) {
      this.formData.services.splice(index, 1);
    },
    // 储值价
    storedPriceChange(val) {
      if (Number(val) == 0) {
        this.formData.stored_price = null;
        this.$refs.stored.currentValue = null;
      }
    },

    // 套餐校验
    validateForm() {
      const data = this.formData;

      if (data.meal_lists.length <= 0) {
        this.$Message.error('请添加产品');
        return false;
      }

      if (!/^\d([\d\.]+)?/.test(data.price)) {
        this.$Message.error('请输入有效的商品价格');
        return false;
      }
      let price = parseFloat(data.price);
      if (price < 0.01) {
        this.$Message.error('商品价格不允许小于0.01元');
        return false;
      }
      if (price > 50000) {
        this.$Message.error('商品价格不允许大于50000.00元');
        return false;
      }

      let isHasEmpty = this.formData.meal_lists.some(item => item.skuId == '');
      if (isHasEmpty) {
        this.$Message.error('商品规格不允许为空');
        return false;
      }

      let isHasEmptyNum = this.formData.meal_lists.some(item => Number(item.num || 0) == 0);
      if (isHasEmptyNum) {
        this.$Message.error('商品数量不允许为空');
        return false;
      }

      if (data.sell_ent_type.length == 0) {
        this.$Message.error('请选择售卖主体');
        return false;
      }

      if (data.xn_scope_tmp.length == 0) {
        this.$Message.error('请选择套餐上架范围');
        return false;
      }

      if (!data.is_recharge_buy) {
        this.$Message.error('请选择储值购买');
        return false;
      }

      return true;
    },

    // 获取套餐的数据
    getFormData() {
      let formData = {
        ...this.formData
      };
      let tc_infos = formData.meal_lists.map(item => {
        return {
          id: item.skuId,
          num: item.num
        };
      });
      formData.tc_infos = JSON.stringify(tc_infos);
      delete formData.meal_lists;
      formData.xn_scope = this.formData.xn_scope_tmp.length == 2 ? '9' : this.formData.xn_scope_tmp[0];
      delete formData.xn_scope_tmp;
      return formData;
    },

    // 回显套餐商品数据
    handleEchoData(goods) {
      this.clone_sell_ent_type = cloneDeep(goods.sell_ent_type);
      goods.tc_infos &&
        goods.tc_infos.forEach((tc_item, tc_index) => {
          this.formData.meal_lists.push({
            goodsId: tc_item.goods && tc_item.goods.id,
            skuId: tc_item.id,
            name: tc_item.name,
            goods_type: tc_item.goods_type,
            goods_type_text: tc_item.goods_type_text,
            attrs: tc_item.goods && tc_item.goods.attrs,
            price_section: tc_item.price_section,
            chooseGoodsPrice: tc_item.price, // 选中的商品的价格
            num: Number(tc_item.num) // 商品数量
          });
        });
      this.filterHasChoosedSkuList();

      this.formData.price = Number(goods.price);
      this.formData.stored_price = goods.stored_price == '' ? null : Number(goods.stored_price);
      this.formData.sell_ent_type = goods.sell_ent_type;
      this.formData.xn_scope_tmp = goods.xn_scope == '9' ? ['1', '2'] : [goods.xn_scope];
      this.formData.is_recharge_buy = goods.is_recharge_buy;
      this.formData.xn_scope = goods.xn_scope;
      this.formData.relation_card = goods.relation_card;
      this.formData.grant_type = goods.grant_type;
      for (let key in goods.services ? goods.services : []) {
        goods.services[key].times = Number(goods.services[key].times);
      }
      this.formData.services = goods.services;
      this.originFormData = cloneDeep(this.formData);
      this.$delete(this.originFormData, 'xn_scope_tmp');
    },

    // 选中套餐商品
    onSelectedMeal(items) {
      console.log('-> items', items);
      // 需要区分添加的商品是单个sku还是多个sku,如果是单个sku，不可重复添加，如果是多个sku，sku不可重复添加
      let goodsIds = this.formData.meal_lists.map(item => item.goodsId);
      console.log('-> goodsIds', goodsIds);

      // 对于已经存在的套餐商品不允许添加
      items.forEach(item => {
        if (goodsIds.includes(item.id) && (item.goods_type == '15' || item.goods_type == '25')) {
          // 虚拟商品只能添加一次
          this.$Message.error(`${item.name}已存在,请勿重复添加`);
        } else {
          let hasSkuList = this.formData.meal_lists.filter(i_item => i_item.goodsId == item.id);
          let attrs_length = Object.keys(item.attrs).length;
          // 对于实体商品，如果增加的规格已经达到上限，不允许继续添加
          if (goodsIds.includes(item.id) && hasSkuList.length >= attrs_length) {
            this.$Message.error(`${item.name}规格最多为${attrs_length}种,请勿重复添加`);
          } else {
            this.formData.meal_lists.push({
              goodsId: item.id,
              skuId:
                item.goods_type == '15' || item.goods_type == '25' ? item.attrs[Object.keys(item.attrs)[0]].id : '',
              name: item.name,
              goods_type: item.goods_type,
              goods_type_text: item.goods_type_text,
              attrs: item.attrs,
              price_section: item.price_section,
              chooseGoodsPrice:
                item.goods_type == '15' || item.goods_type == '25'
                  ? item.attrs[Object.keys(item.attrs)[0]].price
                  : null, // 选中的商品的价格
              num: 1, // 商品数量
              relation_card: item.relation_card
            });
          }
        }
      });
      this.mealModalVisible = false;
    },

    // 删除套餐的产品
    onDelMeal: function (index) {
      this.formData.meal_lists.splice(index, 1);
      this.filterHasChoosedSkuList();
    },

    // 生成spu-sku选中的数据
    filterHasChoosedSkuList() {
      this.chooseSkuIdLists = {};
      this.formData.meal_lists.forEach(item => {
        if (this.chooseSkuIdLists[item.goodsId] && this.chooseSkuIdLists[item.goodsId].length) {
          if (!this.chooseSkuIdLists[item.goodsId].includes(item.skuId)) {
            this.chooseSkuIdLists[item.goodsId].push(item.skuId);
          }
        } else {
          this.chooseSkuIdLists[item.goodsId] = [item.skuId];
        }
      });
    },

    // 当前spu对应的sku选项是否可选
    isDisabled(spuId, skuId) {
      let flag = false;
      if (this.chooseSkuIdLists[spuId]) {
        flag = this.chooseSkuIdLists[spuId].includes(skuId) ? true : false;
      }
      return flag;
    },

    // 套餐商品选择规格
    specsChange(key, index) {
      this.formData.meal_lists[index].skuId = this.formData.meal_lists[index].attrs[key]?.id;
      this.formData.meal_lists[index].chooseGoodsPrice = this.formData.meal_lists[index].attrs[key]?.price;

      this.filterHasChoosedSkuList();
    },
    getFormDataDiff(obj1, obj2) {
      if (this.$lodash.isEqual(obj1, obj2)) {
        return [];
      } else {
        const res = this.deepDiff(obj1, obj2, ['tc_infos', 'meal_lists', 'services']);
        if (obj1.grant_type == 1) {
          let isServiceDiff = this.getServicesDiff(obj1.services, obj2.services);
          if (isServiceDiff) {
            res.unshift('services');
          }
        }

        let diffDesc = [];

        if (res.length) {
          diffDesc = res.map(item => this.formDataKeyMap[item]);
        }
        return diffDesc.sort((a, b) => a.sort - b.sort).map(item => item.name) || [];
      }
    },
    getServicesDiff(attrs1, attrs2) {
      if (!Array.isArray(attrs1) || !Array.isArray(attrs2)) {
        return false;
      }
      if (attrs1.length !== attrs2.length) {
        return true;
      }
      for (let i = 0; i < attrs1.length; i++) {
        if (+attrs1[i].times !== +attrs2[i].times) {
          return true;
        }
      }
    }
  }
};
</script>

<style scoped lang="less">
p {
  margin-bottom: 0px;
}
</style>
