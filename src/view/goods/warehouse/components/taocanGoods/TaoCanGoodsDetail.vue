<template>
  <div>
    <FormItem label="设置套餐">
      <table v-if="echoData.tc_infos.length > 0" class="table" style="width: 500px">
        <thead>
          <tr>
            <th>ID</th>
            <th>商品名</th>
            <th>类型</th>
            <th>规格</th>
            <th>价格</th>
            <th>数量</th>
          </tr>
        </thead>
        <tbody>
          <tr v-for="(item, key) in echoData.tc_infos" :key="key">
            <td>{{ item.goods.id }}</td>
            <td>{{ item.name }}</td>
            <td>{{ item.goods_type_text }}</td>
            <td>{{ item.goods_type === '10' ? item.spec : '-' }}</td>
            <td>{{ item.price | number_format }}</td>
            <td>{{ item.num }}</td>
          </tr>
        </tbody>
      </table>
    </FormItem>

    <div class="block-header"><span>价格库存</span></div>

    <FormItem label="价格">
      <p>¥ {{ echoData.price }}</p>
    </FormItem>
    <FormItem label="储值价" v-show="echoData.is_recharge_buy == 'yes'">
      <p v-if="echoData.stored_price">¥ {{ echoData.stored_price }}</p>
      <p v-else>-</p>
    </FormItem>

    <KWidget label="是否关联：" v-if="relation_card" required class="flex-item-align">
      <Radio-group v-model="relation_card">
        <Radio disabled :label="item.id" v-for="(item, index) in relationList" :key="'stored' + index">{{
          item.desc
        }}</Radio>
      </Radio-group>
    </KWidget>

    <KWidget label="发放方式：" v-if="relation_card == 1" required class="flex-item-align">
      <Radio-group v-model="grant_type">
        <Radio disabled :label="item.id" v-for="(item, index) in grantList" :key="'stored' + index">{{
          item.desc
        }}</Radio>
      </Radio-group>
    </KWidget>

    <KWidget label="" v-if="relation_card == 1">
      <table class="table" style="width: 500px">
        <thead>
          <tr>
            <th>服务</th>
            <th>类型</th>
            <th>来源</th>
            <th>发放数量</th>
            <th>操作</th>
          </tr>
        </thead>
        <tbody>
          <tr v-for="(item, key) in services" :key="key">
            <td>{{ item.name }}</td>
            <td>{{ item.serv_type_text }}</td>
            <td>{{ item.source_platform_text }}</td>
            <td>
              <span style="width: 100px" v-if="grant_type == 1">{{ item.times }}</span>
              <div v-else>-</div>
            </td>
            <td>-</td>
          </tr>
        </tbody>
      </table>
    </KWidget>

    <div class="block-header"><span>服务设置</span></div>
  </div>
</template>

<script>
import S from 'utils/util';
import cloneDeep from 'lodash/cloneDeep';
export default {
  name: 'TaoCanGoodsDetail',
  mixins: [],
  components: {},
  props: {
    echoData: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      services: [],
      relation_card: '',
      grant_type: '',
      relationList: [
        { id: '0', desc: '不关联' },
        { id: '1', desc: '关联' }
      ],
      grantList: [
        { id: '1', desc: '购买时发放' },
        { id: '2', desc: '订单交易后按需发放' }
      ]
    };
  },
  watch: {
    echoData: {
      handler(val) {
        if (S.isEmptyObject(val)) return;
        this.services = cloneDeep(val.services || []);
        this.relation_card = cloneDeep(val.relation_card || '');
        this.grant_type = cloneDeep(val.grant_type || '');
      },
      immediate: true
    }
  },
  computed: {},
  mounted() {},
  methods: {}
};
</script>

<style lang="less" scoped></style>
