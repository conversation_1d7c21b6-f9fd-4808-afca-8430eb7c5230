<template>
  <div>
    <FormItem label="服务权益">
      <div class="table-box">
        <table v-if="echoData.services.length > 0" class="table">
          <thead>
            <tr>
              <th style="min-width: 100px">已选服务</th>
              <th style="min-width: 180px">一线城市</th>
              <th style="min-width: 180px">非一线城市</th>
              <th v-if="isOpcPriceExist" style="min-width: 180px">价格</th>
              <!-- <template v-if="opcCols">
                <th v-for="item in opcCols" :key="item.opc_id" style="min-width: 180px">
                  {{ item.opc_name }}
                </th>
              </template> -->
              <th style="min-width: 100px">可用次数</th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="(item, key) in echoData.services" :key="key">
              <td>{{ item.name }}</td>
              <td>
                <div>销售价：¥ {{ item.first_tier_price | number_format }}</div>
                <div>会员价：¥ {{ item.first_tier_vip_price | number_format }}</div>
              </td>
              <td>
                <div>销售价：¥ {{ item.not_first_tier_price | number_format }}</div>
                <div>会员价：¥ {{ item.not_first_tier_vip_price | number_format }}</div>
              </td>
              <td v-if="isOpcPriceExist">
                <div>销售价： {{ formatOpcPrice(item, 'price') }}</div>
                <div>会员价： {{ formatOpcPrice(item, 'vip_price') }}</div>
              </td>
              <!-- <template v-if="item.opc_price">
                <th v-for="v in opcCols" :key="v.opc_id">
                  <div>销售价：{{ formatOpcPrice(v, item, 'price') }}</div>
                  <div>会员价：{{ formatOpcPrice(v, item, 'vip_price') }}</div>
                </th>
              </template> -->
              <td>{{ item.times }}</td>
            </tr>
          </tbody>
        </table>
      </div>
    </FormItem>
    <div class="block-header"><span>价格库存</span></div>

    <FormItem label="一线城市价格">
      <div class="form-item-price">
        <div>
          销售价 <span>¥ {{ echoData.first_tier_price | number_format }}</span>
        </div>
        <div>
          会员价 <span>¥ {{ echoData.first_tier_vip_price | number_format }}</span>
        </div>
      </div>
    </FormItem>
    <FormItem label="非一线城市价格">
      <div class="form-item-price">
        <div>
          销售价 <span>¥ {{ echoData.not_first_tier_price | number_format }}</span>
        </div>
        <div>
          会员价 <span>¥ {{ echoData.not_first_tier_vip_price | number_format }}</span>
        </div>
      </div>
    </FormItem>
    <FormItem label="直营中心价格">
      <kOpcPriceD :data="echoData?.opc_price_list" />
    </FormItem>

    <div class="block-header"><span>服务设置</span></div>

    <FormItem label="商品有效期">
      <p v-if="echoData.service_info.expiration.type == '1'">
        购买后{{ echoData.service_info.expiration.days }}天内有效
      </p>
      <p v-if="echoData.service_info.expiration.type == '2'">截止到{{ echoData.service_info.expiration.date }}日有效</p>
    </FormItem>

    <FormItem label="是否可约">
      <p>{{ echoData.service_info.is_appointment == '1' ? '需要预约' : '免预约' }}</p>
    </FormItem>

    <FormItem label="是否可退">
      <p>{{ echoData.service_info.is_can_refund == '1' ? '可退' : '不可退' }}</p>
    </FormItem>

    <FormItem label="使用时间">
      <p>{{ echoData.service_info.working_time }}</p>
    </FormItem>

    <FormItem label="不可使用日期">
      <p>{{ echoData.service_info.not_working_date }}</p>
    </FormItem>
  </div>
</template>

<script>
import S from 'utils/util';

import kOpcPriceD from '@/components/k-goods-opc/k-opc-price-d.vue';

export default {
  name: 'RstVirtualGoodsDetail',
  components: { kOpcPriceD },
  mixins: [],
  props: {
    echoData: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      opcCols: []
    };
  },
  computed: {
    // 判断服务中是否存在直营中心价格
    isOpcPriceExist() {
      return (
        this.echoData &&
        Array.isArray(this.echoData.services) &&
        this.echoData.services.some(item => Array.isArray(item.opc_price) && item.opc_price.length > 0)
      );
    }
  },
  watch: {
    echoData: {
      handler(val) {
        if (S.isEmptyObject(val)) return;
      },
      immediate: true
    }
  },
  mounted() {},
  methods: {
    formatOpcPrice(row, key) {
      if (row.opc_price.length === 0) {
        return '-';
      } else {
        const temp = row.opc_price[0];
        return temp ? '¥ ' + this.$options.filters.number_format(temp[key]) : '-';
      }
    }
  }
};
</script>

<style lang="less" scoped>
.form-item-price {
  display: flex;
  gap: 0.75rem;
}
.table-box {
  width: 800px;
  overflow: auto;
}
</style>
