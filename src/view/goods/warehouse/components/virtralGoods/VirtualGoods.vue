<template>
  <div class="virtual-goods-wrap mt-20">
    <KWidget label="服务权益:" text required>
      <span class="space6" v-if="isFromUpdateTemplate">添加服务</span>
      <a v-else class="space6" @click="serviceModalVisible = true">添加服务</a>
      <span class="note" style="display: inline-block">设置此商品支持的服务可用次数</span>
      <table v-if="formData.services.length > 0" class="table" style="width: 500px">
        <thead>
          <tr>
            <th>已选服务</th>
            <th>价格</th>
            <th>可用次数</th>
            <th>操作</th>
          </tr>
        </thead>
        <tbody>
          <tr v-for="(item, key) in formData.services" :key="key">
            <td>{{ item.name }}</td>
            <td>{{ item.price | number_format }}</td>
            <td>
              <InputNumber
                :disabled="isFromUpdateTemplate"
                v-model="item.times"
                controls-outside
                :min="1"
                :max="50"
                style="width: 100px"
              />
            </td>
            <td>
              <span v-if="isFromUpdateTemplate">-</span>
              <a v-else @click="onDelService(key)">删除</a>
            </td>
          </tr>
        </tbody>
      </table>
    </KWidget>

    <div class="block-header"><span>价格库存</span></div>

    <KWidget label="价格：" required>
      <InputNumber v-model="formData.price" :precision="2" :active-change="false" :min="0" />
      元
    </KWidget>
    <KWidget label="储值价：" v-show="formData.is_recharge_buy == 'yes'">
      <InputNumber
        v-model="formData.stored_price"
        ref="stored"
        placeholder="请输入储值价"
        :precision="2"
        :min="0"
        @on-change="storedPriceChange"
        :active-change="false"
      />
      元
    </KWidget>

    <div class="block-header"><span>服务设置</span></div>

    <KWidget label="商品有效期" required>
      <RadioGroup v-model="formData.service_info.expiration.type">
        <Radio label="1"><span></span></Radio>
        <div style="display: inline-block; margin-left: -10px">
          购买后
          <InputNumber
            v-model="formData.service_info.expiration.days"
            :min="1"
            placeholder="自定义"
            :disabled="formData.service_info.expiration.type != 1"
          />
          天内有效
        </div>
        <div class="block_10"></div>
        <Radio label="2"><span></span></Radio>
        <div style="display: inline-block; margin-left: -10px">
          截止到
          <DatePicker
            type="date"
            v-model="formData.service_info.expiration.date"
            placeholder="请选择日期"
            format="yyyy-MM-dd"
            @on-change="formData.service_info.expiration.date = arguments[0]"
            :options="expiration_datepicker_options"
            :disabled="formData.service_info.expiration.type != 2"
          />
          日有效
        </div>
      </RadioGroup>
    </KWidget>
    <KWidget label="是否可约：" text>
      <RadioGroup v-model="formData.service_info.is_appointment">
        <Radio label="1">需要预约</Radio>
        <Radio label="2">免预约</Radio>
      </RadioGroup>
    </KWidget>
    <KWidget label="是否可退：" text>
      <RadioGroup v-model="formData.service_info.is_can_refund">
        <Radio label="1">可退</Radio>
        <Radio label="2">不可退</Radio>
      </RadioGroup>
      <span class="note" style="display: inline-block">未使用是否可以退款</span>
    </KWidget>
    <KWidget label="使用时间：" required>
      <Input v-model="formData.service_info.working_time" />
      <span class="note">如：工作日8:00-19:00点间可用</span>
    </KWidget>
    <KWidget label="不可使用日期：" required>
      <Input v-model="formData.service_info.not_working_date" />
      <span class="note">如：周六周日、法定节假日不可用</span>
    </KWidget>
    <!-- <KWidget label="使用规则：">
      <Input v-model="formData.service_info.note_rule" type="textarea" :rows="5" placeholder="请输入使用规则" maxlength="100"
             show-word-limit/>
    </KWidget> -->
    <div class="block-header"><span>售卖主体</span></div>
    <KWidget label="售卖主体：" required text>
      <CheckboxGroup v-model="formData.sell_ent_type">
        <Checkbox
            disabled
          :label="item.id"
          v-for="item in sellEntTypes.filter(item => item.id == 1)"
          :key="item.id"
          >{{ item.desc }}
        </Checkbox>
      </CheckboxGroup>
    </KWidget>
    <KWidget label="上架范围：" required text>
      <CheckboxGroup v-model="formData.xn_scope_tmp">
        <Checkbox label="1">零售服务</Checkbox>
        <Checkbox label="2">问诊治疗</Checkbox>
      </CheckboxGroup>
    </KWidget>
    <KWidget label="储值购买: " required text>
      <Radio-group v-model="formData.is_recharge_buy">
        <Radio :label="item.id" v-for="(item, index) in storedList" :key="'stored' + index">{{ item.desc }}</Radio>
      </Radio-group>
    </KWidget>
    <k-goods-services
      v-model="serviceModalVisible"
      @on-selected="onSelectedService"
      :checked-service="formData.services"
    />
  </div>
</template>

<script>
import S from 'utils/util';
import KGoodsServices from '@/components/k-goods-services';
import cloneDeep from 'lodash/cloneDeep';
import goodsDiff from '../../mixins/goodsDiff';

export default {
  name: 'VirtualGoods',
  mixins: [goodsDiff],
  props: {
    storedList: {
      type: Array,
      default: () => []
    },
    sellEntTypes: {
      type: Array,
      default: () => []
    },
    echoData: {
      type: Object,
      default: () => ({})
    }
  },
  components: {
    KGoodsServices
  },
  watch: {
    echoData: {
      handler(val) {
        console.log('-> %c val  ===    %o', 'font-size: 15px;color: #fa8c16 ;', val);
        if (S.isEmptyObject(val) || val.goods_type !== '15') return;
        this.handleEchoData(val);
      },
      immediate: true
    }
  },
  computed: {
    // 是否来源于修改模板页面
    isFromUpdateTemplate() {
      return this.$route.query.type === 'update';
    },
    // 已经选中的售卖主体不可再取消
    isChoosed() {
      return id => {
        return this.clone_sell_ent_type.indexOf(id) > -1;
      };
    }
  },
  data() {
    return {
      formData: {
        service_info: {
          expiration: {
            type: '1', // 1 购买后${days}天内有效；2 ${date}日期前有效；
            date: '', //
            days: 365 //
          },
          is_appointment: '1', // 1免预约 2需要预约
          is_can_refund: '1', // 1不可退 2可退
          working_time: '', // 工作时间
          not_working_date: '', // 不可使用日期：
          note_rule: '' // 使用规则
        },
        sell_ent_type: ['1'],
        price: 0,
        is_recharge_buy: 'yes', // 虚拟服务的储值购买
        stored_price: null, // 虚拟服务的储值价
        services: [],
        xn_scope_tmp: ['1'],
        xn_scope: ''
      },
      serviceModalVisible: false,
      expiration_datepicker_options: {
        disabledDate(date) {
          return date && date.valueOf() <= Date.now();
        }
      },
      clone_sell_ent_type: [], //保存初始化显示的售卖主体
      originFormData: {},
      formDataKeyMap: {
        price: { sort: 1, name: '商品价格' },
        stored_price: { sort: 2, name: '商品储值价' },
        xn_scope_tmp: { sort: 3, name: '虚拟商品上架范围' },
        expiration: { sort: 4, name: '商品有效期' },
        is_appointment: { sort: 4, name: '是否可约' },
        is_can_refund: { sort: 5, name: '是否可退' },
        working_time: { sort: 6, name: '使用时间' },
        not_working_date: { sort: 7, name: '不可使用日期' },
        sell_ent_type: { sort: 8, name: '售卖主体' },
        xn_scope: { sort: 9, name: '上架范围' },
        is_recharge_buy: { sort: 10, name: '储值购买' }
      }
    };
  },
  methods: {
    storedPriceChange(val) {
      if (Number(val) == 0) {
        this.formData.stored_price = null;
        this.$refs.stored.currentValue = null;
      }
    },
    onDelService(index) {
      this.formData.services.splice(index, 1);
    },
    onSelectedService: function (items) {
      console.log('-> %c items  === %o ', 'font-size: 15px', items);
      this.formData.services = items.map(item => {
        // 编辑时携带次数
        let isExist = this.formData.services.findIndex(service_item => service_item.id === item.id);
        return {
          id: item.id,
          name: item.name,
          price: item.price,
          times: isExist === -1 ? 1 : this.formData.services[isExist].times
        };
      });
      this.serviceModalVisible = false;
    },
    validateForm() {
      console.log('213123');
      const data = this.formData;
      if (data.services.length <= 0) {
        this.$Message.error('请添加服务权益');
        return false;
      }

      if (!data.service_info.working_time) {
        this.$Message.error('请输入使用时间');
        return false;
      }

      if (!data.service_info.not_working_date) {
        this.$Message.error('请输入不可使用日期');
        return false;
      }

      if (!/^\d([\d\.]+)?/.test(data.price)) {
        this.$Message.error('请输入有效的商品价格');
        return false;
      }
      let price = parseFloat(data.price);
      if (price < 0.01) {
        this.$Message.error('商品价格不允许小于0.01元');
        return false;
      }
      if (price > 50000) {
        this.$Message.error('商品价格不允许大于50000.00元');
        return false;
      }

      if (data.sell_ent_type.length == 0) {
        this.$Message.error('请选择售卖主体');
        return false;
      }

      if (data.xn_scope_tmp.length == 0) {
        this.$Message.error('请选择虚拟商品上架范围');
        return false;
      }

      return true;
    },
    getFormData() {
      let formData = {
        ...this.formData
      };
      formData.xn_scope = this.formData.xn_scope_tmp.length == 2 ? '9' : this.formData.xn_scope_tmp[0];
      delete formData.xn_scope_tmp;
      return formData;
    },
    handleEchoData(goods) {
      this.clone_sell_ent_type = cloneDeep(goods.sell_ent_type);
      this.formData.sell_ent_type = goods.sell_ent_type;
      this.formData.is_recharge_buy = goods.is_recharge_buy;
      this.formData.price = Number(goods.price);
      this.formData.stored_price = goods.stored_price == '' ? null : Number(goods.stored_price);
      for (let key in goods.services ? goods.services : []) {
        goods.services[key].times = Number(goods.services[key].times);
      }
      this.formData.services = goods.services;
      this.formData.service_info = !S.isEmptyObject(goods.service_info)
        ? goods.service_info
        : this.formData.service_info;
      this.formData.service_info.expiration.days = Number(this.formData.service_info.expiration.days);
      this.formData.xn_scope_tmp = goods.xn_scope == '9' ? ['1', '2'] : [goods.xn_scope];
      this.formData.xn_scope = goods.xn_scope;
      this.originFormData = cloneDeep(this.formData);
      this.$delete(this.originFormData, 'xn_scope_tmp');
    },
    getFormDataDiff(obj1, obj2) {
      if (this.$lodash.isEqual(obj1, obj2)) {
        return [];
      } else {
        const res = this.deepDiff(obj1, obj2, ['expiration', 'services']);
        console.log('-> %c res  ===    %o', 'font-size: 15px;color: #fa8c16 ;', res);

        let isExpirationDiff = this.getExpirationDiff(obj1.attrs, obj2.attrs);
        if (isExpirationDiff) {
          res.push('expiration');
        }
        let diffDesc = [];

        if (res.length) {
          diffDesc = res.map(item => this.formDataKeyMap[item]);
        }
        return diffDesc.sort((a, b) => a.sort - b.sort).map(item => item.name) || [];
      }
    },
    getExpirationDiff() {
      const oldExpiration = this.getFormData().service_info.expiration;
      const newExpiration = this.originFormData.service_info.expiration;

      if (oldExpiration.type !== newExpiration.type) {
        return true;
      }

      if (oldExpiration.type === '1') {
        return oldExpiration.days !== newExpiration.days;
      }

      return oldExpiration.date !== newExpiration.date;
    }
  }
};
</script>

<style lang="less" scoped></style>
