<template>
  <div>
    <FormItem label="服务权益">
      <table v-if="echoData.services.length > 0" class="table" style="width: 500px">
        <thead>
          <tr>
            <th>已选服务</th>
            <th>价格</th>
            <th>可用次数</th>
          </tr>
        </thead>
        <tbody>
          <tr v-for="(item, key) in echoData.services" :key="key">
            <td>{{ item.name }}</td>
            <td>{{ item.price | number_format }}</td>
            <td>{{ item.times }}</td>
          </tr>
        </tbody>
      </table>
    </FormItem>
    <div class="block-header"><span>价格库存</span></div>

    <FormItem label="价格">
      <p>¥ {{ echoData.price }}</p>
    </FormItem>
    <FormItem label="储值价" v-show="echoData.is_recharge_buy == 'yes'">
      <p v-if="echoData.stored_price">¥ {{ echoData.stored_price }}</p>
      <p v-else>-</p>
    </FormItem>

    <div class="block-header"><span>服务设置</span></div>

    <FormItem label="商品有效期">
      <p v-if="echoData.service_info.expiration.type == '1'">
        购买后{{ echoData.service_info.expiration.days }}天内有效
      </p>
      <p v-if="echoData.service_info.expiration.type == '2'">截止到{{ echoData.service_info.expiration.date }}日有效</p>
    </FormItem>

    <FormItem label="是否可约">
      <p>{{ echoData.service_info.is_appointment == '1' ? '需要预约' : '免预约' }}</p>
    </FormItem>

    <FormItem label="是否可退">
      <p>{{ echoData.service_info.is_can_refund == '1' ? '可退' : '不可退' }}</p>
    </FormItem>

    <FormItem label="使用时间">
      <p>{{ echoData.service_info.working_time }}</p>
    </FormItem>

    <FormItem label="不可使用日期">
      <p>{{ echoData.service_info.not_working_date }}</p>
    </FormItem>
  </div>
</template>

<script>
import S from 'utils/util';

export default {
  name: 'VirtualGoodsDetail',
  mixins: [],
  components: {},
  props: {
    echoData: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {};
  },
  watch: {
    echoData: {
      handler(val) {
        if (S.isEmptyObject(val)) return;
      },
      immediate: true,
    },
  },
  computed: {},
  mounted() {},
  methods: {},
};
</script>

<style lang="less" scoped></style>
