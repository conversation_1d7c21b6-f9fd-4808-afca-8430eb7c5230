<template>
  <div class="goods-item-wrapper" v-if="pageShow">
    <Row>
      <Col span="24">
        <Form ref="formData" :model="formData" :label-width="130" :label-colon="true">
          <div class="block-header"><span>基本信息</span></div>
          <FormItem label="商品名">
            <p>{{ formData.name }}</p>
          </FormItem>

          <FormItem label="商品类型">
            <p>{{ typeDesc[formData.goods_type]?.desc }}</p>
          </FormItem>

          <FormItem label="商品图">
            <material-picture v-model="formData.slide_imgs" :disabled="true"></material-picture>
            <div class="note">建议尺寸：800*800像素，图片大小不超过3.0M，最多9张，你可以拖拽图片调整顺序</div>
          </FormItem>

          <FormItem label="商品详情" prop="leading_person">
            <material-picture v-model="formData.detail_imgs" :disabled="true"></material-picture>
            <div class="note">图片大小不超过3.0M，最多30张，你可以拖拽图片调整顺序</div>
          </FormItem>

          <template v-if="$route.query.id">
            <!-- 实体商品详情 -->
            <physical-goods-detail
              v-if="formData.goods_type == 10"
              :echo-data="physicalEchoData"
            ></physical-goods-detail>
            <!-- 榕树堂实体商品详情 -->
            <rst-physical-goods-detail v-if="+formData.goods_type === 40" :echo-data="physicalEchoData" />

            <!-- 虚拟商品详情 -->
            <virtual-goods-detail v-if="formData.goods_type == 15" :echo-data="virtualEchoData" />

            <!-- 榕树堂虚拟商品详情 -->
            <rst-virtual-goods-detail v-if="+formData.goods_type === 30" :echo-data="virtualEchoData" />

            <!-- 通兑券详情 -->
            <exchange-coupon-detail
              v-if="formData.goods_type == 25 || formData.goods_type == 45"
              :is-rst="formData.goods_type == 45"
              :echo-data="exchangeCouponEchoData"
            ></exchange-coupon-detail>

            <!-- 套餐详情 -->
            <tao-can-goods-detail v-if="formData.goods_type == 20" :echo-data="taoCanEchoData" />

            <!-- 榕树堂套餐详情 -->
            <rst-tao-can-goods-detail v-if="+formData.goods_type === 35" :echo-data="taoCanEchoData" />
          </template>

          <FormItem label="售卖主体">
            <p>{{ formData.sell_ent_type }}</p>
          </FormItem>
          <FormItem label="上架范围" v-if="formData.goods_type === '10'">
            <p v-if="formData.shelf_scope !== '9'">
              {{ formData.shelf_scope == '1' ? '零售服务' : formData.shelf_scope == '2' ? '问诊治疗' : '-' }}
            </p>
            <p v-else>零售服务 问诊治疗</p>
          </FormItem>

          <FormItem label="上架范围" v-else>
            <p v-if="formData.xn_scope !== '9'">
              {{ formData.xn_scope == '1' ? '零售服务' : formData.xn_scope == '2' ? '问诊治疗' : '-' }}
            </p>
            <p v-else>零售服务 问诊治疗</p>
          </FormItem>

          <FormItem label="储值购买" v-if="!['30', '35', '40', '45'].includes(formData.goods_type)">
            <p>{{ isRechargeBuyDesc[formData.is_recharge_buy].desc }}</p>
          </FormItem>

          <FormItem label="一件代发" v-if="formData.goods_type === '10' && formData.source_platform === 'PLAT'">
            <p>
              {{ isDropShoppingDesc[formData.is_drop_shopping] && isDropShoppingDesc[formData.is_drop_shopping].desc }}
            </p>
          </FormItem>

          <!-- <div class="block-header"><span>修改记录</span></div>
          <Table :columns="changeColumns" :data="changeRecord">
            <template slot="create_time" slot-scope="{ row }">
              {{ row.create_time | date_format }}
            </template>
            <template slot="operator_name" slot-scope="{ row }">
              {{ row.operator_name }}({{ row.operator_role_name }})
            </template>
            <template slot="content_describe" slot-scope="{ row }">
              <div v-if="row.content_describe">
                <span>{{ row.content_describe }}</span>
                <a v-if="isShowDetail && row.diff_columns && row.diff_columns.length" @click="lookUp(row)"
                  >查看更改明细{{ row.audit_status === '3' ? '（未通过）' : '' }}</a
                >
              </div>
              <span v-else>-</span>
            </template>
          </Table> -->
        </Form>
      </Col>
    </Row>

    <div class="block_40"></div>
    <div class="block_40"></div>
    <div class="block_40"></div>
    <div class="fixed-bottom-wrapper">
      <template v-if="formData.source_platform === 'CP'">
        <Button class="mr-10" disabled v-if="formData.is_audit === '1'">审核中</Button>
        <Button
          class="mr-10"
          type="primary"
          @click="updateTemplate"
          v-if="formData.status === '200' && formData.is_audit === '0'"
          >修改模板</Button
        >
      </template>
      <back-button></back-button>
    </div>

    <!--    <log-change v-model="logVisible" :diff-id="diff_id"></log-change>-->
    <!-- <log-change-modal
      v-model="logVisible"
      :after-info="afterLogInfo"
      :before-info="beforeLogInfo"
      :diff-columns="diffColumns"
      :sell-ent-type="sellEntType"
      :is-recharge-buy-desc="isRechargeBuyDesc"
    ></log-change-modal> -->
  </div>
  <div v-else>
    <Spin fix></Spin>
  </div>
</template>

<script>
import S from '@/utils/util'; // Some commonly used tools
import moment from 'moment';
import renderHeader from '@/mixins/renderHeader';
import Picture from '@/components/upload/picture';
import LogChangeModal from './components/LogInfo/LogChangeNew.vue';
// import cloneDeep from 'lodash/cloneDeep';

// 实体商品详情
import PhysicalGoodsDetail from './components/physicalGoods/PhysicalGoodsDetail.vue';
// 榕树堂实体商品详情
import RstPhysicalGoodsDetail from './components/physicalGoods/RstPhysicalGoodsDetail.vue';
// 虚拟商品详情
import VirtualGoodsDetail from './components/virtralGoods/VirtualGoodsDetail.vue';
// 榕树堂虚拟商品详情
import RstVirtualGoodsDetail from './components/virtralGoods/RstVirtualGoodsDetail.vue';
// 兑换券详情
import ExchangeCouponDetail from './components/exchangeCoupon/ExchangeCouponDetail.vue';
// 套餐详情
import TaoCanGoodsDetail from './components/taocanGoods/TaoCanGoodsDetail.vue';
// 榕树堂套餐详情
import RstTaoCanGoodsDetail from './components/taocanGoods/RstTaoCanGoodsDetail.vue';
import MyMaterial from '../../../components/MaterialCenter/MyMaterial.vue';

const init_form_data = {
  id: '',
  name: '',
  goods_type: 10,
  sell_ent_type: '',
  desc: '',
  slide_imgs: [],
  detail_imgs: [],
  specs_data: [],
  attrs: [],
  price: 0,
  is_recharge_buy: 'yes', // 虚拟服务的储值购买
  stored_price: null, // 虚拟服务的储值价
  services: [],
  xn_scope_tmp: ['1', '2'],
  xn_scope: '',
  sw_scope_tmp: ['1', '2'],
  shelf_scope: '',
  r: ''
};

export default {
  name: 'edit',
  components: {
    MyMaterial,
    Picture,
    LogChangeModal,
    PhysicalGoodsDetail,
    VirtualGoodsDetail,
    ExchangeCouponDetail,
    TaoCanGoodsDetail,
    RstTaoCanGoodsDetail,
    RstVirtualGoodsDetail,
    RstPhysicalGoodsDetail
  },
  mixins: [renderHeader],

  computed: {
    // 是否展示查看更多
    isShowDetail() {
      return true;
    }
  },

  data() {
    return {
      pageShow: false,
      formData: {
        service_info: {
          expiration: {
            type: '1', // 1 购买后${days}天内有效；2 ${date}日期前有效；
            date: '', //
            days: 365 //
          },
          is_appointment: '1', // 1免预约 2需要预约
          is_can_refund: '1', // 1不可退 2可退
          working_time: '', // 工作时间
          not_working_date: '', // 不可使用日期：
          note_rule: '' // 使用规则
        },
        shelf_scope: '1' // 上架范围
        // ...cloneDeep(init_form_data),
      },

      storedList: [], // 储值购买枚举
      isRechargeBuyDesc: {},
      xnScopeDesc: [],

      // 修改记录
      changeColumns: [
        { title: '时间', slot: 'create_time', align: 'center', width: 140 },
        { title: '操作人', slot: 'operator_name', align: 'center', width: 100 },
        { title: '操作记录', slot: 'content_describe' }
      ],
      changeRecord: [],
      logVisible: false, // 日志变更弹框
      diff_id: '',
      // 详情数据
      physicalEchoData: {}, // 实体商品详情回显数据
      virtualEchoData: {}, // 虚拟商品回显数据
      exchangeCouponEchoData: {}, // 通兑券详情
      taoCanEchoData: {}, // 套餐详情
      typeDesc: {}, // 商品类型
      sellEntType: {}, // 售卖主体
      beforeLogInfo: {},
      afterLogInfo: {},
      diffColumns: [],

      source_platform: '',
      isDropShoppingDesc: [], // 平台代发枚举
      goods_type: ''
    };
  },

  created() {
    this.getIndexOptions();
    if (this.$route.query.id) {
      // 编辑时才有的参数
      this.formData.id = this.$route.query.id;
    }

    if (this.formData.id) {
      this.get();
    } else {
      this.pageShow = true;
    }
  },
  mounted() {},
  methods: {
    // 修改模板
    updateTemplate() {
      this.$router.push({
        path: '/goods/warehouse/edit',
        query: {
          type: 'update',
          id: this.formData.id
        }
      });
    },
    get: function () {
      let params = { id: this.formData.id };
      this.$api
        .getGoodsLibInfo(params)
        .then(data => {
          this.goods_type = data.goods.goods_type;
          let goods = data.goods;
          // 公共数据
          this.formData.source_platform = goods.source_platform; // 商品来源
          this.formData.is_audit = goods.is_audit; // 是否审核中
          this.formData.status = goods.status;
          this.formData.id = goods.id;
          this.formData.name = goods.name;
          this.formData.goods_type = goods.goods_type;
          this.formData.sell_ent_type = goods.sell_ent_type_text;
          this.formData.slide_imgs = goods.slide_imgs;
          this.formData.detail_imgs = goods.detail_imgs;

          // 设置的公共数据
          this.formData.xn_scope = goods.xn_scope;
          this.formData.is_recharge_buy = goods.is_recharge_buy;
          this.formData.is_drop_shopping = goods.is_drop_shopping;
          this.formData.sw_scope_tmp = goods.shelf_scope == '9' ? ['1', '2'] : [goods.shelf_scope];
          this.formData.shelf_scope = goods.shelf_scope;

          this.formData.service_info = !S.isEmptyObject(goods.service_info)
            ? goods.service_info
            : this.formData.service_info;
          this.formData.service_info.expiration.days = Number(this.formData.service_info.expiration.days);
          this.formData.service_info.expiration.date =
            this.formData.service_info.expiration.date === ''
              ? '-'
              : moment(this.formData.service_info.expiration.date).format('YYYY-MM-DD');

          // 实体商品数据
          if (this.formData.goods_type === '10' || this.formData.goods_type === '40') {
            this.formData.xn_scope = goods.shelf_scope;
            this.physicalEchoData = {
              specs_data: goods.specs_data,
              tableData: goods.attrs,
              is_recharge_buy: goods.is_recharge_buy,
              is_drop_shopping: goods.is_drop_shopping,
              relation_card: goods.relation_card,
              grant_type: goods.grant_type,
              services: goods.services
            };
          }

          // 虚拟商品数据
          if (this.formData.goods_type === '15' || this.formData.goods_type === '30') {
            for (let key in goods.services ? goods.services : []) {
              goods.services[key].times = Number(goods.services[key].times);
            }

            this.virtualEchoData = {
              services: goods.services,
              price: goods.price,
              stored_price: goods.stored_price,
              is_recharge_buy: goods.is_recharge_buy,
              service_info: this.formData.service_info,
              not_first_tier_vip_price: goods.not_first_tier_vip_price,
              not_first_tier_price: goods.not_first_tier_price,
              first_tier_vip_price: goods.first_tier_vip_price,
              first_tier_price: goods.first_tier_price,

              opc_price_list: goods.opc_price_list,
              opc_price: goods.opc_price
            };
          }

          // 通兑券数据
          console.log('🚀 ~goods=>', goods);
          if (this.formData.goods_type === '25' || this.formData.goods_type == '45') {
            this.exchangeCouponEchoData = {
              exchange_num: goods.exchange_num,
              services: goods.services,
              remark: goods.remark,
              price: goods.price,
              stored_price: goods.stored_price,
              is_recharge_buy: goods.is_recharge_buy,
              service_info: this.formData.service_info,
              not_first_tier_vip_price: goods.not_first_tier_vip_price,
              not_first_tier_price: goods.not_first_tier_price,
              first_tier_vip_price: goods.first_tier_vip_price,
              first_tier_price: goods.first_tier_price,
              opc_price_list: goods.opc_price_list,
              opc_price: goods.opc_price
            };
          }

          // 套餐数据
          if (this.formData.goods_type === '20' || this.formData.goods_type === '35') {
            this.taoCanEchoData = {
              tc_infos: goods.tc_infos,
              price: goods.price,
              stored_price: goods.stored_price,
              is_recharge_buy: goods.is_recharge_buy,
              relation_card: goods.relation_card,
              grant_type: goods.grant_type,
              services: goods.services,
              not_first_tier_vip_price: goods.not_first_tier_vip_price,
              not_first_tier_price: goods.not_first_tier_price,
              first_tier_vip_price: goods.first_tier_vip_price,
              first_tier_price: goods.first_tier_price,

              opc_price_list: goods.opc_price_list,
              opc_price: goods.opc_price
            };
          }

          if (goods.source_platform === 'PLAT') {
            this.source_platform = 'PLAT_GOODS';
          } else if (goods.source_platform === 'COM') {
            this.source_platform = 'COM_GOODS';
          } else if (goods.source_platform === 'CP') {
            this.source_platform = 'CP_GOODS';
          }

          this.getGoodsOperationlog();
          this.pageShow = true;
        })
        .catch(error => {
          this.$Message.error(error.errmsg);
        });
    },

    // 储值购买枚举值
    getIndexOptions() {
      this.$api.getGoodsLibOptions().then(res => {
        this.storedList = S.descToArrHandle(res.isRechargeBuyDesc);
        this.isDropShoppingDesc = res.isDropShoppingDesc;
        this.isRechargeBuyDesc = res.isRechargeBuyDesc;
        this.sellEntType = res.sellEntType;
        this.typeDesc = res.typeDesc;
        this.xnScopeDesc = res.xnScopeDesc;
      });
    },

    // 获取操作记录
    getGoodsOperationlog() {
      let params = { type_id: this.$route.query.id, type: this.source_platform, page: 1, pageSize: 100 };
      this.$api.getGoodsOperationlog(params).then(res => {
        this.changeRecord = res.list;
        if (res.list.length && res.list[0].diff_columns.length) {
          let item = res.list[0];
          this.diffColumns = item.diff_columns;
          this.beforeLogInfo = item.snapshot.before;
          this.afterLogInfo = item.snapshot.after;
        }
      });
    },

    lookUp(item) {
      this.diffColumns = item.diff_columns;
      this.beforeLogInfo = item.snapshot.before;
      this.afterLogInfo = item.snapshot.after;
      this.logVisible = true;
    }
  }
};
</script>

<style lang="less" scoped>
.goods-item-wrapper {
  .ivu-input-wrapper,
  .ivu-select {
    width: 80%;
    max-width: none;
  }

  .ivu-input {
    max-width: none;
  }

  .ks-goods-type {
    position: relative;
    display: inline-block;
    cursor: pointer;
    margin: 0 0 0 10px;
    text-align: center;
    width: 115px;
    border-radius: 2px;
    border: 1px solid #cacaca;
    padding: 8px 0;
  }

  .ks-goods-type.active {
    border-color: #155bd4;
  }

  .ks-goods-type.active:after {
    content: '';
    display: inline-block;
    position: absolute;
    width: 24px;
    height: 24px;
    bottom: -1px;
    right: -1px;
    background: url(https://img-sn-i01s-cdn.rsjxx.com/image/2021/0224/155801_7284881.png) no-repeat;
    background-size: 24px auto;
  }

  .ks-goods-type_name {
    display: block;
    font-weight: bold;
  }

  .ks-goods-type_desc {
    display: block;
    color: #999;
  }

  .ks-input-number {
    width: 100px;

    .ivu-input-number-input {
      text-align: center;
    }
  }

  .ivu-date-picker-editor {
    width: 100% !important;
  }
}

::v-deep .picture-display {
  display: inline-block;
}

::v-deep .ivu-form-item {
  margin-bottom: 10px;

  // .ivu-form-item-label {
  //   width: 170px !important;
  // }

  // .ivu-form-item-content {
  //   margin-left: 170px !important;
  // }
}

.ivu-table-first-col {
  border-left: 1px solid #e8eaec;
  border-right: 1px solid #e8eaec;
}
</style>
<style>
.table-col-right-border {
  border-right: 1px solid #e8eaec;
}

.table-col-border {
  border-right: 1px solid #e8eaec;
  border-left: 1px solid #e8eaec;
}

.table-col-left-border {
  border-left: 1px solid #e8eaec;
}
</style>
