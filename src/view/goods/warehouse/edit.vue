<template>
  <div class="goods-item-wrapper" v-if="pageShow">
    <div class="block-header"><span>商品类型</span></div>
    <div
      v-if="!formData.id || (formData.id && formData.goods_type == 10)"
      class="ks-goods-type"
      :class="{ active: formData.goods_type == 10 }"
      @click="formData.goods_type = '10'"
    >
      <span>
        <span class="ks-goods-type_name">实物商品</span>
        <span class="ks-goods-type_desc">(物流发货)</span>
      </span>
    </div>

    <div
      v-if="!formData.id || (formData.id && formData.goods_type == 15)"
      class="ks-goods-type"
      :class="{ active: formData.goods_type == 15 }"
      @click="formData.goods_type = '15'"
    >
      <span>
        <span class="ks-goods-type_name">虚拟商品</span>
        <span class="ks-goods-type_desc">(无需物流)</span>
      </span>
    </div>
    <div style="display: inline-block">
      <div
        v-if="!formData.id || (formData.id && formData.goods_type == 25)"
        class="ks-goods-type"
        :class="{ active: formData.goods_type == 25 }"
        @click="formData.goods_type = '25'"
      >
        <span>
          <span class="ks-goods-type_name">通兑券</span>
          <span class="ks-goods-type_desc">(仅限兑换虚拟商品)</span>
        </span>
      </div>

      <div
        v-if="!formData.id || (formData.id && formData.goods_type == 20)"
        class="ks-goods-type"
        :class="{ active: formData.goods_type == 20 }"
        @click="formData.goods_type = '20'"
      >
        <span>
          <span class="ks-goods-type_name">套餐商品</span>
          <span class="ks-goods-type_desc">商品组合售卖</span>
        </span>
      </div>
    </div>

    <goods-base ref="goodsBase" :base-info="baseInfo" />

    <component
      :is="getComponentInfo.componentName"
      :ref="getComponentInfo.ref"
      :stored-list="storedList"
      :echo-data="goodsFormData"
      :sell-ent-types="sellEntTypes"
      :is-drop-shopping-desc="isDropShoppingDesc"
    ></component>

    <!--    <physical-goods-->
    <!--      :stored-list="storedList"-->
    <!--      :echo-data="goodsFormData"-->
    <!--      :sell-ent-types="sellEntTypes"-->
    <!--      :is-drop-shopping-desc="isDropShoppingDesc"-->
    <!--      v-show="formData.goods_type === '10'"-->
    <!--      ref="physicalGoods"-->
    <!--    ></physical-goods>-->

    <!--    <virtual-goods-->
    <!--      :stored-list="storedList"-->
    <!--      :sell-ent-types="sellEntTypes"-->
    <!--      v-show="formData.goods_type === '15'"-->
    <!--      :echo-data="goodsFormData"-->
    <!--      ref="virtualGoods"-->
    <!--    ></virtual-goods>-->

    <!--    &lt;!&ndash; 通兑券 &ndash;&gt;-->
    <!--    <exchange-coupon-->
    <!--      :stored-list="storedList"-->
    <!--      :sell-ent-types="sellEntTypes"-->
    <!--      :echo-data="goodsFormData"-->
    <!--      v-show="formData.goods_type == 25"-->
    <!--      ref="exchangeCoupon"-->
    <!--    >-->
    <!--    </exchange-coupon>-->

    <!--    &lt;!&ndash; 套餐商品 &ndash;&gt;-->
    <!--    <tao-can-goods-->
    <!--      :stored-list="storedList"-->
    <!--      :sell-ent-types="sellEntTypes"-->
    <!--      :echo-data="goodsFormData"-->
    <!--      v-show="formData.goods_type == 20"-->
    <!--      ref="taocanGoods"-->
    <!--    ></tao-can-goods>-->

    <div class="block_40"></div>
    <div class="block_40"></div>
    <div class="block_40"></div>

    <div class="fixed-bottom-wrapper">
      <Button @click="updateSubmit" v-if="isFromUpdateTemplate" class="mr-10" type="primary" :loading="saveUpBtnLoading"
        >保存并提交更新审核
      </Button>
      <template v-else>
        <Button @click="onSave()" class="mr-10" type="primary" :loading="saveBtnLoading">暂存为草稿</Button>
        <Button @click="submit" class="mr-10" type="primary" :loading="saveUpBtnLoading">保存并提交初始化审核</Button>
      </template>
      <back-button></back-button>
    </div>

    <!-- 提交二次确认 -->
    <submit-confirm-modal
      :objSellEntTypes="objSellEntTypes"
      :sell_ent_type="checked_sell_ent_type"
      v-model="submitConfirmVisible"
      @success="onSave('1')"
    ></submit-confirm-modal>

    <!-- 修改模板后，字段异动提示弹窗 -->
    <update-confirm-modal
      v-model="updateConfirmVisible"
      @success="onSave()"
      :diff-data="diffData"
    ></update-confirm-modal>
  </div>
  <div v-else>
    <Spin fix></Spin>
  </div>
</template>

<script>
import S from '@/utils/util';
// 基础数据
import GoodsBase from './components/GoodsBase.vue';
// 实体商品
import PhysicalGoods from './components/physicalGoods/PhysicalGoods.vue';
// 虚拟商品
import VirtualGoods from './components/virtralGoods/VirtualGoods.vue';
// 通兑券
import ExchangeCoupon from './components/exchangeCoupon/ExchangeCoupon.vue';
// 套餐商品
import TaoCanGoods from './components/taocanGoods/TaoCanGoods.vue';
// 提交二次确认
import SubmitConfirmModal from './components/SubmitConfirmModal.vue';
// 修改模板后，字段异动提示弹窗
import UpdateConfirmModal from './components/UpdateConfirmModal.vue';
// 异动数据检测
import DataMove from './mixins/DataMove';

export default {
  name: 'edit',
  mixins: [DataMove],
  components: {
    GoodsBase,
    VirtualGoods,
    PhysicalGoods,
    ExchangeCoupon,
    TaoCanGoods,
    SubmitConfirmModal,
    UpdateConfirmModal
  },

  computed: {
    // 是否来源于修改模板页面
    isFromUpdateTemplate() {
      return this.$route.query.type === 'update';
    },
    getComponentInfo() {
      return this.goodsTypeMaps[this.formData.goods_type];
    }
  },
  watch: {},

  data() {
    return {
      pageShow: false,
      formData: {
        id: '',
        goods_type: '10'
      },
      goodsFormData: {},
      getLoading: true,
      saveBtnLoading: false, // 保存按钮状态
      saveUpBtnLoading: false, // 保存并发布按钮状态
      storedList: [], // 储值购买枚举
      sellEntTypes: [], // 售卖主体枚举
      objSellEntTypes: {},
      isDropShoppingDesc: [], // 平台代发枚举
      baseInfo: {
        name: '',
        slide_imgs: [],
        detail_imgs: []
      },

      submitConfirmVisible: false, // 提交二次确认
      updateConfirmVisible: false, // 修改模板后，字段异动提示弹窗

      goodsTypeMaps: {
        10: { componentName: 'PhysicalGoods', ref: 'physicalGoods' },
        15: { componentName: 'VirtualGoods', ref: 'virtualGoods' },
        20: { componentName: 'TaoCanGoods', ref: 'taocanGoods' },
        25: { componentName: 'ExchangeCoupon', ref: 'exchangeCoupon' }
      },
      diffData: [], // 异动数据

      checked_sell_ent_type: [] // 勾选的售卖主体
    };
  },

  created() {
    this.getIndexOptions();
    if (this.$route.query.id) {
      // 编辑时才有的参数
      this.formData.id = this.$route.query.id;
    }

    if (this.formData.id) {
      this.get();
    } else {
      this.pageShow = true;
    }
  },
  mounted() {
    console.log(this.formData);
  },
  methods: {
    storedPriceChange(val) {
      if (Number(val) == 0) {
        this.formData.stored_price = null;
        this.$refs.stored.currentValue = null;
      }
    },

    getFormData: function () {
      if (!this.$refs.goodsBase.validateForm()) {
        return false;
      }
      // 实体商品校验拦截
      if (this.formData.goods_type == 10 && !this.$refs.physicalGoods.validateForm()) {
        return false;
      }
      // 虚拟商品校验拦截
      if (this.formData.goods_type == 15 && !this.$refs.virtualGoods.validateForm()) {
        return false;
      }
      // 通兑券校验拦截
      if (this.formData.goods_type == 25 && !this.$refs.exchangeCoupon.validateForm()) {
        return false;
      }
      // 套餐校验拦截
      if (this.formData.goods_type == 20 && !this.$refs.taocanGoods.validateForm()) {
        return false;
      }

      let formData = {
        ...this.formData,
        ...this.$refs.goodsBase.formData
      };

      if (this.formData.goods_type === '10') {
        formData = { ...formData, ...this.$refs.physicalGoods.getFormData() };
      }
      if (this.formData.goods_type === '15') {
        formData = { ...formData, ...this.$refs.virtualGoods.getFormData() };
      }
      // 合并通兑券数据
      if (this.formData.goods_type === '25') {
        formData = { ...formData, ...this.$refs.exchangeCoupon.getFormData() };
      }
      // 合并套餐数据
      if (this.formData.goods_type === '20') {
        formData = { ...formData, ...this.$refs.taocanGoods.getFormData() };
      }

      return formData;
    },

    submit() {
      let formData = this.getFormData();
      if (!formData) {
        return;
      }
      this.checked_sell_ent_type = formData.sell_ent_type || [];
      this.submitConfirmVisible = true;
    },

    updateSubmit() {
      let formData = this.getFormData();
      if (!formData) {
        return;
      }

      let diffData = [...this.$refs.goodsBase.getDiffData(), ...this.$refs[this.getComponentInfo.ref].getDiffData()];
      console.log('-> %c diffData  ===    %o', 'font-size: 15px;color: #fa8c16 ;', diffData);
      if (diffData && diffData.length) {
        this.diffData = diffData;
        this.updateConfirmVisible = true;
        return;
      }
      this.onSave();
    },

    onSave: function (up = '') {
      let formData = this.getFormData();
      if (!formData) {
        return;
      }

      if (up === '1') {
        this.saveUpBtnLoading = true;
      } else {
        this.saveBtnLoading = true;
      }
      let params = {
        up: up, // 1:表示分发
        ...formData,
        stored_price: formData.stored_price == null ? '' : formData.stored_price
      };

      // 不支持储值价时，储值价滞空
      if (params.is_recharge_buy === 'no') {
        params.stored_price = '';
      }

      console.log('-> params', params);

      this.$api
        .changeGoodsLib(params)
        .then(() => {
          this.$Message.success({
            content: '保存成功'
          });
          this.$router.push({
            path: '/goods/warehouse/list'
          });
        })
        .catch(error => {
          this.$Message.error(error.errmsg);
        })
        .finally(() => {
          this.saveBtnLoading = false;
          this.saveUpBtnLoading = false;
        });
    },

    get: function () {
      let params = { id: this.formData.id };
      this.$api
        .getGoodsLibInfo(params)
        .then(data => {
          let goods = data.goods;
          this.formData.id = goods.id;
          this.formData.goods_type = goods.goods_type;

          // 基础信息回显
          this.baseInfo.name = goods.name;
          this.baseInfo.detail_imgs = goods.detail_imgs;
          this.baseInfo.slide_imgs = goods.slide_imgs;
          this.goodsFormData = goods;

          // 获取冻结数据
          // this.getFreezeData(goods)

          this.pageShow = true;
        })
        .catch(error => {
          this.$Message.error(error.errmsg);
        });
    },

    // 储值购买枚举值
    getIndexOptions() {
      this.$api.getGoodsLibOptions().then(res => {
        this.storedList = S.descToArrHandle(res.isRechargeBuyDesc);
        this.sellEntTypes = S.descToArrHandle(res.sellEntType);
        this.objSellEntTypes = res.sellEntType;
        this.isDropShoppingDesc = S.descToArrHandle(res.isDropShoppingDesc);
      });
    }
  }
};
</script>

<style lang="less" scoped>
.goods-item-wrapper {
  .ivu-input-wrapper,
  .ivu-select {
    width: 80%;
    max-width: none;
  }

  .ivu-input {
    max-width: none;
  }

  .ks-goods-type {
    position: relative;
    display: inline-block;
    cursor: pointer;
    margin: 0 0 0 10px;
    text-align: center;
    width: 115px;
    border-radius: 2px;
    border: 1px solid #cacaca;
    padding: 8px 0;
  }

  .ks-goods-type.active {
    border-color: #155bd4;
  }

  .ks-goods-type.active:after {
    content: '';
    display: inline-block;
    position: absolute;
    width: 24px;
    height: 24px;
    bottom: -1px;
    right: -1px;
    background: url(https://img-sn-i01s-cdn.rsjxx.com/image/2021/0224/155801_7284881.png) no-repeat;
    background-size: 24px auto;
  }

  .ks-goods-type_name {
    display: block;
    font-weight: bold;
  }

  .ks-goods-type_desc {
    display: block;
    color: #999;
  }

  .ks-input-number {
    width: 100px;

    .ivu-input-number-input {
      text-align: center;
    }
  }

  .ivu-date-picker-editor {
    width: 100% !important;
  }
}

::v-deep .picture-display {
  display: inline-block;
}
</style>
