<template>
  <div>
    <Form inline :label-width="0" @submit.native.prevent @keyup.enter.native="onSearch">
      <Row>
        <Col>
          <FormItem>
            <Input type="text" v-model="queryFormData.name" placeholder="请输入商品名称" />
          </FormItem>
        </Col>

        <Col>
          <FormItem>
            <Select v-model="queryFormData.goods_type" placeholder="请选择商品类型" clearable>
              <Option v-for="item in typeDesc" :key="item.kw" :value="item.id">{{ item.desc }}</Option>
            </Select>
          </FormItem>
        </Col>

        <Col>
          <FormItem>
            <Select v-model="queryFormData.sell_ent_type" placeholder="请选择售卖主体" clearable>
              <Option v-for="item in sellEntType" :key="item.kw" :value="item.id">{{ item.desc }}</Option>
            </Select>
          </FormItem>
        </Col>

        <Col>
          <FormItem>
            <Select v-model="queryFormData.source_platform" placeholder="请选择商品来源" clearable>
              <Option v-for="item in sourcePlatformDesc" :key="item.kw" :value="item.id">{{ item.desc }}</Option>
            </Select>
          </FormItem>
        </Col>

        <Col>
          <FormItem>
            <Button type="primary" @click="onSearch">筛选</Button>
            <Dvd />
            <Dvd />
            <Dvd />
            <Button @click="onResetSearch">重置</Button>
          </FormItem>
        </Col>
      </Row>
    </Form>

    <div class="table-wrapper">
      <div class="table-fun">
        <Button type="primary" @click="createGoods">新建商品</Button>
      </div>
      <div class="panel-nav">
        <a class="nav" :class="{ active: !$route.query.valid_sale_status }" @click.prevent.capture="onStatusChange('')">
          全部
        </a>
        <a
          class="nav"
          v-for="item in statusValidSaleDesc"
          :key="item.id"
          :class="{ active: $route.query.valid_sale_status == item.id }"
          @click.prevent.capture="onStatusChange(item.id)"
        >
          {{ item.desc }}
          <Tag :color="getTagColor(item.id)">{{ listCount?.[item.id] }}</Tag>
        </a>
      </div>
      <Table :loading="tableLoading" :columns="tableCols" :data="list" :height="$store.state.app.clientHeight - 320">
        <template slot-scope="{ row }" slot="info">
          <div class="media-left media-middle">
            <img
              :src="row.main_img | imageStyle('B.w300')"
              style="width: 35px; margin-right: 5px"
              class="img-rounded"
            />
          </div>
          <div class="media-body media-middle">
            <div class="_3U0W">
              <KLink :to="{ path: '/goods/warehouse/edit', query: { id: row.id } }">{{ row.name }}</KLink>
            </div>
          </div>
        </template>
        <template slot-scope="{ row }" slot="price_section">
          <template v-if="['40'].includes(row.goods_type)">
            <div style="margin: 4px 0">
              <span v-if="!row?.opc_price_section?.first_tier_price?.max">
                <span>一&nbsp;&nbsp;&nbsp;线</span>
                <span>：￥{{ row.opc_price_section.first_tier_price.min }}</span>
              </span>
              <span v-else>
                <span>一&nbsp;&nbsp;&nbsp;线</span>
                <span>：￥{{ row.opc_price_section.first_tier_price.min }}</span>
                <span style="margin: 0 2px">-</span>
                <span>￥{{ row.opc_price_section.first_tier_price.max }}</span>
              </span>
              <span style="margin-left: 4px" v-if="!row?.opc_price_section?.first_tier_vip_price?.max">
                <span>会员价：</span>
                <span>￥{{ row.opc_price_section.first_tier_vip_price.min }}</span>
              </span>
              <span style="margin-left: 4px" v-else>
                <span>会员价：</span>
                <span>￥{{ row.opc_price_section.first_tier_vip_price.min }}</span>
                <span style="margin: 0 2px">-</span>
                <span>￥{{ row.opc_price_section.first_tier_vip_price.max }}</span>
              </span>
            </div>
            <div style="margin: 4px 0">
              <span v-if="!row?.opc_price_section?.not_first_tier_price?.max">
                <span>非一线</span>
                <span>：￥{{ row.opc_price_section.not_first_tier_price.min }}</span>
              </span>
              <span v-else>
                <span>非一线</span>
                <span>：￥{{ row.opc_price_section.not_first_tier_price.min }}</span>
                <span style="margin: 0 2px">-</span>
                <span>￥{{ row.opc_price_section.not_first_tier_price.max }}</span>
              </span>
              <span style="margin-left: 4px" v-if="!row?.opc_price_section?.not_first_tier_vip_price?.max">
                <span>会员价：</span>
                <span>￥{{ row.opc_price_section.not_first_tier_vip_price.min }}</span>
              </span>
              <span style="margin-left: 4px" v-else>
                <span>会员价：</span>
                <span>￥{{ row.opc_price_section.not_first_tier_vip_price.min }}</span>
                <span style="margin: 0 2px">-</span>
                <span>￥{{ row.opc_price_section.not_first_tier_vip_price.max }}</span>
              </span>
            </div>
          </template>
          <template v-else>
            <div style="margin: 4px 0" v-if="!row.price_section.max">￥{{ row.price_section.min }}</div>
            <div style="margin: 4px 0" v-else>￥{{ row.price_section.min }} - ￥{{ row.price_section.max }}</div>
          </template>
        </template>

        <!-- 储值价 -->
        <template slot-scope="{ row }" slot="stored_price_section">
          <p v-if="!row.stored_price || row.is_recharge_buy === 'no'">-</p>
          <template v-else>
            <p v-if="!row.stored_price_section.max">￥{{ row.stored_price_section.min }}</p>
            <p v-else>￥{{ row.stored_price_section.min }} - ￥{{ row.stored_price_section.max }}</p>
          </template>
        </template>

        <template slot-scope="{ row }" slot="is_drop_shopping_text">
          {{ row.is_drop_shopping_text || '-' }}
        </template>

        <template slot-scope="{ row }" slot="stock">
          {{ row.stock }}
        </template>
        <template slot-scope="{ row }" slot="sales">
          {{ row.sales }}
        </template>

        <template slot-scope="{ row }" slot="status_text">
          <mark-status :type="getStatusTextColor(row.status)">{{ row.status_text }}</mark-status>
        </template>

        <template slot-scope="{ row }" slot="create_time">
          {{ row.create_time | date_format }}
        </template>

        <template slot-scope="{ row }" slot="update_time">
          {{ row.update_time | date_format }}
        </template>

        <template slot-scope="{ row }" slot="status">
          {{ statusDesc[row.status].desc }}
        </template>
        <template slot-scope="{ row }" slot="operate">
          <!--          <Poptip confirm transfer title="确定分发？" @on-ok="shelfUp(row.id)" v-if="row.status === '800'">-->
          <!--            <a>分发</a>-->
          <!--          </Poptip>-->
          <template v-if="!['30', '35', '40'].includes(row.goods_type)">
            <KLink
              :to="{ path: '/goods/warehouse/edit', query: { id: row.id } }"
              class="mr-10"
              v-if="row.source_platform === 'CP' && row.status === '800' && row.is_audit !== '1'"
            >
              编辑
            </KLink>
          </template>
          <KLink :to="{ path: '/goods/warehouse/detail', query: { id: row.id } }" class="mr-10">详情</KLink>
          <template v-if="!['30', '35', '40'].includes(row.goods_type)">
            <Poptip confirm transfer title="确定复制？" @on-ok="onCopy(row.id)">
              <a>复制</a>
            </Poptip>
          </template>
        </template>
      </Table>
    </div>

    <div class="block_20"></div>
    <!-- <div class="batch flex flex-item-between"> -->
    <!-- <div class="batch_l">
        <Button>批量分发</Button>
        <span class="ml10">已选中1个商品</span>
      </div> -->
    <KPage
      :current="+queryFormData.page"
      :page-size="+queryFormData.pageSize"
      :total="total"
      style="text-align: center"
      @on-change="handleCurrentChange"
      @on-page-size-change="handleSizeChange"
    />
    <!-- </div> -->
  </div>
</template>

<script>
import search from '@/mixins/search';
import S from 'utils/util';
import renderHeader from '@/mixins/renderHeader';

let init_query_form_data = {
  page: 1,
  pageSize: 20,
  name: '',
  goods_type: '',
  sell_ent_type: '',
  source_platform: '',
  valid_sale_status: '',
  r: ''
};
export default {
  name: 'list',
  components: {},
  mixins: [search, renderHeader],
  props: {},
  data() {
    return {
      apiName: 'getGoodsLibList',
      queryFormData: { ...init_query_form_data },
      tableCols: [
        // { type: 'selection',minWidth: 30 },
        { title: 'ID', key: 'id', minWidth: 50 },
        { title: '商品', key: 'name', minWidth: 80 },
        { title: '售卖主体', key: 'sell_ent_type_text', align: 'center', minWidth: 80 },
        { title: '商品类型', key: 'goods_type_text', minWidth: 70 },
        { title: '商品来源', key: 'source_platform_text', minWidth: 80 },
        { title: '默认零售价', slot: 'price_section', align: 'center', minWidth: 244 },
        { title: '默认储值价', slot: 'stored_price_section', align: 'center', minWidth: 80 },
        { title: '平台代发', slot: 'is_drop_shopping_text', align: 'center', minWidth: 80 },
        {
          title: '在售门店数',
          key: 'on_sale_num',
          minWidth: 100,
          align: 'center',
          renderHeader: (h, params) =>
            this._renderHeader(h, params, '在售门店数指已将商品添加到商品列表，且状态为上架中的门店数量')
        },
        {
          title: '销量',
          key: 'sales',
          minWidth: 80,
          align: 'center',
          renderHeader: (h, params) => this._renderHeader(h, params, '销量指商品的销售数量，不考虑后续发生的退款/退货')
        },
        { title: '分发状态', slot: 'status_text', minWidth: 80 },
        { title: '创建时间', slot: 'create_time', minWidth: 130, align: 'center' },
        { title: '更新时间', slot: 'update_time', minWidth: 130, align: 'center' },
        // { title: '商品状态', key: 'status', minWidth: 50 },
        { title: '操作', slot: 'operate', align: 'left', width: 160 }
      ],
      tableLoading: false,

      list: [],
      total: 0,
      typeDesc: {},
      sellEntType: [], // 售卖主体
      // statusDesc: [],

      listCount: {},
      statusValidSaleDesc: {}, // 状态枚举
      sourcePlatformDesc: {}
    };
  },
  computed: {
    getTagColor() {
      return type => {
        switch (type) {
          case 'VALID_SALE_WAIT':
            return 'warning';
          case 'VALID_SALE_COMPLETE':
            return 'success';
          case 'VALID_SALE_UPDATE':
            return 'error';
          default:
            return 'info';
        }
      };
    },
    getStatusTextColor() {
      return status => {
        switch (status) {
          case '200':
            return 'success';
          case '800':
            return 'warn';
        }
      };
    }
  },
  watch: {},
  created() {
    this.getOptionsList();
    this.queryFormData = S.merge(this.queryFormData, this.$route.query);
    this.submitQueryForm(true);
  },
  mounted() {},
  methods: {
    // 状态改变调用接口
    onStatusChange(valid_sale_status) {
      this.queryFormData.page = 1;
      this.queryFormData.valid_sale_status = valid_sale_status;
      this.submitQueryForm();
    },

    handlerListData(data) {
      this.listCount = data?.list_count || {};
    },

    // 新建商品
    createGoods() {
      // this.$Message.success('新建商品')
      this.$router.push({
        path: '/goods/warehouse/edit'
      });
    },
    // 重置
    onResetSearch() {
      this.queryFormData = { ...init_query_form_data };
      this.submitQueryForm();
    },

    // 获取枚举值
    getOptionsList() {
      this.$api.getGoodsLibOptions().then(res => {
        this.typeDesc = S.descToArrHandle(res.typeDesc);
        this.statusDesc = S.descToArrHandle(res.statusDesc);
        this.statusValidSaleDesc = S.descToArrHandle(res.statusValidSaleDesc);
        this.sellEntType = S.descToArrHandle(res.sellEntType);
        this.sourcePlatformDesc = S.descToArrHandle(res.sourcePlatformDesc);
      });
    },

    onCopy(id) {
      let params = { id };
      this.$api
        .copyGoodsLib(params)
        .then(() => {
          this.$Message.success('复制成功');
          this.submitQueryForm(true);
        })
        .catch(error => {
          this.$Message.error(error.errmsg);
        });
    }

    // shelfUp(id) {
    //   let params = { id, act: 'UP' };
    //   this.$api
    //     .changeGoodsLibStatus(params)
    //     .then(res => {
    //       this.$Message.success('分发成功');
    //       this.submitQueryForm(true);
    //     })
    //     .catch(error => {
    //       this.$Message.error(error.errmsg);
    //     });
    // },
  },
  beforeRouteUpdate(to, from, next) {
    this.queryFormData = S.merge(init_query_form_data, to.query);
    this.loadList();
    next();
  },
  filters: {}
};
</script>

<style lang="less" scoped>
::v-deep .ivu-form-item {
  margin-bottom: 16px;

  label {
    vertical-align: middle;
  }
}

.table-wrapper {
  .table-fun {
    padding-bottom: 10px;
  }
}

.ml10 {
  margin-left: 10px;
}
</style>
