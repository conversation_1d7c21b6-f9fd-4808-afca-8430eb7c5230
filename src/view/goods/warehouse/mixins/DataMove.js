import S from '@/utils/util';
import cloneDeep from 'lodash/cloneDeep';
export default {
  data() {
    return {
      // *************** 基础数据的字段映射 ***************
      base_data_movement_map: {
        name: {
          desc: '商品名',
          isMovement: false,
        },
        slide_imgs: {
          desc: '商品图',
          isMovement: false,
        },
        detail_imgs: {
          desc: '商品详情',
          isMovement: false,
        },
      },
      base_data_freeze: {}, // 数据的冻结对比

      // *************** 通兑券数据的字段映射 ***************
      coupon_data_movement_map: {
        exchange_num: {
          desc: '兑换次数',
          isMovement: false,
          isNumber: true,
        },
        remark: {
          desc: '备注',
          isMovement: false,
        },
        price: {
          desc: '价格',
          isMovement: false,
          isNumber: true,
        },
        service_info: {
          desc: '商品有效期',
          isMovement: false,
        },
        stored_price: {
          desc: '储值价',
          isMovement: false,
          isNumber: true,
        },
        sell_ent_type: {
          desc: '售卖主体',
          isMovement: false,
        },
        xn_scope: {
          desc: '上架范围',
          isMovement: false,
        },
        is_recharge_buy: {
          desc: '储值购买',
          isMovement: false,
        },


      },
      coupon_data_freeze: {}, // 数据的冻结对比


      // *************** 套餐数据的字段映射 ***************
      taocan_data_movement_map: {
        exchange_num: {
          desc: '价格',
          isMovement: false,
          isNumber: true,
        },
        stored_price: {
          desc: '储值价',
          isMovement: false,
          isNumber: true,
        },
        sell_ent_type: {
          desc: '售卖主体',
          isMovement: false,
        },
        xn_scope: {
          desc: '上架范围',
          isMovement: false,
        },
        is_recharge_buy: {
          desc: '储值购买',
          isMovement: false,
        },
      },
      taocan_data_freeze: {}, // 数据的冻结对比
    };
  },
  methods: {
    // ********* 检测拦截 *********
    checkData () {
      this.checkDataCommon(this.$refs.goodsBase.formData, 'base_data_movement_map', 'base_data_freeze')

      // 检测通兑券数据是否有数据异动
      if ( this.formData.goods_type === '25' ) {
        let getFormdata = this.$refs.exchangeCoupon.getFormData()
        this.checkDataCommon(getFormdata, 'coupon_data_movement_map', 'coupon_data_freeze')
      }

      // 检测套餐数据是否有数据异动
      if ( this.formData.goods_type === '20' ) {
        let getFormdata = this.$refs.taocanGoods.getFormData()
        this.checkDataCommon(getFormdata, 'taocan_data_movement_map', 'taocan_data_freeze')
      }
    },

    // 通用检测逻辑
    checkDataCommon (submit_data = {}, map_data_kay = {}, freeze_data_key = {}) {
      let base_data = submit_data
      for (const key in this[map_data_kay]) {
        if (this[map_data_kay][key].isNumber) {
          if ( Number(base_data[key]) !== Number(this[freeze_data_key][key]) ) {
            this[map_data_kay][key].isMovement = true;
          }
        }else{
          if ( JSON.stringify(base_data[key]) !== JSON.stringify(this[freeze_data_key][key]) ) {
            this[map_data_kay][key].isMovement = true;
          }
        }
      }
      console.log("-> 检测数据是否有数据异动", this[map_data_kay]);

    },

    // 检测基础数据是否有数据异动
    checkBaseData() {
      let base_data = this.$refs.goodsBase.formData;
      for (const key in this.base_data_movement_map) {
        if (JSON.stringify(base_data[key]) !== JSON.stringify(this.base_data_freeze[key]) ) {
          this.base_data_movement_map[key].isMovement = true;
        }
      }
      console.log("-> 检测基础数据是否有数据异动", this.base_data_movement_map);
    },

    // 检测套餐数据是否有数据异动
    checkTaocanData() {
      let taocan_data = this.$refs.taocanGoods.getFormData();
      console.log("-> taocan_data", taocan_data);
      console.log("-> taocan_data_freeze", this.taocan_data_freeze);
      for (const key in this.taocan_data_movement_map) {
        if (JSON.stringify(taocan_data[key]) !== JSON.stringify(this.taocan_data_freeze[key]) ) {
          this.taocan_data_movement_map[key].isMovement = true;
        }
      }
      console.log("-> 检测套餐数据是否有数据异动", this.taocan_data_movement_map);
    },

    // ********* 检测拦截 *********

    // ********* 获取冻结数据 *********
    getFreezeData(goods) {
      // 获取基础数据的冻结数据
      this.getBaseFreezeData(goods);

      // 通兑券数据
      if ( this.formData.goods_type === '25' ) {
        this.getCouponFreezeData(goods)
      }

      // 套餐数据
      if ( this.formData.goods_type === '20' ) {
        this.getTaocanFreezeData(goods)
      }


    },

    // 获取基础数据的冻结数据
    getBaseFreezeData(goods) {
      this.base_data_freeze = cloneDeep({
        name: goods.name,
        slide_imgs: goods.slide_imgs,
        detail_imgs: goods.detail_imgs,
      });
    },

    // 获取套餐数据的冻结数据
    getTaocanFreezeData(goods) {
      this.taocan_data_freeze = cloneDeep({
        price: goods.price,
        stored_price: goods.stored_price,
        sell_ent_type: goods.sell_ent_type,
        xn_scope: goods.xn_scope,
        is_recharge_buy: goods.is_recharge_buy,
      });
    },

    // 获取通兑券数据的冻结数据
    getCouponFreezeData(goods) {
      let service_info = !S.isEmptyObject(goods.service_info)
        ? goods.service_info
        : this.$refs.exchangeCoupon.formData.service_info;

      this.coupon_data_freeze = cloneDeep({
        exchange_num: goods.exchange_num,
        remark: goods.remark,
        price: goods.price,
        stored_price: goods.stored_price,
        service_info: service_info,
        sell_ent_type: goods.sell_ent_type,
        xn_scope: goods.xn_scope,
        is_recharge_buy: goods.is_recharge_buy,
      });
    },
    // ********* 获取冻结数据 *********
  },
};
