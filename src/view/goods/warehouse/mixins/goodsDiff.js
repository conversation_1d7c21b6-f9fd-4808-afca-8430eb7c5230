export default {
  methods: {
    deepDiff(obj1, obj2, ignoreKeys = []) {
      const diffKeys = [];

      function compare(o1, o2, currentKey = '') {
        if (ignoreKeys.includes(currentKey)) {
          return;
        }
        if (typeof o1 !== typeof o2) {
          diffKeys.push(currentKey);
          return;
        }

        if (typeof o1 === 'object'&& o1 !== null && o2 !== null) {
          if (Array.isArray(o1)) {
            if (!arraysAreEqual(o1, o2)) {
              diffKeys.push(currentKey);
            }
          } else {
            const keys = new Set([...Object.keys(o1), ...Object.keys(o2)]);

            for (const key of keys) {
              // const nestedKey = currentKey ? `${currentKey}.${key}` : key;
              compare(o1[key], o2[key], key);
            }
          }
        } else {
          if (o1 !== o2) {
            diffKeys.push(currentKey);
          }
        }
      }

      function arraysAreEqual(arr1, arr2) {
        if (arr1.length !== arr2.length) {
          return false;
        }

        const sortedArr1 = [...arr1].sort();
        const sortedArr2 = [...arr2].sort();

        for (let i = 0; i < sortedArr1.length; i++) {
          if (typeof sortedArr1[i] === 'object' && typeof sortedArr2[i] === 'object') {
            if (!deepEqual(sortedArr1[i], sortedArr2[i])) {
              return false;
            }
          } else if (sortedArr1[i] !== sortedArr2[i]) {
            return false;
          }
        }

        return true;
      }

      function deepEqual(value1, value2) {
        if (typeof value1 !== typeof value2) {
          return false;
        }

        if (typeof value1 === 'object' && value1 !== null && value2 !== null) {
          if (Array.isArray(value1) && Array.isArray(value2)) {
            return arraysAreEqual(value1, value2);
          }

          const keys1 = Object.keys(value1);
          const keys2 = Object.keys(value2);

          if (keys1.length !== keys2.length) {
            return false;
          }

          for (const key of keys1) {
            if (!deepEqual(value1[key], value2[key])) {
              return false;
            }
          }

          return true;
        } else {
          return value1 === value2;
        }
      }

      compare(obj1, obj2);
      return diffKeys;
    },
    getDiffData() {
      return this.getFormDataDiff(this.getFormData(), this.originFormData);
    },
  },
};
