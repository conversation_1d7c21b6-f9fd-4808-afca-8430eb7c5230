body {
  background-color: #f9f9f9;
}

@bg-color: #53B8A1;

.login-wrapper {
  background-image: url("../../../assets/image/login/login_bg_new.png");
  background-size: 100% 100%;
  height: 100vh;
  width: 100%;
  position: relative;
  z-index: 1;

  .login-box {
    // width: 320px;
    width: 410px;

    .login-t-l {
      width: fit-content;

      .home-icon {
        display: block;
        height: 49px;
        width: 165px;
      }
    }

    .login-t-h {
      margin: 0 auto;
      width: 1px;
      height: 49px;
      background: #FFFFFF;
    }

    .login-t-r {
      align-self: flex-end;

      .login-t-title {
        font-size: 26px;
        line-height: 32px;
        font-weight: bold;
        color: #FFFFFF;
        letter-spacing: 0.7px;
        -webkit-user-select: none; /*webkit浏览器*/
        user-select: none;
      }
    }
    .login-btm {
      background: #fff;
      box-sizing: border-box;
      padding: 0 40px;
      margin-top: 28px;
      height: 500px;
      border-radius: 4px;
      .nav {
        line-height: 60px;
        font-size: 25px;
        font-family: Source Han Sans CN;
        padding-top: 26px;
        font-weight: 400;
        div {
          cursor: pointer;
          text-align: center;
          flex: 1;
          //border-bottom: 2px solid ;
          //border-radius: 1px;
          //border-color: rgba(151, 151, 151, 0.1);
        }
        .cur{
          color: #1157E5;
          font-weight: 500;
        }
      }
      .nav-bar{
        height:2px;
        background:  rgba(151, 151, 151, 0.3);
        position:relative;
        .cur{
          height:2px;
          background:  #1157E5;
          width:50%;
          position:absolute;
          transition: transform 300ms ease-in-out;
        }
      }
      .login-msg {
        font-size: 14px;
        color: #C6C8D2;
        text-align: right;
        cursor: pointer;
        line-height: normal;
        margin-top: 13px;
        span:hover{
          color: #155BD4;
          display: inline-block;
        }
      }
    }
  }

  .myicon {
    position: absolute;
    top: 50%;
    left: 20px;
    transform: translateY(-50%);
    font-size: 20px;
  }

  .login-auth {
    position: absolute;
    right: 12px;
    top: 5px;
    cursor: pointer;
    color: #1157E5;
    font-size: 14px;
  }

  .forget-tit {
    font-size: 25px;
    line-height: normal;
  }

  .changepsd {
    padding-top: 26px;
  }


  .golgointxt {
    width: 100%;
    text-align: center;
    font-size: 14px;
    color: #1157E5;
    margin-top: 8px;
    cursor: pointer;
  }

}

// i-view样式覆盖
.login-input, .login-refund {
  .ivu-input {
    border-radius: 4px;
    padding-left: 21px;
    font-size: 14px;

  }
}

.login-input .ivu-form-item {
  margin-top: 34px !important;
}

.login-input .ivu-input-icon-clear {
  top: 50%;
  transform: translateY(-50%);
  // color: #f0f0f0;
}

.login-input input[type="number"] {
  -moz-appearance: textfield;
}

// 选择框样式覆盖
.login-input,.login-refund{
  .ivu-select-selection {
    padding-left: 21px;
  }
}

.login-input .ivu-select-single .ivu-select-selection .ivu-select-placeholder, .login-input .ivu-select-single .ivu-select-selection .ivu-select-selected-value {
  font-size: 14px;
}


// .ivu-select-item .ivu-icon{
//   visibility: hidden;
// }
// .ivu-select-item-selected .ivu-icon{
//   visibility: visible;
// }

// 下拉框option选项样式覆盖
// 选中

.ivu-select-item-selected {
  position: relative;
}


.ivu-icon-md-checkmark:before {
  font-size: 18px;
}

.ivu-select-item-selected:after {
  font-family: "Ionicons";
  text-rendering: optimizeLegibility;
  line-height: 1;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  // vertical-align: -0.125em;
  text-align: center;
  font-size: 28px;
  content: '\F171';
  color: #447cdd;
  position: absolute;
  top: 2px;
  right: 8px;
}

.ivu-select-single .ivu-select-selection .ivu-select-placeholder, .ivu-select-single .ivu-select-selection .ivu-select-selected-value {
  display: block;
  padding-left: 0px;
  padding-right: 24px;
}

input::-webkit-outer-spin-button,input::-webkit-inner-spin-button{
  -webkit-appearance: none !important;
}
input[type="number"]{-moz-appearance:textfield;}


.ivu-btn {
  border-radius: 4px;
}