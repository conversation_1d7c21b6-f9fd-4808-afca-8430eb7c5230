<template>
  <div class="login-wrapper flex flex-item-center">
    <div class="login-box">
      <div class="login-top flex flex-item-center">
        <div class="login-t-l">
          <img alt="home-icon" class="home-icon" src="@/assets/image/login/logo-white.png" />
        </div>
        <div class="login-t-h"></div>
        <div class="login-t-r">
          <h3 class="login-t-title">直营运营中心后台</h3>
        </div>
      </div>
      <div class="login-btm">
        <div v-if="!forgetPassword" class="login-right">
          <div class="nav flex flex-item-between">
            <div :class="{ cur: status == 0 }" class="nav-password" @click="changeTap(0)">密码登录</div>
            <div :class="{ cur: status == 1 }" class="nav-code" @click="changeTap(1)">验证码登录</div>
          </div>
          <div class="nav-bar">
            <div ref="cur" :style="barStyle" class="cur"></div>
          </div>
          <div class="login-input">
            <Form autocomplete="off" @submit.native.prevent @keyup.enter.native="onLogin">
              <FormItem v-if="status == 0" style="margin-top: 26px">
                <Input
                  ref="pwdInput"
                  v-model="loginData.mobile"
                  clearable
                  placeholder="请输入手机号或用户名"
                  size="large"
                  type="tel"
                  @on-blur="getSupplierList"
                  @on-clear="clearSupplier"
                ></Input>
              </FormItem>
              <!-- 手机号码 -->
              <FormItem v-if="status == 1" style="margin-top: 20px">
                <Input
                  ref="authInput"
                  v-model="loginData.mobile"
                  clearable
                  placeholder="请输入手机号"
                  size="large"
                  type="number"
                  @on-blur="getSupplierList"
                ></Input>
              </FormItem>
              <!-- 选择门店 -->
              <FormItem style="margin-top: 20px">
                <div v-if="company_list.length > 1 || opc_name == ''" style="position: relative">
                  <Select v-model="loginData.opc_id" placeholder="请选择直营中心" size="large">
                    <Option v-for="item in company_list" :key="item.id" :value="item.id">
                      {{ item.name }}
                    </Option>
                  </Select>
                </div>
                <div v-else>
                  <Input v-model="opc_name" readonly size="large"></Input>
                </div>
              </FormItem>
              <!-- 密码 -->
              <FormItem v-if="status == 0" clearable style="margin-top: 20px">
                <Input
                  v-model="loginData.password"
                  clearable
                  placeholder="请输入密码"
                  size="large"
                  type="password"
                ></Input>
              </FormItem>
              <!-- 验证码 -->

              <FormItem v-if="status == 1" style="margin-top: 20px">
                <Input v-model="loginData.auth_code" placeholder="请输入验证码" size="large" type="number"></Input>
                <div class="login-auth">
                  <vac ref="vac" :auto-start="false" :left-time="60000" @finish="onCountDownFinish">
                    <span slot="process" slot-scope="{ timeObj }">
                      {{ timeObj.ceil.s }}
                    </span>
                    <span slot="before" @click="onCountDownStart">获取验证码</span>
                    <span slot="finish" @click="onCountDownStart">重新获取</span>
                  </vac>
                </div>
              </FormItem>

              <Button
                style="width: 100%; height: 41px; font-size: 16px; margin-top: 26px"
                type="primary"
                @click="onLogin"
                >登录
              </Button>
              <div v-if="status == 0" class="login-msg">
                <span @click="goForget">忘记密码？</span>
              </div>
            </Form>
          </div>
        </div>
        <div v-if="forgetPassword" class="login-right changepsd">
          <div class="forget-tit">找回密码</div>
          <div class="login-refund">
            <Form ref="psdForm" :model="loginData" :rules="ruleLoginData">
              <FormItem prop="mobile" style="margin-top: 20px">
                <Input
                  v-model="loginData.mobile"
                  clearable
                  placeholder="请输入手机号"
                  size="large"
                  type="number"
                  @on-blur="getSupplierList"
                ></Input>
              </FormItem>
              <FormItem style="margin-top: 20px">
                <div v-if="company_list.length > 1 || opc_name == ''" style="position: relative">
                  <Select v-model="loginData.opc_id" placeholder="请选择直营中心" size="large">
                    <Option v-for="item in company_list" :key="item.id" :value="item.id">
                      {{ item.name }}
                    </Option>
                  </Select>
                </div>
                <div v-else>
                  <Input v-model="opc_name" readonly size="large"></Input>
                </div>
              </FormItem>

              <FormItem prop="auth_code" style="margin-top: 20px">
                <Input v-model="loginData.auth_code" placeholder="请输入验证码" size="large" type="number"></Input>
                <div class="login-auth">
                  <vac ref="vac2" :auto-start="false" :left-time="60000" size="large" @finish="onCountDownFinish">
                    <span slot="process" slot-scope="{ timeObj }">
                      {{ timeObj.ceil.s }}
                    </span>
                    <span slot="before" @click="onCountDownStart">获取验证码</span>
                    <span slot="finish" @click="onCountDownStart">重新获取</span>
                  </vac>
                </div>
              </FormItem>
              <FormItem prop="new_password" style="margin-top: 20px">
                <Input
                  v-model="loginData.new_password"
                  clearable
                  placeholder="输入新密码(6-20位字母数字组合)"
                  size="large"
                  type="password"
                ></Input>
              </FormItem>
              <FormItem prop="confirm_password" style="margin-top: 20px">
                <Input
                  v-model="loginData.confirm_password"
                  clearable
                  placeholder="确认新密码"
                  size="large"
                  type="password"
                ></Input>
              </FormItem>
              <Button
                style="width: 100%; height: 42px; font-size: 16px; margin-top: 12px"
                type="primary"
                @click="changePassWord"
                >保存
              </Button>
            </Form>
          </div>
          <div class="golgointxt"><span @click="goLogin">去登录</span></div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
/* eslint-disable */
import S from '@/utils/util'; // Some commonly used tools
import io from '@/utils/request'; // Http request
import * as runtime from '@/utils/runtime'; // Runtime information
/* eslint-disable */
import './index.less';
import Vue from 'vue';
import VueAwesomeCountdown from 'vue-awesome-countdown';
import config from '@/config';

Vue.use(VueAwesomeCountdown, 'vac');

// Vue.use(VueAwesomeCountdown, 'vac')

let windowHeight = parseInt(window.outerHeight);

export default {
  name: 'index',

  data() {
    return {
      pageHeight: '',
      countdowning: false,
      // mobile: '',
      // auth_code: '',
      opc_name: '',
      loginData: {
        mobile: '',
        auth_code: '',
        opc_id: '',
        password: '', //密码
        new_password: '', //新密码
        confirm_password: '' //再次密码
      },
      status: 0, //1,验证码登录，0,密码登录
      forgetPassword: false,
      ruleLoginData: {
        mobile: [{ required: true, message: '请填写手机号码', trigger: 'blur' }],
        auth_code: [{ required: true, message: '请填写验证码', trigger: 'blur' }],
        new_password: [{ required: true, message: '请填新密码', trigger: 'blur' }],
        confirm_password: [{ required: true, message: '请再次填写新密码', trigger: 'blur' }]
      },
      company_list: [],
      barOffset: ''
    };
  },
  computed: {
    barStyle() {
      return {
        transform: `translate3d(${this.barOffset}px,0px,0px)`
      };
    }
  },
  created() {
    this.getWindowHeight();
    window.addEventListener('resize', this.getWindowHeight);
  },

  methods: {
    getWindowHeight() {
      this.pageHeight = windowHeight - 110 + 'px';
    },
    // 切换登录方式
    changeTap(state) {
      this.status = state;
      this.loginData.opc_id = '';
      this.opc_name = '';
      for (let key in this.loginData) {
        this.loginData[key] = '';
      }
      this.company_list = [];
      if (state === 0) {
        this.barOffset = '0';
        // this.$nextTick(() => {
        // 	this.$refs.pwdInput.focus()
        // })
      } else {
        this.barOffset = '165';
        // this.$nextTick(() => {
        // 	this.$refs.authInput.focus()
        // })
      }
    },
    // 忘记密码
    goForget() {
      for (let key in this.loginData) {
        this.loginData[key] = '';
      }
      this.company_list = [];
      this.opc_name = '';
      this.forgetPassword = true;
    },
    clearSupplier() {
      this.company_list = [];
      this.opc_name = '';
      this.loginData.opc_id = '';
    },
    // 获取验证码
    onCountDownStart() {
      if (this.loginData.mobile.trim() == '') {
        this.$Message.error('请输入手机号');
        return;
      }
      io.post('pms_opc/mobile.sendauthcode', { mobile: this.loginData.mobile })
        .then(() => {
          this.$Message.success('发送成功');
          if (!this.forgetPassword) {
            this.$refs.vac.startCountdown(true);
          } else {
            this.$refs.vac2.startCountdown(true);
          }
          this.countdowning = true;
        })
        .catch(error => {
          this.$Message.error(error.errmsg);
        });
    },
    // 获取诊所名称列表
    getSupplierList() {
      console.log(this.loginData, this.status, this.forgetPassword);
      const { mobile } = this.loginData;

      if (!mobile) {
        return;
      }
      if (this.status == 1) {
        let telreg = this.regRole(this.loginData.mobile);
        if (!telreg) {
          this.$Message.error('请输入正确的号码');
          return;
        }
      }
      let query = {
        mobile: mobile
      };
      this.clearSupplier();
      this.$api.getComList(query).then(
        res => {
          this.company_list = res.list;
          if (res.list.length > 0) {
            this.loginData.opc_id = res.list[0].id;
            this.opc_name = res.list[0].name;
          }
        },
        err => {
          // console.log(err)
          this.$Message.error(err.errmsg);
        }
      );
    },
    onCountDownFinish() {
      this.countdowning = false;
    },
    // 登录
    onLogin() {
      console.log('ads', this.loginData.opc_id);
      let from = this.$route.query.from || '/';
      if (this.status == 1) {
        if (this.loginData.mobile.trim() == '') {
          this.$Message.error('请输入手机号');
          return;
        }
        if (this.loginData.opc_id.trim() == '') {
          this.$Message.error('请选择直营中心');
          return;
        }
        if (this.loginData.auth_code.trim() == '') {
          this.$Message.error('请输入验证码');
          return;
        }
        let telreg = this.regRole(this.loginData.mobile);
        if (!telreg) {
          this.$Message.error('请输入正确的号码');
          return;
        }
        let { mobile, auth_code, opc_id } = this.loginData;
        this.$api
          .login({ mobile, auth_code, opc_id })
          .then(userInfo => {
            this.writeLoginInfo(userInfo);
          })
          .catch(error => {
            this.$Message.error(error.errmsg);
          });
      } else {
        let query = {
          account: this.loginData.mobile,
          password: S.encrypt(
            JSON.stringify({
              password: this.loginData.password,
              expired_time: Date.parse(new Date()) / 1000
            })
          ),
          opc_id: this.loginData.opc_id,
          version: config.cryptoVersion
        };
        if (!query.account) {
          this.$Message.error('请先输入账号');
          return;
        }
        if (!query.opc_id) {
          this.$Message.error('请先选择直营中心');
          return;
        }
        if (!this.loginData.password) {
          this.$Message.error('请先输入密码');
          return;
        }
        this.$api
          .getAccountlogin(query)
          .then(
            userInfo => {
              this.writeLoginInfo(userInfo);
            },
            err => {
              this.$Message.error(err.errmsg);
            }
          )
          .catch(e => {});
      }
    },
    writeLoginInfo(userInfo) {
      let from = this.$route.query.from || '/';
      let info = {
        uid: userInfo['mer_uid'],
        name: userInfo['user_name'],
        clinicid: userInfo['clinic_id'],
        opc_id: userInfo['opc_id'],
        role_name: userInfo['role_name'],
        opc_name: userInfo['opc_name'],
        prov_code: userInfo['prov_code'],
        mobile: userInfo['mobile'],
        cp_name: userInfo['cp_name'],
        expires: userInfo['expires'] ? Number(userInfo['expires']) : 7
      };
      let expiresTime = userInfo['expire_secs']
        ? new Date(new Date().getTime() + userInfo.expire_secs * 1000)
        : this.$moment()
            .add(userInfo['expires'] ? Number(userInfo['expires']) : 7, 'days')
            .valueOf();
      // 鉴权
      var EXPIRES_MINUTES = 6;
      const inFiveMinutes = new Date(new Date().getTime() + EXPIRES_MINUTES * 60 * 1000);
      const isJJ = localStorage.getItem('is_jj');
      info.expires = isJJ ? inFiveMinutes : new Date(expiresTime);
      info.time_stamp = Math.floor(this.$moment(isJJ ? inFiveMinutes : expiresTime).valueOf() / 1000);
      console.log('%c [ info ]-438', 'font-size:13px; background:#72556b; color:#b699af;', info);
      // 将date格式的日期转化为时间戳存储
      runtime.writeLoginCookie(info);
      this.$Message.success('登录成功');
      try {
        let url = S.uri().path(decodeURIComponent(from)).build();
        console.log(url);
        location.href = location.origin + '/opc' + url;
      } catch (e) {
        location = '/opc/';
      }
    },
    // 修改密码
    changePassWord() {
      this.$refs.psdForm.validate(valid => {
        if (valid) {
          if (this.loginData.new_password != this.loginData.confirm_password) {
            this.$Message.error('两次密码不一致');
            return;
          }
          this.getRetrievepass();
        } else {
          this.$Message.error('请完善信息');
        }
      });
    },
    getRetrievepass() {
      let telreg = this.regRole(this.loginData.mobile);
      if (!telreg) {
        this.$Message.error('请输入正确的号码');
        return;
      }
      let query = {
        mobile: this.loginData.mobile,
        auth_code: this.loginData.auth_code,
        opc_id: this.loginData.opc_id,
        new_password: S.encrypt(
          JSON.stringify({
            password: this.loginData.new_password,
            expired_time: Date.parse(new Date()) / 1000
          })
        ),
        confirm_password: S.encrypt(
          JSON.stringify({
            password: this.loginData.confirm_password,
            expired_time: Date.parse(new Date()) / 1000
          })
        ),
        version: config.cryptoVersion
      };
      this.$api
        .changeRetrievepass(query)
        .then(
          res => {
            console.log(res);
            this.$Message.success('修改密码成功');
            this.forgetPassword = false;
            for (let key in this.loginData) {
              this.loginData[key] = '';
            }
            this.company_list = [];
            this.opc_name = '';
          },
          err => {
            this.$Message.error(err.errmsg);
          }
        )
        .catch(error => {});
    },
    regRole(tel) {
      let flag;
      let reg = /^1[3456789]\d{9}$/;
      flag = reg.test(tel);
      return flag;
    },
    goLogin() {
      for (let key in this.loginData) {
        this.loginData[key] = '';
      }
      this.forgetPassword = false;
      this.clearSupplier();
    }
  },

  destroyed() {
    window.removeEventListener('resize', this.getWindowHeight);
  }
};
</script>

<style lang="less" scoped>
.ivu-select-single {
}
</style>
