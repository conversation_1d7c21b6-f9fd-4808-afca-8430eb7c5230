<template>
  <Modal
    ref="customModal"
    :value="value"
    width="400px"
    :title="title"
    :footer-hide="false"
    :mask-closable="false"
    :lock-scroll="true"
    :closable="false"
    @on-visible-change="changeVisible"
  >
    <div class="content">
      <Icon type="md-alert" color="orange" size="30"/>
      <div class="text">{{ confirm_info.message }}</div>
    </div>
    <div slot="footer">
      <Button :loading="loading" type="primary" @click="confirm">{{ confirm_info.status === 'warn' ? '继续' : '好的' }}</Button>
      <Button @click="closeModal">取消</Button>
    </div>
  </Modal>
</template>

<script>
export default {
  name: 'confirmModal',
  mixins: [],

  components: {},
  props: {
    value: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      default: ''
    },
    confirmFn: Function,
    loading: {
      type: <PERSON>olean,
      default: false
    },
    confirm_info: {
      type: Object,
      default: () => {}
    }
  },

  data() {
    return {
    };
  },

  computed: {},

  watch: {},

  created() {},

  mounted() {},

  destroyed() {},

  methods: {
    changeVisible(visible) {
      if (visible) {
      }else{
        this.closeModal();
      }
    },

    clearData() {
    },

    closeModal() {
      this.clearData()
      this.$emit('input', false);
    },

    confirm() {
      this.confirmFn().then( res => {
        this.$emit('success');
        this.closeModal()
      } )
    },

  }
};
</script>

<style scoped lang="less">
::v-deep .ivu-modal-body {
  overflow-y: auto;
}
:deep(.ivu-modal-footer) {
  border-top: none;
}
.content {
  padding: 20px 20px 0px;
  display: flex;
  align-items: flex-start;
  .text {
    margin-left: 10px;
    font-size: 14px;
  }
}
</style>
