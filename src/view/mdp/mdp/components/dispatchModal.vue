<template>
  <div>
    <Modal
      ref="customModal"
      :value="value"
      width="800px"
      :title="title"
      :footer-hide="false"
      :mask-closable="false"
      :lock-scroll="true"
      @on-visible-change="changeVisible"
    >
      <div class="content">
        <div class="content-l hidden-scroll">
          <div class="select-box">
            <Input
              v-model="opc_keyword"
              placeholder="搜索运营中心"
              @on-change="queryChange"
              style="width: 100%; max-width: unset"
              clearable
              disabled
            />
          </div>
          <div class="store-box">
            <Spin v-if="opc_searchLoading" class="spin-box"></Spin>
            <div v-else-if="!opc_list.length" class="empty-text">暂无数据</div>
            <div
              v-else
              class="store-item store-item--active flex flex-item-align flex-item-between"
              v-for="(item, index) in opc_list"
              style="cursor: default"
              :key="index"
            >
              <div>
                <div>{{ item.name }}</div>
                <div>联系人: {{ item.leading_person }} {{ item.leading_mobile }}</div>
              </div>
              <Icon type="ios-arrow-forward" size="20" />
            </div>
          </div>
        </div>
        <div class="content-r hidden-scroll">
          <div class="select-box">
            <Form @submit.native.prevent>
              <FormItem style="margin: 0px;">
                <Input
                  v-model="keyword"
                  placeholder="搜索诊所门店"
                  @on-change="queryChange"
                  style="width: 100%; max-width: unset"
                  clearable
                />
              </FormItem>
            </Form>
          </div>
          <div class="store-box">
            <Spin v-if="searchLoading" class="spin-box"></Spin>
            <div v-else-if="!store_list.length" class="empty-text">暂无数据</div>
            <div
              v-else
              class="store-item"
              :class="{ 'store-item--active': select_list.includes(item.id) }"
              v-for="(item, index) in store_list"
              :key="index"
              @click="selectStore(item)"
            >
              <div>{{ item.name }}</div>
              <div>联系人: {{ item.leading_person }} {{ item.leading_mobile }}</div>
            </div>
          </div>
        </div>
      </div>
      <div slot="footer">
        <div class="flex flex-item-align" style="justify-content: flex-end">
          <div style="margin-right: 10px">已选：({{ select_list.length }})</div>
          <Button @click="closeModal">取消</Button>
          <Button :loading="confirmLoading" type="primary" @click="confirm">确认分发</Button>
        </div>
      </div>
    </Modal>
    <!--  确认弹窗 -->
    <confirm-modal
      v-model="confirmVisible"
      :confirm_info="confirm_info"
      :loading="loading"
      :confirmFn="confirmFn"
    ></confirm-modal>
  </div>
</template>

<script>
import util from '@/utils/util';
import confirmModal from './confirmModal.vue';
export default {
  name: 'dispatchModal',
  mixins: [],

  components: {
    confirmModal
  },
  props: {
    value: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      default: '分发门店'
    },
    selected_items: {
      type: Object,
      default: () => {}
    }
  },

  data() {
    return {
      confirmLoading: false,

      opc_list: [],
      opc_searchLoading: false,
      opc_select_list: [],
      opc_keyword: '',

      searchLoading: false,
      store_list: [],
      select_list: [],
      keyword: '',

      confirmVisible: false,
      confirm_info: {},
      loading: false
    };
  },

  computed: {},

  watch: {},

  created() {},

  mounted() {},

  destroyed() {},

  methods: {
    queryChange() {
      this.searchMethod(this.keyword);
    },
    searchMethod: util.debounce(function (query) {
      this.searchLoading = true;
      this.$api.getMdpMemberSearchCli({ keyword: query }).then(res => {
        this.searchLoading = false;
        this.store_list = res.list;
      });
    }, 200),

    selectStore(val) {
      let index = this.select_list.findIndex(item => item === val.id);
      if (index === -1) {
        this.select_list.push(val.id);
      } else {
        this.select_list.splice(index, 1);
      }
      console.log('=>(dispatchModal.vue:107) this.select_list', this.select_list);
    },

    MODAL_SCROLL_TOP() {
      let MODAL_EL = this.$el.getElementsByClassName('ivu-modal-body')[0];
      if (MODAL_EL) {
        MODAL_EL.scrollTop = 0;
      }
    },

    changeVisible(visible) {
      if (visible) {
        this.getMdpMemberSearchSource()
        this.searchMethod();
      } else {
        this.closeModal();
      }
    },

    clearData() {
      this.store_list = []
      this.select_list = []
      this.keyword = ''
    },

    closeModal() {
      this.clearData();
      this.MODAL_SCROLL_TOP();
      this.$emit('input', false);
    },

    getMdpMemberDispatch(force = false) {
      return new Promise(resolve => {
        let params = {
          force: force,
          clinic_ids: this.select_list,
          ids: Object.keys(this.selected_items || {})
        };
        this.confirmLoading = true;
        this.$api
          .getMdpMemberDispatch(params)
          .then(
            res => {
              resolve(res);
              if (res.status === 'warn' || res.status === 'error') {
                this.confirm_info = res;
                this.confirmVisible = true;
              } else {
                this.$Message.success('分发成功');
                this.$emit('success');
                this.closeModal();
              }
            },
            err => this.$Message.error(err.errmsg)
          )
          .finally(() => {
            this.confirmLoading = false;
            this.loading = false;
          });
      });
    },

    confirm() {
      if (!this.select_list.length) {
        this.$Message.error('请选择分发的门店');
        return;
      }
      this.getMdpMemberDispatch();
    },

    confirmFn() {
      return new Promise(resolve => {
        if (this.confirm_info.status === 'error') {
          this.confirmVisible = false;
          return;
        }
        this.loading = true;
        this.getMdpMemberDispatch(true).then(res => {
          this.confirmVisible = false
          this.$emit('success');
          this.closeModal();
        });
      });
    },

    getMdpMemberSearchSource() {
      let params = {};
      this.$api.getMdpMemberSearchSource(params).then(
        res => {
          this.opc_list = res.list || []
        },
        err => this.$Message.error(err.errmsg)
      );
    },
  }
};
</script>

<style scoped lang="less">
.content {
  display: flex;
  border: 1px solid #e6dede;
  height: 464px;
  .content-l,
  .content-r {
    flex: 1;
    overflow-y: auto;
    .select-box {
      padding: 10px;
      background: #fff;
      position: sticky;
      top: 0px;
    }

    .store-box {
      .spin-box {
        margin-top: 200px;
        margin-left: 180px;
      }
      .store-item {
        padding: 6px 20px;
        cursor: pointer;
        border-bottom: 1px solid #faf8f8;
        margin-bottom: 1px;
        &:hover {
          color: #155bd4;
        }
      }
      .store-item--active {
        color: #155bd4;
        background: #f1f1ff;
      }
    }
  }
  .content-r {
    flex: 1;
    border-left: 1px solid #e6dede;
  }
  .empty-text {
    margin-top: 200px;
    margin-left: 180px;
    color: #ccc;
  }
}
</style>
