<template>
  <div class="detail-box">
    <Alert type="warning" v-if="detailInfo.audit_status === '3'">
      <div class="mb10">审核时间：{{ detailInfo.audit_time | date_format('YYYY-MM-DD HH:mm:ss') }}</div>
      <div>审核意见：{{ detailInfo.audit_reason || '-' }}</div>
    </Alert>
    <Form
      :label-width="100"
      :disabled="isDetail || is_all_disabled"
      ref="queryFormData"
      :label-colon="true"
      :model="queryFormData"
      :rules="ruleValidate"
    >
      <div class="block-header">基本信息</div>
      <div class="form-box">
        <FormItem label="头像" prop="avatar">
          <Picture
            v-model="queryFormData.avatar"
            :limit="1"
            :useCropper="true"
            :isQueryDetail="isDetail || is_all_disabled"
            @on-success="pictureSuccess('avatar')"
          />
        </FormItem>

        <FormItem label="姓名" prop="name">
          <Input
            v-model="queryFormData.name"
            placeholder="请输入姓名"
            :disabled="is_disabled"
            show-word-limit
            maxlength="10"
          />
        </FormItem>

        <FormItem label="系统账号" prop="mobile">
          <Input
            v-model="queryFormData.mobile"
            style="width: 100%"
            placeholder="请输入登入系统的手机号码"
            maxlength="11"
            show-word-limit
            :disabled="is_disabled"
          ></Input>
        </FormItem>

        <FormItem label="MDP身份" prop="mdp_type">
          <div class="flex flex-item-align">
            <Select
              :disabled="is_disabled"
              v-model="queryFormData.mdp_type"
              ref="selectRef"
              clearable
              @on-change="() => (queryFormData.level_id = '')"
            >
              <Option v-for="(item, index) in mdp_level" :value="item.role_name" :key="'role' + index">{{
                item.role_name
              }}</Option>
            </Select>

            <Select v-model="queryFormData.level_id" :disabled="is_disabled" v-if="!isMDP" style="margin-left: 20px">
              <Option v-for="item in getLevelList" :value="item.id" :key="item.id">{{ item.level }}</Option>
            </Select>
          </div>
        </FormItem>

        <FormItem label="当月生效职级" prop="level_id" v-if="isMDP">
          <Select v-model="queryFormData.level_id" :disabled="is_disabled">
            <Option v-for="item in getLevelList" :value="item.id" :disabled="item.can_create === '0'" :key="item.id">{{ item.level }}</Option>
          </Select>
          <div style="color: #ccc; line-height: 22px">
            职级和业绩提成相关，会根据完成的服务或订单产生的金额进行分账或记账收入
          </div>
        </FormItem>
        <FormItem label="次月生效职级" prop="next_level_id" v-if="isMDP">
          <Select v-model="queryFormData.next_level_id">
            <Option v-for="item in getLevelList" :value="item.id" :disabled="item.can_create === '0'" :key="item.id">{{ item.level }}</Option>
          </Select>
        </FormItem>

        <FormItem label="证件类型" prop="id_card_type">
          <Select v-model="queryFormData.id_card_type" :disabled="is_disabled" clearable placeholder="请选择证件类型">
            <Option v-for="(item, key) in id_card_type" :value="key" :key="key">{{ item.desc }}</Option>
          </Select>
        </FormItem>

        <FormItem label="证件号" prop="idcard">
          <Input
            type="text"
            placeholder="请输入证件号"
            :disabled="is_disabled"
            v-model="queryFormData.idcard"
            @on-change="replaceIdBrackets"
          />
        </FormItem>

        <FormItem label="证照信息" prop="is_has_photo" v-if="queryFormData.mdp_type === 'P(理疗师)'">
          <RadioGroup v-model="queryFormData.is_has_photo" @on-change="photoChange">
            <Radio :label="key" v-for="(item, key) in has_card_img" :key="key">{{ item.desc }}</Radio>
          </RadioGroup>
          <Picture
            v-model="queryFormData.card_img"
            :isQueryDetail="isDetail || is_all_disabled"
            :limit="9"
            style="margin-top: 10px"
            v-if="queryFormData.is_has_photo === '1'"
            @on-success="pictureSuccess('is_has_photo')"
          />
        </FormItem>

        <FormItem label="个人介绍">
          <Input
            v-model="queryFormData.desc"
            type="textarea"
            :autosize="{ minRows: 6, maxRows: 6 }"
            maxlength="500"
            show-word-limit
            placeholder="请输入个人介绍"
          />
        </FormItem>
      </div>
    </Form>
    <div class="fixed-bottom-wrapper">
      <back-button></back-button>
      <Button class="ml10" v-if="is_can_save" type="primary" :loading="confirmLoading" @click="save">
        确认并提交审核
      </Button>
      <audit-refuse v-if="!isDetail && detailInfo.audit_status === '1'" class="ml10" @ok="handleRefuse"></audit-refuse>
      <audit-pass v-if="!isDetail && detailInfo.audit_status === '1'" class="ml10" @ok="handlePass"></audit-pass>
    </div>
  </div>
</template>

<script>
import Picture from '@/components/upload/picture';
import { validateIDCard } from '@/utils/validator';
export default {
  name: 'create',
  components: {
    Picture
  },
  mixins: [],
  data() {
    const validateCardType = (rule, value, callback) => {
      if (!value) {
        callback(new Error('该选择证件类型'));
      }else{
        if (this.queryFormData.idcard) {
          this.$refs.queryFormData?.validateField('idcard');
        }
        callback();
      }
    }
    const validateMobileCard = (rule, value, callback) => {
      const gatIDCardValidate = /^8[123]0000(?:19|20)\d{2}(?:0[1-9]|1[0-2])(?:0[1-9]|[12]\d|3[01])\d{3}[\dX]$/;
      const id_card_type = this.queryFormData.id_card_type;
      const idcard = this.queryFormData.idcard;
      if (!id_card_type) {
        callback(new Error('请先选择证件类型'));
      } else if (!idcard) {
        callback(new Error('请输入身份证号码'));
      } else if (!validateIDCard(idcard) && !gatIDCardValidate.test(idcard)) {
        callback(new Error('请输入正确的身份证号'));
      } else {
        callback();
      }
    };
    const validateMobile = (rule, value, callback) => {
      if (value === '') {
        callback(new Error('请输入正确的手机号码'));
      } else {
        const reg = /^1[3456789]\d{9}$/;
        if (!reg.test(value)) {
          callback(new Error('请输入正确的手机号码'));
        }
        callback();
      }
    };

    const validatePhoto = (rule, value, callback) => {
      const { is_has_photo, card_img } = this.queryFormData;
      if (is_has_photo === '') {
        callback(new Error('请选择证照类型'));
      } else if (is_has_photo === '1' && !card_img.length) {
        callback(new Error('请上传证照信息'));
      } else {
        callback();
      }
    };

    const validateMdpType = (rule, value, callback) => {
      const { mdp_type, level_id } = this.queryFormData;
      if (!mdp_type) {
        callback(new Error('请选择MDP身份'));
      } else if (!this.isMDP && !level_id) {
        callback(new Error('请选择'));
      } else {
        callback();
      }
    };

    return {
      queryFormData: {
        avatar: '',
        name: '',
        mobile: '',
        mdp_type: '', // mdp身份
        level_id: '', // 当月生效职级
        next_level_id: '', // 次月生效职级
        id_card_type: '', // 证件类型
        idcard: '',
        is_has_photo: '1', // 是否有证照
        card_img: [],
        desc: ''
      },
      ruleValidate: {
        avatar: [{ required: true, message: '请上传头像', trigger: 'change' }],
        name: [{ required: true, message: '该填写服务名称', trigger: 'change' }],
        mobile: [{ required: true, trigger: 'blur', validator: validateMobile }],
        mdp_type: [{ required: true, validator: validateMdpType, trigger: 'change' }],
        level_id: [{ required: true, message: '请选择当月生效职级', trigger: 'change' }],
        next_level_id: [{ required: true, message: '请选择次月生效职级', trigger: 'change' }],
        id_card_type: [{ required: true, validator: validateCardType, trigger: 'change' }],
        idcard: [{ required: true, trigger: 'blur', validator: validateMobileCard }],
        is_has_photo: [{ required: true, trigger: 'blur', validator: validatePhoto }]
      },

      detailInfo: {}, // 详情信息
      confirmLoading: false,
      mdp_level: [],
      id_card_type: [],
      has_card_img: []
    };
  },
  computed: {
    isMDP() {
      let list = ['M(店长)', 'D(医生)', 'P(理疗师)'];
      return list.includes(this.queryFormData.mdp_type);
    },
    // 获取MDP对应的职级
    getLevelList() {
      let mdp_type = this.queryFormData.mdp_type;
      let list = [];
      this.mdp_level?.some(item => {
        if (item.role_name === mdp_type) {
          list = item.list || [];
          return true;
        }
      });
      return list || [];
    },
    is_can_save() {
      return (
        !this.isDetail &&
        (!this.$route.query.id || this.detailInfo.audit_status === '2' || this.detailInfo.audit_status === '3')
      );
    },
    isDetail() {
      return this.$route.query.type === 'detail';
    },
    is_all_disabled() {
      return this.detailInfo.audit_status === '1';
    },
    is_disabled() {
      return !!this.detailInfo.id && this.detailInfo.opc_dispatch === '1';
    }
  },
  watch: {},
  created() {
    this.getMdpMemberOptions();
  },
  mounted() {},
  methods: {
    photoChange () {
      this.queryFormData.card_img = []
      if (this.queryFormData.is_has_photo == '2') {
        this.$refs.queryFormData?.validateField('is_has_photo');
      }
    },
    replaceIdBrackets() {
      // 将中文括号替换为英文括号
      this.queryFormData.idcard = this.queryFormData.idcard.replace(/（/g, '(').replace(/）/g, ')');
    },
    pictureSuccess(filed) {
      if (filed) {
        setTimeout(res => {
          this.$refs.queryFormData.validateField(filed);
        });
      }
    },
    handleRefuse(reason) {
      this.getMdpMemberAudit('3', reason);
    },
    handlePass() {
      this.getMdpMemberAudit('2');
    },
    getMdpMemberAudit(action, reason = '') {
      let params = {
        id: this.$route.query.id,
        audit_status: action,
        audit_reason: reason
      };
      this.$api.getMdpMemberAudit(params).then(
        res => {
          this.$Message.success(`审核${action === '2' ? '通过' : '驳回'}成功`);
          this.$router.push('/mdp/mdp/list');
        },
        err => this.$Message.error(err.errmsg)
      );
    },
    getMdpMemberOptions() {
      let params = {};
      this.$api.getMdpMemberOptions(params).then(
        res => {
          this.mdp_level = res.mdp_level;
          this.id_card_type = res.id_card_type;
          this.has_card_img = res.has_card_img;

          let id = this.$route.query.id;
          if (id) {
            this.getMdpMemberInfo(id);
          }
        },
        err => this.$Message.error(err.errmsg)
      );
    },
    save() {
      this.$refs.queryFormData.validate(valid => {
        if (valid) {
          this.getMdpMemberEdit();
        }
      });
    },

    // 获取详情
    getMdpMemberInfo(id) {
      let params = {
        id,
        scene: this.$route.query.field === 'audit_info' ? 'audit_info' : 'info'
      };
      this.$api.getMdpMemberInfo(params).then(
        res => {
          this.detailInfo = res;
          this.handlerData(res);
        },
        err => this.$Message.error(err.errmsg)
      );
    },

    handlerData(data) {
      this.queryFormData.avatar = data.avatar;
      this.queryFormData.name = data.name;
      this.queryFormData.mobile = data.mobile;
      this.queryFormData.mdp_type = data.mdp_role_name;
      this.queryFormData.level_id = data.level_id;
      this.queryFormData.next_level_id = data.next_level_id;
      this.queryFormData.id_card_type = data.id_card_type;
      this.queryFormData.idcard = data.idcard;
      this.queryFormData.is_has_photo = data.card_img?.length ? '1' : '2';
      this.queryFormData.card_img = data.card_img || [];
      this.queryFormData.desc = data.desc;
    },

    getMdpMemberEdit() {
      this.confirmLoading = true;
      let params = {
        id: this.$route.query.id,
        ...this.queryFormData
      };
      this.$api
        .getMdpMemberEdit(params)
        .then(
          res => {
            this.$Message.success('提交成功');
            this.$router.push('/mdp/mdp/list');
          },
          err => this.$Message.error(err.errmsg)
        )
        .finally(() => (this.confirmLoading = false));
    }
  },
  deactivated() {
    this.$refs.selectRef?.clearSingleSelect();
  },
  beforeDestroy() {
    this.$refs.selectRef?.clearSingleSelect();
  }
};
</script>

<style lang="less" scoped>
.detail-box {
  padding-bottom: 50px;
  .form-box {
    margin-left: 50px;
    width: 50%;
  }
}
</style>
