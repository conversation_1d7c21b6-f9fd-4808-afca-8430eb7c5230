<template>
  <div>
    <Form inline :label-width="0" @submit.native.prevent @keyup.enter.native="onSearch" ref="searchDom">
      <Row>
        <FormItem>
          <Input type="text" v-model="queryFormData.name" placeholder="员工姓名" />
        </FormItem>
        <FormItem>
          <Select v-model="queryFormData.mdp_role_name" clearable placeholder="MDP身份" @on-change="mdpChange">
            <Option v-for="item in mdp_level" :key="item.role_name" :value="item.role_name">{{
              item.role_name
            }}</Option>
          </Select>
        </FormItem>
        <FormItem>
          <Select
            ref="levelSelect"
            v-model="queryFormData.mdp_level_id"
            :disabled="!queryFormData.mdp_role_name"
            clearable
            :placeholder="!queryFormData.mdp_role_name ? '请先选择MDP身份' : '职级'"
          >
            <Option v-for="item in level_list" :key="item.id" :value="item.id">{{ item.level }}</Option>
          </Select>
        </FormItem>
        <FormItem>
          <Select
            v-model="queryFormData.has_card_img"
            v-if="queryFormData.mdp_role_name === 'P(理疗师)'"
            clearable
            placeholder="理疗师证照"
          >
            <Option v-for="item in has_card_img" :key="item.kw" :value="item.kw">{{ item.desc }}</Option>
          </Select>
        </FormItem>
        <!--        <FormItem>-->
        <!--          <Select v-model="queryFormData.status" clearable placeholder="账号状态">-->
        <!--            <Option v-for="item in status_list" :key="item.kw" :value="item.kw">{{ item.desc }}</Option>-->
        <!--          </Select>-->
        <!--        </FormItem>-->

        <FormItem>
          <store-search ref="store" v-model="queryFormData.clinic_id" :isClearable="true"></store-search>
        </FormItem>
      </Row>
      <Row>
        <FormItem>
          <Button type="primary" class="mr10" @click="onSearch">筛选</Button>
          <Button class="mr10" @click="onResetSearch">重置</Button>
          <Button type="primary" @click="create()">创建</Button>
        </FormItem>
      </Row>
    </Form>

    <div class="panel-nav flex flex-item-between">
      <div>
        <a
          class="nav"
          :class="{ active: queryFormData.list_status === type_item.id }"
          v-for="(type_item, type_index) in list_status"
          @click.prevent.capture="onStatusChange(type_item.id)"
          :key="type_index + 'type'"
          >{{ type_item.desc }}
          <Tag :color="getTagColor(type_item.id)">{{ list_status_count[type_item.id] }}</Tag>
        </a>
      </div>
    </div>

    <Table
      @on-select-all="selectAll"
      @on-select="select"
      @on-select-all-cancel="selectAllCancel"
      @on-select-cancel="selectCancel"
      :loading="tableLoading"
      :columns="tableCols"
      :data="list"
      :height="$store.state.app.clientHeight - 370"
    >
      <template slot-scope="{ row }" slot="name">
        <div class="lv-box">
          <div class="label">头像：</div>
          <div class="value">
            <viewer v-if="row.avatar" :images="[row.avatar]" class="flex flex-item-center">
              <img :src="row.avatar | imageStyle" alt="头像" class="avatar-img" />
            </viewer>
            <div v-else>-</div>
          </div>
        </div>
        <div class="lv-box">
          <div class="label">姓名：</div>
          <div class="value">{{ row.name || '-' }}</div>
        </div>
        <div class="lv-box">
          <div class="label">手机号：</div>
          <div class="value">{{ row.mobile || '-' }}</div>
        </div>
      </template>
      <template slot="current_level" slot-scope="{ row }">
        <div>{{ row.level || '-' }}</div>
      </template>

      <template slot="next_level" slot-scope="{ row }">
        <div>{{ row.next_level_name || '-' }}</div>
      </template>
      <template slot="clinic_info" slot-scope="{ row }">
        <Tooltip :max-width="300" :disabled="row.clinic_name.length < 10" :content="row.clinic_name" placement="top">
          <div class="ecs ecs-2" :class="{ cursor: row.clinic_name.length > 9 }">
            {{ row.clinic_name || '-' }}
          </div>
        </Tooltip>
      </template>

      <template slot="opc_name" slot-scope="{ row }">
        <div>{{ row.opc_name || '-' }}</div>
      </template>

      <template slot="create_info" slot-scope="{ row }">
        <div class="lv-box">
          <div class="label">创建来源：</div>
          <div class="value">{{ row.source_text || '-' }}</div>
        </div>
        <div class="lv-box">
          <div class="label">创建人：</div>
          <div class="value">{{ row.creator || '-' }}</div>
        </div>
        <div class="lv-box">
          <div class="label">创建时间：</div>
          <div class="value">{{ row.create_time | date_format }}</div>
        </div>
      </template>

      <template slot="update_time" slot-scope="{ row }">
        <div>{{ row.update_time | date_format }}</div>
      </template>

      <template slot="action" slot-scope="{ row, index }">
        <OperationFolding :maxVisibleActions="2" :actions="getActions(row, index)" :row="row"></OperationFolding>
      </template>
    </Table>

    <div class="block_20"></div>
    <div style="height: 30px">
      <div class="btn-group" v-show="queryFormData.list_status === '1' || queryFormData.list_status === '2'">
        <div class="group-label">批量操作:</div>
        <Button type="primary" @click="batchDispatch">分发门店</Button>
      </div>
    </div>
    <KPage
      :current="+queryFormData.page"
      :page-size="+queryFormData.pageSize"
      :total="total"
      style="text-align: center"
      @on-change="handleCurrentChange"
      @on-page-size-change="handleSizeChange"
    />
    <!-- 分发门店 -->
    <dispatch-modal
      v-model="dispatchVisible"
      :selected_items="selected_items"
      @success="dispatchSuccess"
    ></dispatch-modal>
  </div>
</template>

<script>
import S from '@/utils/util'; // Some commonly used tools
import search from '@/mixins/search';
import storeSearch from './components/store-search.vue';
import dispatchModal from './components/dispatchModal.vue';
let init_query_form_data = {
  page: 1,
  pageSize: 20,
  name: '',
  mdp_role_name: '',
  mdp_level_id: '',
  has_card_img: '',
  // status: '',
  list_status: '1',
  clinic_id: '',
  r: ''
};

export default {
  name: 'list',
  components: { storeSearch, dispatchModal },
  mixins: [search],
  data() {
    return {
      queryFormData: { ...init_query_form_data },
      apiName: 'getMdpMemberList',
      list_status_count: {},
      cols: [
        { type: 'selection', align: 'center', width: 40, hideSelectAll: true },
        { title: '员工姓名', slot: 'name', align: 'center', minWidth: 180 },
        { title: 'MDP身份', key: 'mdp_role_name', align: 'center', minWidth: 100 },
        { title: '当月生效职级', slot: 'current_level', align: 'center', minWidth: 100 },
        { title: '次月生效职级', slot: 'next_level', align: 'center', minWidth: 100 },
        { title: '所属门店', slot: 'clinic_info', align: 'center', minWidth: 100 },
        { title: '所属运营中心', slot: 'opc_name', align: 'center', minWidth: 100 },
        { title: '类型', key: 'audit_type_text', align: 'center', minWidth: 100 },
        { title: '创建信息', slot: 'create_info', align: 'center', minWidth: 190 },
        { title: '更新时间', slot: 'update_time', align: 'center', minWidth: 140 },
        { title: '操作', slot: 'action', align: 'center', minWidth: 120 }
      ],
      tableLoading: false,

      list: [],
      total: 0,
      list_status: [], // tab type
      mdp_level: [],
      has_card_img: [],
      status_list: [],
      selected_items: {},
      dispatchVisible: false
    };
  },
  created() {
    this.getMdpMemberOptions();
    this.queryFormData = S.merge(this.queryFormData, this.$route.query);
    this.submitQueryForm(true);
  },
  watch: {
    list() {
      this.echoSelectedItems();
    }
  },
  computed: {
    tableCols() {
      return this.cols.filter(item => {
        if (this.queryFormData.list_status !== '1' && this.queryFormData.list_status !== '2') {
          return item.type !== 'selection';
        } else {
          return item;
        }
      });
    },
    level_list() {
      let list = [];
      this.mdp_level.some(item => {
        if (item.role_name === this.queryFormData.mdp_role_name) {
          list = item.list;
          return true;
        }
      });
      return list;
    },
    getActions() {
      return (row, index) => {
        return [
          {
            label: '审核',
            handler: this.toDetail,
            tagType: 'a',
            params: { row: row, field: 'audit_info' },
            isHidden: row.audit_status !== '1'
          },
          {
            label: '重新提交',
            handler: this.toDetail,
            tagType: 'a',
            params: { row: row },
            isHidden: row.audit_status !== '3' || this.queryFormData.list_status === '2'
          },
          {
            label: '编辑',
            handler: this.toDetail,
            tagType: 'a',
            params: { row: row },
            isHidden: !(row.audit_status == '2' && this.queryFormData.list_status === '1')
          },
          {
            label: '详情',
            handler: this.toDetail,
            tagType: 'a',
            params: { row: row, type: 'detail' }
          }
        ];
      };
    },
    getTagColor() {
      return type => {
        switch (type) {
          case '2':
            return 'warning';
          case '3':
            return 'error';
          case '1':
            return 'success';
          default:
            return 'default';
        }
      };
    }
  },
  methods: {
    dispatchSuccess() {
      this.selected_items = {};
      this.onSearch();
    },
    // 是否存在禁用的员工 todo 当增加离职禁用功能时，会用到
    // handlerListData() {
    // this.list.forEach((item, index) => {
    //   if (item.status === 'OFF') {
    //     this.$set(this.list, index, { ...item, _disabled: true });
    //   }
    // });
    // },
    batchDispatch() {
      if (!Object.keys(this.selected_items).length) {
        this.$Message.error('请先选择员工');
        return;
      }
      this.dispatchVisible = true;
    },
    // 根据选中的数据进行回显
    echoSelectedItems() {
      this.list.forEach((item, index) => {
        if (item.id in this.selected_items) {
          this.list[index]._checked = true;
        }
      });
    },
    checkM(waitAddList = []) {
      let is_has = Object.values(this.selected_items).some(item => item.is_m_role === '1');
      let wait_is_has = waitAddList.some(item => item.is_m_role === '1');
      if ((Object.keys(this.selected_items).length && wait_is_has) || is_has) {
        this.$Message.error('店长分发不能和其他角色组合');
        return false;
      } else {
        return true;
      }
    },
    // 全选
    selectAll(val) {
      val.forEach((item, index) => {
        this.$set(this.list, index, { ...item, _checked: true });
        this.$set(this.selected_items, item.id, item);
      });
    },
    // 选择单个
    select(val, row) {
      if (this.checkM([row])) {
        this.$set(this.selected_items, row.id, row);
      } else {
        let index = this.list.findIndex(item => item.id === row.id);
        setTimeout(() => {
          this.$set(this.list, index, { ...row, _checked: false });
        }, 0);
      }
    },
    // 全部取消
    selectAllCancel() {
      this.list &&
        this.list.forEach((item, index) => {
          this.$set(this.list, index, { ...item, _checked: false });
          this.$delete(this.selected_items, item.id);
        });
    },
    // 取消单个
    selectCancel(val, row) {
      let index = this.list.findIndex(item => item.id === row.id);
      this.$set(this.list, index, { ...row, _checked: false });
      this.$delete(this.selected_items, row.id);
    },
    mdpChange() {
      this.queryFormData.mdp_level_id = '';
      this.queryFormData.has_card_img = '';
      this.$refs?.levelSelect?.clearSingleSelect()
    },
    handlerListData(data) {
      this.list_status_count = data.list_status_count;
    },
    getMdpMemberOptions() {
      let params = {};
      this.$api.getMdpMemberOptions(params).then(
        res => {
          this.list_status = S.descToArrHandle(res.list_status);
          this.mdp_level = res.mdp_level || [];
          this.has_card_img = S.descToArrHandle(res.has_card_img);
          // this.status_list = S.descToArrHandle(res.status);
        },
        err => this.$Message.error(err.errmsg)
      );
    },
    onSearch: function () {
      this.queryFormData.page = 1;
      this.submitQueryForm();
    },
    onResetSearch: function () {
      this.queryFormData = { ...init_query_form_data };
      this.submitQueryForm();
    },

    onPageChange: function (page, pageSize) {
      this.queryFormData.page = page;
      this.queryFormData.pageSize = pageSize;
      this.submitQueryForm();
    },

    onStatusChange: function (list_status) {
      this.queryFormData.page = 1;
      this.queryFormData.list_status = list_status;
      this.submitQueryForm();
    },
    create() {
      this.$router.push('/mdp/mdp/create');
    },
    toDetail({ row, type, field }) {
      if(row.audit_status === '3') {
        field = 'audit_info';
      }
      this.$router.push({
        path: '/mdp/mdp/create',
        query: {
          id: row.id,
          type,
          field,
        }
      });
    }
  },

  beforeRouteUpdate: function (to, from, next) {
    this.queryFormData = S.merge(init_query_form_data, to.query);
    this.loadList();
    next();
  }
};
</script>

<style lang="less" scoped>
.mr8 {
  margin-right: 8px;
}
.avatar-img {
  width: 30px;
  height: 30px;
  cursor: pointer;
  border-radius: 50%;
  object-fit: cover;
}
.lv-box {
  display: flex;
  align-items: center;

  .label {
    width: 60px;
    min-width: 60px;
    text-align: right;
  }

  .value {
    color: #999;
    text-align: left;
  }
}
.btn-group {
  .group-label {
    margin-right: 10px;
  }
  display: flex;
  align-items: center;
}
.cursor {
  cursor: pointer;
}
</style>
