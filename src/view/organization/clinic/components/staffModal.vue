<template>
  <Modal
    :value="value"
    width="1000px"
    :title="title"
    :footer-hide="true"
    :mask-closable="false"
    :lock-scroll="true"
    @on-visible-change="changeVisible"
  >
    <div class="content">
      <div class="flex">
        <Input
          class="mr10"
          v-model="queryFormData.keywords"
          placeholder="用户名/手机号/账号"
          clearable
          style="width: 180px"
        />

        <Select
          class="mr10"
          v-model="queryFormData.role_id"
          placeholder="请选择后台角色"
          style="width: 180px"
          clearable
        >
          <Option value="">全部</Option>
          <Option v-for="(item, index) in roles" :label="item.name" :value="item.id" :key="index">
            <div class="flex flex-item-between">
              <span>{{ item.name }}</span>
              <span class="text-role">{{ item.source === 'SYSTEM' ? '系统内建' : '自定义' }}</span>
            </div>
          </Option>
        </Select>

        <Select v-model="queryFormData.status" placeholder="请选择状态" style="width: 180px" clearable>
          <Option value="">全部</Option>
          <Option v-for="(status, statusKey) in statusDesc" :value="statusKey" :key="statusKey"
            >{{ status.desc }}
          </Option>
        </Select>

        <Button class="ml10" type="primary" @click="onSearch">筛选</Button>
        <Button class="ml10" type="default" @click="onResetSearch">重置</Button>
      </div>
      <div style="margin-top: 20px">
        <Table height="360" ref="selection" :columns="tableCols" :data="list" :loading="tableLoading">
          <template slot-scope="{ row }" slot="name">
            {{ row.name }}
          </template>
          <template slot-scope="{ row }" slot="mdp_role_name">
            {{ row.mdp_role_name || '-' }}
          </template>
          <template slot-scope="{ row }" slot="mdp_level_name">
            {{ row.mdp_level_name || '-' }}
          </template>
          <template slot-scope="{ row }" slot="mobile">
            {{ row.mobile }}
          </template>
          <template slot-scope="{ row }" slot="account">
            {{ row.account }}
          </template>
          <template slot-scope="{ row }" slot="weAppRole">
            {{ row.weapp_role_name || '-' }}
          </template>

          <template slot-scope="{ row, index }" slot="sign">
            <div v-if="row.audit_status === '1'">
              <div v-show="!row.sign">-</div>
              <div v-show="row.sign" style="cursor: pointer">
                <viewer v-if="row.sign" :images="[row.sign]" class="flex flex-item-center">
                  <img :src="row.sign | imageStyle" class="image" style="width: 60px; height: auto; margin: 10px 0px" />
                </viewer>
                <span v-else>-</span>
              </div>
            </div>
            <div v-else>-</div>
          </template>
          <template slot-scope="{ row }" slot="role">
            <div class="flex flex-wrap pt8" v-if="row.role_name.length" style="flex-wrap: wrap">
              <div
                class="sys-tag"
                :class="{ 'custom-tag': item.source === 'CUSTOM' }"
                v-for="(item, index) in row.role_name"
                :key="index"
              >
                <Tooltip theme="light" trigger="hover" :content="item.source_text">
                  <div>{{ item.name }}</div>
                </Tooltip>
              </div>
            </div>
            <div v-else>-</div>
          </template>
          <template slot-scope="{ row }" slot="auditStatus">
            <!-- 已认证 -->
            <div class="flex flex-item-align" v-if="row.audit_status === '1'">
              <img
                v-if="row.status === 'OFF'"
                style="width: 14px; height: 14px"
                src="https://img-sn-i01s-cdn.rsjxx.com/rsjxx/2024/0701/105048_91063.png"
              />
              <img
                v-else
                style="width: 14px; height: 14px"
                src="https://img-sn-i01s-cdn.rsjxx.com/rsjxx/2024/0626/160353_47936.png"
              />
              <p style="margin-left: 7px">{{ row.audit_status_text }}</p>
            </div>
            <!-- 审核中 -->
            <div class="flex flex-item-align" v-else-if="row.audit_status === '2'">
              <img
                v-if="row.status === 'OFF'"
                style="width: 14px; height: 14px"
                src="https://img-sn-i01s-cdn.rsjxx.com/rsjxx/2024/0701/105857_86928.png"
              />
              <img
                v-else
                style="width: 14px; height: 14px"
                src="https://img-sn-i01s-cdn.rsjxx.com/rsjxx/2024/0626/161037_99513.png"
              />
              <p style="margin-left: 7px">{{ row.audit_status_text }}</p>
            </div>

            <!-- 去认证 -->
            <div v-else>
              <div v-if="row.is_doc == 1">-</div>
              <div v-else>{{ row.audit_status_text }}</div>
            </div>
          </template>
          <template slot-scope="{ row }" slot="enable_commission">
            <span>{{ row.enable_commission == '1' ? '参与' : '不参与' }}</span>
          </template>
          <template slot-scope="{ row }" slot="status">
            <span v-if="row.status == 'OFF'" class="text-danger">{{ statusDesc[row.status].desc }}</span>
            <span v-else>{{ statusDesc[row.status].desc }}</span>
          </template>
          <template slot-scope="{ row, index }" slot="operate">
            <OperationFolding :actions="getActions(row, index)" :row="row"></OperationFolding>
          </template>
        </Table>

        <div class="block_20"></div>

        <KPage
          :total="total"
          :page-size="queryFormData.pageSize"
          :current="queryFormData.page"
          @on-change="handleCurrentChange"
          @on-page-size-change="handleSizeChange"
          style="text-align: center"
        />
      </div>
    </div>
  </Modal>
</template>

<script>
let init_query_from_data = {
  page: 1,
  pageSize: 20,
  keywords: '',
  role_id: '',
  status: ''
};
const ACTIONS = act => {
  return { ENABLED: '启用', DISABLED: '停用' }[act];
};
export default {
  name: 'staffModal',
  mixins: [],

  components: {},
  props: {
    value: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      default: '门店员工'
    },
    current_row: {
      type: Object,
      default: () => {}
    }
  },

  data() {
    return {
      queryFormData: { ...init_query_from_data },
      cols: [
        { title: '姓名', slot: 'name', minWidth: 80 },
        { title: 'MDP身份', slot: 'mdp_role_name', minWidth: 80, type: 'rst' },
        { title: '职级', slot: 'mdp_level_name', minWidth: 60, type: 'rst' },
        { title: '手机号', slot: 'mobile', minWidth: 90 },
        { title: '账号', slot: 'account', minWidth: 90 },
        { title: '后台角色', slot: 'role', minWidth: 150 },
        { title: '资质认证状态', slot: 'auditStatus', minWidth: 100 },
        { title: '小程序权限', slot: 'weAppRole', minWidth: 80 },
        { title: '电子签名', slot: 'sign', minWidth: 80, align: 'center' },
        { title: '服务分佣', slot: 'enable_commission', minWidth: 80 },
        { title: '状态', slot: 'status', fixed: 'right', width: 40 },
        { title: '操作', slot: 'operate', fixed: 'right', minWidth: 80, align: 'left' }
      ],
      tableLoading: false,

      list: [],
      total: 0,
      typeList: [], //服务类型数据
      statusDesc: {},
      roles: []
    };
  },

  computed: {
    tableCols() {
      if (this.current_row.is_rst === '1') {
        return this.cols.filter(item => item.slot !== 'enable_commission');
      } else {
        return this.cols.filter(item => item.type !== 'rst');
      }
    },
    getActions() {
      return (row, index) => {
        return [
          {
            label: row.status === 'OFF' ? '启用账号' : '停用账号',
            confirmText: row.status === 'OFF' ? '确定启用账号?' : '确定停用账号？',
            handler: this.onStatus,
            params: {
              id: row.id,
              act: row.status === 'OFF' ? 'ENABLED' : 'DISABLED'
            },
            tagType: 'Poptip',
            isHidden: row.source === 'SYSTEM' && row.role_id === '1'
          }
        ];
      };
    }
  },

  watch: {},

  created() {},

  mounted() {},

  destroyed() {},

  methods: {
    changeVisible(visible) {
      if (visible) {
        this.onSearch();
      } else {
        this.closeModal();
      }
    },

    clearData() {
      this.queryFormData = { ...init_query_from_data };
      this.queryFormData.page = 1;
      this.list = [];
      this.total = 0;
    },

    closeModal() {
      this.clearData();
      this.$emit('input', false);
    },

    confirm() {
      this.$emit('success');
      this.closeModal();
    },

    onSearch: function () {
      this.queryFormData.page = 1;
      this.get();
    },

    onResetSearch() {
      this.queryFormData = { ...init_query_from_data };
      this.get();
    },

    handleSizeChange(val) {
      console.log('-> val', val);
      this.queryFormData.page = 1;
      this.queryFormData.pageSize = val;
      this.get();
    },
    handleCurrentChange(val) {
      this.queryFormData.page = val;
      this.get();
    },

    get() {
      this.tableLoading = true;
      let params = {
        ...this.queryFormData,
        clinic_id: this.current_row?.clinic_id,
        is_rst: this.current_row.is_rst === '1'
      };
      this.$api.getMemberClinicMemberList(params).then(data => {
        this.list = data.members;
        this.statusDesc = data.statusDesc;
        this.roles = data.all_roles;
        this.total = data.total;
        this.tableLoading = false;
      });
    },

    onStatus(params) {
      this.$api.getMemberClinicMemberStatus(params).then(
        res => {
          this.$Message.success(ACTIONS(params.act) + '成功');
          this.onSearch();
        },
        err => this.$Message.error(err.errmsg)
      );
    }
  }
};
</script>

<style scoped lang="less">
::v-deep .ivu-modal-body {
  max-height: 500px;
  min-height: 500px;
  overflow-y: auto;
}
</style>

<style lang="less" scoped>
.text-role {
  color: #8c8c8c;
  font-size: 12px;
  padding-right: 16px;
}

.rit-text {
  float: right;
  color: #aaa;
  width: 170px;
  text-align: right;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.sys-tag {
  cursor: pointer;
  padding: 1px 8px;
  width: fit-content;
  background: #3088ff;
  border-radius: 3px;
  font-weight: 400;
  font-size: 12px;
  color: #ffffff;
  //line-height: 20px;
  margin-right: 8px;
  margin-bottom: 8px;
  display: flex;
  align-items: center;
  min-width: fit-content;

  &:last-child {
    margin-right: 0px;
  }
}

.custom-tag {
  cursor: pointer;
  padding: 1px 8px;
  width: fit-content;
  background: rgba(48, 136, 255, 0.05) !important;
  color: #3088ff !important;
  border-radius: 3px;
  border: 1px solid #3088ff;
}

.pt8 {
  padding-top: 8px;
}

p {
  margin-bottom: 0px;
}

::v-deep .ivu-table .del-cell td {
  color: #c0c4cc !important;

  a,
  p,
  div,
  span {
    color: #c0c4cc !important;
  }

  .sys-tag {
    background: #c0c4cc !important;

    div {
      color: #fff !important;
    }
  }

  .custom-tag {
    background: rgba(48, 136, 255, 0.05) !important;
    border: 1px solid #c0c4cc;

    div {
      color: #c0c4cc !important;
    }
  }
}

.custom-picture-radio {
  padding-top: 10px;

  .picture-list .picture-list__pic {
    width: 76px;
    height: 30px;
  }
}
</style>
