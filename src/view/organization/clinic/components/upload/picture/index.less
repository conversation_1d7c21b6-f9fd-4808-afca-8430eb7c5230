.picture-wrapper {
  //padding: 10px 0;
}
.picture-list {
  display: flex;
  flex-wrap: wrap;
}
.picture-list .picture-list__pic {
  position: relative;
  display: inline-block;
  //background: no-repeat;
  //background-size: cover;
  //background-position: 50%;
  width: 65px;
  height: 65px;
  margin: 0 10px 10px 0;
  cursor: move;
}
.picture-list__remove {
  position: absolute;
  width: 20px;
  height: 20px;
  top: -10px;
  right: -10px;
  cursor: pointer;
  background: url(https://img-sn-i01s-cdn.rsjxx.com/backstage/2019/0509/103116_32332.png)
    no-repeat;
  background-size: 20px 20px;
  background-position: 50%;
}
.picture-list .ks-dd-dragging {
  opacity: 0.5;
  z-index: 2;
}
.picture-list .ks-dd-proxy {
  opacity: 1;
  position: absolute;
}
.picture-list .ks-dd-dragging .picture-list__remove {
  display: none;
}
.picture-upload {
  display: inline-block;
  margin: 0 10px 10px 0;
}
.picture-upload .picture-upload-icon {
  display: inline-block;
  width: 65px;
  height: 65px;
  margin: 0 0 10px;
  border: 1px dashed #bbb;
  border-radius: 2px;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
  cursor: pointer;
  background: url(https://img-sn-i01s-cdn.rsjxx.com/backstage/2019/0509/103258_76866.png)
    no-repeat;
  background-size: 26px 22px;
  background-position: 50%;
}
.picture-upload {
  width: 65px;
  height: 65px;
}
.picture-upload img {
  width: 100%;
}
.pdf-box {
  position: relative;
  margin-right: 15px;
}
.picture-upload-icon {
  position: relative;
  > span {
    position: absolute;
    display: inline-block;
    width: 100%;
    text-align: center;
    top: 46px;
    font-weight: bold;
    color: #bbbbbb;
  }
}
