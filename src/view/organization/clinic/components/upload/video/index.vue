<template>
  <div class="flex picture-wrapper">
    <!-- </div> -->
    <div class="picture-list">
      <draggable v-model="getImgUrls" style="display: inline" @change="onDragChange">
        <div class="picture-list__pic" v-for="(url, key) in videoList" :key="key" @click="clickFunc(url)">
          <!-- <img -->
          <!--   :src="url + '-B.w300'" -->
          <!--   :data-source="url" -->
          <!--   style="position: absolute; width: 100%; height: 100%; left: 0" -->
          <!-- /> -->

          <Tooltip content="点击查看">
            <a
              target="_blank"
              style="
                font-size: 50px;
                text-align: center;
                display: inline-block;
                width: 100%;
                padding-top: 6px;
                padding-left: 6px;
              "
              @click="clickFunc(url)"
            >
              <svg-icon iconClass="video" style="color: #5e5e5e"></svg-icon>
            </a>
          </Tooltip>
          <!-- <a @click="clickFunc(img)">点击查看视频{{ key + 1 }}</a> -->
          <div class="picture-list__remove" @click="onRemove(key)" v-if="!isQueryDetail"></div>
        </div>
      </draggable>
    </div>

    <!-- 添加图片样式会换行,暂时手动调整 -->
    <div class="picture-display">
      <div class="picture-upload" v-show="showUploadBtn">
        <Upload
          action="https://upload.qiniup.com"
          ref="upload"
          name="file"
          type="select"
          accept="video/*"
          :multiple="multiple"
          :data="{ token: token }"
          :format="formatList"
          :show-upload-list="false"
          :before-upload="onBeforeUpload"
          :on-progress="onProgress"
          :on-success="onSuccess"
          :on-error="onError"
          :disabled="disabled"
        >
          <div v-if="!isQueryDetail" class="picture-upload-icon">
            <span>{{ getImgUrls.length }} / {{ limit }}</span>
          </div>
        </Upload>
      </div>
      <div>
        <slot style="align-self: self-end"></slot>
      </div>
    </div>

    <Modal
      v-model="visible"
      title="视频预览"
      :mask-closable="false"
      :width="750"
      class-name="vertical-center-modal"
      footer-hide
    >
      <video v-if="visible" controls :autoplay="true" :src="videoUrl" style="width: 100%; height: 100%; left: 0" />
    </Modal>
  </div>
</template>

<script>
import Vue from 'vue';
import draggable from 'vuedraggable';
import 'viewerjs/dist/viewer.css';
import './index.less';
import Viewer from 'v-viewer';
import imageUrl from 'utils/imageUrl';
import request from 'utils/request';

Vue.use(Viewer);

export default {
  name: 'k-picture',
  components: {
    draggable
  },
  props: {
    limit: {
      type: Number,
      default: 1
    },
    value: {
      type: [String, Array],
      default() {
        return [];
      }
    },
    isQueryDetail: {
      type: Boolean,
      default: false
    },
    disabled: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      showUploadBtn: true,
      imgUrls: [],
      isArray: true,
      token: '',
      domain: '',
      fileUrls: [],
      formatList: ['mp4'],
      visible: false,
      videoUrl: ''
    };
  },
  created() {
    // console.log( this.isQueryDetail )
    // console.log("-> %c this.$attrs  === %o", "font-size: 15px;color: green;", this.$attrs)
  },
  methods: {
    onUploadSuccess: function (imgUrl) {
      if (this.imgUrls.length < this.limit) {
        this.imgUrls.push(imgUrl);
        this.notifyEmit();
      }
    },
    onRemove: function (key) {
      this.imgUrls.splice(key, 1);
      this.notifyEmit();
    },
    onDragChange: function () {
      this.notifyEmit();
    },
    notifyEmit: function () {
      this.$emit('input', this.isArray ? this.imgUrls : this.imgUrls[0] || '');
      this.$emit('change', this.isArray ? this.imgUrls : this.imgUrls[0] || '');
    },
    onBeforeUpload: async function (file) {
      console.log(file);
      // 都转mb
      let fileSize = file.size / (1024 * 1024);
      let size = 1024;
      console.log(fileSize, size);
      if (fileSize > size) {
        this.$Message.error('上传文件大小不能超过1G');
        return;
      }
      let ext =
        file.name.lastIndexOf('.') > 0
          ? file.name.substring(file.name.lastIndexOf('.') + 1, file.name.length).toLowerCase()
          : '';
      console.log(ext);

      this.$Message.warning({
        content: '正在上传中...'
      });
      await request.get('/pms_opc/qiniu.getuptoken', { data: { ext } }).then(
        data => {
          this.token = data.token;
          this.domain = data.domain;
        },
        reject => {
          this.$Message.error({
            content: reject.errmsg || String(reject),
            duration: 3
          });
          throw new Error(reject.errmsg || reject);
        }
      );
    },

    onProgress: function (event, file, fileList) {
      // S.log(event, 'onProgress event')
      // S.log(file, 'onProgress file')
      // S.log(fileList, 'onProgress fileList')
    },

    onSuccess: function (response, file, fileList) {
      console.log('-> response', response);
      let fileUrl = this.domain + '/' + response.key;
      this.fileUrls.push(fileUrl);
      // this.onUploadSuccess(fileUrl, this.fileUrls);
      if (this.imgUrls.length < this.limit) {
        this.imgUrls.push(fileUrl);
        this.notifyEmit();
      }
      this.$Message.success({
        content: '上传成功'
      });
      // this.$emit('on-success', fileUrl, this.fileUrls);
      // S.log(response, 'onSuccess response')
      // S.log(file, 'onSuccess file')
      // S.log(fileList, 'onSuccess fileList')
    },

    onError: function (response, file, fileList) {
      this.$Message.error({
        content: file.error
      });
      // S.log(response, 'onError response')
      // S.log(file, 'onError file')
      // S.log(fileList, 'onError fileList')
    },
    clickFunc(item) {
      console.log(item);
      this.videoUrl = item;
      this.visible = true;
    }
  },
  computed: {
    multiple() {
      return this.limit <= 1 ? false : true;
    },
    videoList() {
      return this.imgUrls.filter(item => {
        return item.indexOf('.mp4') > -1;
      });
    },
    getImgUrls: {
      get() {
        const imgSuffix = this.formatList;
        console.log(
          this.imgUrls.filter(item => {
            const suffix = item.split('.').pop();
            return imgSuffix.includes(suffix);
          })
        );
        return this.imgUrls.filter(item => {
          const suffix = item.split('.').pop();
          return imgSuffix.includes(suffix);
        });
      },
      set(val) {
        console.log(val);
        this.imgUrls = val;
      }
    }
  },
  watch: {
    value: {
      immediate: true,
      handler: function () {
        console.log(this.value);
        if (typeof this.value == 'string') {
          this.isArray = false;
          this.imgUrls = this.value != '' ? [this.value] : [];
          console.log(this.imgUrls);
        } else {
          this.imgUrls = [...this.value];
        }
      }
    },

    imgUrls: {
      immediate: true,
      handler: function () {
        // imageUrl
        // console.log( this.imgUrls )
        for (let key in this.imgUrls) {
          this.imgUrls[key] = imageUrl.imageCdnDomain(this.imgUrls[key]);
        }
        this.showUploadBtn = this.imgUrls.length >= this.limit ? false : true;
      }
    }
  }
};
</script>

<style lang="less" scoped></style>
