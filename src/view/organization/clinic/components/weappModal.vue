<template>
  <Modal
    :value="value"
    width="500px"
    :title="title"
    :footer-hide="false"
    :mask-closable="false"
    :lock-scroll="true"
    @on-visible-change="changeVisible"
  >
    <div class="content">
      <Form ref="formData" :model="formData" :label-width="100" :label-colon="true" :rules="formDataRules">
        <FormItem label="小程序简称" prop="weapp_name">
          <Input
            v-model.trim="formData.weapp_name"
            placeholder="请输入小程序简称"
            maxlength="10"
            show-word-limit
          />
          <div class="flex flex-item-align">
            <div style="color: #ccc;">应用于榕树堂小程序门店的名称展示</div>
            <tooltip placement="right" max-width="400">
              <a style="margin-left: 10px;">示例</a>
              <template #content>
                <img style="width: 300px;" src="https://static.rsjxx.com/image/2025/0524/183309_53231.png" />
              </template>
            </tooltip>
          </div>
        </FormItem>
        <FormItem label="小程序状态" prop="is_hide_mp">
          <RadioGroup v-model="formData.is_hide_mp">
            <Radio label="0">显示</Radio>
            <Radio label="1">隐藏</Radio>
          </RadioGroup>
          <div style="color:#ccc;">是否在榕树堂小程序中显示门店</div>
          <div v-if="current_row.ap_switch !== 'ENABLE'"   style="color:red;">诊所暂未开通线上统一收银功能</div>
        </FormItem>
      </Form>
    </div>
    <div slot="footer">
      <Button @click="closeModal">取消</Button>
      <Button :loading="confirmLoading" type="primary" @click="confirm">确定</Button>
    </div>
  </Modal>
</template>

<script>
import cloneDeep from 'lodash.clonedeep';
export default {
  name: 'weappModal',
  mixins: [],

  components: {},
  props: {
    value: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      default: '小程序设置'
    },
    current_row: {
      type: Object,
      default: () => {}
    }
  },

  data() {
    return {
      confirmLoading: false,
      formData: {
        weapp_name: '',
        is_hide_mp: '1'
      },
      formDataRules: {
        weapp_name: [{ required: true, message: '请输入小程序简称', trigger: 'change' }],
        is_hide_mp: [{ required: true, message: '请选择小程序状态', trigger: 'change' }]
      }
    };
  },

  computed: {},

  watch: {},

  created() {},

  mounted() {},

  destroyed() {},

  methods: {
    changeVisible(visible) {
      if (visible) {
        let row = cloneDeep(this.current_row);
        this.formData.weapp_name = row.weapp_name;
        this.formData.is_hide_mp = row.is_hide_mp;
      } else {
        this.closeModal();
      }
    },

    clearData() {
      // 清除表单数据
      this.$refs.formData.resetFields();
    },

    closeModal() {
      this.clearData();
      this.$emit('input', false);
    },

    confirm() {
      this.$refs.formData.validate(valid => {
        if (valid) {
          this.getClinicEnterChangeweapp();
        }
      });
    },

    getClinicEnterChangeweapp() {
      let params = {
        id: this.current_row.id,
        ...this.formData
      };
      this.confirmLoading = true;
      this.$api
        .getClinicEnterChangeweapp(params)
        .then(
          res => {
            this.$emit('success');
            this.confirmLoading = false;
            this.closeModal();
          },
          err => this.$Message.error(err.errmsg)
        )
        .finally(() => (this.confirmLoading = false));
    }
  }
};
</script>

<style scoped lang="less"></style>
