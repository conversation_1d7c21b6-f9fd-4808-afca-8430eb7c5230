<template>
  <div>
    <Form
      ref="clinicForm"
      :disabled="$route.query.open_status === 'EXAMINE' || $route.query.open_status === 'details'"
      :label-width="180"
      :model="formData"
      :rules="ruleValidate"
      label-colon
    >
      <div class="block-header">诊所基本信息</div>
      <div>
        <Row>
          <Col span="13">
            <!--诊所类型在创建诊所时或状态为已驳回时可编辑(暂时兼容线上未填写的诊所)-->
            <FormItem label="诊所类型" prop="type">
              <div class="flex">
                <Select
                  v-model="formData.direct_type"
                  placeholder="请选择诊所类型"
                  :disabled="formData.open_status !== 'EXAMINE_FAIL' && disableEditType"
                  :class="{ validError: formData.direct_type }"
                  @on-change="formData.type = ''"
                >
                  <Option v-for="item in directTypeDesc" :key="item.id" :value="item.id">{{ item.desc }}</Option>
                </Select>
                <Select
                  v-model="formData.type"
                  placeholder="请选择诊所子类型"
                  :disabled="(formData.open_status !== 'EXAMINE_FAIL' && disableEditType) || !formData.direct_type"
                  @on-change="changeClinicType"
                >
                  <Option v-for="item in getTypeDesc" :key="item.id" :value="item.id">{{ item.desc }}</Option>
                </Select>
              </div>
            </FormItem>
          </Col>

          <Col span="13">
            <FormItem v-if="isIdShow" label="开业状态" prop="open_status">
              <Select v-model="formData.open_status" disabled placeholder="开业状态">
                <Option v-for="(item, key) in openStatusDesc" :key="key" :value="item.id">{{ item.desc }}</Option>
              </Select>
            </FormItem>
          </Col>

          <Col span="13">
            <FormItem
              v-if="formData.open_status === 'EXAMINE_FAIL'"
              label="驳回原因"
              style="color: red"
              class="red-item"
            >
              <div style="color: red; width: 100%; border: 1px solid red; border-radius: 2px; padding-left: 8px">
                {{ formData.examine_rejected_msg }}
              </div>
            </FormItem>
          </Col>

          <Col span="13">
            <FormItem v-if="defaultFormData.disabled_msg" label="禁用原因" style="color: red" class="red-item">
              <div style="color: red; width: 100%; border: 1px solid red; border-radius: 2px; padding-left: 8px">
                {{ formData.disabled_msg }}
              </div>
            </FormItem>
          </Col>

          <Col span="13">
            <FormItem
              v-if="formData.disabled_reject_msg || formData.enabled_reject_msg"
              :label="`驳回${formData.disable_audit_status === 'DISABLE_REJECT' ? '禁用' : '启用'}原因`"
              style="color: red"
              class="red-item"
            >
              <div style="color: red; width: 100%; border: 1px solid red; border-radius: 2px; padding-left: 8px">
                {{ formData.disabled_reject_msg || formData.enabled_reject_msg }}
              </div>
            </FormItem>
          </Col>

          <Col span="13">
            <FormItem label="诊所名称">
              <Input v-model="formData.name" placeholder="诊所名称"></Input>
            </FormItem>
          </Col>

          <Col span="13">
            <FormItem v-if="isIdShow" label="诊所编号" prop="manufacturer">
              <Input v-model="formData.clinic_code" :disabled="isUserEdit" placeholder="诊所编号"></Input>
            </FormItem>
          </Col>

          <Col span="13">
            <FormItem label="诊所门店编号" prop="offline_serial_number">
              <div class="flex">
                RSJ-
                <Select
                  v-model="formData.offline_province_code"
                  filterable
                  placeholder="省份简写"
                  style="width: 100px; margin: 0 5px"
                >
                  <Option v-for="(item, key) in abbreviationOptions" :key="item.value" :value="item.value"
                    >{{ item.label }}
                  </Option>
                </Select>
                NO.
                <Input
                  style="flex: 1; margin-left: 5px"
                  type="number"
                  :maxlength="4"
                  placeholder="输入4位流水号"
                  @on-change="changeNo"
                  @on-blur="formatNo"
                  v-model="formData.offline_serial_number"
                ></Input>
              </div>
            </FormItem>
          </Col>
          <Col span="13">
            <FormItem label="诊所负责人" prop="leading_person">
              <Input v-model="formData.leading_person" placeholder="诊所负责人"></Input>
            </FormItem>
          </Col>
          <Col span="13">
            <FormItem label="负责人手机号" prop="leading_mobile">
              <Input v-model="formData.leading_mobile" placeholder="负责人手机号"></Input>
            </FormItem>
          </Col>
          <Col span="13">
            <FormItem label="诊所地址">
              <div class="addWrap">
                <div class="addressBox" style="width: 55%">
                  <!--									<v-region v-model="selectedAddress" :disabled="$route.query.open_status==='EXAMINE'"-->
                  <!--									          @values="regionChange"></v-region>-->
                  <el-cascader
                    v-model="selectedAddress"
                    :options="options"
                    clearable
                    :disabled="$route.query.open_status && $route.query.open_status !== 'EXAMINE_FAIL'"
                    placeholder="请选择诊所地址"
                    size="small"
                    popper-class="address-com"
                    style="width: 100%"
                    @change="regionChange"
                  >
                  </el-cascader>
                </div>

                <div class="addressInput ml10">
                  <Input
                    v-model="formData.address_other"
                    :disabled="$route.query.open_status && $route.query.open_status !== 'EXAMINE_FAIL'"
                    placeholder="详细地址"
                  ></Input>
                </div>
              </div>
            </FormItem>
          </Col>

          <Col span="13">
            <FormItem v-if="isRstClinic" label="设置地图点位" prop="lon">
              <a @click="changeShowMap">{{ showMap === true ? '收起' : '展开' }}</a>
              <div class="map" v-show="showMap">
                <a-map
                  :has-position="position"
                  @getAddressDetail="getAddressDetail"
                  :open_status="formData.open_status"
                  :checkDetail="getCheckDetail"
                ></a-map>
              </div>
            </FormItem>

            <FormItem label="诊所经纬度" v-if="$route.query.id && isRstClinic">
              <p>
                <span style="margin-right: 16px">经度：{{ formData.lon }}</span>
                <span>纬度：{{ formData.lat }}</span>
              </p>
            </FormItem>
          </Col>

          <Col span="13">
            <FormItem label="试营业时间">
              <DatePicker
                type="date"
                format="yyyy-MM-dd"
                placeholder="试营业时间"
                style="width: 100%"
                :options="dateOptions"
                @on-change="time => (formData.soft_open_date = time)"
                :value="formData.soft_open_date"
              ></DatePicker>
            </FormItem>
          </Col>

          <Col span="13">
            <FormItem label="开业时间">
              <DatePicker
                type="date"
                format="yyyy-MM-dd"
                placeholder="开业时间"
                style="width: 100%"
                @on-change="time => (formData.open_date = time)"
                :value="formData.open_date"
                :options="dateOptions"
                :disabled="partnerIsEdit('open_date')"
              ></DatePicker>
            </FormItem>
          </Col>

          <Col span="13">
            <FormItem label="诊所合同扫描件（个人）" prop="contract_images">
              <Picture
                accept="image/jpg,image/jpeg,image/png,.pdf"
                :format="['jpg', 'jpeg', 'png', 'pdf']"
                v-model="formData.contract_images"
                :is-query-detail="
                  (isUserEdit &&
                    defaultFormData.contract_images?.length > 0 &&
                    formData.open_status !== 'EXAMINE_FAIL') ||
                  $route.query.open_status === 'details'
                "
                :disabled="
                  (isUserEdit &&
                    defaultFormData.contract_images?.length > 0 &&
                    formData.open_status !== 'EXAMINE_FAIL') ||
                  $route.query.open_status === 'details'
                "
                :limit="9"
              />
              <Input :value="String(formData.contract_images)" v-show="false" />
            </FormItem>
          </Col>

          <Col span="13">
            <FormItem v-if="isUserEdit && formData.use_system_time !== '0'" label="开始使用系统时间">
              <Input :disabled="true" :value="formData.use_system_time | date_format"></Input>
            </FormItem>
          </Col>

          <Col span="13">
            <FormItem label="诊所实际控制人姓名" prop="control_person">
              <Input v-model="formData.control_person" placeholder="请输入诊所实际控制人姓名"></Input>
            </FormItem>
          </Col>
          <Col span="13">
            <FormItem label="诊所实际控制人手机号" prop="control_mobile">
              <Input v-model="formData.control_mobile" placeholder="请输入诊所实际控制人手机号"></Input>
            </FormItem>
          </Col>
          <Col span="13">
            <FormItem label="诊所实际控制人身份证号" prop="control_idcard">
              <Input v-model="formData.control_idcard" placeholder="请输入诊所实际控制人身份证号"></Input>
            </FormItem>
          </Col>
        </Row>
      </div>
    </Form>
    <Form
      v-if="formData.open_status && formData.open_status !== 'EXAMINE' && formData.open_status !== 'EXAMINE_FAIL'"
      ref="prodForm"
      :label-width="290"
      :model="formData"
      :rules="ruleValidate"
      label-colon
      :disabled="$route.query.open_status === 'details'"
    >
      <div class="block-header">诊所筹备进度信息</div>
      <Row>
        <Col v-for="(item, index) in shop_config" :key="item.addr_init" span="14">
          <FormItem :label="`Step.${index + 1} ${item.title}`">
            <Input :value="item.status_option[item.status]" disabled style="flex: 1" />
          </FormItem>
          <FormItem label="预期完成">
            <CheckboxGroup v-model="item.select" @on-change="val => changeOpeningStatus(val, index)">
              <Checkbox
                v-for="(subItem, index) in item.checkbox_option"
                :key="subItem.title"
                :label="index"
                :disabled="caseDownDisa(item, index)"
              >
                {{ subItem.title }}
              </Checkbox>
            </CheckboxGroup>
          </FormItem>

          <FormItem v-if="index === 0 && item.select.includes('1')" label="店面选址的完成时间">
            <DatePicker
              type="date"
              format="yyyy-MM-dd"
              placeholder="试营业时间"
              style="width: 100%"
              @on-change="time => (item.finish_date = time)"
              :value="item.finish_date"
              :disabled="caseDownDisa(item, '1') && historyDisa('finish_date')"
            ></DatePicker>
          </FormItem>
          <FormItem v-if="index === 0 && item.select.includes('1')" label="诊所选址评估报告">
            <div
              v-if="
                ($route.query.open_status === 'EXAMINE' || $route.query.open_status === 'details') &&
                item.estimate_img.length === 0
              "
            >
              -
            </div>
            <Picture
              v-else
              accept="image/jpg,image/jpeg,image/png,.pdf"
              :format="['jpg', 'jpeg', 'png', 'pdf']"
              v-model="item.estimate_img"
              :limit="9"
              :disabled="caseDownDisa(item, '1') && historyDisa('estimate_img')"
              :is-query-detail="caseDownDisa(item, '1') && historyDisa('estimate_img')"
            />
          </FormItem>
          <FormItem v-if="index === 0 && item.select.includes('2')" label="房屋签约合同扫描件">
            <div
              v-if="
                ($route.query.open_status === 'EXAMINE' || $route.query.open_status === 'details') &&
                item.signing_img.length === 0
              "
            >
              -
            </div>
            <Picture
              v-else
              accept="image/jpg,image/jpeg,image/png,.pdf"
              :format="['jpg', 'jpeg', 'png', 'pdf']"
              v-model="item.signing_img"
              :limit="9"
              :disabled="caseDownDisa(item, '2') && historyDisa('signing_img')"
              :is-query-detail="caseDownDisa(item, '2') && historyDisa('signing_img')"
            />
          </FormItem>

          <FormItem v-if="index === 1 && item.select.includes('1')" label="PDF平面图">
            <div
              v-if="
                ($route.query.open_status === 'EXAMINE' || $route.query.open_status === 'details') &&
                item.pdf_plane_img.length === 0
              "
            >
              -
            </div>
            <Picture
              v-else
              accept="image/jpg,image/jpeg,image/png,.pdf"
              :format="['jpg', 'jpeg', 'png', 'pdf']"
              v-model="item.pdf_plane_img"
              :is-query-detail="defaultShopConfig[index].pdf_plane_img.length === 0 ? false : true"
              :disabled="caseDownDisa(item, '1')"
              :limit="9"
            />
          </FormItem>
          <FormItem v-if="index === 1 && item.select.includes('3')" label="装修的开始时间" :required="!isHistoryData">
            <DatePicker
              type="date"
              format="yyyy-MM-dd"
              placeholder="装修的开始时间"
              style="width: 100%"
              @on-change="time => (item.renovation_st = time)"
              :value="item.renovation_st"
            ></DatePicker>
          </FormItem>
          <FormItem v-if="index === 1 && item.select.includes('4')" label="装修的完成时间" :required="!isHistoryData">
            <DatePicker
              type="date"
              format="yyyy-MM-dd"
              placeholder="装修的完成时间"
              style="width: 100%"
              @on-change="time => (item.renovation_et = time)"
              :value="item.renovation_et"
            ></DatePicker>
          </FormItem>
          <FormItem
            v-if="index === 1 && item.select.includes('5')"
            label="工程验收的完成时间"
            :required="!isHistoryData"
          >
            <DatePicker
              type="date"
              format="yyyy-MM-dd"
              placeholder="工程验收的完成时间"
              style="width: 100%"
              @on-change="time => (item.check_accept_date = time)"
              :value="item.check_accept_date"
            ></DatePicker>
          </FormItem>
          <FormItem v-if="index === 1 && item.select.includes('5')" label="请上传验收表" :required="!isHistoryData">
            <Picture
              accept="image/jpg,image/jpeg,image/png,.pdf"
              :format="['jpg', 'jpeg', 'png', 'pdf']"
              :is-query-detail="$route.query.open_status === 'details'"
              v-model="item.check_accept_img"
              :limit="9"
            />
          </FormItem>

          <!-- <FormItem v-if="index === 2 && item.select.includes('business')" label="工商注册凭证" :required="isHistoryData"> -->
          <!--   <Picture v-model="item.business_img" :limit="1" /> -->
          <!-- </FormItem> -->
          <FormItem
            v-if="index === 2 && item.select.includes('business')"
            label="工商注册的开始时间"
            :required="!isHistoryData"
          >
            <DatePicker
              type="date"
              format="yyyy-MM-dd"
              placeholder="工商注册的开始时间"
              style="width: 100%"
              @on-change="time => (item.business_st = time)"
              :value="item.business_st"
            ></DatePicker>
          </FormItem>
          <FormItem
            v-if="index === 2 && item.select.includes('business')"
            label="工商注册的完成时间"
            :required="!isHistoryData"
          >
            <DatePicker
              type="date"
              format="yyyy-MM-dd"
              placeholder="工商注册的完成时间"
              style="width: 100%"
              @on-change="time => (item.business_et = time)"
              :value="item.business_et"
            ></DatePicker>
          </FormItem>
          <FormItem v-if="index === 2 && item.select.includes('business')" label="营业执照名称">
            <Input
              v-model="item.organization_name"
              placeholder="请输入营业执照名称"
              :disabled="defaultShopConfig[index].organization_name?.length ? true : false"
            />
          </FormItem>
          <FormItem
            v-if="index === 2 && item.select.includes('business')"
            label="统一社会信用代码"
            :required="!isHistoryData"
          >
            <Input
              v-model="item.organization_code"
              placeholder="请输入18位统一社会信用代码"
              :disabled="defaultShopConfig[index].organization_code?.length ? true : false"
              maxlength="18"
              show-word-limit
            />
          </FormItem>
          <FormItem
            v-if="index === 2 && item.select.includes('business')"
            label="法人身份证号"
            :required="!isHistoryData"
          >
            <Input v-model="item.legal_person_idcard" placeholder="请输入法人身份证号" />
          </FormItem>
          <FormItem
            v-if="index === 2 && item.select.includes('business')"
            label="法人手机号"
            :required="!isHistoryData"
          >
            <Input v-model="item.legal_person_mobile" placeholder="请输入法人手机号" />
          </FormItem>
          <FormItem v-if="index === 2 && item.select.includes('business')" label="营业执照" :required="!isHistoryData">
            <Picture
              :is-query-detail="$route.query.open_status === 'details'"
              accept="image/jpg,image/jpeg,image/png,.pdf"
              :format="['jpg', 'jpeg', 'png', 'pdf']"
              v-model="item.business_img"
              :limit="9"
            />
          </FormItem>
          <!-- -------- -->
          <FormItem
            v-if="index === 2 && item.select.includes('tcm')"
            label="中医备案证的开始注册时间"
            :required="!isHistoryData"
          >
            <DatePicker
              type="date"
              format="yyyy-MM-dd"
              placeholder="中医备案证的开始注册时间"
              style="width: 100%"
              @on-change="time => (item.tcm_st = time)"
              :value="item.tcm_st"
            ></DatePicker>
          </FormItem>
          <FormItem
            v-if="index === 2 && item.select.includes('tcm')"
            label="中医备案证的完成注册时间"
            :required="!isHistoryData"
          >
            <DatePicker
              type="date"
              format="yyyy-MM-dd"
              placeholder="中医备案证的完成注册时间"
              style="width: 100%"
              @on-change="time => (item.tcm_et = time)"
              :value="item.tcm_et"
            ></DatePicker>
          </FormItem>
          <FormItem v-if="index === 2 && item.select.includes('tcm')" label="中医备案证名称" :required="!isHistoryData">
            <Input v-model="item.tcm_name" placeholder="请输入中医备案证名称" />
          </FormItem>
          <FormItem v-if="index === 2 && item.select.includes('tcm')" label="中医备案证" :required="!isHistoryData">
            <Picture
              :is-query-detail="$route.query.open_status === 'details'"
              accept="image/jpg,image/jpeg,image/png,.pdf"
              :format="['jpg', 'jpeg', 'png', 'pdf']"
              v-model="item.tcm_img"
              :limit="9"
            />
          </FormItem>

          <FormItem v-if="index === 3 && item.select.includes('1')" label="小程序的上线时间" :required="!isHistoryData">
            <DatePicker
              type="date"
              format="yyyy-MM-dd"
              placeholder="小程序的上线时间"
              style="width: 100%"
              @on-change="time => (item.applet_online_date = time)"
              :value="item.applet_online_date"
              :disabled="caseDownDisa(item, '1')"
            ></DatePicker>
          </FormItem>
          <FormItem v-if="index === 3 && item.select.includes('2')" label="银联账户信息" :required="!isHistoryData">
            <Input v-model="item.unionpay_name" placeholder="银联账户信息" :disabled="caseDownDisa(item, '2')" />
          </FormItem>
          <FormItem v-if="index === 3 && item.select.includes('2')" label="银联上线时间" :required="!isHistoryData">
            <DatePicker
              type="date"
              format="yyyy-MM-dd"
              placeholder="银联上线时间"
              style="width: 100%"
              @on-change="time => (item.unionpay_online_date = time)"
              :value="item.unionpay_online_date"
              :disabled="caseDownDisa(item, '2')"
            ></DatePicker>
          </FormItem>

          <FormItem
            v-if="index === 5 && item.select.includes('2')"
            label="中药签约的完成时间"
            :required="!isHistoryData"
          >
            <DatePicker
              type="date"
              format="yyyy-MM-dd"
              placeholder="中药签约的完成时间"
              style="width: 100%"
              @on-change="time => (item.zy_sign_date = time)"
              :value="item.zy_sign_date"
              :disabled="caseDownDisa(item, '2')"
            ></DatePicker>
          </FormItem>

          <FormItem v-if="index === 6 && item.select.includes('1')" label="门店全景视频" :required="!isHistoryData">
            <Video v-model="item.panorama_video" :limit="9" :is-query-detail="$route.query.open_status === 'details'" />
          </FormItem>

          <FormItem v-if="index === 8 && item.select.includes('prove')" label="医证验收图片" :required="!isHistoryData">
            <Picture
              :is-query-detail="$route.query.open_status === 'details'"
              accept="image/jpg,image/jpeg,image/png,.pdf"
              :format="['jpg', 'jpeg', 'png', 'pdf']"
              v-model="item.prove_img"
              :limit="9"
            />
          </FormItem>
          <FormItem
            v-if="index === 8 && item.select.includes('affair')"
            label="店务验收图片"
            :required="!isHistoryData"
          >
            <Picture
              :is-query-detail="$route.query.open_status === 'details'"
              accept="image/jpg,image/jpeg,image/png,.pdf"
              :format="['jpg', 'jpeg', 'png', 'pdf']"
              v-model="item.affair_img"
              :limit="9"
            />
          </FormItem>
          <FormItem
            v-if="index === 8 && item.select.includes('door')"
            label="诊所门头验收图片"
            :required="!isHistoryData"
          >
            <Picture
              :is-query-detail="$route.query.open_status === 'details'"
              accept="image/jpg,image/jpeg,image/png,.pdf"
              :format="['jpg', 'jpeg', 'png', 'pdf']"
              v-model="item.door_img"
              :limit="9"
            />
          </FormItem>

          <FormItem
            v-if="index === 9 && item.select.includes('shop')"
            label="商城入驻协议（PDF版）"
            :required="!isHistoryData"
          >
            <Picture
              accept=".pdf"
              :format="['pdf']"
              v-model="item.shop_img"
              :limit="9"
              :is-query-detail="defaultShopConfig[index].shop_img.length === 0 ? false : true"
              :disabled="caseDownDisa(item, 'shop')"
            />
          </FormItem>
          <FormItem
            v-if="index === 9 && item.select.includes('technology')"
            label="技术服务合同（PDF版）"
            :required="!isHistoryData"
          >
            <Picture
              accept=".pdf"
              :format="['pdf']"
              v-model="item.technology_img"
              :limit="9"
              :is-query-detail="defaultShopConfig[index].technology_img.length === 0 ? false : true"
              :disabled="caseDownDisa(item, 'technology')"
            />
          </FormItem>
          <FormItem
            v-if="index === 9 && item.select.includes('cli_coop')"
            label="榕树家中医诊所合作协议（PDF版）"
            :required="!isHistoryData"
          >
            <Picture
              accept=".pdf"
              :format="['pdf']"
              v-model="item.cli_coop_img"
              :limit="9"
              :is-query-detail="defaultShopConfig[index].cli_coop_img.length === 0 ? false : true"
              :disabled="caseDownDisa(item, 'cli_coop')"
            />
          </FormItem>
          <FormItem
            v-if="index === 9 && item.select.includes('cp_coop')"
            label="城市合伙人榕树家中医诊所合作协议（PDF版）"
            :required="!isHistoryData"
          >
            <Picture
              accept=".pdf"
              :format="['pdf']"
              v-model="item.cp_coop_img"
              :limit="9"
              :is-query-detail="defaultShopConfig[index].cp_coop_img.length === 0 ? false : true"
              :disabled="caseDownDisa(item, 'cp_coop')"
            />
          </FormItem>
        </Col>
      </Row>
    </Form>
    <div v-if="$route.query.open_status == 'EXAMINE'" class="fixed-bottom-wrapper">
      <back-button></back-button>
      <Button style="margin: 0 20px" type="error" @click="showRefuseModal">审核驳回</Button>
      <Button type="primary" @click="passCheck">审核通过</Button>
    </div>
    <div v-else class="fixed-bottom-wrapper">
      <back-button style="margin-right: 20px"></back-button>

      <span v-if="$route.query.open_status !== 'details'">
        <Button v-if="!isIdShow" type="primary" @click="createClinic('clinicForm', false)">保存</Button>
        <Button v-else type="primary" @click="createClinic('clinicForm', true)">保存</Button>
      </span>
    </div>
    <div class="block_45"></div>
    <Modal v-model="refuseModalVisible" :mask-closable="false" title="驳回审核" @on-ok="submitRefuseReason">
      <div style="width: 100%">
        <p class="label">请输入驳回原因：</p>
        <Input
          v-model="examine_rejected_msg"
          :autosize="{ minRows: 3, maxRows: 6 }"
          style="max-width: 1000px"
          type="textarea"
        />
      </div>
    </Modal>
  </div>
</template>

<script>
/* eslint-disable */
/* eslint-disable */
import Picture from '@/components/FullPdfPictureUpload/picture';
import Video from '@/components/FullPdfPictureUpload/video';
import S from 'utils/util';
import { getUser } from 'utils/runtime';
import { CodeToText, regionData, abbreviationData } from '@/utils/chinaMap';
import { cloneDeep } from 'lodash';
import AMap from '@/components/AMap/AMap';
export default {
  name: 'edit',
  components: {
    Picture,
    Video,
    AMap
  },
  data() {
    const validateMobile = (rule, value, callback) => {
      if (value === '') {
        callback(new Error('请输入正确的手机号码'));
      } else {
        const reg = /^1[3456789]\d{9}$/;
        if (!reg.test(value)) {
          callback(new Error('请输入正确的手机号码'));
        }
        callback();
      }
    };

    const validateMobileNull = (rule, value, callback) => {
      if (value === '' || value === null || value === undefined) {
        callback();
      } else {
        const reg = /^1[3456789]\d{9}$/;
        if (!reg.test(value)) {
          callback(new Error('请输入正确的手机号码'));
        }
        callback();
      }
    };

    const validateMobileCard = (rule, value, callback) => {
      if (value === '' || value === null || value === undefined) {
        callback();
      } else {
        const reg = /^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/;
        if (!reg.test(value)) {
          callback(new Error('请输入正确的身份证号'));
        }
        callback();
      }
    };

    const validateMobileImg = (rule, value, callback) => {
      if (value === '' || value === null || value === undefined || value?.length === 0) {
        callback(new Error('请上传诊所合同扫描件'));
      } else {
        callback();
      }
    };
    const validateRstCp = (rule, value, callback) => {
      console.log('=>(detail.vue:778) this.isRstClinic', this.isRstClinic);
      if (this.isRstClinic && (value === '' || value === null || value === undefined || value?.length === 0)) {
        callback(new Error('榕树堂诊所需选择城市合伙人'));
      } else {
        callback();
      }
    };

    return {
      test: [
        'https://vod-sn-i01s-cdn.rsjxx.com/rsjxx/2023/0718/174733_42265.mp4',
        'https://vod-sn-i01s-cdn.rsjxx.com/rsjxx/2023/0718/174733_42265.mp4'
      ],
      hasPartner: '2', // 是否有所属的城市合伙人
      partnerList: [], // 城市合伙人列表
      options: [],
      examine_rejected_msg: '', //审核不通过说明
      formData: {
        id: '',
        company_name: '', //所属分公司
        name: '', //诊所名称
        clinic_code: '', //诊所编码
        open_status: '', // 开通状态
        open_status_text: '', // 开通状态说明
        // payment_type: 'MANY', // 支付方式
        payment_type_text: '', // 支付方式说明
        // deposit_fee: 0, // 定金
        // trail_fee: 0, // 尾款
        open_date: '', // 开业时间
        soft_open_date: '',
        leading_person: '', // 负责人
        leading_mobile: '', //负责人手机号
        prov_name: '', //省
        prov_code: '', //省编码
        city_name: '', //市
        city_code: '', //市编码
        county_name: '', //区
        county_code: '', //区编码
        address_other: '', //详细地址
        // trail_images: [], //定金图片
        // deposit_images: [], //尾款图片
        business_img: [],
        tcm_img: [],
        examine_rejected_msg: '',
        cp_id: '', // 所属城市合伙人id
        offline_serial_number: '', // 流水号
        offline_province_code: '', // 简称编号
        offline_code: '', //诊所门店编号
        direct_type: 'DIGITIZE', // 直营类型
        type: '', // 诊所类型
        use_system_time: '',
        lon: '', //纬度
        lat: '' //经度
      },
      defaultFormData: {},
      ruleValidate: {
        name: [{ required: true, message: '请填写诊所名', trigger: 'blur' }],
        cp_id: [{ required: true, validator: validateRstCp, trigger: 'change,blur' }],
        offline_serial_number: [{ required: true, message: '请填写诊所门店编号', trigger: 'blur' }],
        leading_person: [{ required: true, message: '请填写诊所负责人名', trigger: 'blur' }],
        leading_mobile: [{ required: true, trigger: 'blur', validator: validateMobile }],
        control_mobile: [{ required: false, trigger: 'blur', validator: validateMobileNull }],
        control_idcard: [{ required: false, trigger: 'blur', validator: validateMobileCard }],
        contract_images: [{ required: true, trigger: 'change', validator: validateMobileImg }],
        prod_spec: [{ required: true, message: '请填写商品规格信息', trigger: 'blur' }],
        prod_unit: [{ required: true, message: '请填写包装单位', trigger: 'blur' }],
        type: [{ required: true, message: '请选择诊所类型', trigger: 'change' }],
        // deposit_fee: [{ required: true, validator: fee_validator, trigger: 'blur', type: 'number' }],
        // trail_fee: [{ required: true, validator: fee_validator, trigger: 'blur', type: 'number' }],
        // deposit_images: [{ required: true, message: '请上传凭证', trigger: 'change', type: 'array' }],
        // trail_images: [{ required: true, message: '请上传凭证', trigger: 'change', type: 'array' }]
        // business_img: [{required: true, message: '请上传凭证', trigger: 'change'}],
        // tcm_img: [{required: true, message: '请上传凭证', trigger: 'change'}],
        lon: [{ required: true, message: '请选择地图点位', trigger: 'change,blur' }]
      },
      shop_config: [],
      defaultShopConfig: {},
      selectedAddress: [],
      // options
      openStatusDesc: [],
      paymentTypeDesc: [],
      refuseModalVisible: false,
      isEdit: true, // 城市合伙人是否可以编辑,
      abbreviationOptions: [],

      openDateDisabled: false, // 默认不禁止
      isHistoryData: false, // 是否是历史数据
      directTypeDesc: [],
      typeDesc: [],
      disableEditType: false, // 编辑诊所类型(兼容作用，兼容线上之前未选择过诊所类型的诊所) (默认可编辑)

      dateOptions: {
        disabledDate(date) {
          return date && date.valueOf() > Date.now();
        }
      },
      showMap: false,
      position: []
    };
  },
  created() {
    let { company_name, prov_name, prov_code } = getUser();
    // 放开省公司对诊所地址限制 11.23
    // let data = []
    // if(!prov_code){
    //   data = regionData
    // }else {
    //  data = regionData.filter(item=>item.value === prov_code)
    // }
    // this.options = data

    this.id = this.$route.query.id;
    this.options = regionData;
    this.abbreviationOptions = abbreviationData;
    this.formData.offline_province_code = prov_code; //  接口覆盖数据
    console.log('-> this.formData.offline_province_code', this.formData.offline_province_code);
    this.getOptions().then(() => {
      if (this.$route.query.id) {
        // 编辑时才有的参数
        this.getDetail(this.$route.query.id);
      } else {
        this.formData.company_name = company_name;
        this.formData.prov_name = prov_name;
        this.formData.prov_code = prov_code;

        this.defaultFormData = cloneDeep(this.formData);
        console.log(this.defaultFormData, 'this.defaultFormData');
        console.log('-> prov_code', prov_code);
      }
    });

    // 获取城市合伙人列表
    this.getPartnerList();
  },
  computed: {
    // 是否是榕树堂诊所
    isRstClinic() {
      return this.formData.type === 'RST';
    },
    // 地图是否禁用
    getCheckDetail() {
      if (this.$route.query.open_status === 'EXAMINE' || this.$route.query.open_status === 'details') {
        return 'true';
      }
      return '';
    },
    getTypeDesc() {
      const direct_type = this.formData.direct_type;
      return direct_type === 'NON_DIRECT' ? this.typeDesc : this.typeDesc.filter(item => item.id !== 'COOP_COM');
    },
    isUserEdit() {
      return !!this.id;
    },
    partnerIsEdit() {
      return function (checkKey) {
        const open_status = this.$route.query.open_status;
        console.log(this.isEdit);
        if (open_status === 'EXAMINE_FAIL') {
          return false;
        } else if (checkKey === 'cp_id') {
          if (this.isEdit) {
            return !!this.isIdShow;
          }
          return true;
        } else if (checkKey === 'open_date') {
          if (this.defaultFormData[checkKey]?.length) {
            return true;
          }
          return false;
        }
      };
    },
    offline_code() {
      if (!this.formData.offline_province_code && !this.formData.offline_serial_number) {
        return '';
      } else {
        let simpleProv = abbreviationData.find(item => item.value === this.formData.offline_province_code);
        let simpleProveName = simpleProv.label;
        return `RSJ-${simpleProveName}NO.${this.formData.offline_serial_number}`;
      }
    },
    isIdShow() {
      return !!this.$route.query.id;
    },
    caseDownDisa() {
      return function (item, index) {
        let { type } = item;
        switch (type) {
          //选址确认
          case 'addr_init':
            if (this.defaultShopConfig[0].select.includes(index)) {
              return true;
            }
            // if (!item.select.includes('2')) {
            //   return true;
            // }
            return false;
          //装修筹备
          case 'finishings_init':
            if (index === '1') {
              return true;
            } else {
              return false;
            }
          //证件办理
          case 'certificate_init':
            return false;
          //系统安装
          case 'system_init':
            return true;
          //人员招募及培训
          case 'personnel_init':
            return false;
          //货品筹备
          case 'goods_init':
            return true;
          //诊所签约
          case 'clinic_sign_init':
            return true;
        }
      };
    },
    historyDisa() {
      return function (key) {
        if (this.defaultShopConfig[0][key].length) {
          return true;
        }
        // if (!item.select.includes('2')) {
        //   return true;
        // }
        return false;
      };
    }
  },
  watch: {},
  mounted() {
    // this.$router.onReady(() => {
    // });
  },
  methods: {
    getAddressDetail(position) {
      this.formData.lon = position[0] + '';
      this.formData.lat = position[1] + '';
    },
    changeShowMap() {
      this.showMap = !this.showMap;
    },
    // 是否有所属城市合伙人事件
    haPartnerChange(val) {
      if (val == 2) {
        this.formData.cp_id = '';
      }
    },

    changeTime(time, index) {
      console.log(time, index);
      console.log(this.shop_config);
      // this.shop_config[index].estimate_date = time;
      console.log(this.shop_config);
    },
    //地区选择
    regionChange(address) {
      console.log('-> address', address);
      if (address.length) {
        console.log(CodeToText[address[0]]);
        const province = {
          value: CodeToText[address[0]],
          key: address[0]
        };
        const city = {
          value: CodeToText[address[1]],
          key: address[1]
        };
        const area = {
          value: CodeToText[address[2]],
          key: address[2]
        };
        console.log(province, city, area);
        this.formData.prov_name = province.value;
        this.formData.prov_code = province.key;
        this.formData.city_name = city.value;
        this.formData.city_code = city.key;
        (this.formData.county_name = area.value), (this.formData.county_code = area.key);
      } else {
        this.formData.prov_name = '';
        this.formData.prov_code = '';
        this.formData.city_name = '';
        this.formData.city_code = '';
        this.formData.county_name = '';
        this.formData.county_code = '';
      }
    },
    // regionChange(address) {
    // 	let province = {key:'', value: ''}
    // 	let city = {key:'', value: ''}
    // 	let area = {key:'', value: ''}
    // 	console.log('-> address', address)
    // 	if (address.length) {
    // 		console.log(CodeToText[address[0]])
    // 		address[0]&&(province = {value: CodeToText[address[0]], key: address[0],})
    // 		address[1]&&(city = {value: CodeToText[address[1]], key: address[1],})
    // 		address[2]&&(area = {value: CodeToText[address[2]], key: address[2],})
    // 		console.log(province, city, area)
    // 		this.formData.prov_name = province.value; this.formData.prov_code = province.key
    // 		this.formData.city_name = city.value; this.formData.city_code = city.key
    // 		this.formData.county_name = area.value, this.formData.county_code = area.key
    // 	} else {
    // 		this.formData.prov_name = ''
    // 		this.formData.prov_code = ''
    // 		this.formData.city_name = ''
    // 		this.formData.city_code = ''
    // 		this.formData.county_name = ''
    // 		this.formData.county_code = ''
    // 	}
    // },
    // onChangePaymentType(val) {
    // 	console.log(val)
    // 	this.formData.payment_type = val.value
    // },
    //开业勾选
    changeOpeningStatus(val, index) {
      if (index === 2) {
      }
      if (val.length === Object.keys(this.shop_config[index].checkbox_option).length) {
        this.shop_config[index].status = 'SUCC';
      } else {
        this.shop_config[index].status = 'PROGRESS';
      }
    },
    async getOptions() {
      await this.$api.getClinicOptions().then(res => {
        this.openStatusDesc = S.descToArrHandle(res.openStatusDesc);
        // this.paymentTypeDesc = S.descToArrHandle(res.paymentTypeDesc);
        this.directTypeDesc = S.descToArrHandle(res.directTypeDesc).filter(item => item.is_opc === '1');
        this.typeDesc = S.descToArrHandle(res.typeDesc);
      });
    },
    showRefuseModal() {
      this.refuseModalVisible = true;
    },
    submitRefuseReason() {
      this.review('FAIL', this.examine_rejected_msg);
    },
    getDetail(id) {
      this.$api.getClinicDetail({ id }).then(res => {
        if (res.lon !== '0.000000' && res.lat !== '0.000000') {
          this.position = [Number(res.lon), Number(res.lat)];
        }
        this.formData = res;
        if (!this.formData.type) {
          this.disableEditType = false;
        } else {
          this.disableEditType = true;
        }
        this.isHistoryData = res.is_history_config === '1' ? true : false;
        // this.formData.deposit_fee = Number(res.deposit_fee);
        // this.formData.trail_fee = Number(res.trail_fee);
        this.formData.offline_province_code = res.offline_province_code || res.prov_code; // 未取值时获取默认省

        // cp_id有值,代表有所属城市合伙人
        if (Number(res.cp_id)) {
          this.hasPartner = '1';
          this.isEdit = false;
        }

        if (!!res.open_date) {
          this.openDateDisabled = true;
        }
        if (res.shop_config.length) {
          this.shop_config = res.shop_config;
          this.defaultShopConfig = cloneDeep(this.shop_config);
        }
        // this.selectedAddress = {
        // 	province:  res.prov_code,
        // 	city: res.city_code,
        // 	area: res.county_code,
        // 	town: null,
        // }
        // this.selectedAddress = [res.prov_code, res.city_code,res.county_code]

        this.defaultFormData = cloneDeep(this.formData);
        if (res.county_code) {
          this.selectedAddress = [res.prov_code, res.city_code, res.county_code];
          return;
        }
        if (res.city_code) {
          this.selectedAddress = [res.prov_code, res.city_code];
          return;
        }
        if (res.prov_code) {
          this.selectedAddress = [res.prov_code];
        }
      });
    },
    review(examine_status, examine_rejected_msg) {
      const params = { id: this.$route.query.id, examine_status };
      let isPass = true;
      if (examine_rejected_msg) {
        params.examine_rejected_msg = examine_rejected_msg;
        isPass = false;
      }
      this.$http.post('/pms_opc/clinic.enter.examine', params).then(
        res => {
          this.$Message.success(`${isPass ? '通过审核成功' : '驳回审核成功'}`);
          this.$router.replace('/organization/clinic/list');
        },
        err => this.$Message.error(err.errmsg)
      );
    },
    passCheck() {
      this.$Modal.confirm({
        title: '通过审核',
        content: '您确定要通过该审核吗？',
        onOk: () => {
          this.review('SUCC');
        }
      });
    },
    handleSubmit(name) {},
    handleReset(name) {
      this.$refs[name].resetFields();
    },
    createClinic(name, isEdit) {
      this.$refs[name].validate(valid => {
        if (valid) {
          let params = this.getParams();
          params.contract_images = ['https://img-sn01.rsjxx.com/rsjxx/2025/0524/141206_30907.jpg'];

          if (this.$route.query.id) {
            params.id = this.$route.query.id;
          }
          let isHistoryData = this.isHistoryData;
          console.log(this.isHistoryData);
          console.log(isEdit);
          if (isEdit) {
            let params_config = {};
            let hasUploadImg = true;
            console.log('-> params_config', params_config);
            console.log(JSON.parse(JSON.stringify(this.shop_config)));
            try {
              this.shop_config.forEach((item, index) => {
                // console.log('-> item', item);
                // params_config[item.type] = { select: item.select, estimate_date: item.estimate_date };
                params_config[item.type] = { select: item.select };
                // console.log(this.isCheckFunc(params_config, item, index), '------------>', index);
                if (!this.isCheckFunc(params_config, item, index, isHistoryData)) {
                  hasUploadImg = false;
                }
                console.log(hasUploadImg);
              });
            } catch (error) {
              console.log(error);
            }
            console.log(hasUploadImg);
            if (!hasUploadImg) {
              return;
            }
            params.shop_config = params_config;
            console.log(params_config);
          }
          if (this.hasPartner == 1 && !Number(this.formData.cp_id)) {
            this.$Message.error('请选择所属城市合伙人');
            return;
          }

          this.$api
            .editClinic(params)
            .then(
              res => {
                this.$Message.success(`${this.$route.query.id ? '编辑' : '新建'}诊所成功`);
                this.$router.replace('/organization/clinic/list');
              },
              err => this.$Message.error(err.errmsg)
            )
            .catch(err => {
              console.log(err);
            });
        } else {
          this.$Message.error('请完善表单信息');
        }
      });
    },
    isCheckFunc(params_config, item, index, isHistoryData) {
      // console.log(index, 'index');
      switch (index) {
        case 0:
          params_config['addr_init'].finish_date = item.finish_date;
          params_config['addr_init'].estimate_img = item.estimate_img;
          params_config['addr_init'].signing_img = item.signing_img;
          if (isHistoryData) {
            return true;
          }
          // if (item.select.includes('1') && (!item.finish_date || !item.finish_date.length)) {
          //   this.$Message.error('请选择店面选址的完成时间');
          //   return false;
          // }
          // if (item.select.includes('1') && (!item.estimate_img || !item.estimate_img.length)) {
          //   this.$Message.error('请上传诊所选址评估报告');
          //   return false;
          // }
          // if (item.select.includes('2') && (!item.signing_img || !item.signing_img.length)) {
          //   this.$Message.error('请上传房屋签约合同扫描件');
          //   return false;
          // }
          return true;
        case 1:
          params_config['finishings_init'].pdf_plane_img = item.pdf_plane_img;
          params_config['finishings_init'].renovation_st = item.renovation_st;
          params_config['finishings_init'].renovation_et = item.renovation_et;
          params_config['finishings_init'].check_accept_date = item.check_accept_date;
          params_config['finishings_init'].check_accept_img = item.check_accept_img;
          if (isHistoryData) {
            return true;
          }
          // if (item.select.includes('1') && (!item.pdf_plane_img || !item.pdf_plane_img.length)) {
          //   this.$Message.error('请选择PDF平面图');
          //   return false;
          // }
          if (item.select.includes('3') && (!item.renovation_st || !item.renovation_st.length)) {
            this.$Message.error('请上传装修的开始时间');
            return false;
          }
          if (item.select.includes('4') && (!item.renovation_et || !item.renovation_et.length)) {
            this.$Message.error('请上传装修的完成时间');
            return false;
          }
          if (item.select.includes('5') && (!item.check_accept_date || !item.check_accept_date.length)) {
            this.$Message.error('请上传工程验收的完成时间');
            return false;
          }
          if (item.select.includes('5') && (!item.check_accept_img || !item.check_accept_img.length)) {
            this.$Message.error('请上传验收表');
            return false;
          }
          return true;
        case 2:
          params_config['certificate_init'].business_st = item.business_st;
          params_config['certificate_init'].business_et = item.business_et;
          params_config['certificate_init'].organization_name = item.organization_name;
          params_config['certificate_init'].organization_code = item.organization_code;
          params_config['certificate_init'].legal_person_idcard = item.legal_person_idcard;
          params_config['certificate_init'].legal_person_mobile = item.legal_person_mobile;
          params_config['certificate_init'].business_img = item.business_img;

          params_config['certificate_init'].tcm_st = item.tcm_st;
          params_config['certificate_init'].tcm_et = item.tcm_et;
          params_config['certificate_init'].tcm_name = item.tcm_name;
          params_config['certificate_init'].tcm_img = item.tcm_img;

          let checkOrganizationCode = (object = {}) => {
            if (
              object.select.includes('business') &&
              (item.organization_code.length || (!item.organization_code.length && !isHistoryData))
            ) {
              if (object.organization_code.length != 18) {
                this.$Message.error('请上传18位统一社会信用代码');
                return false;
              }

              // 对社会信用代码是否含英文字母“O”校验
              let organization_code_reg = /^[^oO]*$/;
              if (!organization_code_reg.test(object.organization_code)) {
                this.$Message.error('统一社会信用代码不能包含字母o');
                return false;
              }
            }
            return true;
          };
          if (isHistoryData && !checkOrganizationCode(item)) {
            return false;
          }

          if (isHistoryData) {
            return true;
          }
          if (item.select.includes('business') && (!item.business_st || !item.business_st.length)) {
            this.$Message.error('请上传工商注册的开始时间');
            return false;
          }
          if (item.select.includes('business') && (!item.business_et || !item.business_et.length)) {
            this.$Message.error('请上传工商注册的完成时间');
            return false;
          }
          // if (item.select.includes('business') && (!item.organization_name || !item.organization_name.length)) {
          //   this.$Message.error('请上传营业执照名称');
          //   return false;
          // }
          // if (item.select.includes('business') && (!item.organization_code || !item.organization_code.length)) {
          //   this.$Message.error('请上传统一社会信用代码');
          //   return false;
          // }
          if (!checkOrganizationCode(item)) {
            return false;
          }

          if (item.select.includes('business') && (!item.legal_person_idcard || !item.legal_person_idcard.length)) {
            this.$Message.error('请上传法人身份证号');
            return false;
          }
          if (item.select.includes('business') && (!item.legal_person_mobile || !item.legal_person_mobile.length)) {
            this.$Message.error('请上传法人手机号');
            return false;
          }
          if (item.select.includes('business') && (!item.business_img || !item.business_img.length)) {
            this.$Message.error('请上传营业执照');
            return false;
          }

          if (item.select.includes('tcm') && (!item.tcm_st || !item.tcm_st.length)) {
            this.$Message.error('请上传中医备案证的开始注册时间');
            return false;
          }
          if (item.select.includes('tcm') && (!item.tcm_et || !item.tcm_et.length)) {
            this.$Message.error('请上传中医备案证的完成注册时间');
            return false;
          }
          if (item.select.includes('tcm') && (!item.tcm_name || !item.tcm_name.length)) {
            this.$Message.error('请输入中医备案证名称');
            return false;
          }
          if (item.select.includes('tcm') && (!item.tcm_img || !item.tcm_img.length)) {
            this.$Message.error('请上传中医备案凭证');
            return false;
          }
          return true;
        case 3:
          params_config['system_init'].applet_online_date = item.applet_online_date;
          params_config['system_init'].unionpay_name = item.unionpay_name;
          params_config['system_init'].unionpay_online_date = item.unionpay_online_date;
          if (isHistoryData) {
            return true;
          }
          if (item.select.includes('1') && (!item.applet_online_date || !item.applet_online_date.length)) {
            this.$Message.error('请选择小程序的上线时间');
            return false;
          }
          if (item.select.includes('2') && (!item.unionpay_name || !item.unionpay_name.length)) {
            this.$Message.error('请上传银联账户信息');
            return false;
          }
          if (item.select.includes('2') && (!item.unionpay_online_date || !item.unionpay_online_date.length)) {
            this.$Message.error('请上传银联上线时间');
            return false;
          }
          return true;
        case 5:
          params_config['goods_init'].zy_sign_date = item.zy_sign_date;
          if (isHistoryData) {
            return true;
          }
          if (item.select.includes('2') && (!item.zy_sign_date || !item.zy_sign_date.length)) {
            this.$Message.error('请上传中药签约的完成时间');
            return false;
          }
          return true;
        case 6:
          params_config['info_init'].panorama_video = item.panorama_video;
          if (isHistoryData) {
            return true;
          }
          if (item.select.includes('1') && (!item.panorama_video || !item.panorama_video.length)) {
            this.$Message.error('请上传门店全景视频');
            return false;
          }
          return true;
        case 8:
          // console.log(item, index);
          params_config['check_accept_init'].prove_img = item.prove_img;
          params_config['check_accept_init'].affair_img = item.affair_img;
          params_config['check_accept_init'].door_img = item.door_img;
          if (isHistoryData) {
            return true;
          }
          if (item.select.includes('prove') && (!item.prove_img || !item.prove_img.length)) {
            this.$Message.error('请上传医证验收图片');
            return false;
          }
          if (item.select.includes('affair') && (!item.affair_img || !item.affair_img.length)) {
            this.$Message.error('请上传店务验收图片');
            return false;
          }
          if (item.select.includes('door') && (!item.door_img || !item.door_img.length)) {
            this.$Message.error('请上传诊所门头验收图片');
            return false;
          }
          return true;
        case 9:
          params_config['clinic_sign_init'].shop_img = item.shop_img;
          params_config['clinic_sign_init'].technology_img = item.technology_img;
          params_config['clinic_sign_init'].cli_coop_img = item.cli_coop_img;
          params_config['clinic_sign_init'].cp_coop_img = item.cp_coop_img;
          if (isHistoryData) {
            return true;
          }
          if (item.select.includes('shop') && (!item.shop_img || !item.shop_img.length)) {
            this.$Message.error('请上传商城入驻协议');
            return false;
          }
          if (item.select.includes('technology') && (!item.technology_img || !item.technology_img.length)) {
            this.$Message.error('请上传技术服务合同');
            return false;
          }
          if (item.select.includes('cli_coop') && (!item.cli_coop_img || !item.cli_coop_img.length)) {
            this.$Message.error('请上传榕树家中医诊所合作协议');
            return false;
          }
          if (item.select.includes('cp_coop') && (!item.cp_coop_img || !item.cp_coop_img.length)) {
            this.$Message.error('请上传城市合伙人榕树家中医诊所合作协议');
            return false;
          }
          return true;
        default:
          return true;
      }

      // if (index === 2) {
      //   params_config[item.type].business_img = item.business_img;
      //   params_config[item.type].tcm_img = item.tcm_img;
      //   if (item.select.includes('business') && (!item.business_img || !item.business_img.length)) {
      //     this.$Message.error('请上传工商注册凭证');
      //     return false;
      //   }
      //   if (item.select.includes('tcm') && (!item.tcm_img || !item.tcm_img.length)) {
      //     this.$Message.error('请上传中医备案凭证');
      //     return false;
      //   }
      // }
      // return true;
    },
    getParams() {
      const {
        name,
        // deposit_fee,
        // trail_fee,
        leading_person,
        leading_mobile,
        prov_name,
        prov_code,
        city_name,
        city_code,
        county_name,
        county_code,
        address_other,
        // trail_images,
        // deposit_images,
        contract_images,
        open_date,
        soft_open_date,
        control_person,
        control_mobile,
        control_idcard,
        // payment_type,
        cp_id,
        offline_serial_number,
        offline_province_code,
        direct_type,
        type,
        lon,
        lat
      } = this.formData;
      let params = {
        name,
        // deposit_fee,
        // trail_fee,
        leading_person,
        leading_mobile,
        prov_name,
        prov_code,
        city_name,
        city_code,
        county_name,
        open_date,
        soft_open_date,
        county_code,
        address_other,
        control_person,
        control_mobile,
        control_idcard,
        // payment_type,
        cp_id,
        fee_images: {
          // trail_images,
          // deposit_images
          contract_images
        },
        offline_serial_number,
        offline_province_code,
        offline_code: this.offline_code,
        direct_type,
        type,
        lon,
        lat
      };
      return params;
    },

    // /**
    //  * @description: 只显示 特定 状态下的合伙人
    //  * @param { partnerList } 合伙人列表
    //  * @param { status = PASS } 合伙人状态
    // */

    // filterPartnerList ( partnerList ,status = 'PASS') {
    // 	if (!partnerList) return []
    // 	return partnerList.filter( item => item.status === status )
    // },

    // 获取所属城市合伙人
    getPartnerList() {
      let params = {
        page: 1,
        pageSize: 100,
        status: 'PASS'
      };
      this.$api.getPartnerList(params).then(res => {
        this.partnerList = res.list;
      });
    },
    formatNo() {
      if (Number(this.formData.offline_serial_number) == 0) {
        this.formData.offline_serial_number = null;
      }
      if (this.formData.offline_serial_number) {
        this.formData.offline_serial_number = String(this.formData.offline_serial_number).padStart(4, '0');
      }
    },
    changeNo() {
      this.$nextTick(() => {
        if (this.formData.offline_serial_number.length > 4) {
          this.formData.offline_serial_number = this.formData.offline_serial_number.slice(0, 4);
        }
      });
      // setTimeout(()=> {
      //   this.formData.offline_serial_number = this.formData.offline_serial_number.slice(0,4)
      //   this.$forceUpdate()
      // },200)
    },
    changeClinicType() {
      // this.$refs.clinicForm.validateField('cp_id');
    }

    // parserNo(value){
    //   return '123'
    // }
  }
};
</script>

<style lang="less" scoped>
.number-box {
  width: 39px;
  height: 32px;
  line-height: 32px;
  text-align: center;
  border: 1px solid #bbb;
  border-left: 0;
  background-color: #f13f13f9;
}

.red-item {
  ::v-deep .ivu-form-item-label {
    color: red;
  }
}

.time-out {
  margin: 0 0 10px;
}

.validError {
  ::v-deep .ivu-select-selection {
    border: 1px solid #bbb !important;
  }
  ::v-deep .ivu-select-arrow {
    color: #808695;
  }
}
</style>
<style lang="less">
.addWrap {
  display: flex;

  //.addressBox {
  //	// /deep/ .rg-select__el{
  //	//   /deep/ .rg-select__content {
  //	//     padding: 0 30px 0px 13px !important;
  //	//   }
  //	// }
  //	div.rg-select div.rg-select__el div.rg-select__content {
  //		padding: 0 30px 0px 13px !important;
  //		font-size: 12px;
  //	}
  //}
  //
  .addressInput {
    flex: 1;
  }
}
</style>
