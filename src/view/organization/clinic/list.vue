<template>
  <div class="f_order-wrapper">
    <Form inline @submit.native.prevent @keyup.enter.native="onSearch">
      <Row>
        <FormItem>
          <Input v-model="queryFormData.name" placeholder="诊所名称/编号/门店编号" clearable />
        </FormItem>
        <FormItem>
          <Input v-model="queryFormData.keyword" placeholder="联系人姓名/手机号" clearable />
        </FormItem>
        <FormItem>
          <Select v-model="queryFormData.open_status" placeholder="开业状态" style="width: 200px" clearable>
            <Option v-for="item in openingStatus" :key="item.id" :value="item.id">{{ item.desc }}</Option>
          </Select>
        </FormItem>
        <FormItem>
          <Input v-model="queryFormData.organization_name" placeholder="营业执照名称" clearable />
        </FormItem>
        <FormItem>
          <tcm-name-search
            ref="tcm-search"
            v-model="queryFormData.id"
            :isClearable="true"
            :width="180"
          ></tcm-name-search>
        </FormItem>
      </Row>
      <Row>
        <FormItem>
          <Select v-model="queryFormData.disable_audit_status" placeholder="禁用/启用的审核状态" clearable>
            <Option v-for="item in disableAuditStatusDesc" :key="item.id" :value="item.id">{{ item.desc }}</Option>
          </Select>
        </FormItem>
        <FormItem>
          <clinic-type-search
            v-model="queryFormData.type"
            :clinic_direct_type="queryFormData.direct_type"
            @directChange="directChange"
          ></clinic-type-search>
        </FormItem>
        <FormItem style="text-align: left">
          <Button class="mr10" type="primary" @click="onSearch">筛选</Button>
          <Button class="mr10" type="default" @click="onResetSearch">重置</Button>
          <Button type="default" @click="exportFunc">导出</Button>
        </FormItem>
      </Row>
    </Form>
    <div class="table-wrapper">
      <div class="panel-nav flex flex-item-between">
        <div>
          <a :class="{ active: !queryFormData.open_status }" class="nav" @click.prevent.capture="onStatusChange('')">
            全部
          </a>
          <a
            :class="{ active: $route.query.open_status == 'EXAMINE' }"
            class="nav"
            @click.prevent.capture="onStatusChange('EXAMINE')"
          >
            平台审核中
            <Tag color="warning">{{ status_total.EXAMINE && status_total['EXAMINE'].num }}</Tag>
          </a>
          <a
            :class="{ active: $route.query.open_status == 'EXAMINE_FAIL' }"
            class="nav"
            @click.prevent.capture="onStatusChange('EXAMINE_FAIL')"
          >
            已驳回
            <Tag color="error">{{ status_total.EXAMINE_FAIL && status_total['EXAMINE_FAIL'].num }}</Tag>
          </a>
          <a
            :class="{ active: $route.query.open_status == 'PREPARE' }"
            class="nav"
            @click.prevent.capture="onStatusChange('PREPARE')"
          >
            筹备中
            <Tag color="primary">{{ status_total.PREPARE && status_total['PREPARE'].num }}</Tag>
          </a>
          <a
            :class="{ active: $route.query.open_status == 'PICKER' }"
            class="nav"
            @click.prevent.capture="onStatusChange('PICKER')"
          >
            已选址
            <Tag color="primary">{{ status_total.PICKER && status_total['PICKER'].num }}</Tag>
          </a>
          <a
            :class="{ active: $route.query.open_status == 'RENONATION' }"
            class="nav"
            @click.prevent.capture="onStatusChange('RENONATION')"
          >
            装修中
            <Tag color="primary">{{ status_total.RENONATION && status_total['RENONATION'].num }}</Tag>
          </a>
          <a
            :class="{ active: $route.query.open_status == 'SOFT_OPEN' }"
            class="nav"
            @click.prevent.capture="onStatusChange('SOFT_OPEN')"
          >
            试营业
            <Tag color="primary">{{ status_total.SOFT_OPEN && status_total['SOFT_OPEN'].num }}</Tag>
          </a>
          <a
            :class="{ active: $route.query.open_status == 'FINISHED' }"
            class="nav"
            @click.prevent.capture="onStatusChange('FINISHED')"
          >
            已开业
            <Tag color="success">{{ status_total.FINISHED && status_total['FINISHED'].num }}</Tag>
          </a>
          <a
            :class="{ active: $route.query.open_status == 'DISABLED' }"
            class="nav"
            @click.prevent.capture="onStatusChange('DISABLED')"
          >
            已禁用
            <Tag color="error">{{ status_total.DISABLED && status_total['DISABLED'].num }}</Tag>
          </a>
        </div>
        <div class="creat-button">
          <Button type="primary" @click="$router.push('/organization/clinic/detail')"> 创建诊所 </Button>
        </div>
      </div>
      <Table :columns="tableCols" :data="list" :loading="tableLoading" :height="$store.state.app.clientHeight - 325">
        <template slot="offline_code" slot-scope="{ row }">
          {{ row.offline_code || '-' }}
        </template>

        <template slot="prepay_balance" slot-scope="{ row }">
          <k-link
            v-if="isHasBalanceDetailPage && row.prepay_balance.id > 0"
            :to="{ path: '/finance/balance/detail', query: { id: row.prepay_balance.id } }"
            target="_blank"
            >{{ row.prepay_balance?.clinic ? `¥${row.prepay_balance.clinic}` : '-' }}</k-link
          >
          <div v-else>{{ row.prepay_balance?.clinic ? `¥${row.prepay_balance.clinic}` : '-' }}</div>
        </template>

        <template slot="open_status_text" slot-scope="{ row }">
          <div>{{ row.open_status_text }}</div>
        </template>
        <template slot="type_text" slot-scope="{ row }">
          <div v-if="row.type_text">{{ row.direct_type_text }} / {{ row.type_text }}</div>
          <div v-else>-</div>
        </template>
        <!--        <template slot-scope="{ row }" slot="subject_auth_status">-->
        <!--          <div>{{ row.yzt_into_status }}</div>-->
        <!--&lt;!&ndash;          <Tooltip :content="row.out_request_no" v-if="row.out_request_no">&ndash;&gt;-->
        <!--&lt;!&ndash;            <a>(进件ID)</a>&ndash;&gt;-->
        <!--&lt;!&ndash;          </Tooltip>&ndash;&gt;-->
        <!--        </template>-->
        <template slot-scope="{ row }" slot="yzt_into_status">
          <div>{{ row.yzt_into_status || '-' }}</div>
          <!--          <Tooltip :content="row.out_request_no" v-if="row.out_request_no">-->
          <!--            <a>(进件ID)</a>-->
          <!--          </Tooltip>-->
          <!-- <p v-if="row.open_status === 'PREPARE'">{{ row.shop_config_succ_desc }}</p> -->
        </template>
        <template slot-scope="{ row }" slot="cd_yzt_into_status">
          <div>{{ row.cd_yzt_into_status || '-' }}</div>
          <!--          <Tooltip :content="row.out_request_no" v-if="row.out_request_no">-->
          <!--            <a>(进件ID)</a>-->
          <!--          </Tooltip>-->
          <!-- <p v-if="row.open_status === 'PREPARE'">{{ row.shop_config_succ_desc }}</p> -->
        </template>
        <template slot-scope="{ row }" slot="disable_audit_status_text">
          <div>{{ row.disable_audit_status_text || '-' }}</div>
        </template>
        <!--        <template slot-scope="{ row }" slot="unionPay_status">-->
        <!--          <div>{{ row.union_pay_status }}</div>-->
        <!--          &lt;!&ndash; <p v-if="row.open_status === 'PREPARE'">{{ row.shop_config_succ_desc }}</p> &ndash;&gt;-->
        <!--        </template>-->
        <template slot="city_name" slot-scope="{ row }">
          {{ row.city_name || '-' }}
        </template>

        <template slot="open_date" slot-scope="{ row }">
          {{ row.open_date || '-' }}
        </template>

        <!-- <template slot="timeout" slot-scope="{ row }"> -->
        <!--   {{ Number(row.overtime_num) ? `${row.overtime_num} 项` : '-' }} -->
        <!-- </template> -->
        <!---->
        <template slot="create_time" slot-scope="{ row }">
          {{ row.create_time | date_format }}
        </template>

        <template slot="weapp_name" slot-scope="{ row }">
          <a @click="setWeappInfo(row)">{{ row.weapp_name || '设置' }}</a>
        </template>

        <template slot="organization_name" slot-scope="{ row }">
          {{ row.organization_name || '-' }}
        </template>

        <template slot="tcm_name" slot-scope="{ row }">
          {{ row.tcm_name || '-' }}
        </template>

        <template slot="cp_name" slot-scope="{ row }">
          {{ row.cp_name || '-' }}
        </template>

        <template slot="soft_open_date" slot-scope="{ row }">
          <span>{{ row.soft_open_date || '-' }}</span>
        </template>

        <template slot="action" slot-scope="{ row, index }">
          <!-- <a @click="toDetail(row.id,'isEdit',row.open_status)" v-if="row.open_status !== 'EXAMINE'" style="margin-right: 10px;">编辑</a> -->
          <!-- <a @click="toDetail(row.id, row.open_status)" v-if="row.open_status === 'EXAMINE'">审核</a> -->
          <!--诊所待平台审核或已禁用不展示编辑-->
          <div class="flex">
            <a
              class="mr10"
              @click="toDetail(row.id, row.open_status)"
              v-if="row.open_status !== 'EXAMINE' && row.open_status !== 'DISABLED'"
              >编辑</a
            >
            <div
              class="mr10"
              style="display: inline-block"
              v-if="row.open_status !== 'EXAMINE' && row.open_status !== 'EXAMINE_FAIL'"
            >
              <Poptip
                v-if="row.can_disabled === '1'"
                placement="top-end"
                :ref="`${row.id}pop`"
                confirm
                @on-ok="clinicDisabledAndEnable(row, 'DISABLE')"
                @on-popper-hide="resetPopper(row, index)"
              >
                <div slot="title">
                  <div>确认禁用?</div>
                  <div class="flex">
                    <span style="width: 70px"><span style="color: red">*</span> 禁用原因:</span>
                    <Input
                      class="flex-1"
                      :class="{ validError: validDisabledReason }"
                      v-model="forbiddenReason"
                      type="textarea"
                      :autosize="{ minRows: 3, maxRows: 3 }"
                    ></Input>
                  </div>
                </div>
                <a>禁用</a>
              </Poptip>
              <Poptip
                v-else-if="row.can_enabled === '1'"
                confirm
                title="确认要启用?"
                @on-ok="confirmAgain(row)"
                placement="top-end"
              >
                <a>启用</a>
              </Poptip>
              <div v-else></div>
            </div>

            <a class="mr10" @click="seeStaff(row)">门店员工</a>
            <a @click="toDetail(row.id, 'details')">详情</a>
          </div>
        </template>
      </Table>
      <div class="block_20"></div>
      <KPage
        :current.sync="+queryFormData.page"
        :page-size.sync="+queryFormData.pageSize"
        :total="total"
        style="text-align: center"
        @on-change="handleCurrentChange"
        @on-page-size-change="handleSizeChange"
      />
    </div>
    <!-- 门店员工 -->
    <staff-modal v-model="staffVisible" :current_row="current_row"></staff-modal>
    <!-- 小程序设置 -->
    <weapp-modal v-model="weappVisible" :current_row="current_row" @success="onSearch"></weapp-modal>
  </div>
</template>

<script>
import S from 'utils/util';
import search from '@/mixins/search';
import clinicTypeSearch from '@/components/remote-search/clinic-type-search.vue';
import tcmNameSearch from '@/components/tcm-name-search/tcm-name-search.vue';
import staffModal from './components/staffModal.vue';
import weappModal from './components/weappModal.vue';
const init_query_form_data = {
  page: 1,
  pageSize: 20,
  open_status: '',
  department_id: '',
  keyword: '',
  name: '',
  type: '',
  direct_type: 'DIGITIZE',
  disable_audit_status: '',
  r: ''
};
export default {
  name: 'list',
  mixins: [search],
  components: { clinicTypeSearch, tcmNameSearch, staffModal, weappModal },
  data() {
    return {
      apiName: 'getClinicList',
      queryFormData: { ...init_query_form_data },
      isHasBalanceDetailPage: S.rootPageCheck('/finance/balance/detail'),
      status_total: {},
      tableCols: [
        { title: '编号', key: 'clinic_code', align: 'center', width: 60 },
        { title: '诊所门店编号', slot: 'offline_code', align: 'center', width: 110 },
        { title: '诊所类型', slot: 'type_text', align: 'center', width: 100 },
        { title: '所属城市', slot: 'city_name', align: 'center', width: 100 },
        { title: '诊所名称', key: 'name', align: 'center', width: 100 },
        { title: '营业执照名称', slot: 'organization_name', align: 'center', width: 140 },
        { title: '中医备案证名称', slot: 'tcm_name', align: 'center', width: 140 },
        { title: '联系人', key: 'leading_person', align: 'center', width: 100 },
        { title: '联系手机号', key: 'leading_mobile', align: 'center', width: 100 },
        { title: '预付款可用余额', slot: 'prepay_balance', minWidth: 130, align: 'center' },
        { title: '状态', slot: 'open_status_text', align: 'center', width: 100 },
        // { title: '银联状态', slot: 'unionPay_status', align: 'center', minWidth: 80 },
        // { title: '进件状态', slot: 'subject_auth_status', align: 'center', minWidth: 80 },
        { title: '海南主体进件', slot: 'yzt_into_status', align: 'center', minWidth: 110 },
        { title: '成都主体进件', slot: 'cd_yzt_into_status', align: 'center', minWidth: 110 },
        { title: '禁用/启用的审核状态', slot: 'disable_audit_status_text', align: 'center', minWidth: 130 },
        // { title: '筹备进度', slot: 'prepar_progress', align: 'center', width: 100 },
        { title: '试营业时间', slot: 'soft_open_date', align: 'center', width: 100 },
        { title: '开业时间', slot: 'open_date', align: 'center', width: 80 },
        // { title: '超时预警', slot: 'timeout', align: 'center', width: 100 },
        { title: '小程序设置', slot: 'weapp_name', align: 'center', width: 110 },
        { title: '创建时间', slot: 'create_time', align: 'center', width: 100 },
        { title: '操作', slot: 'action', align: 'center', width: 170, fixed: 'right' }
      ],
      openingStatus: [],
      selectableDepartList: [],
      disableAuditStatusDesc: [],
      validDisabledReason: false,
      forbiddenReason: '',
      staffVisible: false,
      current_row: {},
      weappVisible: false
    };
  },
  computed: {},
  watch: {},
  created() {
    this.queryFormData = S.merge(this.queryFormData, this.$route.query);
    this.submitQueryForm(true);
    this.getOptionsList();
  },
  mounted() {},
  methods: {
    setWeappInfo(row) {
      this.current_row = row;
      this.weappVisible = true;
    },
    seeStaff(row) {
      this.current_row = row;
      this.staffVisible = true;
    },
    onStatusChange(status) {
      this.queryFormData.page = 1;
      this.queryFormData.open_status = status;
      this.submitQueryForm();
    },
    onResetSearch() {
      this.queryFormData = { ...init_query_form_data };
      this.submitQueryForm();
    },
    async getOptionsList() {
      await this.$api.getClinicOptions().then(res => {
        this.openingStatus = S.descToArrHandle(res.openStatusDesc);
        this.disableAuditStatusDesc = S.descToArrHandle(res.disableAuditStatusDesc);
      });
    },
    toDetail(id, open_status) {
      this.$router.push({
        path: '/organization/clinic/detail',
        query: {
          id,
          open_status
        }
      });
    },
    disableClick(id) {
      let params = { id };
      this.$api
        .clinicDisabled(params)
        .then(res => {
          this.$Message.success('禁用成功');
        })
        .catch(err => this.$Message.error(err.errmsg));
    },
    directChange(val) {
      this.$set(this.queryFormData, 'direct_type', val);
    },
    exportFunc() {
      this.$api.exportClinicList(this.queryFormData).then(res => {
        this.downloadLoading = false;
        window.open(res.url);
      });
    },
    clinicDisabledAndEnable(row, role) {
      if (role === 'DISABLE' && !this.forbiddenReason) {
        this.$Message.error('请输入禁用原因');
        this.validDisabledReason = true;
        this.$refs[`${row.id}pop`].visible = true;
        return;
      }
      let params = { id: row.id, disabled_msg: this.forbiddenReason, role };
      this.$api
        .clinicDisabledAndEnable(params)
        .then(res => {
          if (res.error_msg) {
            this.$Modal.error({
              title: '无法启用',
              content: `<p>${res.error_msg}</p>`
            });
          } else {
            this.$Message.success(`已申请${role === 'DISABLE' ? '禁用' : '启用'}，待审核`);
            this.loadList();
          }
        })
        .catch(err => this.$Message.error(err.errmsg));
    },
    resetPopper(row, index) {
      console.log('=>(list.vue:402) 触发关闭 ');
      this.forbiddenReason = '';
      this.validDisabledReason = false;
    },
    confirmAgain(row) {
      // 已禁用的诊所的门头编号是否与未禁用的诊所的门头编号相同
      if (row.is_enabled_clear == 1) {
        this.$Modal.confirm({
          title: '确认要启用吗？',
          content: '<p>此诊所的门店编号与其他诊所相同，启用之后，此诊所的门店编号将被清空。</p>',
          onOk: () => {
            this.clinicDisabledAndEnable(row, 'ENABLE');
          }
        });
      } else {
        this.clinicDisabledAndEnable(row, 'ENABLE');
      }
    }
  },
  beforeRouteUpdate: function (to, from, next) {
    this.queryFormData = S.merge(init_query_form_data, to.query);
    this.loadList();
    next();
  }
};
</script>

<style lang="less" scoped></style>
