<template>
  <Modal :value="value" title="操作记录弹窗" width="900" :mask-closable="false" @on-visible-change="changeVisible">
    <div class="container">
      <Table :columns="tableCols" :data="list" :loading="tableLoading" :height="380">
        <template v-slot:create_time="{ row }">
          {{ row.create_time | date_format }}
        </template>
        <template v-slot:before="{ row }">
          <div v-if="row.before?.type === 'array'">
            {{ row.before?.value?.length ? row.before?.value?.join('、') : '-' }}
          </div>
          <div v-else>{{ row.before?.value || '-' }}</div>
        </template>
        <template v-slot:after="{ row }">
          <div v-if="row.before?.type === 'array'">
            {{ row.after?.value?.length ? row.after?.value?.join('、') : '-' }}
          </div>
          <div v-else>{{ row.after?.value || '-' }}</div>
        </template>
      </Table>
      <div class="block_20"></div>
      <KPage
        :current="queryFormData.page"
        :page-size="queryFormData.pageSize"
        :total="total"
        style="text-align: center"
        @on-change="handleCurrentChange"
        @on-page-size-change="handleSizeChange"
      />
    </div>
    <div slot="footer">
      <Button type="primary" style="margin-left: 20px" @click="cancel">确定</Button>
    </div>
  </Modal>
</template>

<script>
const init_form_data = {
  page: 1,
  pageSize: 10,
};
export default {
  name: 'recordModal',
  components: {},
  props: {
    value: {
      type: Boolean,
      default: false
    },

    id: {
      type: String,
      default: ''
    }
  },
  mixins: [],
  data() {
    return {
      queryFormData: { ...init_form_data },
      goods_list: [],
      company_list: [],
      tableCols: [
        { title: '操作时间', slot: 'create_time', align: 'center', width: 140 },
        { title: '操作主体', key: 'source', align: 'center', width: 70 },
        { title: '操作人', key: 'operator_name', align: 'center', width: 100 },
        { title: '变更字段', key: 'field_name', align: 'center' },
        { title: '更改前', slot: 'before', align: 'center', width: 200 },
        { title: '更改后', slot: 'after', align: 'center', width: 200 }
      ],
      list: [],
      tableLoading: false,
      total: 0
    };
  },
  computed: {},
  watch: {},
  created() {},
  mounted() {},
  methods: {
    TABLE_SCROLL_TOP () {
      let TABLE_EL = this.$el.getElementsByClassName('ivu-table-body')[0]
      if ( TABLE_EL ) {
        TABLE_EL.scrollTop = 0
      }
    },
    cancel() {
      this.formData = {
        ...init_form_data
      };
      this.$emit('input', false);
    },
    changeVisible(flag) {
      if (!flag) {
        this.TABLE_SCROLL_TOP()
        this.queryFormData = { ...init_form_data };
        this.$emit('input', false);
      } else {
        this.getPCGetfieldlog();
      }
    },
    handleCurrentChange(val) {
      this.queryFormData.page = val;
      this.getPCGetfieldlog();
    },
    handleSizeChange(val) {
      this.queryFormData.page = 1;
      this.queryFormData.pageSize = val;
      this.getPCGetfieldlog();
    },
    getPCGetfieldlog() {
      this.tableLoading = true
      let params = {
        id: this.id,
        page: this.queryFormData.page,
        pageSize: this.queryFormData.pageSize
      };
      this.$api
        .getPCGetfieldlog(params)
        .then(res => {
          this.tableLoading = false
          this.list = res.list;
          this.total = +res.total;
        })
        .catch(err => this.$Message.error(err.errmsg))
        .finally(() => this.tableLoading = false)
    }
  }
};
</script>

<style lang="less" scoped>
ul,
li {
  list-style: inside;
}
.edit_row {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 13px;
  .edit_label {
    width: 52px;
    text-align: left;
    margin-right: 10px;
  }
  .edit_value {
    flex: 1;
  }
}
</style>
