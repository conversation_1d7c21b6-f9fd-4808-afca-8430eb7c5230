<template>
  <div>
    <div class="block-header flex flex-item-align flex-item-between">
      <div>基本信息</div>
      <a @click="recordModalVisible = true" v-if="$route.query.id">操作记录</a>
    </div>

    <Form
      style="min-width: 600px; width: 50%"
      ref="formData"
      :model="formData"
      :rules="ruleValidate"
      :label-width="140"
      :label-colon="true"
      :disabled="isDetail"
    >
      <FormItem label="状态" v-if="detailInfo.status">
        <Input v-model="detailInfo.status_desc" disabled></Input>
      </FormItem>

      <FormItem label="禁用原因" class="label-disabled" v-if="detailInfo.status === 'DISABLED'">
        <Input
          type="textarea"
          :autosize="{ minRows: 1, maxRows: 6 }"
          v-model="detailInfo.disabled_reason"
          readonly
        ></Input>
      </FormItem>

      <FormItem label="个人客户名称" prop="name">
        <Input v-model="formData.name" maxlength="30" placeholder="输入个人客户名称" show-word-limit></Input>
      </FormItem>

      <FormItem label="手机号" prop="mobile">
        <Input
          :maxlength="11"
          type="number"
          v-model="formData.mobile"
          placeholder="请输入手机号"
          @on-blur="getVipUserInfo"
        ></Input>
      </FormItem>

      <FormItem label="会员名称">
        <div>
          {{ vip_type_desc }}
        </div>
      </FormItem>

      <FormItem label="个人客户类型" prop="custom_type" v-if="detailInfo.type !== 'PUBLIC'">
        <CheckboxGroup v-model="formData.custom_type">
          <div class="flex flex-item-align" v-for="(item, index) in customTypeOptions" :key="index">
            <Checkbox :label="item.id">{{ item.desc }}</Checkbox>
            <div class="tip" v-if="getCustomTypeTip(item.id)">{{ getCustomTypeTip(item.id) }}</div>
          </div>
        </CheckboxGroup>
      </FormItem>

      <FormItem label="备注">
        <Input
          maxlength="300"
          show-word-limit
          v-model="formData.remark"
          type="textarea"
          :autosize="{ minRows: 6, maxRows: 10 }"
          placeholder="请输入备注"
        ></Input>
      </FormItem>
    </Form>

    <div class="block_45"></div>

    <div class="fixed-bottom-wrapper">
      <back-button></back-button>
      <Button v-if="!isDetail" type="primary" @click="handleSubmit" style="margin-left: 10px" :loading="saveLoading"
        >保存
      </Button>
    </div>

    <record-modal v-model="recordModalVisible" :id="$route.query.id"></record-modal>
  </div>
</template>

<script>
import recordModal from './components/recordModal.vue';
import S from 'utils/util';

const validateMobile = (rule, value, callback) => {
  if (value === '') {
    callback(new Error('请输入正确的手机号码'));
  } else {
    const reg = /^1[3456789]\d{9}$/;
    if (!reg.test(value)) {
      callback(new Error('请输入正确的手机号码'));
    }
    callback();
  }
};
export default {
  name: 'edit',
  components: { recordModal },
  mixins: [],
  data() {
    return {
      recordModalVisible: false,
      saveLoading: false,
      formData: {
        name: '',
        mobile: '',
        custom_type: [],
        remark: '',
      },
      ruleValidate: {
        name: [{ required: true, message: '请输入个人客户名称', trigger: 'change' }],
        mobile: [
          { required: true, message: '请输入手机号', trigger: 'change' },
          { required: true, validator: validateMobile },
        ],
        custom_type: [{ required: true, type: 'array', message: '请选择个人客户类型', trigger: 'change' }],
      },
      detailInfo: {},
      customTypeOptions: [],
      vip_type_desc: '-',
    };
  },
  computed: {
    getCustomTypeTip() {
      return type => {
        switch (type) {
          case 'PURCHASE':
            return '(省公司对个人销售采购单的采购主体)';
          case 'STORE':
            return '(计划要开店，但是暂时没有营业执照，只能通过私人账户打款)';
          default:
            '';
        }
      };
    },
    isDetail() {
      return !!this.$route.query.id && this.$route.query.type === 'detail';
    },
  },
  watch: {},
  created() {
    this.getPCOptions();
    if (this.$route.query.id) {
      this.getPCDetail();
    }
  },
  mounted() {},
  methods: {
    getVipUserInfo() {
      // 如果满足手机号正则
      const mobile = this.formData.mobile;
      const reg = /^1[3456789]\d{9}$/;
      if (reg.test(this.formData.mobile)) {
        this.$api.getUserVipByMobile({ mobile }).then(res => {
          console.log('%c [ res ]-158', 'font-size:13px; background:#4e7469; color:#92b8ad;', res);
          this.handleVipDesc(res.vip_type_desc);
        });
      }
    },
    handleVipDesc(vip_type_desc) {
      this.vip_type_desc = vip_type_desc.length > 0 ? vip_type_desc.join('；') : '-';
    },
    handleSubmit() {
      this.$refs['formData'].validate(valid => {
        if (valid) {
          this.getPCEdit();
        }
      });
    },

    getPCDetail() {
      let params = {
        id: this.$route.query.id,
      };
      this.$api.getPCDetail(params).then(
        res => {
          this.detailInfo = res.detail;
          this.formData.name = res.detail.name;
          this.formData.mobile = res.detail.mobile;
          this.formData.custom_type = res.detail.custom_type;
          this.formData.remark = res.detail.remark;
          this.handleVipDesc(res.detail.vip_type_desc);
        },
        err => this.$Message.error(err.errmsg)
      );
    },

    getPCEdit() {
      this.saveLoading = true;
      let params = {
        id: this.$route.query.id,
        ...this.formData,
      };
      this.$api
        .getPCEdit(params)
        .then(
          res => {
            this.saveLoading = false;
            this.$Message.success(this.$route.query.id ? '编辑成功' : '创建成功');
            this.$router.back();
          },
          err => this.$Message.error(err.errmsg)
        )
        .finally(() => (this.saveLoading = false));
    },

    getPCOptions() {
      this.$api.getPCOptions().then(
        res => {
          this.customTypeOptions = S.descToArrHandle(res.customTypeDesc || {});
        },
        err => this.$Message.error(err.errmsg)
      );
    },
  },
};
</script>

<style lang="less" scoped>
.tip {
  color: #999;
}
::v-deep .label-disabled {
  .ivu-form-item-label {
    color: red !important;
  }
  .ivu-input {
    border-color: red;
    color: red;
  }
}
</style>
