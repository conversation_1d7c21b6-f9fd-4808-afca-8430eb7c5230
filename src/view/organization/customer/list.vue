<template>
  <div class="f_order-wrapper">
    <Form inline @submit.native.prevent @keyup.enter.native="onSearch">
      <Row>
        <FormItem>
          <Input v-model="queryFormData.name" placeholder="请输入个人客户名称/ID" clearable />
        </FormItem>
        <FormItem>
          <Select v-model="user_vip_type" placeholder="会员名称" multiple clearable>
            <Option v-for="item in userTypeDesc" :key="item.id" :value="item.id" :disabled="isVipDisable(item.id)">
              {{ item.desc }}
            </Option>
          </Select>
        </FormItem>
        <FormItem>
          <Input v-model="queryFormData.mobile" placeholder="请输入手机号" clearable />
        </FormItem>

        <FormItem>
          <Select v-model="queryFormData.status" placeholder="请选择状态" clearable>
            <Option v-for="item in statusDesc" :key="item.id" :value="item.id">{{ item.desc }}</Option>
          </Select>
        </FormItem>

        <FormItem>
          <DatePicker
            type="daterange"
            clearable
            :options="disabledTime"
            format="yyyy-MM-dd"
            placeholder="请选择创建时间"
            v-model="timeRange"
            @on-change="times => handleTimeChange(times)"
            class="time-range"
          ></DatePicker>
        </FormItem>
      </Row>
      <Row>
        <FormItem style="text-align: left">
          <Button type="primary" class="mr10" @click="onSearch">筛选</Button>
          <Button type="default" class="mr10" @click="onResetSearch">重置</Button>
          <Button class="mr10" :loading="downloadLoading" type="default" @click="downloadExcel(queryFormData)"
            >导出
          </Button>
          <Button type="primary" @click="addCustomer()">新增个人客户</Button>
        </FormItem>
      </Row>
    </Form>
    <div class="table-wrapper">
      <Table :loading="tableLoading" :columns="tableCols" :data="list" :height="$store.state.app.clientHeight - 297">
        <template slot="mobile" slot-scope="{ row }">
          {{ row.mobile || '-' }}
        </template>

        <template slot="custom_type" slot-scope="{ row }">
          <div v-if="row.custom_type_desc.length">
            <div
              class="flex flex-item-align flex-item-center"
              v-for="(c_item, c_index) in row.custom_type_desc"
              :key="c_index"
            >
              <div class="mark"></div>
              <div>{{ c_item }}</div>
            </div>
          </div>
          <div v-else>-</div>
        </template>
        <template slot-scope="{ row }" slot="vip_type_desc">
          <div v-if="row.vip_type_desc.length">
            <div class="flex flex-item-align" v-for="(c_item, c_index) in row.vip_type_desc" :key="c_index">
              <div class="mark"></div>
              <div>{{ c_item }}</div>
            </div>
          </div>
          <div v-else>-</div>
        </template>
        <template slot="create_time" slot-scope="{ row }">
          {{ row.create_time | date_format }}
        </template>

        <template slot="balance" slot-scope="{ row }">
          <k-link
            v-if="isHasBalanceDetailPage && row.prepay_balance?.id != 0"
            :to="{ path: '/finance/balance/detail', query: { id: row.prepay_balance?.id } }"
            target="_blank"
          >
            {{ row.prepay_balance?.balance || 0 }}
          </k-link>
          <div v-else>{{ row.prepay_balance?.balance || 0 }}</div>
        </template>

        <template slot-scope="{ row, index }" slot="action">
          <a class="mr10" @click="toDetail(row)">编辑</a>
          <Poptip
            class="mr10"
            v-if="row.status == 'ENABLED'"
            placement="top-end"
            :ref="`${row.id}pop`"
            confirm
            @on-ok="changeCustomerStatus(row, 'DISABLED')"
            @on-popper-hide="resetPopper(row, index)"
          >
            <div slot="title">
              <div>确认禁用?</div>
              <div class="flex">
                <span style="width: 70px"><span style="color: red">*</span> 禁用原因:</span>
                <Input
                  style="width: 200px"
                  class="flex-1"
                  :class="{ validError: validDisabledReason }"
                  v-model="forbiddenReason"
                  type="textarea"
                  :autosize="{ minRows: 3, maxRows: 3 }"
                ></Input>
              </div>
            </div>
            <a>禁用</a>
          </Poptip>
          <Poptip
            v-else
            class="mr10"
            confirm
            title="确认要启用?"
            @on-ok="changeCustomerStatus(row, 'ENABLED')"
            placement="top-end"
          >
            <a>启用</a>
          </Poptip>
          <a @click="toDetail(row, 'detail')">详情</a>
        </template>
      </Table>
      <div class="block_20"></div>
      <KPage
        :total="total"
        :page-size="+queryFormData.pageSize"
        :current="+queryFormData.page"
        @on-change="handleCurrentChange"
        @on-page-size-change="handleSizeChange"
        style="text-align: center"
      />
    </div>
  </div>
</template>

<script>
import downloadExcel from '@/mixins/downloadExcel';
import S from 'utils/util';
import search from '@/mixins/search';

const init_query_form_data = {
  page: 1,
  pageSize: 20,
  name: '',
  mobile: '',
  status: '',
  user_type: '',
  st: '',
  et: '',
  r: '',
};
export default {
  name: 'list',
  mixins: [search, downloadExcel],
  components: {},
  data() {
    return {
      downloadApiName: 'getPCExportlist',
      apiName: 'getPCList', // 获取明细列表
      queryFormData: { ...init_query_form_data },
      timeRange: [],
      disabledTime: {
        disabledDate(date) {
          return date && date.valueOf() > Date.now();
        },
      },
      tableCols: [
        { title: '个人ID', key: 'id', align: 'center' },
        { title: '个人客户名称', key: 'name', align: 'center' },
        { title: '会员名称', slot: 'vip_type_desc', align: 'center', width: 145 },
        { title: '手机号', slot: 'mobile', align: 'center' },
        { title: '客户类型', slot: 'custom_type', align: 'center' },
        { title: '预付款可用余额', slot: 'balance', align: 'center' },
        { title: '状态', key: 'status_desc', align: 'center' },
        { title: '创建时间', slot: 'create_time', align: 'center' },
        { title: '操作', slot: 'action', align: 'center' },
      ],
      statusDesc: [],
      forbiddenReason: '',
      validDisabledReason: false,
      isHasBalanceDetailPage: S.rootPageCheck('/finance/balance/detail'),
      userTypeDesc: [],
    };
  },
  computed: {
    user_vip_type: {
      set(val) {
        this.queryFormData.user_type = val.join(',');
      },
      get() {
        return this.queryFormData.user_type ? this.queryFormData.user_type.split(',') : [];
      },
    },
    isVipDisable() {
      const nonVipId = this.userTypeDesc.find(item => item.is_vip === '0')?.id;
      return id => {
        if (!this.user_vip_type.length) return false;
        const isNonVipSelected = this.user_vip_type.includes(nonVipId);
        return isNonVipSelected ? id !== nonVipId : id === nonVipId;
      };
    },
  },
  watch: {},
  created() {
    this.getPCOptions();
    this.queryFormData = S.merge(this.queryFormData, this.$route.query);
    this.submitQueryForm(true);
  },
  mounted() {},
  methods: {
    addCustomer() {
      this.$router.push('/organization/customer/edit');
    },
    toDetail(row, type) {
      this.$router.push({
        path: '/organization/customer/edit',
        query: {
          type,
          id: row.id,
        },
      });
    },
    onResetSearch() {
      this.queryFormData = { ...init_query_form_data };
      this.timeRange = [];
      this.submitQueryForm();
    },
    changeCustomerStatus(row, status) {
      if (status === 'DISABLED' && !this.forbiddenReason) {
        this.$Message.error('请输入禁用原因');
        this.validDisabledReason = true;
        this.$refs[`${row.id}pop`].visible = true;
        return;
      }
      let params = { id: row.id, disabled_reason: this.forbiddenReason, status };
      this.$api.getPCDisabled(params).then(
        res => {
          if (res.error_msg) {
            this.$Modal.error({
              title: '无法启用',
              content: `<p>${res.error_msg}</p>`,
            });
            return;
          }
          this.$Message.success(`${status === 'DISABLED' ? '禁用' : '启用'}成功`);
          this.loadList();
        },
        err => this.$Message.error(err.errmsg)
      );
    },

    resetPopper() {
      this.forbiddenReason = '';
      this.validDisabledReason = false;
    },

    getPCOptions() {
      this.$api.getPCOptions().then(
        res => {
          this.statusDesc = S.descToArrHandle(res.statusDesc);
          this.userTypeDesc = S.descToArrHandle(res.vipTypeDesc);
        },
        err => this.$Message.error(err.errmsg)
      );
    },
  },
  beforeRouteUpdate(to, from, next) {
    this.queryFormData = S.merge(init_query_form_data, to.query);
    this.getTimeRange();
    this.loadList();
    next();
  },
};
</script>

<style scoped lang="less">
.mark {
  width: 4px;
  height: 4px;
  border-radius: 50%;
  background: #333;
  margin-right: 6px;
}
</style>
