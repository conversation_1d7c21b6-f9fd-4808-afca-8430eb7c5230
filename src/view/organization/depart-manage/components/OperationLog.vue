<template>
  <Modal
      v-model="visible"
      title="操作记录"
      width="900"
      :styles="{ top: '8vh' }"
      @on-ok="handleOk"
  >
    <div class="operation-log-table">
      <Table :columns="columns" :data="tableData" :loading="loading" :height="$store.state.app.clientHeight - 300">
        <template #create_time="{ row }">
          {{ row.create_time * 1000 ? moment(row.create_time * 1000).format('YYYY-MM-DD HH:mm:ss') : '-' }}
        </template>
        <template #before_value="{ row }">
          {{ row.before_value || '-' }}
        </template>
        <template #after_value="{ row }">
          {{ row.after_value || '-' }}
        </template>
      </Table>
    </div>

    <KPage
        :total="total"
        :page-size="pageSize"
        :current="current"
        @on-change="handleCurrentChange"
        @on-page-size-change="handleSizeChange"
        style="text-align: center"
    />
    <template #footer>
      <Button type="primary" @click="handleOk">确定</Button>
    </template>
  </Modal>
</template>

<script>
import moment from "moment";
export default {
  name: 'OperationLog',

  props: {
    id: {
      type: [String, Number],
      required: true
    }
  },

  data() {
    return {
      moment,
      visible: false,
      loading: false,
      current: 1,
      pageSize: 20,
      total: 0,
      tableData: [],
      columns: [
        {
          title: '操作时间',
          slot: 'create_time',
          width: 160
        },
        {
          title: '操作人',
          key: 'operator_name',
          width: 120
        },
        {
          title: '变更字段',
          key: 'remark',
          width: 120
        },
        {
          title: '更改前',
          key: 'before_value',
          ellipsis: true,
          tooltip: true,
          minWidth: 140,
        },
        {
          title: '更改后',
          key: 'after_value',
          ellipsis: true,
          tooltip: true,
          minWidth: 140
        }
      ]
    }
  },

  methods: {
    // 显示弹窗
    show() {
      this.visible = true
      this.current = 1
      this.getLogList()
    },

    // 获取日志列表
    async getLogList() {
      try {
        this.loading = true
        console.log({
          id: this.id,
          page: this.current,
          pageSize: this.pageSize
        }, 'searchParams')
        const res = await this.$api.getDepartManageLog({
          id: this.id,
          page: this.current,
          pageSize: this.pageSize
        })
        this.tableData = res.list || []
        this.total = res.total || 0
      } catch (error) {
        this.$Message.error('获取操作记录失败')
        console.error('获取操作记录失败:', error)
      } finally {
        this.loading = false
      }
    },

    handleSizeChange(val) {
      this.pageSize = val
      this.current = 1
      this.getLogList()
    },
    handleCurrentChange(val) {
      this.current = val
      this.getLogList()
    },
    // 确定按钮点击
    handleOk() {
      this.tableData = []
      this.visible = false
    }
  }
}
</script>

<style lang="less" scoped>
.operation-log-table {
  margin-bottom: 16px;
}
</style>