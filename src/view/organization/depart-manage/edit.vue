<script>
import _ from 'lodash'
import OperationLog from './components/OperationLog.vue'

export default {
  name: 'DepartEdit',
  
  // 组件数据
  data() {
    return {
      // 表单数据对象
      formData: {
        name: '',
        clinic_range: [], // 管理范围，多选
        remark: '' // 备注信息
      },
      
      // 诊所列表相关数据
      clinicList: {
        loading: false,
        data: [] // 添加data属性存储诊所列表
      },
      
      // 保存按钮loading状态
      saveLoading: false,
      
      // 表单验证规则
      ruleValidate: {
        name: [{ required: true, message: '请输入部门名称', trigger: 'blur' }],
        clinic_range: [{ required: true, type: 'array', message: '请选择管理范围', trigger: 'change' }]
      },

      manageRangeLoading: false
    }
  },

  // 计算属性
  computed: {
    // 页面类型：add-新增，edit-编辑，view-查看
    pageType() {
      return this.$route.query.type || 'add'
    },
    
    // 是否为查看模式
    isView() {
      return this.pageType === 'view'
    },
    
    // 当前编辑的部门ID
    id() {
      return this.$route.query.id
    }
  },

  // 生命周期钩子
  created() {
    // 初始化数据
    this.initPageData()
  },

  mounted() {
    // 更新面包屑文本
    this.updateBreadcrumb()
  },

  components: {
    OperationLog
  },

  methods: {
    // 初始化页面数据
    async initPageData() {
      try {
        // 并行加载诊所列表和管理范围选项
        if (!this.id) {
          await this.getClinicList()
        }

        // 如果是编辑或查看模式，加载详情
        if (this.id) {
          await this.getDetail()
        }
      } catch (error) {
        this.$Message.error('数据加载失败')
        console.error('初始化页面数据失败:', error)
      }
    },

    // 更新面包屑文本
    updateBreadcrumb() {
      this.$nextTick(() => {
        const dom = document.querySelectorAll('.ivu-breadcrumb-item-link')
        if (dom.length) {
          const lastBreadcrumb = dom[dom.length - 1]
          const textMap = {
            view: '详情',
            edit: '编辑',
            add: '新增'
          }
          lastBreadcrumb.innerText = textMap[this.pageType] || '新增'
        }
      })
    },

    // Select组件打开关闭的回调
    handleSelectChange(isOpen) {
      if (!isOpen) {
        this.getClinicList()
      }
    },

    // 获取诊所列表
    async getClinicList(params = {}) {
      try {
        this.clinicList.loading = true
        const res = await this.$api.getDepartClinicsList(params)
        this.clinicList.data = res.list || []
      } catch (error) {
        this.$Message.error('获取诊所列表失败')
        console.error('获取诊所列表失败:', error)
      } finally {
        this.clinicList.loading = false
      }
    },

    // 远程搜索诊所
    remoteSearchClinic: _.debounce(async function(query) {
      if (!query) {
        return this.getClinicList()
      }

      try {
        this.clinicList.loading = true
        const res = await this.$api.getDepartClinicsList({ keyword: query })
        this.clinicList.data = res.list || []
      } catch (error) {
        this.$Message.error('搜索诊所失败')
        console.error('搜索诊所失败:', error)
      } finally {
        this.clinicList.loading = false
      }
    }, 300),

    // 获取部门详情
    async getDetail() {
      try {
        const data = await this.$api.getDepartManageDetail({ id: this.id })

        // 格式化表单数据
        this.formData = {
          ...data,
          clinic_range: Array.isArray(data.clinic_range) ? data.clinic_range : (data.clinic_range ? [data.clinic_range] : [])
        }
        await this.getClinicList({ ids: data.clinic_range })
        data.clinic_range.forEach((key, i) => {
          if (this.clinicList.data.every(clinic => clinic.id !== key)) {
            this.clinicList.data.push({
              id: key,
              name: data?.clinic_name_range?.[i]
            })
          }
        })
      } catch (error) {
        this.$Message.error('获取部门详情失败')
        console.error('获取部门详情失败:', error)
      }
    },

    // 提交表单
    async handleSubmit() {
      try {
        // 表单验证
        const valid = await this.$refs.formValidate.validate()
        if (!valid) return

        this.saveLoading = true
        const params = {
          ...this.formData,
          ...(this.id ? { id: this.id } : {})
        }
        await this.$api.saveDepartManage(params)
        this.$Message.success((this.pageType === 'add' ? '新增' : '修改') +'成功')
        this.$router.push('/organization/depart-manage/list')
      } catch (error) {
        this.$Message.error(error?.errmsg || '保存失败')
      } finally {
        this.saveLoading = false
      }
    },

    // 显示操作记录
    showOperationLog() {
      this.$refs.operationLog.show()
    }
  }
}
</script>

<template>
  <div class="depart-edit-wrapper">
    <div class="flex wrapper-header">
      <div>基本信息</div>
      <a @click="showOperationLog" v-if="pageType !== 'add'">操作记录</a>
    </div>
    <!-- 部门表单 -->
    <Form
        ref="formValidate"
        class="depart-edit-form"
        label-colon
        :style="{ marginTop: isView ? '24px' : 0 }"
        :model="formData"
        :rules="!isView ? ruleValidate : {}"
        :label-width="100"
    >
      <!-- 状态 -->
      <FormItem label="状态" v-if="isView">
        <Input :class="formData.status === 'OFF' ? 'text-danger' : ''" v-model="formData.status_desc" disabled />
      </FormItem>
      <!-- 部门ID，仅编辑和查看模式显示 -->
      <FormItem label="部门ID" prop="id" v-if="pageType !== 'add'">
        <Input v-model="formData.id" placeholder="请输入部门ID" disabled />
      </FormItem>

      <!-- 部门名称 -->
      <FormItem label="部门名称" prop="name">
        <Input v-model="formData.name" placeholder="请输入部门名称" :disabled="isView" />
      </FormItem>

      <!-- 管理范围选择 -->
      <FormItem label="管理范围" prop="clinic_range">
        <Select
          v-model="formData.clinic_range"
          placeholder="请选择或搜索诊所"
          :disabled="isView"
          multiple
          filterable
          remote
          :remote-method="remoteSearchClinic"
          :loading="clinicList.loading"
          allowClear
          @on-open-change="handleSelectChange"
        >
          <Option 
            v-for="item in clinicList.data" 
            :key="item.id" 
            :value="item.id"
            :label="item.name"
          >
            {{ item.name }} <span v-if="item.is_off === '1'" style="margin-left: 4px; color:#999999;">(已禁用)</span>
          </Option>
        </Select>
      </FormItem>

      <!-- 备注信息 -->
      <FormItem label="备注">
        <Input 
          v-model="formData.remark" 
          type="textarea" 
          :rows="4"
          show-word-limit
          :maxlength="100"
          placeholder="请输入备注" 
          :disabled="isView"
        />
      </FormItem>
    </Form>

    <!-- 底部按钮区域 -->
    <div class="fixed-bottom-wrapper">
      <back-button></back-button>
      <Button 
        v-if="!isView" 
        type="primary" 
        :loading="saveLoading" 
        @click="handleSubmit" 
        class="ml10"
      >
        保存
      </Button>
<!--      <Button-->
<!--        v-if="isView"-->
<!--        type="primary"-->
<!--        @click="showOperationLog"-->
<!--        class="ml10"-->
<!--      >-->
<!--        操作记录-->
<!--      </Button>-->
    </div>

    <OperationLog 
      ref="operationLog"
      :id="id"
    />
  </div>
</template>

<style scoped lang="less">
.depart-edit-wrapper {
  padding: 16px;
  position: relative;
  .depart-edit-form {
    width: 50%;
    min-width: 650px;
  }
  
  .ml10 {
    margin-left: 10px;
  }
}
.wrapper-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: #efefef;
  padding: 10px;
  margin-bottom: 28px;
  > div:first-child {
    font-size: 14px;
    padding-left: 6px;
    font-weight: 700;
  }
}
:deep(.text-danger input) {
  color: #a94442 !important;
}
:deep(.ivu-input[disabled], fieldset[disabled] .ivu-input) {
  color: #999999;
}
:deep(.ivu-select-disabled .ivu-icon-ios-close) {
  display: none;
}
:deep(.ivu-select-disabled .ivu-tag .ivu-tag-text) {
  margin-right: 0;
  color: #999;
}
</style>