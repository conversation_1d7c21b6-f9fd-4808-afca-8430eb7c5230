<template>
  <div class="depart-manage-wrapper">
    <Form inline @submit.native.prevent @keyup.enter.native="onSearch">
      <Row>
        <FormItem>
          <Select
              v-model="queryFormData.department_id"
              placeholder="请选择部门"
              style="width: 200px"
              clearable
          >
            <Option
                v-for="item in selectableDepartList"
                :key="item.id"
                :value="item.id"
            >{{ item.name }}</Option
            >
          </Select>
        </FormItem>
        <FormItem>
          <Input v-model="queryFormData.clinic_name" placeholder="请输入管理范围（诊所名称）" clearable />
        </FormItem>
        <FormItem>
          <Button type="primary" @click="onSearch">筛选</Button>
          <Button class="ml10" type="default" @click="onResetSearch">重置</Button>
          <Button class="ml10" type="primary" @click="handleAdd">创建部门</Button>
        </FormItem>
      </Row>
    </Form>
    <div class="table-wrapper">
      <Table :loading="tableLoading" :columns="tableCols" :data="list" :height="$store.state.app.clientHeight - 300">
        <template slot-scope="{ row }" slot="member_count">
          <a v-if="+row.member_count > 0" @click="toMemberList(row)">{{ row.member_count }}</a>
          <div v-else>0</div>
        </template>
        <template slot-scope="{ row }" slot="clinic_count">
          <a v-if="+row.clinic_count > 0" @click="toClinicList(row)">{{ row.clinic_count }}</a>
          <div v-else>0</div>
        </template>
        <template slot-scope="{ row }" slot="status">
          <div :class="row.status === 'OFF' ? 'text-danger' : ''">{{ row.status_desc || '-' }}</div>
        </template>
        <template slot-scope="{ row }" slot="action">
          <a @click="handleEdit(row)" class="mr10">编辑</a>
          <a @click="handleDetail(row)" class="mr10">详情</a>
          <Poptip
            confirm
            :title="`确认${row.status === 'ON' ? '禁用' : '启用'}该部门吗？`"
            @on-ok="() => handleToggleStatus(row)"
          >
            <a class="mr10">{{ row.status === 'ON' ? '禁用' : '启用' }}</a>
          </Poptip>
        </template>
      </Table>

      <div class="block_20"></div>
      <KPage
        :total="total"
        :page-size.sync="+queryFormData.pageSize"
        :current.sync="+queryFormData.page"
        @on-change="handleCurrentChange"
        @on-page-size-change="handleSizeChange"
        style="text-align: center"
      />
    </div>
  </div>
</template>

<script>
import S from 'utils/util';
import search from '@/mixins/search';

const init_query_form_data = {
  page: 1,
  pageSize: 20,
  keyword: '',
  scope: ''
};

export default {
  name: 'DepartManage',
  components: {},
  mixins: [search],
  data() {
    return {
      apiName: 'getDepartList', // API名称
      queryFormData: { ...init_query_form_data },
      tableCols: [
        { title: '部门ID', key: 'id', align: 'center', width: 100 },
        { title: '部门名称', key: 'name', align: 'center', tooltip: true, maxWidth: 150 },
        { title: '管理范围', key: 'clinic_range_name_desc', align: 'center', tooltip: true },
        { title: '员工数量', slot: 'member_count', align: 'center', width: 100 },
        { title: '诊所数量', slot: 'clinic_count', align: 'center', width: 100 },
        { title: '状态', slot: 'status', align: 'center', width: 100 },
        { title: '操作', slot: 'action', align: 'center', width: 200 }
      ],
      selectableDepartList: []
    };
  },
  created() {
    this.queryFormData = S.merge(this.queryFormData, this.$route.query);
    this.getSelectableDepartList();
    this.submitQueryForm(true);
  },
  methods: {
    getSelectableDepartList() {
      this.$api.selectableDepartList({
        source: 'department'
      }).then(res => {
        this.selectableDepartList = res?.list || []
      })
    },
    // 重置搜索
    onResetSearch() {
      this.queryFormData = { ...init_query_form_data };
      this.submitQueryForm();
    },

     // 新增部门
     handleAdd() {
      this.$router.push({
        path: '/organization/depart-manage/edit',
        query: { type: 'add' }
      });
    },

    // 编辑部门
    handleEdit(row) {
      this.$router.push({
        path: '/organization/depart-manage/edit',
        query: { type: 'edit', id: row.id }
      });
    },

    // 查看详情
    handleDetail(row) {
      this.$router.push({
        path: '/organization/depart-manage/edit',
        query: { type: 'view', id: row.id }
      });
    },

    // 启用/禁用
    handleToggleStatus(row) {
      const action = row.status === 'ON' ? '禁用' : '启用';
      this.$api.changeDepartManageStatus({ id: row.id, status: row.status === "ON" ? 'OFF' : "ON" }).then(() => {
        this.$Message.success(`${action}成功`);
        this.submitQueryForm();
      }).catch(err => {
        console.log(err, 'err')
        this.$Message.error(err.errmsg || '修改失败');
      });
    },

    // 跳转到员工列表
    toMemberList(row) {
      this.$router.push({
        path: '/setting/member/list',
        query: {
          department_id: row.id
        }
      })
    },

    // 跳转到诊所列表
    toClinicList(row) {
      this.$router.push({
        path: '/organization/clinic/list',
        query: {
          department_id: row.id
        }
      })
    }
  },
  beforeRouteUpdate(to, from, next) {
    this.queryFormData = S.merge(init_query_form_data, to.query);
    this.loadList();
    next();
  },
};
</script>

<style lang="less" scoped>
.ml10 {
  margin-left: 10px;
}
.mr10 {
  margin-right: 10px;
}
</style>