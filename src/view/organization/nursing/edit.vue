<template>
  <div class="edit-wrapper">
    <div class="block-header">养疗馆基本信息</div>
    <Row>
      <Col span="10">
        <Form ref="formData" :model="formData" :rules="ruleValidate" :label-width="130" :label-colon="true">
          <FormItem label="养疗馆编号" v-if="$route.query.id && (isReadonly || $route.query.open_status == 'EXAMINE')">
            <p>{{ formData.rxj_code }}</p>
          </FormItem>

          <FormItem label="所属省公司">
            <Input
              v-if="
                !$route.query.id ||
                (($route.query.open_status == 'EXAMINE_FAIL' ||
                  $route.query.open_status == 'PREPARE' ||
                  $route.query.open_status == 'FINISHED') &&
                  !isReadonly)
              "
              v-model="formData.company_name"
              placeholder="所属省公司"
              disabled
            ></Input>
            <p v-else>{{ formData.company_name }}</p>
          </FormItem>

          <FormItem
            label="驳回原因"
            style="color: red"
            v-if="$route.query.open_status === 'EXAMINE_FAIL' && !$route.query.isEdit"
            class="red-item"
          >
            <div style="text-indent: 8px; color: red; width: 100%; border-radius: 2px">
              {{ formData.examine_rejected_msg }}
            </div>
          </FormItem>

          <FormItem label="养疗馆名称" prop="name">
            <Input
              v-if="
                !$route.query.id ||
                (($route.query.open_status == 'EXAMINE_FAIL' ||
                  $route.query.open_status == 'PREPARE' ||
                  $route.query.open_status == 'FINISHED') &&
                  !isReadonly)
              "
              v-model="formData.name"
              placeholder="输入养疗馆名称"
            ></Input>
            <p v-else>{{ formData.name || '-' }}</p>
          </FormItem>

          <FormItem label="所属合伙人">
            <p>{{ formData.partner_name || '-' }}</p>
          </FormItem>

          <FormItem label="状态" v-if="$route.query.id && (isReadonly || $route.query.open_status == 'EXAMINE')">
            <p>{{ formData.open_status_text }}</p>
          </FormItem>

          <FormItem label="负责人" prop="leading_person">
            <Input
              v-if="
                !$route.query.id ||
                (($route.query.open_status == 'EXAMINE_FAIL' ||
                  $route.query.open_status == 'PREPARE' ||
                  $route.query.open_status == 'FINISHED') &&
                  !isReadonly)
              "
              v-model="formData.leading_person"
              placeholder="输入负责人"
            ></Input>
            <p v-else>{{ formData.leading_person }}</p>
          </FormItem>

          <FormItem label="负责人手机号" prop="leading_mobile">
            <Input
              v-if="
                !$route.query.id ||
                (($route.query.open_status == 'EXAMINE_FAIL' ||
                  $route.query.open_status == 'PREPARE' ||
                  $route.query.open_status == 'FINISHED') &&
                  !isReadonly)
              "
              :maxlength="11"
              type="number"
              v-model="formData.leading_mobile"
              placeholder="输入负责人手机号"
            ></Input>
            <p v-else>{{ formData.leading_mobile }}</p>
          </FormItem>

          <FormItem label="开业时间">
            <p>{{ formData.open_date || '-' }}</p>
          </FormItem>

          <FormItem label="养疗馆地址">
            <div
              v-if="
                !$route.query.id ||
                (($route.query.open_status == 'EXAMINE_FAIL' ||
                  $route.query.open_status == 'PREPARE' ||
                  $route.query.open_status == 'FINISHED') &&
                  !isReadonly)
              "
              class="addWrap"
            >
              <div class="addressBox" style="width: 55%">
                <el-cascader
                  v-model="selectedAddress"
                  :options="options"
                  clearable
                  :disabled="$route.query.open_status === 'EXAMINE'"
                  placeholder="请选择联系地址"
                  size="small"
                  popper-class="address-com"
                  style="width: 100%"
                  @change="regionChange"
                >
                </el-cascader>
              </div>

              <div class="addressInput ml10">
                <Input v-model="formData.address_other" placeholder="详细地址"></Input>
              </div>
            </div>
            <p v-else>{{ fullAddress }}</p>
          </FormItem>

          <FormItem label="设置地图点位" prop="lon">
            <a @click="changeShowMap">{{ showMap === true ? '收起' : '展开' }}</a>
            <div class="map" v-show="showMap">
              <AMap
                :id="id"
                :hasPosition="position"
                @getAddressDetail="getAddressDetail"
                :is-api-ready="isApiReady"
                :open_status="$route.query.open_status"
                :checkDetail="$route.query.checkDetail"
              ></AMap>
            </div>
          </FormItem>
          <FormItem label="养疗馆经纬度" v-if="$route.query.id">
            <p>
              <span style="margin-right: 16px">经度：{{ formData.lon }}</span>
              <span>纬度：{{ formData.lat }}</span>
            </p>
          </FormItem>

          <FormItem label="开始使用系统时间" v-if="$route.query.id && formData.use_system_time !== '0'">
            <Input :disabled="true" :value="formData.use_system_time | date_format"></Input>
          </FormItem>
        </Form>
      </Col>
    </Row>
    <Form
      v-if="$route.query.open_status !== 'EXAMINE' && $route.query.open_status !== 'EXAMINE_FAIL' && shop_config.length"
      :disabled="prepar_progress === '100'"
      ref="configForm"
      :label-width="130"
      label-colon
    >
      <div class="block-header">养疗馆筹备进度</div>
      <Row>
        <Col v-for="(item, index) in shop_config" :key="item.addr_init" span="14">
          <FormItem :label="`Step.${index + 1} ${item.title}`">
            <div class="flex flex-item-align">
              <Tag :color="getColor(item.status_option[item.status])">{{ item.status_option[item.status] }}</Tag>
              <div class="flex flex-1 time-out ml10">
                <DatePicker
                  type="date"
                  format="yyyy-MM-dd"
                  :disabled="isReadonly || $route.query.open_status == 'FINISHED'"
                  placeholder="请选择预期完成时间"
                  @on-change="time => changeTime(time, index)"
                  style="flex: 1"
                  :value="item.estimate_date"
                ></DatePicker>
                <span
                  style="color: red; flex: 0.2; text-align: center; margin-left: 20px"
                  v-if="item.over_date && item.status_option[item.status] != '已完成'"
                  >已超时{{ item.over_date }}天</span
                >
              </div>
            </div>
          </FormItem>
          <FormItem label="">
            <CheckboxGroup v-model="item.select" @on-change="val => changeOpeningStatus(val, index)">
              <Checkbox
                :disabled="isReadonly || $route.query.open_status == 'FINISHED'"
                v-for="(subItem, subIndex) in item.checkbox_option"
                :key="subItem.title"
                :label="subIndex + ''"
              >
                {{ subItem.title }}
              </Checkbox>
            </CheckboxGroup>
          </FormItem>
          <FormItem v-if="index === 2 && item.select.includes('business')" label="工商注册凭证" required>
            <Picture
              v-model="item.business_img"
              :isQueryDetail="isReadonly || $route.query.open_status == 'FINISHED'"
              :limit="1"
            />
          </FormItem>
          <div class="custom-img" v-for="(UnionPay_item, UnionPay_index) in item.checkbox_option" :key="UnionPay_index">
            <!-- <FormItem v-if="index===3&&item.select.includes(`${UnionPay_index}`)" :label="`${UnionPay_item.title}凭证`" required>
              <Picture v-model="item[UnionPay_index+'_img']" :isQueryDetail="isReadonly" :limit="1"/>
            </FormItem> -->
          </div>
        </Col>
      </Row>
    </Form>

    <div class="block_45"></div>

    <div v-if="$route.query.open_status == 'EXAMINE'" class="fixed-bottom-wrapper">
      <Button @click="back">返回</Button>
      <Button style="margin: 0 20px" type="error" @click="showRefuseModal">审核驳回</Button>
      <Button type="primary" @click="passCheck">审核通过</Button>
    </div>

    <div v-else class="fixed-bottom-wrapper">
      <Button style="margin-right: 20px" @click="back">返回</Button>
      <Button type="primary" @click="handleSubmit" :loading="saveLoading" v-if="!isReadonly || !$route.query.id"
        >保存
      </Button>
    </div>

    <Modal v-model="refuseModalVisible" :mask-closable="false" title="驳回审核" @on-ok="submitRefuseReason">
      <div style="width: 100%">
        <p class="label">请输入驳回原因：</p>
        <Input
          v-model="examine_rejected_msg"
          :autosize="{ minRows: 3, maxRows: 6 }"
          style="max-width: 1000px"
          type="textarea"
        />
      </div>
    </Modal>
  </div>
</template>

<script>
// import S from 'utils/util'
import Picture from '@/components/upload/picture';
import AMap from '@/components/AMap/AMap';
import { getUser } from 'utils/runtime';
import { CodeToText, regionData } from '@/utils/chinaMap';

export default {
  name: 'edit',
  components: {
    Picture,
    AMap
  },
  mixins: [],
  props: {},
  data() {
    const validateMobile = (rule, value, callback) => {
      if (value === '') {
        callback(new Error('请输入正确的手机号码'));
      } else {
        const reg = /^1[3456789]\d{9}$/;
        if (!reg.test(value)) {
          callback(new Error('请输入正确的手机号码'));
        }
        callback();
      }
    };

    return {
      typeDesc: '', // 类型list
      // options: regionData, // 地区枚举值
      selectedAddress: [], // 地区选择
      saveLoading: false, // 保存loading
      options: [], // 地址
      formData: {
        company_name: '', // 所属省公司
        name: '', // 养疗馆名称
        leading_person: '', // 联系人
        leading_mobile: '', // 联系电话
        prov_name: '', //省
        prov_code: '', //省编码
        city_name: '', //市
        city_code: '', //市编码
        county_name: '', //区
        county_code: '', //区编码
        address_other: '', //详细地址
        lon: '', //纬度
        lat: '' //经度
      },
      ruleValidate: {
        name: [{ required: true, message: '养疗馆名称', trigger: 'blur' }],
        leading_person: [{ required: true, message: '请输入负责人名称', trigger: 'blur' }],
        leading_mobile: [
          { required: true, message: '请输入联系人电话', trigger: 'change' },
          { required: true, validator: validateMobile }
        ],
        lon: [{ required: true, message: '请选择地图点位', trigger: 'change' }]
      },

      shop_config: [], // 养疗馆筹备进度

      refuseModalVisible: false,
      examine_rejected_msg: '', //审核不通过说明
      position: [],
      showMap: false,
      address: '',
      prepar_progress: '', //准备
      isApiReady: false,
      id: ''
    };
  },
  computed: {
    getColor() {
      return status => {
        switch (status) {
          case '未开始':
            return 'default';
          case '已完成':
            return '#19be6b';
          default:
            return '#ff9900';
        }
      };
    },
    fullAddress() {
      return `${this.formData.prov_name}${this.formData.city_name}${this.formData.county_name}${this.formData.address_other}`;
    },
    // 是否为只读项
    isReadonly() {
      return !this.$route.query.isEdit || this.$route.query.checkDetail;
    }
  },
  watch: {},
  created() {
    this.init();
  },
  mounted() {
    console.log(this.$route.query);
  },
  methods: {
    /**
     * @description 初始化一些数据
     * */
    init() {
      let data = [];
      let { company_name, prov_name, prov_code } = getUser();
      this.formData.company_name = company_name;
      this.options = regionData;

      if (this.$route.query.id) {
        // 编辑时才有的参数
        this.getNursingInfo(this.$route.query.id);
        this.id = this.$route.query.id;
      } else {
        this.formData.company_name = company_name;
        this.formData.prov_name = prov_name;
        this.formData.prov_code = prov_code;
      }
    },

    /**
     * @description 地区选择事件
     * @param { address } 省市区地址集合
     */
    regionChange(address) {
      if (address.length) {
        const province = {
          value: CodeToText[address[0]],
          key: address[0]
        };
        const city = {
          value: CodeToText[address[1]],
          key: address[1]
        };
        const area = {
          value: CodeToText[address[2]],
          key: address[2]
        };
        this.formData.prov_name = province.value;
        this.formData.prov_code = province.key;
        this.formData.city_name = city.value;
        this.formData.city_code = city.key;
        (this.formData.county_name = area.value), (this.formData.county_code = area.key);
      } else {
        this.formData.prov_name = '';
        this.formData.prov_code = '';
        this.formData.city_name = '';
        this.formData.city_code = '';
        this.formData.county_name = '';
        this.formData.county_code = '';
      }
      // this.address = `${this.formData.prov_name}${this.formData.city_name}${this.formData.county_name}${this.formData.address_other}`
    },

    /**
     * @description 设置地区回显
     * @param { county_code } 区的code
     * @param { city_code } 市的code
     * @param { prov_code } 省的code
     *
     * */
    setSelectAddress(county_code = '', city_code = '', prov_code = '') {
      if (county_code) {
        this.selectedAddress = [prov_code, city_code, county_code];
        return;
      }
      if (city_code) {
        this.selectedAddress = [prov_code, city_code];
        return;
      }
      if (prov_code) {
        this.selectedAddress = [prov_code];
      }
    },

    // 返回养疗馆列表
    back() {
      this.$router.replace('/organization/nursing/list');
    },

    // 创建/编辑 确定按钮
    handleSubmit() {
      this.$refs['formData'].validate(valid => {
        if (valid) {
          this.editRxjEnter();
        }
      });
    },

    /**
     * @description 创建/编辑养疗馆
     * */
    editRxjEnter() {
      this.saveLoading = true;
      const {
        name,
        leading_person,
        leading_mobile,
        prov_name,
        prov_code,
        city_name,
        city_code,
        county_name,
        county_code,
        address_other,
        lon,
        lat
      } = this.formData;
      let params = {
        name,
        leading_person,
        leading_mobile,
        prov_name,
        prov_code,
        city_name,
        city_code,
        county_name,
        county_code,
        address_other,
        lon,
        lat
      };

      if (this.$route.query.id) {
        params.id = this.$route.query.id;
      }

      if (this.$route.query.id) {
        let params_config = {};
        let hasUploadImg = true;
        this.shop_config.map((item, index) => {
          params_config[item.type] = {
            select: item.select,
            estimate_date: item.estimate_date
          };
          if (index === 2) {
            params_config[item.type].business_img = item.business_img;
            if (item.select.includes('business') && (!item.business_img || !item.business_img.length)) {
              this.$Message.error('请上传工商注册凭证');
              hasUploadImg = false;
              this.saveLoading = false;
            }
          }

          // if ( index === 3 ) {
          //   let list = Object.keys(item.checkbox_option)
          //   list.some( key => {
          //     if ( item[`${key}_img`] && item[`${key}_img`].length ) {
          // 	    params_config[item.type][`${key}_img`] = item[`${key}_img`]
          //     }
          //     if ( item.select.includes(key)&&(!item[`${key}_img`]||!item[`${key}_img`].length) ) {
          //       this.$Message.error(`请上传${item.checkbox_option[key].title}凭证`)
          //       hasUploadImg = false
          //       this.saveLoading = false
          //       return true
          //     }
          //   } )
          // }
        });
        if (!hasUploadImg) {
          this.saveLoading = false;
          return;
        }
        params.shop_config = params_config;
      }

      this.$api
        .editRxjEnter(params)
        .then(res => {
          this.$Message.success(`${this.$route.query.id ? '编辑' : '新建'}养疗馆成功`);
          this.back();
        })
        .catch(error => this.$Message.error(error.errmsg))
        .finally(() => {
          this.saveLoading = false;
        });
    },

    /**
     * @description 获取养疗馆信息
     * @param { id } 养疗馆id
     * */
    getNursingInfo(id) {
      this.$api.getRxjEnterInfo({ id }).then(res => {
        console.log('-> %c res  === %o ', 'font-size: 15px;color: green;', res);
        this.formData = res;
        if (res.lon !== '0.000000' && res.lat !== '0.000000') {
          this.position = [Number(res.lon), Number(res.lat)];
          console.log(this.position, 'this.position');
        }
        this.isApiReady = true;
        this.prepar_progress = res.prepar_progress;
        if (res.shop_config.length) {
          this.shop_config = res.shop_config;
        }

        this.setSelectAddress(res.county_code, res.city_code, res.prov_code);
        // this.address = `${this.formData.prov_name}${this.formData.city_name}${this.formData.county_name}${this.formData.address_other}`
      });
    },

    // 审核驳回
    showRefuseModal() {
      this.refuseModalVisible = true;
    },
    // 审核驳回弹窗的确定
    submitRefuseReason() {
      this.review('FAIL', this.examine_rejected_msg);
    },
    // 审核通过
    passCheck() {
      this.$Modal.confirm({
        title: '通过审核',
        content: '您确定要通过该审核吗？',
        onOk: () => {
          this.review('SUCC');
        }
      });
    },
    /**
     * @description 审核/驳回的接口
     * @param { examine_status } 审核的状态
     * @param { examine_rejected_msg } 驳回的原因
     * */
    review(examine_status, examine_rejected_msg) {
      const params = { id: this.$route.query.id, examine_status };
      let isPass = true;
      if (examine_rejected_msg) {
        params.examine_rejected_msg = examine_rejected_msg;
        isPass = false;
      }
      this.$api.getRxjEnterExamine(params).then(
        res => {
          this.$Message.success(`${isPass ? '通过审核成功' : '驳回审核成功'}`);
          this.$router.replace('/organization/nursing/list');
        },
        err => this.$Message.error(err.errmsg)
      );
    },

    // 开业勾选
    changeOpeningStatus(val, index) {
      if (val.length === Object.keys(this.shop_config[index].checkbox_option).length) {
        this.shop_config[index].status = 'SUCC';
      } else if (val.length == 0) {
        this.shop_config[index].status = 'NOT_START';
      } else {
        this.shop_config[index].status = 'PROGRESS';
      }
    },
    changeTime(time, index) {
      this.shop_config[index].estimate_date = time;
    },
    getAddressDetail(position) {
      // console.log( '🚀 ~ file: edit.vue ~ line 480 ~ getAddress ~ address', address )
      // console.log( '🚀 ~ file: edit.vue ~ line 480 ~ getAddress ~ addressDetail', addressDetail )
      // console.log( '🚀 ~ file: edit.vue ~ line 480 ~ getAddress ~ position', position )
      // const { prov, city, county, other } = addressDetail
      // this.formData.prov_name = prov.name
      // this.formData.prov_code = prov.code
      // this.formData.city_name = city.name
      // this.formData.city_code = city.code
      // this.formData.county_name = county.name
      // this.formData.county_code = county.code
      // this.formData.address_other = other
      this.formData.lon = position[0] + '';
      this.formData.lat = position[1] + '';
      // this.address = address
      // this.setSelectAddress( this.formData.county_code, this.formData.city_code, this.formData.prov_code )
    },
    changeShowMap() {
      this.showMap = !this.showMap;
    }
  },
  filters: {}
};
</script>

<style lang="less" scoped>
::v-deep .ivu-form-item {
  margin-bottom: 20px;

  .ivu-form-item-label {
    width: 170px !important;
  }

  .ivu-form-item-content {
    margin-left: 170px !important;
  }
}

::v-deep .custom-img {
  .ivu-form-item-label {
    line-height: 1.5;
  }
}

.addWrap {
  display: flex;

  .addressInput {
    flex: 1;
  }
}

.ivu-col {
  min-width: 600px;
}

.mr10 {
  margin-right: 10px;
}

.ml10 {
  margin-left: 10px;
}

p {
  margin: 0;
}

.red-item {
  ::v-deep .ivu-form-item-label {
    color: red;
  }
}
</style>
