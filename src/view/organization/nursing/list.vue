<template>
  <div class="f_order-wrapper">
    <Form
      inline
      @submit.native.prevent
      @keyup.enter.native="onSearch"
    >
      <Row>
        <FormItem>
          <Input
              v-model="queryFormData.name"
              placeholder="养疗馆名称"
              clearable
          />
        </FormItem>
        <FormItem>
          <Input
              v-model="queryFormData.keyword"
              placeholder="联系人姓名/手机号"
              clearable
          />
        </FormItem>
        <FormItem>
          <Select
              v-model="queryFormData.open_status"
              style="width: 200px"
              clearable
              placeholder="请选择状态"
          >
            <Option
                v-for="item in openingStatus"
                :key="item.id"
                :value="item.id"
            >{{ item.desc }}</Option
            >
          </Select>
        </FormItem>

        <FormItem style="text-align: left">
          <Button class="mr10" type="primary" @click="onSearch">筛选</Button>
          <Button type="default" @click="onResetSearch">重置</Button>
        </FormItem>
      </Row>
    </Form>
    <div class="table-wrapper">
      <div class="panel-nav flex flex-item-between">
        <div>
          <a
            :class="{active: !queryFormData.open_status}"
            class="nav"
            @click.prevent.capture="onStatusChange('')"
          >
            全部
          </a>
          <a
            :class="{active: $route.query.open_status == 'EXAMINE'}"
            class="nav"
            @click.prevent.capture="onStatusChange('EXAMINE')"
          >
            审核中
            <Tag color="warning">{{
              status_total.EXAMINE && status_total["EXAMINE"].num
            }}</Tag>
          </a>
          <a
            :class="{active: $route.query.open_status == 'EXAMINE_FAIL'}"
            class="nav"
            @click.prevent.capture="onStatusChange('EXAMINE_FAIL')"
          >
            已驳回
            <Tag color="error">{{
              status_total.EXAMINE_FAIL && status_total["EXAMINE_FAIL"].num
            }}</Tag>
          </a>
          <a
            :class="{active: $route.query.open_status == 'PREPARE'}"
            class="nav"
            @click.prevent.capture="onStatusChange('PREPARE')"
          >
            开业准备中
            <Tag color="primary">{{
              status_total.PREPARE && status_total["PREPARE"].num
            }}</Tag>
          </a>
          <a
            :class="{active: $route.query.open_status == 'FINISHED'}"
            class="nav"
            @click.prevent.capture="onStatusChange('FINISHED')"
          >
            已开业
            <Tag color="success">{{
              status_total.FINISHED && status_total["FINISHED"].num
            }}</Tag>
          </a>
          <a
            :class="{active: $route.query.open_status == 'DISABLED'}"
            class="nav"
            @click.prevent.capture="onStatusChange('DISABLED')"
          >
            已禁用
            <Tag color="error">{{
              status_total.DISABLED && status_total["DISABLED"].num
            }}</Tag>
          </a>
          <a
            :class="{active: $route.query.open_status == 'OVERTIME'}"
            class="nav"
            @click.prevent.capture="onStatusChange('OVERTIME')"
          >
            超时预警
            <Tag color="warning">{{
              status_total.OVERTIME && status_total["OVERTIME"].num
            }}</Tag>
          </a>
        </div>
        <div class="creat-button">
          <!--					<Button type="primary" @click="createModalVisible = true">添加收入明细</Button>-->
          <!--					<Button type="primary" @click="createModalVisible = true" style="margin-left: 20px;">添加支出明细</Button>-->
          <!-- <Button type="primary" @click="createNursing"> 创建养疗馆 </Button> -->
        </div>
      </div>
      <Table
        :columns="tableCols"
        :data="list"
        :loading="tableLoading"
        :height="$store.state.app.clientHeight - 275"
      >
        <template slot="prepay_balance" slot-scope="{row}">
          <!--					<p>{{ row.prepay_balance ? `￥${row.prepay_balance}` : '-' }}</p>-->
          <p
            style="margin: 0"
            v-if="
              Number(row.prepay_balance.clinic) > 0 ||
              Number(row.prepay_balance.company) > 0
            "
          >
            养疗馆: ￥{{ row.prepay_balance.rxj }}<br />省公司: ￥{{
              row.prepay_balance.company
            }}
          </p>
          <p style="margin: 0" v-else>-</p>
        </template>

        <template slot="prepar_progress" slot-scope="{row}">
          <p class="tl">
            <Icon
              type="md-checkmark-circle"
              :style="{
                color: row.prepar_progress === '100' ? '#1BBE6B' : '#DEDEDE',
                marginRight: '3px',
              }"
            />
            {{ row.prepar_progress === "100" ? "筹备完成" : "筹备中" }}:
            {{ row.prepar_progress + "%" }}
          </p>
          <!--          <p class="tl" v-else>-->
          <!--            <Icon type="md-checkmark-circle" :style="{color: row.open_status === 'FINISHED'?  '#1BBE6B' :'#DEDEDE'}"-->
          <!--                  style="margin-right:3px"/>-->
          <!--            {{ row.open_status_text || '-' }}-->
          <!--          </p>-->
          <p
            class="tl"
            :style="{color: row.is_union_open == '1' ? '' : '#ccc'}"
          >
            <Icon
              type="md-checkmark-circle"
              :style="{
                color: row.is_union_open == '1' ? '#1BBE6B' : '#DEDEDE',
              }"
              style="margin-right: 3px"
            />
            银联电子钱包
          </p>
          <p
            class="tl"
            :style="{color: row.is_ledger_open == '1' ? '' : '#ccc'}"
          >
            <Icon
              type="md-checkmark-circle"
              :style="{
                color: row.is_ledger_open == '1' ? '#1BBE6B' : '#DEDEDE',
              }"
              style="margin-right: 3px"
            />
            银联分账
          </p>
        </template>
        <template slot="is_publish" slot-scope="{row}">
          <p>{{ row.open_status_text }}</p>
          <div v-if="row.open_status === 'PREPARE'">
            {{ row.shop_config_succ_desc }}
          </div>
        </template>

        <template  slot="open_date" slot-scope="{row}">
          {{ row.open_date || '-' }}
        </template>

        <template slot="overtime_num" slot-scope="{row}">
          <p>{{ Number(row.overtime_num) ? `${row.overtime_num} 项` : "-" }}</p>
        </template>

        <template slot-scope="{ row }" slot="subject_auth_status">
          <p>{{ row.yzt_into_status || '-' }}</p>
        </template>
        <template slot="create_time" slot-scope="{row}">
          <p>{{ row.create_time | date_format }}</p>
        </template>

        <template slot="action" slot-scope="{row}">
          <!-- <a
            @click="toDetail(row.id, row.open_status)"
            v-if="row.open_status == 'EXAMINE'"
            style="margin-right: 10px"
            >审核</a
          > -->
          <!-- <a
            @click="toDetail(row.id, row.open_status, true)"
            v-if="row.open_status == 'EXAMINE_FAIL'"
            style="margin-right: 10px"
            >修改</a
          > -->
          <!-- <a
            @click="toDetail(row.id, row.open_status, true)"
            v-if="row.open_status == 'PREPARE' || row.open_status == 'FINISHED'"
            style="margin-right: 10px"
            >编辑</a
          > -->
          <a @click="toDetail(row.id, row.open_status, '', true)">详情</a>
        </template>
      </Table>
      <div class="block_20"></div>
      <KPage
        :current.sync="+queryFormData.page"
        :page-size.sync="+queryFormData.pageSize"
        :total="total"
        style="text-align: center"
        @on-change="handleCurrentChange"
        @on-page-size-change="handleSizeChange"
      />
    </div>
  </div>
</template>

<script>
import S from "utils/util";
import search from "@/mixins/search";

const init_query_form_data = {
  page: 1,
  pageSize: 20,
  open_status: "",
  keyword: "",
  name: "",
  r: "",
};
export default {
  name: "list",
  mixins: [search],
  components: {},
  data() {
    return {
      apiName: "getRxjList",
      queryFormData: {...init_query_form_data},
      status_total: {},
      tableCols: [
        {title: "编号", key: "rxj_code", align: "center",width: 120},
        {title: "养疗馆名称", key: "name", align: "center",minWidth: 120},
        {title: "所属省公司", key: "company_name", align: "center",minWidth: 120},
        {title: "联系人", key: "leading_person", align: "center",minWidth: 120},
        {title: "联系电话", key: "leading_mobile", align: "center",minWidth: 120},
        {title: "首采预付款余额", slot: "prepay_balance", align: "center",minWidth: 120},
        {title: "状态", key: "open_status_text", align: "center",minWidth: 120},
        { title: '进件状态', slot: 'subject_auth_status', align: 'center', minWidth: 110 },
        {
          title: "总进度",
          slot: "prepar_progress",
          align: "center",
          width: 140,
        },
        {title: "开业状态", slot: "is_publish", align: "center", width: 120},
        { title: '开业时间', slot: 'open_date', align: 'center', width: 160 },
        {title: "超时预警", slot: "overtime_num", align: "center", width: 120},
        {title: "创建时间", slot: "create_time", align: "center", width: 160 },
        {title: "操作", slot: "action", fixed: "right", align: "center", width: 60},
      ],
      openingStatus: [],
    };
  },
  computed: {},
  watch: {},
  created() {
    this.queryFormData = S.merge(this.queryFormData, this.$route.query);
    this.submitQueryForm(true);
    this.getOptionsList();
  },
  mounted() {},
  methods: {
    onStatusChange(status) {
      this.queryFormData.page = 1;
      this.queryFormData.open_status = status;
      this.submitQueryForm();
    },
    onResetSearch() {
      this.queryFormData = {...init_query_form_data};
      this.submitQueryForm();
      this.$refs["partner-search"].clear();
    },
    // api-获取状态枚举
    getOptionsList() {
      this.$api.getRxjEnterOptions().then(
        res => {
          this.openingStatus = S.descToArrHandle(res.openStatusDesc);
        },
        err => this.$Message.error(err.errmsg),
      );
    },

    /**
     * @description 创建/编辑养辽馆
     * */
    createNursing() {
      this.$router.push({
        path: "/organization/nursing/edit",
        query: {},
      });
    },

    /**
     * @description 详情/审核
     * @param { isEdit } 为true表示只读状态，审核时也是只读
     * @param { id } 养辽馆id
     * */
    toDetail(id, open_status = "", isEdit, checkDetail) {
      let query = {
        id,
        open_status,
      };
      isEdit && (query.isEdit = isEdit);

      checkDetail && (query.checkDetail = checkDetail);
      this.$router.push({
        path: "/organization/nursing/edit",
        query,
      });
    },
  },
  beforeRouteUpdate: function (to, from, next) {
    this.queryFormData = S.merge(init_query_form_data, to.query);
    this.loadList();
    next();
  },
};
</script>

<style lang="less" scoped>
p {
  margin: 0;
}

.tl {
  text-align: left;
  line-height: 18px;
}
</style>
