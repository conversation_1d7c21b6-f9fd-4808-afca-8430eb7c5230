<template>
  <Modal :value="value" title="操作记录弹窗" width="900" :mask-closable="false" @on-visible-change="changeVisible">
    <div class="container">
      <Table :columns="tableCols" :data="list" :loading="tableLoading" :height="300">
        <template v-slot:create_time="{ row }">
          {{ row.create_time | date_format }}
        </template>
        <template v-slot:before="{ row }">
          <div v-if="row.before?.type === 'files'">
            <Picture v-if="row.before?.value?.length > 0" :value="row.before?.value" :isQueryDetail="true" />
            <div v-else>-</div>
          </div>
          <div v-else>{{ row.before?.value || '-' }}</div>
        </template>
        <template v-slot:after="{ row }">
          <Picture v-if="row.after?.type === 'files'" :value="row.after?.value" :isQueryDetail="true" />
          <div v-else>{{ row.after?.value || '-' }}</div>
        </template>
      </Table>
      <div class="block_20"></div>
      <KPage
        :current="queryFormData.page"
        :page-size="queryFormData.pageSize"
        :total="total"
        style="text-align: center"
        @on-change="handleCurrentChange"
        @on-page-size-change="handleSizeChange"
      />
    </div>
    <div slot="footer">
      <Button type="primary" style="margin-left: 20px" @click="cancel">确定</Button>
    </div>
  </Modal>
</template>

<script>
import Picture from '@/components/upload/picture';
const init_form_data = {
  page: 1,
  pageSize: 10,
  goods_id: '',
  company_id: ''
};
export default {
  name: 'changeRecordModal',
  components: { Picture },
  props: {
    value: {
      type: Boolean,
      default: false
    },

    id: {
      type: String,
      default: ''
    }
  },
  mixins: [],
  data() {
    return {
      queryFormData: { ...init_form_data },
      goods_list: [],
      company_list: [],
      searchLoading: false,
      tableCols: [
        { title: '操作时间', slot: 'create_time', align: 'center', width: 140 },
        { title: '操作主体', key: 'source', align: 'center', width: 70 },
        { title: '操作人', key: 'operator_name', align: 'center', width: 100 },
        { title: '变更字段', key: 'field_name', align: 'center' },
        { title: '更改前', slot: 'before', align: 'center', width: 200 },
        { title: '更改后', slot: 'after', align: 'center', width: 200 }
      ],
      list: [],
      tableLoading: false,
      total: 0
    };
  },
  computed: {},
  watch: {},
  created() {},
  mounted() {},
  methods: {
    cancel() {
      this.formData = {
        ...init_form_data
      };
      this.$emit('input', false);
    },
    changeVisible(flag) {
      if (!flag) {
        this.queryFormData = { ...init_form_data };
        this.$emit('input', false);
      } else {
        this.getOutsideCompanyFieldLog();
      }
    },
    handleCurrentChange(val) {
      this.queryFormData.page = val;
      this.getOutsideCompanyFieldLog();
    },
    handleSizeChange(val) {
      this.queryFormData.page = 1;
      this.queryFormData.pageSize = val;
      this.getOutsideCompanyFieldLog();
    },
    getOutsideCompanyFieldLog() {
      let params = {
        id: this.id,
        page: this.queryFormData.page,
        pageSize: this.queryFormData.pageSize
      };
      console.log('=>(detail.vue:241) params', params);
      this.$api
        .getOutsideCompanyFieldLog(params)
        .then(res => {
          console.log('=>(detail.vue:242) res', res);
          this.list = res.list;
          this.total = +res.total;
        })
        .catch(err => this.$Message.error(err.errmsg));
    }
  }
};
</script>

<style lang="less" scoped>
ul,
li {
  list-style: inside;
}
.edit_row {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 13px;
  .edit_label {
    width: 52px;
    text-align: left;
    margin-right: 10px;
  }
  .edit_value {
    flex: 1;
  }
}
</style>
