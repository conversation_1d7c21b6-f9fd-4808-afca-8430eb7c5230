<template>
  <div class="edit-wrapper">
    <div class="block-header flex flex-item-between">
      <div>基本信息</div>
      <a @click="recordVisible = true" v-if="$route.query.status !== 'create'">操作记录</a>
    </div>
    <Row>
      <Col span="10">
        <Form
          ref="formValidate"
          :model="formValidate"
          :rules="ruleValidate"
          :label-width="140"
          :label-colon="true"
          :disabled="isOnlyRead"
        >
          <FormItem label="状态" v-if="formValidate.status">
            <div class="item-lineHeight flex">
              <p class="contents">{{ formValidate.status_desc }}</p>
              <p class="contents" v-show="formValidate.status === 'DISABLED'" style="margin-left: 10px; color: red">
                (禁用原因：{{ formValidate.disabled_reason }})
              </p>
            </div>
          </FormItem>

          <FormItem label="外部公司名称" prop="name">
            <Input v-model="formValidate.name" :maxlength="30" placeholder="输入外部公司名称" show-word-limit></Input>
          </FormItem>

          <FormItem label="营业执照名称" prop="organization_name">
            <Input v-model="formValidate.organization_name" :placeholder="isOnlyRead ? '' : '输入营业执照名称'"></Input>
          </FormItem>

          <FormItem label="统一社会信用代码" prop="organization_code">
            <Input
              v-model="formValidate.organization_code"
              :placeholder="isOnlyRead ? '' : '请输入18位统一社会信用代码'"
              :disabled="isDisabledCredit"
              maxlength="18"
              show-word-limit
            ></Input>
          </FormItem>

          <FormItem label="联系人">
            <Input v-model="formValidate.leading_person" :placeholder="isOnlyRead ? '' : '输入联系人姓名'"></Input>
          </FormItem>

          <FormItem label="联系电话">
            <Input
              :maxlength="11"
              type="number"
              v-model="formValidate.leading_mobile"
              :placeholder="isOnlyRead ? '' : '输入联系人手机号'"
            ></Input>
          </FormItem>

          <FormItem label="公司地址">
            <div class="addWrap">
              <div class="addressBox" style="width: 55%">
                <el-cascader
                  v-model="selectedAddress"
                  :options="options"
                  :disabled="isOnlyRead"
                  clearable
                  :placeholder="isOnlyRead ? '' : '请选择外部公司地址'"
                  size="small"
                  popper-class="address-com"
                  style="width: 100%"
                  @change="regionChange"
                >
                </el-cascader>
              </div>

              <div class="addressInput ml10">
                <Input v-model="formValidate.address_other" :placeholder="isOnlyRead ? '' : '详细地址'"></Input>
              </div>
            </div>
          </FormItem>

          <FormItem label="营业执照">
            <div v-if="isOnlyRead && formValidate.organization_images.length === 0">-</div>
            <Picture v-else v-model="formValidate.organization_images" :limit="9" :isQueryDetail="isOnlyRead" />
          </FormItem>
        </Form>
      </Col>
    </Row>

    <div class="block_45"></div>

    <div class="fixed-bottom-wrapper">
      <back-button></back-button>
      <Button
        v-if="$route.query.status !== 'onlyRead'"
        type="primary"
        @click="handleSubmit"
        style="margin-left: 10px"
        :loading="saveLoading"
        >保存</Button
      >
    </div>
    <record-modal v-model="recordVisible" :id="$route.query.id"></record-modal>
  </div>
</template>

<script>
import S from 'utils/util';
import Picture from '@/components/upload/picture';
import { CodeToText, regionData } from '@/utils/chinaMap';
import recordModal from './components/recordModal';
export default {
  name: 'edit',
  components: {
    Picture,
    recordModal
  },
  mixins: [],

  props: {},
  data() {
    return {
      typeDesc: '', // 类型list
      options: regionData, // 地区枚举值
      selectedAddress: [], // 地区选择
      saveLoading: false, // 保存loading

      formValidate: {
        name: '', // 合伙人名称
        organization_name: '', // 营业执照名称
        organization_code: '', // 统一社会信用代码
        leading_person: '', // 联系人
        leading_mobile: '', // 联系电话
        organization_images: [], // 营业执照

        prov_name: '', //省
        prov_code: '', //省编码
        city_name: '', //市
        city_code: '', //市编码
        county_name: '', //区
        county_code: '', //区编码
        address_other: '' //详细地址
      },
      ruleValidate: {
        name: [{ required: true, message: '输入外部公司名称', trigger: 'blur' }],
        organization_name: [{ required: true, message: '输入营业执照名称', trigger: 'blur' }],
        organization_code: [
          { required: true, message: '输入18位统一社会信用代码', trigger: 'blur' },
          { type: 'string', pattern: /^[^oO]*$/, message: '统一社会信用代码不能包含字母o', trigger: 'blur' }
        ]
      },
      isDisabledCredit: false,
      recordVisible: false // 操作记录弹窗
    };
  },
  computed: {
    isOnlyRead() {
      return this.$route.query.status === 'onlyRead';
    }
  },
  watch: {},
  created() {},
  mounted() {
    let id = this.$route.query.id;
    if (id) {
      this.getOutsideCompanyDetail(id);
    }
  },
  methods: {
    //地区选择
    regionChange(address) {
      if (address.length) {
        console.log(CodeToText[address[0]]);
        const province = {
          value: CodeToText[address[0]],
          key: address[0]
        };
        const city = {
          value: CodeToText[address[1]],
          key: address[1]
        };
        const area = {
          value: CodeToText[address[2]],
          key: address[2]
        };
        console.log(province, city, area);
        this.formValidate.prov_name = province.value;
        this.formValidate.prov_code = province.key;
        this.formValidate.city_name = city.value;
        this.formValidate.city_code = city.key;
        (this.formValidate.county_name = area.value), (this.formValidate.county_code = area.key);
      } else {
        this.formValidate.prov_name = '';
        this.formValidate.prov_code = '';
        this.formValidate.city_name = '';
        this.formValidate.city_code = '';
        this.formValidate.county_name = '';
        this.formValidate.county_code = '';
      }
    },
    setSelectAddress(county_code = '', city_code = '', prov_code = '') {
      if (county_code) {
        this.selectedAddress = [prov_code, city_code, county_code];
        return;
      }
      if (city_code) {
        this.selectedAddress = [prov_code, city_code];
        return;
      }
      if (prov_code) {
        this.selectedAddress = [prov_code];
      }
    },

    handleSubmit() {
      this.$refs['formValidate'].validate(valid => {
        if (valid) {
          if (this.formValidate.leading_mobile) {
            const reg = /^1[3456789]\d{9}$/;
            if (!reg.test(this.formValidate.leading_mobile)) {
              this.$Message.error('请输入正确的手机号码');
              return;
            }
          }
          if (this.formValidate.organization_code) {
            if (this.formValidate.organization_code.length < 18) {
              this.$Message.error('请输入18位统一社会信用代码');
              return;
            }
          }
          this.editOutsideCompany();
        }
      });
    },

    // 创建/编辑城市合伙人
    editOutsideCompany() {
      this.saveLoading = true;
      let params = {
        ...this.formValidate,
        id: this.$route.query.id
      };
      this.$api
        .editOutsideCompany(params)
        .then(res => {
          this.$Message.success('保存成功');
          this.$router.back();
        })
        .catch(error => this.$Message.error(error.errmsg))
        .finally(() => {
          this.saveLoading = false;
        });
    },

    // api-获取合伙人信息
    getOutsideCompanyDetail(id) {
      this.$api.getOutsideCompanyDetail({ id }).then(res => {
        this.formValidate = {
          ...res.detail
        };

        if (res.detail.organization_code) {
          this.isDisabledCredit = true;
        }
        const { prov_code, city_code, county_code } = res.detail;
        this.setSelectAddress(county_code, city_code, prov_code);
      });
    }
  },
  filters: {}
};
</script>
<style lang="less" scoped>
.line {
  margin: 20px 0;
  margin-left: 10px;
  margin-right: -60px;
  height: 0.5px;
  background: #e8eaec;
}

.ivu-form-item {
  margin-bottom: 20px;
}

.addWrap {
  display: flex;

  .addressInput {
    flex: 1;
  }
}
.ivu-col {
  min-width: 600px;
}
.contents {
  display: contents;
}

::v-deep .el-cascader {
  .el-cascader-panel {
    font-size: 12px;
  }

  .el-input {
    .el-input__inner {
      border: 1px solid #bcc3d7;
      border-radius: 2px;
      padding-left: 7px;
      font-size: 12px;
    }
  }
}

.error-input {
  ::v-deep .ivu-input {
    border-color: #f56c6c;
    color: #f56c6c;
  }
}
</style>
