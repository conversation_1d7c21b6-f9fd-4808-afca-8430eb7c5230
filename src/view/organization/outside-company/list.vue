<template>
  <div class="f_order-wrapper">
    <Form inline @submit.native.prevent @keyup.enter.native="onSearch">
      <Row>
        <FormItem>
          <Input v-model="queryFormData.name" placeholder="请输入外部公司名称或编号" clearable />
        </FormItem>
        <FormItem>
          <Input v-model="queryFormData.organization_name" placeholder="请输入营业执照名称" clearable />
        </FormItem>
        <FormItem>
          <Input v-model="queryFormData.leading_person" placeholder="请输入联系人姓名/手机号" clearable />
        </FormItem>
        <FormItem>
          <Select v-model="queryFormData.status" clearable placeholder="请选择状态">
            <Option v-for="item in optionList" :key="item.id" :value="item.id">{{ item.desc }}</Option>
          </Select>
        </FormItem>
        <FormItem>
          <DatePicker
            v-model="timeRange"
            :options="disabledTime"
            class="time-range"
            clearable
            format="yyyy-MM-dd"
            placeholder="请选择创建时间"
            type="daterange"
            @on-change="times => handleTimeChange(times)"
          ></DatePicker>
        </FormItem>
      </Row>
      <Row>
        <FormItem style="text-align: left">
          <Button class="mr10" type="primary" @click="onSearch">筛选</Button>
          <Button class="mr10" type="default" @click="onResetSearch">重置</Button>
          <Button class="mr10" type="default" @click="exportExcel">导出</Button>
          <Button type="primary" @click="jumpToOutsideCompanyEdit('', 'create')">新增外部公司</Button>
        </FormItem>
      </Row>
    </Form>
    <div class="table-wrapper">
      <Table :loading="tableLoading" :columns="tableCols" :data="list" :height="$store.state.app.clientHeight - 300">
        <template slot-scope="{ row }" slot="organization_name">
          <div>{{ row.organization_name || '-' }}</div>
        </template>

        <template slot-scope="{ row }" slot="organization_code">
          <div>{{ row.organization_code || '-' }}</div>
        </template>

        <template slot-scope="{ row }" slot="leading_person">
          <div>{{ row.leading_person || '-' }}</div>
        </template>
        <template slot-scope="{ row }" slot="leading_mobile">
          <div>{{ row.leading_mobile || '-' }}</div>
        </template>

        <template slot="balance" slot-scope="{ row }">
          <k-link
            v-if="isHasBalanceDetailPage && row.prepay_balance?.id != 0"
            :to="{ path: '/finance/balance/detail', query: { id: row.prepay_balance?.id } }"
            target="_blank"
            >{{ row.prepay_balance.balance || 0 }}
          </k-link>
          <div v-else>{{ row.prepay_balance.balance || 0 }}</div>
        </template>

        <template slot-scope="{ row }" slot="create_time">
          <p>{{ row.create_time | date_format }}</p>
        </template>

        <template slot-scope="{ row, index }" slot="action">
          <a @click="jumpToOutsideCompanyEdit(row.id)" class="mr10">编辑</a>
          <div style="display: inline-block" class="mr10">
            <Poptip
              v-if="row.status === 'ENABLED'"
              placement="top-end"
              :ref="`${row.id}pop`"
              confirm
              @on-ok="changeOutsideCompanyStatus(row, 'DISABLED')"
              @on-popper-hide="resetPopper(row, index)"
            >
              <div slot="title">
                <div>确认禁用?</div>
                <div class="flex">
                  <span style="width: 70px"><span style="color: red">*</span> 禁用原因:</span>
                  <Input
                    class="flex-1"
                    :class="{ validError: validDisabledReason }"
                    v-model="forbiddenReason"
                    type="textarea"
                    :autosize="{ minRows: 3, maxRows: 3 }"
                  ></Input>
                </div>
              </div>
              <a>禁用</a>
            </Poptip>
            <Poptip
              v-else
              confirm
              title="确认要启用?"
              @on-ok="changeOutsideCompanyStatus(row, 'ENABLED')"
              placement="top-end"
            >
              <a>启用</a>
            </Poptip>
          </div>
          <a @click="jumpToOutsideCompanyEdit(row.id, 'onlyRead')" class="mr10">详情</a>
        </template>
      </Table>
      <div class="block_20"></div>
      <KPage
        :total="total"
        :page-size="+queryFormData.pageSize"
        :current="+queryFormData.page"
        @on-change="handleCurrentChange"
        @on-page-size-change="handleSizeChange"
        style="text-align: center"
      />
    </div>
  </div>
</template>

<script>
import S from 'utils/util';
import search from '@/mixins/search';
const init_query_form_data = {
  page: 1,
  pageSize: 20,
  name: '', // 合伙人名称
  organization_name: '', // 营业执照名称
  leading_person: '', // 联系人姓名/手机号
  status: '', // 状态
  st: '',
  et: ''
};
export default {
  name: 'list',
  components: {},
  mixins: [search],
  props: {},
  data() {
    return {
      apiName: 'getOutsideCompanyList',
      queryFormData: { ...init_query_form_data },
      timeRange: [],
      status_total: {},
      disabledTime: {
        disabledDate(date) {
          return date && date.valueOf() > Date.now();
        }
      },

      tableCols: [
        { title: '编号', key: 'id', align: 'center' },
        { title: '外部公司', key: 'name', align: 'center' },
        { title: '营业执照名称', slot: 'organization_name', align: 'center' },
        { title: '统一社会信用代码', slot: 'organization_code', align: 'center', width: 180 },
        { title: '联系人', slot: 'leading_person', align: 'center' },
        { title: '联系电话', slot: 'leading_mobile', align: 'center', width: 120 },
        { title: '预付款可用余额', slot: 'balance', align: 'center', width: 100 },
        { title: '状态', key: 'status_desc', align: 'center' },
        { title: '创建时间', slot: 'create_time', align: 'center', width: 140 },
        { title: '操作', slot: 'action', align: 'center', width: 120 }
      ],
      optionList: [],
      company_list: [],
      forbiddenReason: '',
      validDisabledReason: false,
      isHasBalanceDetailPage: S.rootPageCheck('/finance/balance/detail')
    };
  },
  computed: {},
  watch: {},
  created() {
    this.queryFormData = S.merge(this.queryFormData, this.$route.query);
    this.submitQueryForm(true);
    this.getOptionsList();
  },
  mounted() {},
  methods: {
    // 创建合伙人/编辑合伙人
    jumpToOutsideCompanyEdit(id = '', status) {
      this.$router.push({
        path: '/organization/outside-company/edit',
        query: { id, status }
      });
    },
    onResetSearch() {
      this.queryFormData = { ...init_query_form_data };
      this.timeRange = [];
      this.submitQueryForm();
    },
    getOptionsList() {
      this.$api.getOutsideCompanyOptions().then(res => {
        this.optionList = S.descToArrHandle(res.statusDesc);
      });
    },
    changeOutsideCompanyStatus(row, status) {
      if (status === 'DISABLED' && !this.forbiddenReason) {
        this.$Message.error('请输入禁用原因');
        this.validDisabledReason = true;
        this.$refs[`${row.id}pop`].visible = true;
        return;
      }
      let params = { id: row.id, disabled_reason: this.forbiddenReason, status };
      this.$api
        .changeOutsideCompanyStatus(params)
        .then(res => {
          if (res.error_msg) {
            this.$Modal.error({
              title: '无法启用',
              content: `<p>${res.error_msg}</p>`
            });
            return;
          }
          this.$Message.success(`${status === 'DISABLED' ? '禁用' : '启用'}成功`);
          this.loadList();
        })
        .catch();
    },
    resetPopper() {
      console.log('=>(list.vue:402) 触发关闭 ');
      this.forbiddenReason = '';
      this.validDisabledReason = false;
    },
    exportExcel() {
      this.exportLoading = true;
      let params = {
        ...this.queryFormData
      };
      this.$api.exportOutsideCompanyList(params).then(
        res => {
          this.action = res.url;
          this.download(res.url);
          this.exportLoading = false;
        },
        err => {
          this.$Message.error(err.errmsg);
          this.exportLoading = false;
        }
      );
    },
    download(url) {
      const downloadLink = document.createElement('a');
      downloadLink.setAttribute('href', url);
      downloadLink.setAttribute('target', '_blank');
      downloadLink.setAttribute('style', 'display:none');
      document.body.appendChild(downloadLink);
      downloadLink.click();
      document.body.removeChild(downloadLink);
    }
  },
  beforeRouteUpdate(to, from, next) {
    this.queryFormData = S.merge(init_query_form_data, to.query);
    this.getTimeRange();
    this.loadList();
    next();
  },
  filters: {}
};
</script>
<style lang="less" scoped>
.mr10 {
  margin-right: 10px;
}
.block-width {
  display: inline-block;
  width: 26px;
  min-width: 26px;
}
.link {
  color: #155bd4;
  cursor: pointer;
}
p {
  margin: 0;
}

.validError {
  ::v-deep textarea {
    border-color: red;
  }
}
</style>
