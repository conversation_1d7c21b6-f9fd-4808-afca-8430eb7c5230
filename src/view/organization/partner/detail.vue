<template>
  <div class="wrapper">
    <div class="edit-content">
      <KWidget :labelWidth="150" label="城市合伙人名称:">
        <div class="item-lineHeight">{{ partnerInfo.name || '-' }}</div>
      </KWidget>

      <KWidget :labelWidth="150" label="类型:">
        <div class="item-lineHeight">{{ partnerInfo.type_text || '-' }}</div>
      </KWidget>

      <KWidget :labelWidth="150" label="状态:">
        <div class="item-lineHeight flex">
          <p>{{ partnerInfo.status_text || '-' }}</p>
          <p v-show="partnerInfo.status === 'REJECT'" style="margin-left: 10px; color: red">
            (驳回原因：{{ partnerInfo.op_info && partnerInfo.op_info.reject_reason }})
          </p>
        </div>
      </KWidget>

      <KWidget :labelWidth="150" label="公司名称:">
        <div class="item-lineHeight">{{ partnerInfo.company_name || '-' }}</div>
      </KWidget>

      <KWidget :labelWidth="150" label="统一社会信用代码:">
        <div class="item-lineHeight">{{ partnerInfo.usc_code || '-' }}</div>
      </KWidget>

      <KWidget :labelWidth="150" label="营业执照:">
        <div class="item-lineHeight">
          <viewer :images="partnerInfo.licence" class="" v-if="partnerInfo.licence.length">
            <img
              class="image cursor"
              v-for="(licence_item, licence_index) in partnerInfo.licence"
              :key="licence_index + 'licence'"
              :src="licence_item | imageStyle('B.w300')"
            />
          </viewer>
          <p v-else>-</p>
        </div>
      </KWidget>

      <KWidget :labelWidth="150" label="初始入驻缴纳金额:">
        <div class="item-lineHeight">{{ partnerInfo.money || '-' }} <span v-if="partnerInfo.money">元</span></div>
      </KWidget>

      <KWidget :labelWidth="150" label="支付凭证:">
        <div class="item-lineHeight">
          <viewer :images="partnerInfo.voucher_imgs" class="" v-if="partnerInfo.voucher_imgs.length">
            <img
              class="image cursor"
              v-for="(voucher_item, voucher_index) in partnerInfo.voucher_imgs"
              :key="voucher_index + 'voucher'"
              :src="voucher_item | imageStyle('B.w300')"
            />
          </viewer>
          <p v-else>-</p>
        </div>
      </KWidget>

      <KWidget :labelWidth="150" label="备注:">
        <div class="item-lineHeight">{{ partnerInfo.remark || '-' }}</div>
      </KWidget>

      <p class="line"></p>

      <KWidget :labelWidth="150" label="联系人:">
        <div class="item-lineHeight">{{ partnerInfo.leading_person || '-' }}</div>
      </KWidget>

      <KWidget :labelWidth="150" label="联系电话:">
        <div class="item-lineHeight">{{ partnerInfo.leading_mobile || '-' }}</div>
      </KWidget>

      <KWidget :labelWidth="150" label="联系地址:">
        <div class="item-lineHeight">{{ partnerInfo.address || '-' }}</div>
      </KWidget>
    </div>
    <div class="block_45"></div>
    <div class="fixed-bottom-wrapper">
      <Button @click="back" class="mr10">返回</Button>
      <Button
        type="error"
        @click="isShowModel()"
        class="mr10"
        v-if="partnerInfo.status === 'WAIT_AUDIT' && $route.query.status === 'WAIT_AUDIT'"
        >审核驳回</Button
      >
      <Button
        type="primary"
        @click="passCheck()"
        v-if="partnerInfo.status === 'WAIT_AUDIT' && $route.query.status === 'WAIT_AUDIT'"
        >审核通过</Button
      >
    </div>

    <Modal v-model="refuseModalVisible" :mask-closable="false" title="驳回审核">
      <div style="width: 100%">
        <p class="label">请输入驳回原因：</p>
        <Input
          v-model="reject_reason"
          :autosize="{ minRows: 3, maxRows: 6 }"
          style="max-width: 1000px"
          type="textarea"
        />
      </div>
      <div slot="footer">
        <Button type="default" @click="modalCancel()">取消</Button>
        <Button type="primary" @click="submitRefuseReason" :loading="confirmLoading">确定</Button>
      </div>
    </Modal>
  </div>
</template>

<script>
export default {
  name: 'edit',
  components: {},
  mixins: [],
  props: {},
  data() {
    return {
      refuseModalVisible: false, // 审核弹窗flag
      reject_reason: '', //审核不通过说明

      confirmLoading: false,

      partnerInfo: {
        name: '', // 姓名
        type_text: '', // 类型
        company_name: '', // 公司名称
        usc_code: '', // 统一社会信用代码
        licence: [], // 营业执照
        money: '', // 初始入驻缴纳金额
        voucher_imgs: [], // 支付凭证
        remark: '', // 备注

        leading_person: '', // 联系人
        leading_mobile: '', // 联系电话
        address: '', // 联系地址

        status: '', // 审核状态
        op_info: {} // 审核人员信息
      }
    };
  },
  computed: {},
  watch: {},
  created() {},
  mounted() {
    let id = this.$route.query.id || '';
    if (id) {
      this.getPartnerInfo(id);
    }
  },
  methods: {
    // 审核弹窗逻辑
    submitRefuseReason() {
      this.review('REJECT', this.reject_reason);
    },
    // 通过审核
    passCheck() {
      this.$Modal.confirm({
        title: '通过审核',
        content: '您确定要通过该审核吗？',
        loading: true,
        onOk: () => {
          this.review('PASS');
        }
      });
    },
    isShowModel(flag = true) {
      this.refuseModalVisible = flag;
    },
    modalCancel() {
      this.isShowModel(false);
    },
    review(act, reject_reason) {
      if (act === 'PASS') {
      } else {
        if (!reject_reason.length) {
          this.$Message.error(`驳回原因不能为空`);
          return;
        }
      }
      const params = { id: this.$route.query.id, act };
      let isPass = true;
      if (reject_reason) {
        params.reject_reason = reject_reason;
        isPass = false;
      }
      this.$api.getPartnerStatus(params).then(
        res => {
          this.$Message.success(`${isPass ? '城市合伙人信息已审核通过' : '城市合伙人信息已驳回'}`);
          this.$router.push('/organization/partner/list');
          this.modalCancel();
        },
        err => this.$Message.error(err.errmsg)
      );
    },

    // 返回
    back() {
      this.$router.push('/organization/partner/list');
    },

    // api-获取合伙人信息
    getPartnerInfo(id) {
      this.$api.getPartnerInfo({ id }).then(res => {
        this.partnerInfo = {
          ...res.partner,
          licence: res.partner.licence,
          voucher_imgs: res.partner.voucher_imgs,
          ...{
            address: `${res.partner.prov_name}${res.partner.city_name}${res.partner.county_name}${res.partner.address_other}`
          }
        };
      });
    }
  },
  filters: {}
};
</script>
<style lang="less" scoped>
.item-lineHeight {
  line-height: 30px;
  font-size: 14px;
}
.line {
  margin: 10px 40px;
  // width: 50%;
  height: 0.5px;
  background: #e8eaec;
}
::v-deep .widget-form-label {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}
.widget-form-group {
  margin-top: 6px;
}
.cursor {
  cursor: pointer;
}
.image {
  width: 60px;
  height: auto;
  margin: 10px 10px;
}
.mr10 {
  margin-right: 10px;
}
p {
  margin: 0;
}
</style>
