<template>
  <div class="edit-wrapper">
    <Row>
      <Col span="10">
        <Form ref="formValidate" :model="formValidate" :rules="ruleValidate" :label-width="130" :label-colon="true">
          <FormItem label="城市合伙人名称" prop="name">
            <Input v-model="formValidate.name" :maxlength="30" placeholder="输入合伙人名称，支持公司简称"></Input>
          </FormItem>

          <FormItem label="类型" prop="type">
            <RadioGroup v-model="formValidate.type">
              <Radio :label="item.id" v-for="( item, index ) in typeDesc" :key="index">{{ item.desc }}</Radio>
            </RadioGroup>
          </FormItem>

          <FormItem label="公司名称" prop="company_name">
            <Input v-model="formValidate.company_name" placeholder="输入公司全称，与营业执照上一致"></Input>
          </FormItem>
          
          <FormItem label="统一社会信用代码" prop="usc_code">
            <Input v-model="formValidate.usc_code" placeholder="输入营业执照上的统一社会信用代码"></Input>
          </FormItem>

          <FormItem label="营业执照" prop="licence">
            <Picture v-model="formValidate.licence" :limit="4"/>
          </FormItem>

          <FormItem label="初始入驻缴纳金额" prop="money">
            <!-- <Input v-model="formValidate.money" type="number" placeholder="请输入初始入驻缴纳金额">
            <span slot="append">元</span></Input> -->
            <InputNumber v-model="formValidate.money" :active-change="false" :min="0" :precision="2"
                  placeholder="请输入初始入驻缴纳金额" style="width: 100%;"></InputNumber>
          </FormItem>

          <FormItem label="支付凭证" prop="voucher_imgs">
            <Picture v-model="formValidate.voucher_imgs" :limit="4"/>
          </FormItem>

          <FormItem label="备注">
            <Input type='textarea' v-model="formValidate.remark" :autosize="{minRows: 2,maxRows: 5}" placeholder="请输入备注"></Input>
          </FormItem>

          <p class="line"></p>

           <FormItem label="联系人" prop="leading_person">
            <Input v-model="formValidate.leading_person" placeholder="输入联系人姓名"></Input>
          </FormItem>

           <FormItem label="联系电话" prop="leading_mobile">
            <Input :maxlength="11" type="number" v-model="formValidate.leading_mobile" placeholder="输入联系人手机号"></Input>
          </FormItem>

          <FormItem label="联系地址">
            <div class="addWrap">
								<div class="addressBox" style="width: 55%;">
									<el-cascader
											v-model="selectedAddress"
											:options="options"
											clearable
											:disabled="$route.query.open_status==='EXAMINE'"
											placeholder="请选择联系地址"
											size="small"
                      popper-class="address-com"
											style="width: 100%;"
											@change="regionChange">
									</el-cascader>
								</div>
								
								<div class="addressInput ml10">
									<Input v-model="formValidate.address_other" placeholder="详细地址"></Input>
								</div>
							</div>
          </FormItem>

        </Form>
      </Col>
    </Row>

    <div class="block_45"></div>

    <div class="fixed-bottom-wrapper">
			<Button style="margin-right: 20px;" @click="back">返回</Button>
			<Button type="primary" @click="handleSubmit" :loading="saveLoading">保存</Button>
		</div>
  </div>
</template>

<script>
import S from 'utils/util';
import Picture from '@/components/upload/picture';
import {getUser} from 'utils/runtime';
import {CodeToText, regionData} from '@/utils/chinaMap';
  export default {
    name: "edit",
    components: {
      Picture
    },
    mixins: [],
			
    props: {

    },
    data () {
      const validateMobile = (rule, value, callback) => {
        if (value === '') {
          callback(new Error('请输入正确的手机号码'))
        } else {
          const reg = /^1[3456789]\d{9}$/
          if (!reg.test(value)) {
            callback(new Error('请输入正确的手机号码'))
          }
          callback()
        }
      }
      const fee_validator = (rule, value, callback) => {
        if (value === '' || value === null) {
          callback(new Error('请输入初始入驻缴纳金额'))
        } else {
          if (value<=0) {
            callback(new Error('初始入驻缴纳金额不能小于等于0'))
          }
          callback()
        }
      }
      
      return {
        typeDesc: '', // 类型list
        options: regionData, // 地区枚举值
        selectedAddress: [], // 地区选择
        saveLoading: false, // 保存loading

        formValidate: {
          name: '', // 合伙人名称
          type: '', // 类型
          company_name: '', // 公司名称
          usc_code: '', // 统一社会信用代码
          licence: [], // 营业执照
          money: null, // 初始入驻缴纳金额
          voucher_imgs: [], // 支付凭证
          remark: '', // 备注
          leading_person: '', // 联系人
          leading_mobile: '', // 联系电话

          prov_name: '',//省
          prov_code: '',//省编码
          city_name: '',//市
          city_code: '',//市编码
          county_name: '',//区
          county_code: '',//区编码
          address_other: '',//详细地址
        },
        ruleValidate: {
          name: [
            { required: true, message: '城市合伙人名称', trigger: 'blur' },
          ],
          type: [
            { required: true, message: '请选择类型', trigger: 'change' },
          ],
           company_name: [
            { required: true, message: '请输入公司名称', trigger: 'change' },
          ],
          usc_code: [
            { required: true, message: '请输入统一社会信用代码', trigger: 'change' },
          ],
          licence: [
            { required: true, message: '请上传营业执照', trigger: 'change', type: 'array' },
          ],
          money: [
            { required: true, validator:fee_validator, trigger: 'change'},
          ],
          voucher_imgs: [
            { required: true, message: '请上传支付凭证', trigger: 'change', type: 'array' },
          ],
          leading_person: [
            { required: true, message: '请输入联系人名称', trigger: 'change' },
          ],
          leading_mobile: [
            { required: true, message: '请输入联系人电话', trigger: 'change'},
            { required: true, validator: validateMobile }
          ],
        }
      }
    },
    computed: {

    },
    watch: {

    },
    created() {

    },
    mounted() {
      // 初始化
      this.init()
      let id = this.$route.query.id
      if (id) {
        this.getPartnerInfo(id)
      }
    },
    methods: {
      init () {
        this.filterRegionData()

        // 获取枚举数据
        this.getPartnerOptions()
      },
      // 过滤城市枚举值
      filterRegionData () {
        let {company_name,prov_code} = getUser()
        let data = []
        if(!prov_code){
          data = regionData
        }else {
          data = regionData.filter(item=>item.value === prov_code)

        }
        this.options = data
      },
      //地区选择
      regionChange(address) {
        if (address.length) {
          console.log(CodeToText[address[0]])
          const province = {
            value: CodeToText[address[0]],
            key: address[0],
          }
          const city = {
            value: CodeToText[address[1]],
            key: address[1],
          }
          const area = {
            value: CodeToText[address[2]],
            key: address[2],
          }
          console.log(province, city, area)
          this.formValidate.prov_name = province.value; this.formValidate.prov_code = province.key
          this.formValidate.city_name = city.value; this.formValidate.city_code = city.key
          this.formValidate.county_name = area.value, this.formValidate.county_code = area.key
        }else {
          this.formValidate.prov_name = ''
          this.formValidate.prov_code = ''
          this.formValidate.city_name = ''
          this.formValidate.city_code = ''
          this.formValidate.county_name = ''
          this.formValidate.county_code = ''
        }
      },
      setSelectAddress (county_code = '', city_code = '', prov_code = '') {
        if(county_code){
					this.selectedAddress = [prov_code,city_code,county_code]
					return
				}
				if(city_code){
					this.selectedAddress = [prov_code,city_code]
					return
				}
				if(prov_code){
					this.selectedAddress = [prov_code]
				}
      },

      handleSubmit () {
        this.$refs['formValidate'].validate((valid) => {
          if (valid) {
            this.getPartnerEdit()
          }
        })
      },

      back () {
        this.$router.push('/organization/partner/list')
      },

      // 创建/编辑城市合伙人
      getPartnerEdit () {
        this.saveLoading = true
        let params = {
          ...this.formValidate,
          id: this.$route.query.id
        }
        this.$api.getPartnerEdit(params).then( res => {
          this.$Message.success('保存成功')
          this.back()
        } ).catch( error => this.$Message.error(error.errmsg) )
        .finally( () => {
          this.saveLoading = false
        } )
      },

      // 获取枚举数据
      getPartnerOptions () {
        this.$api.getPartnerOptions().then(res=>{
          this.typeDesc = S.descToArrHandle(res.typeDesc)

          // 默认选中第一个类型
          this.formValidate.type = this.typeDesc[0].id
        })
      },

      // api-获取合伙人信息
      getPartnerInfo (id) {
        this.$api.getPartnerInfo({id}).then( res => {
          this.formValidate = {
            ...res.partner,
            money: Number(res.partner.money)
          }
          const { prov_code, city_code, county_code  } = res.partner
          this.setSelectAddress(county_code, city_code, prov_code)
        } )
      }
    },
    filters: {

    }
  }
</script>
<style lang="less" scoped>
.line {
  margin: 20px 0;
  margin-left: 10px;
  margin-right: -60px;
  height: 0.5px;
  background: #e8eaec;
}

.ivu-form-item {
  margin-bottom: 20px;
}

.addWrap {
	display: flex;

	.addressInput {
		flex: 1;
	}
}
.ivu-col {
  min-width: 600px;
}


</style>