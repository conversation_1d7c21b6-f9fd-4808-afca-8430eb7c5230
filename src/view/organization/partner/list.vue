<template>
  <div class="f_order-wrapper">
		<Form :label-width="82" inline @submit.native.prevent @keyup.enter.native="onSearch">
			<div class="no-wrap">
				<FormItem label="合伙人名称:">
					<Input v-model="queryFormData.name" placeholder="请输入合伙人名称" clearable/>
				</FormItem>
				<FormItem :label-width="130" label="联系人姓名/手机号:">
					<Input v-model="queryFormData.keyword" placeholder="请输入联系人姓名/手机号" clearable/>
				</FormItem>
				<FormItem label="状态:">
					<Select v-model="queryFormData.status"  clearable  placeholder="请选择状态">
						<Option v-for="item in optionList" :key="item.id" :value="item.id">{{ item.desc }}</Option>
					</Select>
				</FormItem>
				<FormItem label="创建时间:">
					<DatePicker v-model="createTime" :options="disabledTime" class="time-range" clearable
					            format="yyyy-MM-dd"
					            placeholder="请选择创建时间" type="daterange" @on-change="times => handleTimeChange(times)"
					></DatePicker>
				</FormItem>
				<FormItem style="text-align: left">
					<Button class="mr10" type="primary" @click="onSearch">筛选</Button>
					<Button type="default" @click="onResetSearch">重置</Button>
					
				</FormItem>
			</div>
		</Form>
		<div class="table-wrapper">
			<div class="panel-nav flex flex-item-between">
				<div>
          <a :class="{active: !queryFormData.status}" class="nav" @click.prevent.capture="onStatusChange('')">
						全部
					</a>
					<a :class="{active: $route.query.status == 'WAIT_AUDIT'}" class="nav" @click.prevent.capture="onStatusChange('WAIT_AUDIT')">
						审核中
            <Tag color="warning">{{ status_total.WAIT_AUDIT&&status_total['WAIT_AUDIT'].num }}</Tag>
					</a>
          <a :class="{active: $route.query.status == 'PASS'}" class="nav" @click.prevent.capture="onStatusChange('PASS')">
            使用中
          </a>
					<a :class="{active: $route.query.status == 'REJECT'}" class="nav" @click.prevent.capture="onStatusChange('REJECT')">
						已驳回
					</a>
					<!-- <a :class="{active: $route.query.status == 'DISABLED'}" class="nav" @click.prevent.capture="onStatusChange('DISABLED')">
						已禁用
					</a> -->
        </div>
        <div>
          <Button type="primary" @click="createParnter()">创建合伙人</Button>
        </div>
			</div>
			<Table :loading="tableLoading" :columns="tableCols" :data="list" :height="$store.state.app.clientHeight-325">
				<template slot-scope="{row}" slot="leading_person">
          <div>{{ row.leading_person }} / {{ row.leading_mobile }}</div>
				</template>

        <template slot="clinic_num" slot-scope="{row}">
					<span :class="{'link': row.status === 'PASS'}" @click="checkClinic(row)">{{row.clinic_num || 0}}</span>
				</template>

        <template slot-scope="{row}" slot="prepay_wallet">
					<p>{{ row.prepay_wallet && row.prepay_wallet.balance || 0}}</p>
				</template>
        
        <template slot-scope="{row}" slot="status_text">
					<p :style=statusColor(row)>{{ row.status_text }}</p>
				</template>

				<template slot-scope="{row}" slot="create_time">
					<p>{{ row.create_time | date_format }}</p>
				</template>

				<template slot-scope="{row}" slot="action">
          <a @click="createParnter(row.id)" class="mr10 block-width" v-if="row.status !== 'WAIT_AUDIT' && row.status !== 'REJECT'" >编辑</a>
          <a @click="createParnter(row.id)" class="mr10 block-width" v-if="row.status !== 'WAIT_AUDIT' && row.status === 'REJECT'">修改</a>
          <a @click="createParnter(row.id, row.status)" class="mr10 block-width" v-if="row.status == 'WAIT_AUDIT'">审核</a>
          <a @click="toDetail(row.id)">详情</a>
				</template>
			</Table>
			<div class="block_20"></div>
			<KPage :total="total"
			       :page-size.sync="+queryFormData.pageSize"
			       :current.sync="+queryFormData.page"
			       @on-change="handleCurrentChange"
			       @on-page-size-change="handleSizeChange"
			       style="text-align:center"
			/>
		</div>
	
	</div>
</template>

<script>
import S from 'utils/util'
import search from '@/mixins/search'
const init_query_form_data = {
	page: 1,
	pageSize: 20,
  name: '', // 合伙人名称
  keyword: '', // 联系人姓名/手机号
  status: '', // 状态
	st: '',
	et: ''
}
  export default {
    name: "list",
    components: {

    },
    mixins: [search],
    props: {

    },
    data () {
      return {
        apiName:'getPartnerList',
			  queryFormData: {...init_query_form_data},
        createTime: [],
        status_total: {},
        disabledTime: {
          disabledDate(date) {
            return date && date.valueOf() > Date.now()
          },
        },

        tableCols: [
          {title: 'ID', key: 'id',align: 'center'},
          {title: '城市合伙人名称', key: 'name', align: 'center'},
          {title: '联系人/手机号', slot: 'leading_person', align: 'center'},
          {title: '下属诊所', slot: 'clinic_num', align: 'center'},
          {title: '账户余额(元)', slot: 'prepay_wallet', align: 'center'},
          {title: '状态', slot: 'status_text', align: 'center'},
          {title: '创建时间', slot: 'create_time',align: 'center'},
          {title: '操作', slot: 'action', align: 'center'},
        ],
        optionList: [],
        company_list: []
      }
    },
    computed: {
      statusColor( row ) {
        return ( row ) => {
          switch ( row.status ) {
            case 'PASS':
              return {
                color: '#19be6b'
              }
            case 'WAIT_AUDIT':
              return {
                color: '#f90'
              }
            case 'REJECT':
              return {
                color: '#ed4014'
              }
          }
        }
      }
    },
    watch: {

    },
    created() {
      this.queryFormData = S.merge(this.queryFormData, this.$route.query)
      this.submitQueryForm(true)
      this.getOptionsList()
    },
    mounted() {

    },
    methods: {
      // 创建合伙人/编辑合伙人
      createParnter (id = '', status) {
        if (status === 'WAIT_AUDIT') {
          this.toDetail(id,status)
          return
        }

        this.$router.push({
          path: '/organization/partner/edit',
          query: {id}
        })
      },
      checkClinic(row){
        if ( row.status !== 'PASS' ) return
        localStorage.setItem('partner_list', JSON.stringify([row]))
        this.$router.push({
          path: '/organization/clinic/list',
          query: {
            company_id: row.id
          }
        })
      },
      toDetail(id, status = ''){
        this.$router.push({
          path: '/organization/partner/detail',
          query: {id,status}
        })
      },
      onStatusChange(status) {
        this.queryFormData.page = 1
        this.queryFormData.status = status
        this.submitQueryForm()
      },
      onResetSearch(){
        this.queryFormData = {...init_query_form_data}
        this.createTime = []
        this.submitQueryForm()
      },
      getOptionsList(){
        this.$api.getPartnerOptions().then(res=>{
          this.optionList = S.descToArrHandle(res.statusDesc)
        })
      }
    },
    beforeRouteUpdate(to, from, next) {
      this.queryFormData = S.merge(init_query_form_data, to.query)
      this.loadList()
      next()
    },
    filters: {

    }
  }
</script>
<style lang="less" scoped>
.mr10 {
  margin-right: 10px;
}
.block-width {
  display: inline-block;
  width: 26px;
  min-width: 26px;
}
.link {
  color:#155BD4;
  cursor: pointer;
}
p {
  margin: 0;
}
</style>