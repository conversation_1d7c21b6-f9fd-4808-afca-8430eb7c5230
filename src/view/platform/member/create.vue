<template>
  <div>
  <div v-if="!statemanager.createOk" style="width: 400px;">
    <div class="widget-form-group">
      <div class="widget-form-label">姓名:</div>
      <div class="widget-form-content">
        <Input type="text" v-model="formData.username"/>
      </div>
    </div>
    <div class="widget-form-group">
      <div class="widget-form-label">手机号:</div>
      <div class="widget-form-content">
        <Input type="text" v-model="formData.mobile"/>
      </div>
    </div>
    <div class="widget-form-group">
      <div class="widget-form-label">密码:</div>
      <div class="widget-form-content">
        <Input type="password" v-model="formData.password"/>
      </div>
    </div>
    <div class="widget-form-group">
      <div class="widget-form-label"></div>
      <div class="widget-form-content">
        <Checkbox v-model="tmp_auto_password">自动生成密码</Checkbox>
      </div>
    </div>
    <div class="widget-form-group">
      <div class="widget-form-label">邮箱:</div>
      <div class="widget-form-content">
        <Input type="text" v-model="formData.email"/>
      </div>
    </div>
    <div class="widget-form-group">
      <div class="widget-form-label">部门:</div>
      <div class="widget-form-content">
        <Select v-model="formData.department">
          <Option :value="0">请选择部门</Option>
          <Option v-for="item in orgnization" :value="item.id" :key="item.id">{{ item.name }}</Option>
        </Select>
      </div>
    </div>
    <div class="widget-form-group">
      <div class="widget-form-label"></div>
      <div class="widget-form-content">
        <Button type="primary" @click="onSave" :loading="statemanager.saveBtnLoading">保存</Button>
      </div>
    </div>
  </div>

  <div v-if="statemanager.createOk" style="width: 400px;">
    <div class="widget-form-group">
      <div class="widget-form-label">姓名:</div>
      <div class="widget-form-content">
        {{formData.username}}
      </div>
    </div>
    <div class="widget-form-group">
      <div class="widget-form-label">手机号:</div>
      <div class="widget-form-content">
        {{formData.mobile}}
      </div>
    </div>
    <div class="widget-form-group">
      <div class="widget-form-label">密码:</div>
      <div class="widget-form-content">
        {{formData.password}}
      </div>
    </div>
    <div class="widget-form-group">
      <div class="widget-form-label">邮箱:</div>
      <div class="widget-form-content">
        {{formData.email}}
      </div>
    </div>
    <div class="widget-form-group">
      <div class="widget-form-label">部门:</div>
      <div class="widget-form-content">
        {{formData.department}}
      </div>
    </div>

  </div>
  </div>
</template>

<script>
/* eslint-disable */
import S from 'utils/util' // Some commonly used tools
import io from "utils/request" // Http request
import * as runtime from 'utils/runtime' // Runtime information
/* eslint-disable */

export default {
  name: "create",

  data() {
    return {
      orgnization: [],

      formData: {
        username: '',
        mobile: '',
        password: '',
        auto_password: 0,
        email: '',
        department: '',
      },

      tmp_auto_password: false,

      statemanager: {
        saveBtnLoading: false,
        createOk: false
      },
    }
  },

  created() {
    io.get('pms_opc/permission.member.orgnization').then(data => {
      this.orgnization = data
    })

  },

  methods: {
    onSave: function () {
      let formData = {...this.formData}

      if (!formData.username.trim()) {
        this.$Message.error('请填写姓名')
        return
      }

      this.statemanager.saveBtnLoading = true

      io.post('pms_opc/permission.member.create', formData).then((data) => {
        this.formData.password = data.password
        this.formData.department = data.department

        this.statemanager.createOk = true
        this.$Message.success('创建成功')
      }).catch(error => {
          this.$Message.error(error.errmsg)
        }
      ).finally(() => {
        this.statemanager.saveBtnLoading = false
      })
    }
  },

  watch: {
    tmp_auto_password: function () {
      this.formData.auto_password = Boolean(this.tmp_auto_password)
    }
  }
}
</script>

<style scoped>

</style>
