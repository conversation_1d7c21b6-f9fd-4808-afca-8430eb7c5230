<template>
  <div class="menus">
    <p><Button type="primary" @click="onOpenAddModal(0, 'add')">新增菜单</Button></p>
    <div class="ks-menus_container">
      <div v-for="menu in menus" :key="menu.id" class="ks-li dir">
        <div class="ks-li-parent">
          <div class="ks-li-left" @click="statemanager['menu-show-' + menu.id] = !statemanager['menu-show-' + menu.id]">
            <span>
              <i
                v-if="!statemanager['menu-show-' + menu.id] && menu.sub_menus.length > 0"
                class="fa fa-plus-square"
              ></i>
              <i v-else class="fa fa-minus-square"></i>
              {{ menu.name }}
            </span>
          </div>
          <div class="ks-li-url">
            <span>{{ menu.url }}</span>
          </div>
          <div class="ks-li-right">
            <a @click="onOpenAddModal(menu.id, 'edit')" href="javascript:;">修改</a>
            <Poptip confirm title="确认删除?" @on-ok="onDelMenu(menu.id)">
              <a href="javascript:;">删除</a>
            </Poptip>
            <a @click="onOpenAddModal(menu.id, 'add')" href="javascript:;">添加下级</a>
          </div>
        </div>

        <div
          class="ks-li-sub"
          :class="{ hide: !statemanager['menu-show-' + menu.id] }"
          v-if="menu.sub_menus.length > 0"
        >
          <div v-for="sub_menu in menu.sub_menus" class="ks-li" data-id="47" :key="sub_menu.id">
            <div class="ks-li-parent">
              <div class="ks-li-left">
                <span><i class="fa fa-circle"></i> {{ sub_menu.name }}</span>
              </div>
              <div class="ks-li-url">
                <span>{{ sub_menu.url }}</span>
              </div>
              <div class="ks-li-right">
                <a @click="onOpenAddModal(sub_menu.id, 'edit')" href="javascript:;">修改</a>
                <Poptip confirm title="确认删除?" @on-ok="onDelMenu(sub_menu.id)">
                  <a href="javascript:;">删除</a>
                </Poptip>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Modal -->

    <Modal
      v-model="addModal"
      :title="addModalTitle"
      :mask-closable="false"
      :width="430"
      :loading="addModalLoading"
      @on-ok="onAddMenu"
      class="ks-add-menu-modal"
    >
      <div>
        <div class="widget-form-group">
          <div class="widget-form-label">菜单名称:</div>
          <div class="widget-form-content">
            <Input type="text" v-model="formData.name" />
          </div>
        </div>
        <div class="widget-form-group">
          <div class="widget-form-label">菜单类型:</div>
          <div class="widget-form-content" style="line-height: 28px">
            <RadioGroup v-model="formData.type">
              <Radio label="menu"><span>菜单</span></Radio>
              <Radio label="outsideurl"><span>外链</span></Radio>
            </RadioGroup>
          </div>
        </div>
        <div class="widget-form-group">
          <div class="widget-form-label">路由:</div>
          <div class="widget-form-content">
            <Input type="text" v-model="formData.url" />
          </div>
        </div>
        <div class="widget-form-group">
          <div class="widget-form-label">位置:</div>
          <div class="widget-form-content">
            <Select v-model="formData.sort_no">
              <Option :value="0">- 首位 -</Option>
              <template v-for="menu in sameLevelMenus">
                <Option v-if="menu.id != formData.id" :value="Number(menu.sort_no)" :key="menu.id">
                  {{ menu.name }} 之后</Option
                >
              </template>
            </Select>
          </div>
        </div>
        <div class="widget-form-group" v-if="formData.p_id != 0">
          <div class="widget-form-label">分组:</div>
          <div class="widget-form-content">
            <Input type="text" v-model="formData.group_name" placeholder="请输入分组名称" />
          </div>
        </div>

        <div v-if="formData.p_id == 0" class="widget-form-group">
          <div class="widget-form-label">icon:</div>
          <div class="widget-form-content">
            <Input type="text" v-model="formData.icon" />
            <div class="note">
              注：从<a href="https://fontawesome.dashgame.com/" target="_blank">Font Awesome</a>获得图标
            </div>
          </div>
        </div>
      </div>
    </Modal>
  </div>
</template>

<script>
/* eslint-disable */
import S from 'utils/util'; // Some commonly used tools
import io from 'utils/request'; // Http request
import * as runtime from 'utils/runtime'; // Runtime information
/* eslint-disable */
import '../menus.less';

// 添加/编辑的表单数据
let init_formData = {
  id: 0,
  name: '',
  type: '',
  url: '',
  p_id: 0,
  sort_no: 0,
  icon: '',
  group_name: ''
};
export default {
  name: 'menus',
  data() {
    return {
      menus: [],
      addModal: false,
      addModalLoading: true,
      isEditModal: false,
      statemanager: {},
      sameLevelMenus: [], // 同级菜单信息
      formData: { ...init_formData }
    };
  },
  created() {
    this.getsMenus();
  },

  methods: {
    getsMenus: function (callback = function () {}) {
      io({
        url: 'pms_opc/permission.menus.list',
        mehtod: 'get'
      })
        .then(menus => {
          if (menus.length > 0) {
            this.menus = this.handerMenus(menus);
          }
          callback();
        })
        .catch(error => {
          console.log('-> error', error);
          // this.$Message.error(error.errmsg)
        });
    },

    clearFormData: function () {
      this.formData = { ...init_formData };
    },

    handerMenus: function (menus) {
      let new_menus = [];
      menus.forEach(item => {
        if (item.parent_id == 0) {
          item['sub_menus'] = [];
          menus.forEach(item2 => {
            if (item2.parent_id == item.id) {
              item.sub_menus.push(item2);
            }
          });
          new_menus.push(item);
          // 对象新增属性，需要通过this.$set方法来获得响应式
          if (S.isUndefined(this.statemanager['menu-show-' + item.id])) {
            this.$set(this.statemanager, 'menu-show-' + item.id, true);
          }
        }
      });
      return new_menus;
    },

    onOpenAddModal: function (id, action) {
      this.clearFormData();

      if (action == 'edit') {
        this.isEditModal = true;
      } else {
        this.isEditModal = false;
      }

      io.get('pms_opc/permission.menus.get', {
        data: { id, action }
      })
        .then(data => {
          if (action == 'add') {
            this.formData.p_id = id;
          } else {
            this.formData.id = data.info.id;
            this.formData.name = data.info.name;
            this.formData.type = data.info.type;
            this.formData.url = data.info.url;
            this.formData.p_id = data.info.parent_id;
            this.formData.sort_no = Number(data.info.sort_no) - 1; // -1 用于 selected 定位
            this.formData.icon = data.info.icon || '';
            this.formData.group_name = data.info.group_name || '';
          }
          this.sameLevelMenus = data.sameLevelMenus;
          this.addModal = true;
        })
        .catch(error => {
          this.$Message.error(error.errmsg);
        });
    },

    onAddMenu: function () {
      let formData = { ...this.formData };

      if (!formData.name.trim()) {
        this.$Message.error('请填写菜单名称');
        this.addModalLoading = false;
        return;
      }

      if (!formData.type) {
        this.$Message.error('请选择菜单类型');
        this.addModalLoading = false;
        return;
      }

      if (!formData.url) {
        this.$Message.error('请填写url');
        this.addModalLoading = false;
        return;
      }

      let url = 'pms_opc/permission.menus.add';
      if (this.isEditModal) {
        url = 'pms_opc/permission.menus.edit';
      }

      io.post(url, formData)
        .then(() => {
          let self = this;
          this.getsMenus(function () {
            self.$Message.success('保存成功');
            self.addModal = false;
            self.clearFormData();
          });
        })
        .catch(error => {
          this.addModalLoading = false;
          this.$Message.error(error.errmsg);
        });
    },

    onDelMenu: function (id) {
      io.post('pms_opc/permission.menus.del', { id })
        .then(() => {
          let self = this;
          this.getsMenus(function () {
            self.$Message.success('删除成功');
          });
        })
        .catch(error => {
          this.$Message.error(error.errmsg);
        });
    }
  },

  watch: {
    addModalLoading: function () {
      this.$nextTick(() => (this.addModalLoading = true));
    }
  },

  computed: {
    addModalTitle: function () {
      return this.isEditModal ? '编辑菜单' : '添加下级';
    }
  }
};
</script>

<style lang="less"></style>
