<template>
  <div>
    <p>
      <Button type="primary" @click="onOpenCreateRoleModal()">创建角色</Button>
    </p>

    <Table :loading="rolesTableLoading" :columns="rolesTableCols" :data="roles">
      <template slot-scope="{row}" slot="id"> {{row.id}} </template>
      <template slot-scope="{row}" slot="name"> {{row.name}} </template>
      <template slot-scope="{row}" slot="set_permission">
        <router-link :to="{path:'/platform/role/permission', query:{roleid:row.id}}" v-if="row.can_edit==='1'">权限管理</router-link>
        <span v-else>
          -
        </span>
      </template>
<!--      <template slot-scope="{row}" slot="set_users"> <a href="javascript:;">设置用户</a> </template>-->
      <template slot-scope="{row}" slot="created"> {{row.created}} </template>
    </Table>

    <!-- Modal -->
    <Modal v-model="createRoleModal"
           title="新增角色"
           @on-ok="onCreateRole"
           width="300"
           :mask-closable="false"
           :loading="createRoleModalLoading"
    >
      <Input type="text" v-model="formData.name" placeholder="填写角色名称"/>
    </Modal>
  </div>
</template>

<script>
import io from "utils/request";

let init_formData = {
  name: ''
}

export default {
  name: "list",
  data() {
    return {
      roles: [],
      rolesTableCols: [
        {title: 'ID', slot: 'id'},
        {title: '角色名称', slot: 'name'},
        {title: '权限管理', slot: 'set_permission'},
        // {title: '设置用户', slot: 'set_users'},
        {title: '创建时间', slot: 'created'},
      ],
      rolesTableLoading: false,
      createRoleModal: false,
      createRoleModalLoading: false,
      formData: {
        name: ''
      }
    }
  },
  created() {
    this.getsRoles()
  },
  methods: {
    getsRoles: function (callback = function(){}) {
      this.rolesTableLoading = true
      io.get('pms_opc/permission.role.list').then(data => {
        this.roles = this.handerRoles(data.roles)
        this.rolesTableLoading = false
        callback()
      }, reject => {
        this.$Message.error(reject)
      })
    },

    onOpenCreateRoleModal: function () {
      this.clearFormData()
      this.createRoleModal = true
    },

    onCreateRole: function () {
      let formData = {...this.formData}

      if (formData.name.trim() == '') {
        this.$Message.error('请填写url')
        this.createRoleModalLoading = false
      }

      io.post('pms_opc/permission.role.add', formData).then(() => {
        this.$Message.success('创建成功')
        this.getsRoles()
      }, reject => {
        this.addModalLoading = false
        this.$Message.error(reject.errmsg)
      })
    },

    handerRoles: function (roles) {
      return roles
    },

    clearFormData: function () {
      this.formData = {...init_formData}
    },
  },
  watch: {
    createRoleModalLoading: function () {
      this.$nextTick(() => this.createRoleModalLoading = true)
    }
  }
}
</script>

<style scoped>

</style>
