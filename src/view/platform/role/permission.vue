<template>
  <div>
    <div class="ks-menus_container">

      <div v-for="menu in menus" :key="menu.id" class="ks-li dir">

        <div class="ks-li-parent">
          <div class="ks-li-left" @click="statemanager['menu-show-'+menu.id] = !statemanager['menu-show-'+menu.id]">
            <span>
              <i v-if="!statemanager['menu-show-'+menu.id] && menu.sub_menus.length > 0" class="fa fa-plus-square"></i>
              <i v-else class="fa fa-minus-square"></i>
              {{ menu.name }}
            </span>
          </div>
          <div class="ks-li-right">
            <CheckboxGroup v-model="checkedmanger[menu.id]" style="display: inline-block">
              <Checkbox v-for="item in menu.resources" :key="item.id" :label="item.id">{{item.name}}</Checkbox>
            </CheckboxGroup>
            <div style="display: inline-block">
              [<a @click="onCheckAll(menu.id, 'menu')">全选</a><a @click="onUnCheckAll(menu.id, 'menu')">取消</a>]
            </div>
          </div>
        </div>

        <div class="ks-li-sub" :class="{hide: !statemanager['menu-show-'+menu.id]}" v-if="menu.sub_menus.length > 0">
          <div v-for="sub_menu in menu.sub_menus" :key="sub_menu.id" class="ks-li">
            <div class="ks-li-parent">
              <div class="ks-li-left"><span><i class="fa fa-circle"></i> {{ sub_menu.name }}</span></div>
              <div class="ks-li-right">
                <CheckboxGroup v-model="checkedmanger[sub_menu.id]" style="display: inline-block">
                  <Checkbox v-for="item in sub_menu.resources" :key="item.id" :label="item.id">{{item.name}}</Checkbox>
                </CheckboxGroup>
                <div style="display: inline-block">
                  [<a @click="onCheckAll(sub_menu.id, 'sub_menu')">全选</a><a @click="onUnCheckAll(sub_menu.id, 'sub_menu')">取消</a>]
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="block_10"></div>
      <div style="text-align: center;"><Button type="primary" :loading="statemanager.saveLoading" @click="onSave">提交</Button></div>
    </div>
  </div>
</template>

<script>
/* eslint-disable */
import S from 'utils/util' // Some commonly used tools
import io from "utils/request" // Http request
import * as runtime from 'utils/runtime' // Runtime information
/* eslint-disable */
import '../menus.less'

export default {
  name: "permission",
  data() {
    return {
      roleid: this.$route.query.roleid,
      menus: [],
      statemanager: {
        saveLoading: false
      },
      allMenuids: {},
      allResourceids: {},
      checkedmanger: {},
    }
  },
  created() {
    this.getsMenus()
  },
  methods: {
    getsMenus: function (callback = function(){}) {
      let roleid = this.roleid
      io.get('pms_opc/permission.role.resource', {data:{roleid}}).then(data => {
        let menus = this.handerMenus(data.menus)
        this.handerResources(menus)
        this.handerCheckedManger(menus)
        this.menus = menus
        callback()
      }).catch(error => {
        this.$Message.error(error.errmsg)
      })
    },

    handerMenus: function (menus) {
      let new_menus = []
      menus.forEach(item => {
        if (item.parent_id == 0) {
          item['sub_menus'] = []
          menus.forEach(item2 => {
            if (item2.parent_id == item.id) {
              item.sub_menus.push(item2);
            }
          })
          new_menus.push(item)
          // 对象新增属性，需要通过this.$set方法来获得响应式
          if (S.isUndefined(this.statemanager['menu-show-'+item.id])) {
            this.$set( this.statemanager, 'menu-show-'+item.id, true)
          }
        }
      })
      return new_menus
    },

    handerResources: function (menus) {
      menus.forEach(menu => {
        this.allResourceids[menu.id] = []
        this.allMenuids[menu.id] = []
        menu.resources.forEach(item => {
          this.allResourceids[menu.id].push(item.id)
        })
        menu.sub_menus.forEach(sub_menu => {
          this.allResourceids[sub_menu.id] = []
          sub_menu.resources.forEach(item => {
            this.allResourceids[sub_menu.id].push(item.id)
          })
          this.allMenuids[menu.id].push(sub_menu.id)
        })
      })
    },

    handerCheckedManger: function (menus) {
      menus.forEach(menu => {
        let checkedRids = []
        menu.resources.forEach(item => {
          if (item.isGet > 0) {
            checkedRids.push(item.id)
          }
        })
        this.$set(this.checkedmanger, menu.id, checkedRids)

        menu.sub_menus.forEach(sub_menu => {
          let checkedRids = []
          sub_menu.resources.forEach(item => {
            if (item.isGet > 0) {
              checkedRids.push(item.id)
            }
          })
          this.$set(this.checkedmanger, sub_menu.id, checkedRids)
        })
      })
    },

    onCheckAll: function (id, type) {
      if (type == 'sub_menu') this.checkedmanger[id] = this.allResourceids[id]
      if (type == 'menu') {
        this.checkedmanger[id] = this.allResourceids[id]

        this.allMenuids[id].forEach(sub_menu_id => {
          this.checkedmanger[sub_menu_id] = this.allResourceids[sub_menu_id]
        })
      }
    },

    onUnCheckAll: function (id, type) {
      if (type == 'sub_menu') this.checkedmanger[id] = []
      if (type == 'menu') {
        this.checkedmanger[id] = []
        this.allMenuids[id].forEach(sub_menu_id => {
          this.checkedmanger[sub_menu_id] = []
        })
      }
    },

    onSave: function () {
      this.statemanager.saveLoading = true

      let resids = []
      for (let k in this.checkedmanger) {
        resids = resids.concat(this.checkedmanger[k])
      }

      io.post('pms_opc/permission.role.setresource', {
        roleid: this.roleid, resids: JSON.stringify(resids)
      }).then(() => {
        this.$Message.success('保存成功')
      }, reject => {
        this.$Message.error(reject.errmsg)
      }).finally(() => {
        this.statemanager.saveLoading = false
      })
    }

  },
  watch: {

  }
}
</script>

<style scoped>

</style>
