<template>
  <div class="accounting-wrapper">
    <!-- 页面标题 -->
    <div class="page-header">
      <h3>{{ pageTitle }}</h3>
      <div class="employee-info">
        <img
          :src="
            employeeData.physio_avatar
              | imageStyle('B.w300', 'http://img-sn-i01s-cdn.rsjxx.com/rsjxx/2023/1220/151459_81411.png')
          "
          class="avatar"
        />
        <div>
          <div>姓名：{{ employeeData.physio_name }}</div>
          <div>手机号：{{ employeeData.physio_mobile || '-' }}</div>
          <div>身份证号：{{ employeeData.physio_idcard }}</div>
        </div>
      </div>
    </div>

    <!-- 搜索表单 -->
    <Form inline @submit.native.prevent @keyup.enter.native="onSearch">
      <div class="no-wrap">
        <FormItem>
          <Input v-model="queryFormData.order_code" placeholder="订单号" style="width: 180px" />
        </FormItem>
        <FormItem>
          <Input v-model="queryFormData.name_mobile" placeholder="客户姓名/手机号" style="width: 180px" />
        </FormItem>

        <FormItem>
          <DatePicker
            type="daterange"
            clearable
            format="yyyy-MM-dd"
            placeholder="支付时间"
            v-model="pay_times"
            style="width: 180px"
            @on-change="times => handleTimeChange(times, 'pay_time_st', 'pay_time_et')"
          ></DatePicker>
        </FormItem>
        <FormItem>
          <DatePicker
            type="daterange"
            clearable
            format="yyyy-MM-dd"
            placeholder="下单时间"
            v-model="order_times"
            style="width: 180px"
            @on-change="times => handleTimeChange(times, 'order_time_st', 'order_time_et')"
          ></DatePicker>
        </FormItem>

        <!-- v-if="[0, -1].includes(+settle_status)" -->
        <FormItem>
          <DatePicker
            type="daterange"
            clearable
            format="yyyy-MM-dd"
            placeholder="记录时间"
            v-model="create_times"
            style="width: 180px"
            @on-change="times => handleTimeChange(times, 'create_time_st', 'create_time_et')"
          ></DatePicker>
        </FormItem>

        <!-- v-if="settle_status == 1" -->
        <FormItem>
          <DatePicker
            type="daterange"
            clearable
            format="yyyy-MM-dd"
            placeholder="结算时间"
            v-model="settle_times"
            style="width: 180px"
            @on-change="times => handleTimeChange(times, 'settle_time_st', 'settle_time_et')"
          ></DatePicker>
        </FormItem>

        <FormItem>
          <Select v-model="queryFormData.divide_type" style="width: 130px" placeholder="请选择记账类型	" clearable>
            <Option v-for="item in divide_types" :key="item.id" :value="item.id">{{ item.desc }}</Option>
          </Select>
        </FormItem>
        <FormItem>
          <store-search
            ref="store"
            v-model="queryFormData.clinic_id"
            :isClearable="true"
            placeholder="所属诊所"
            style="width: 180px"
          ></store-search>
        </FormItem>

        <FormItem style="text-align: left">
          <Button type="primary" @click="onSearch" style="margin-right: 10px">筛选</Button>
          <Button type="default" @click="onResetSearch" style="margin-right: 10px">重置</Button>
          <!-- <Button
            class="space6"
            :loading="downloadLoading"
            type="default"
            @click="
              downloadExcel({
                physio_id: employeeData?.physio_id
              })
            "
            >导出</Button
          > -->
        </FormItem>
      </div>
    </Form>

    <!-- 数据表格 -->
    <div class="table-wrapper">
      <Table :data="list" :columns="modalColumns" :loading="tableLoading" :height="$store.state.app.clientHeight - 350">
        <template v-slot:order_code="{ row }">
          <KLink
            :to="{
              path: '/purchase/rst-order/detail',
              query: { orderid: row.order_id, orderType: row.source == 'HIS' ? 'his_pay_order' : 'shop_order' }
            }"
            target="_blank"
            >{{ row.order_code }}
          </KLink>
        </template>
        <template v-slot:money="{ row }">
          <div>订单金额：￥{{ row.origin_money }}</div>
          <div>实收金额：￥{{ row.payment_money }}</div>
        </template>
        <template v-slot:user_info="{ row }">
          <div>注册名：{{ row.user_info?.nickname }}</div>
          <div>姓名：{{ row.user_info?.real_name }}</div>
          <div>手机号：{{ row.user_info?.mobile }}</div>
        </template>

        <template v-slot:time="{ row }">
          <div>下单时间：{{ row.order_time }}</div>
          <div>支付时间：{{ row.pay_time }}</div>
        </template>
        <template v-slot:physio_info="{ row }">
          <div>{{ row.physio_info?.level }} / {{ row.physio_info?.ratio_text }}</div>
        </template>

        <!-- 营收 -->
        <template v-slot:payment_money="{ row }">
          <span v-text-format.number="row.payment_money"></span>
        </template>

        <!-- 预估记账 -->
        <template v-slot:esti_commission_real_money="{ row }">
          <span v-text-format.number="row.esti_commission_real_money"></span>
        </template>

        <!-- 实际记账 -->
        <template v-slot:commission_real_money="{ row }">
          <span v-if="row.is_gen_fixed_factor == 0" class="color-999">次月后生成</span>
          <span v-else v-text-format.number="row.commission_real_money" />
        </template>

        <template slot-scope="{ row }" slot="checkin_time">
          <div>{{ row.checkin_time }}</div>
        </template>

        <template slot-scope="{ row }" slot="source_info">
          <div>诊所：{{ row.clinic_name || '-' }}</div>
        </template>
      </Table>

      <div class="block_20"></div>

      <KPage
        :total="total"
        :pageSizeOpts="[10, 20, 50]"
        :page-size.sync="+queryFormData.pageSize"
        :current.sync="+queryFormData.page"
        @on-change="handleCurrentChange"
        @on-page-size-change="handleSizeChange"
        style="text-align: center"
      />
    </div>

    <!-- 返回按钮 -->
    <div class="fixed-bottom-wrapper">
      <back-button></back-button>
    </div>
  </div>
</template>

<script>
import storeSearch from '../../rst/skill/components/store-search.vue';
import moment from 'moment';
import renderHeader from '@/mixins/renderHeader';
import downloadExcel from '@/mixins/downloadExcel';
import S from 'utils/util';
import { pend_pay_tip, jz_estimated_tip, jz_actual_tip } from './components/tip';

let init_query_form_data = {
  page: 1,
  pageSize: 20,
  order_code: '', // 订单号
  batch_divide_code: '', // 结算流水号
  st: '', // 交易时间
  et: '', // 交易时间
  settle_st: '', // 结算时间
  settle_et: '', // 结算时间
  divide_status: '', // 交易状态
  name_mobile: '', //客户姓名/手机号
  pay_time_st: '', //支付时间
  pay_time_et: '', //支付时间-截止
  order_time_st: '', //下单时间
  order_time_et: '', //下单时间-截止
  checkin_time_st: '', //核销时间
  checkin_time_et: '', //核销时间-截止

  settle_time_st: '',
  settle_time_et: '',
  create_time_st: '',
  create_time_et: '',

  clinic_id: '',
  divide_type: '' //1-销售分佣 2-消耗分佣
};

export default {
  name: 'accounting',
  components: { storeSearch },
  mixins: [renderHeader, downloadExcel],
  data() {
    return {
      downloadApiName: 'excelassetslisturl',
      queryFormData: { ...init_query_form_data },
      employeeData: {},
      pageTitle: '',

      tip: {},
      modalColumns: [
        // { title: '订单号', slot: 'order_code', align: 'center', width: 100 },
        // { title: '卡券名称', key: 'card_name', align: 'center', width: 100 },
        // { title: '金额', slot: 'money', align: 'center', width: 150 },
        // { title: '客户信息', slot: 'user_info', align: 'center', width: 150 },
        // { title: '时间', slot: 'time', align: 'center', width: 220 },
        // { title: '等级/比例', slot: 'physio_info', align: 'center', width: 120 },
        // { title: '按订单金额记账（元）', slot: 'commission_origin_money', align: 'center', width: 150 },
        // { title: '按实收金额记账（元）', slot: 'commission_real_money', align: 'center', width: 150 },
        // { title: '所属信息', slot: 'source_info', align: 'center', width: 200 },
        // { title: '核销时间', slot: 'checkin_time', align: 'center', width: 150 }
      ],
      list: [],
      tableLoading: false,
      total: 0,
      pay_times: [],
      order_times: [],
      checkin_times: [],
      create_times: [],
      settle_times: [],
      divide_types: []
    };
  },
  created() {
    this.initData();
  },
  methods: {
    initData() {
      // 结算表格
      const settleCols = [
        { title: '订单号', slot: 'order_code', align: 'center', width: 100 },
        { title: '名称', key: 'card_name', align: 'center', width: 100 },
        { title: '金额', slot: 'money', align: 'center', width: 150 },
        { title: '客户信息', slot: 'user_info', align: 'center', width: 150 },
        { title: '时间', slot: 'time', align: 'center', width: 220 },
        { title: '所属诊所', key: 'clinic_name', align: 'center', width: 120 },
        {
          title: '结算金额',
          renderHeader: (h, params) => this._renderHeader(h, params, pend_pay_tip),
          slot: 'commission_real_money',
          align: 'center',
          width: 200
        },
        { title: '结算时间', key: 'settle_time', align: 'center', width: 150 },
        {
          title: '结算规则',
          key: 'calc_rules_text',
          render: (h, { row }) => {
            return h('span', [`${row.calc_rules_text || '-'}`]);
          },
          align: 'center',
          width: 150
        },
        {
          title: '记账类型',
          key: 'divide_type',
          render: (h, { row }) => {
            return h('span', [`${row.divide_type_desc || '-'}`]);
          },
          align: 'center',
          width: 100
        }
      ];

      // 营收金额
      const revenueCols = [
        { title: '订单号', slot: 'order_code', align: 'center', width: 100 },
        { title: '项目名称', key: 'card_name', align: 'center', width: 100 },
        { title: '营收金额', slot: 'payment_money', align: 'center', width: 150 },
        // { title: '金额', slot: 'money', align: 'center', width: 150 },
        { title: '客户信息', slot: 'user_info', align: 'center', width: 150 },
        { title: '时间', slot: 'time', align: 'center', width: 220 },
        { title: '所属诊所', key: 'clinic_name', align: 'center', width: 120 },
        {
          title: '预估记账业绩',
          renderHeader: (h, params) => this._renderHeader(h, params, jz_estimated_tip, 300),
          slot: 'esti_commission_real_money',
          align: 'center',
          width: 200
        },
        {
          title: '实际记账业绩',
          renderHeader: (h, params) => this._renderHeader(h, params, jz_actual_tip, 300),
          slot: 'commission_real_money',
          align: 'center',
          width: 200
        },
        // {
        //   title: '营收金额',
        //   renderHeader: (h, params) => this._renderHeader(h, params, settle_order_money_tip),
        //   slot: 'payment_money',
        //   align: 'center',
        //   width: 200
        // },
        // {
        //   title: '结算金额',
        //   renderHeader: (h, params) => this._renderHeader(h, params, pend_pay_tip),
        //   slot: 'commission_real_money',
        //   align: 'center',
        //   width: 200
        // },
        { title: '记录时间', key: 'create_time', align: 'center', width: 150 },
        { title: '结算时间', key: 'settle_time', align: 'center', width: 150 },
        {
          title: '结算规则',
          key: 'settle_rules',
          render: (h, { row }) => {
            return h('span', [`${row.settle_rules || '-'}`]);
          },
          align: 'center',
          width: 150
        },
        {
          title: '记账类型',
          key: 'divide_type',
          render: (h, { row }) => {
            return h('span', [`${row.divide_type_desc || '-'}`]);
          },
          align: 'center',
          width: 100
        }
      ];

      // 从路由参数获取员工数据
      const { employeeData, settle_status, clinic_id } = this.$route.query;

      this.queryFormData.clinic_id = clinic_id;
      init_query_form_data.clinic_id = clinic_id;
      if (employeeData) {
        this.settle_status = settle_status;

        const titleMap = {
          // 0: '待结算金额',
          1: '结算金额',
          '-1': '营收金额'
        };

        this.employeeData = JSON.parse(decodeURIComponent(employeeData));
        // this.pageTitle = `${this.employeeData.physio_name} - ${titleMap[settle_status]}详情`;
        this.pageTitle = `${this.employeeData.physio_name} - 营收金额详情`;

        // 设置默认时间范围为当月
        const startDate = moment(this.employeeData.month).startOf('month').format('YYYY-MM-DD');
        const endDate = moment(this.employeeData.month).endOf('month').format('YYYY-MM-DD');
        if (settle_status == -1) {
          this.modalColumns = revenueCols;
          this.create_times = [startDate, endDate];
          this.queryFormData.create_time_st = startDate;
          this.queryFormData.create_time_et = endDate;
        }

        // if (settle_status == 1) {
        //   this.modalColumns = settleCols;
        //   this.settle_times = [startDate, endDate];
        //   this.queryFormData.settle_time_st = startDate;
        //   this.queryFormData.settle_time_et = endDate;
        // } else if (settle_status == -1) {
        //   this.modalColumns = revenueCols;
        //   this.create_times = [startDate, endDate];
        //   this.queryFormData.create_time_st = startDate;
        //   this.queryFormData.create_time_et = endDate;
        // }

        this.getList();
      }
    },
    onSearch() {
      this.queryFormData.page = 1;
      this.getList();
    },
    onResetSearch() {
      this.queryFormData = { ...init_query_form_data };
      this.pay_times = [];
      this.order_times = [];
      this.checkin_times = [];
      this.getList();
    },
    handleSizeChange(val) {
      this.queryFormData.page = 1;
      this.queryFormData.pageSize = val;
      this.getList();
    },
    handleCurrentChange(val) {
      this.queryFormData.page = val;
      this.getList();
    },
    handleTimeChange(times, startTime = 'st', endTime = 'et') {
      if (times) {
        this.queryFormData[startTime] = times[0];
        this.queryFormData[endTime] = times[1];
      } else {
        this.queryFormData[startTime] = '';
        this.queryFormData[endTime] = '';
      }
    },
    getList() {
      this.list = [];
      this.tableLoading = true;
      let params = {
        ...this.queryFormData,
        physio_id: this.employeeData?.physio_id,
        member_id: this.employeeData?.member_id,
        settle_status: this.settle_status
      };
      this.$api
        .getAssetsList(params)
        .then(res => {
          this.list = res.list;
          this.total = res.total;
          this.divide_types = S.descToArrHandle(res.options?.divideTypeDesc);
        })
        .finally(() => (this.tableLoading = false));
    }
  }
};
</script>

<style scoped lang="less">
.accounting-wrapper {
  padding-bottom: 50px;
}

.page-header {
  margin-bottom: 20px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 4px;

  h3 {
    margin: 0 0 15px 0;
    font-size: 18px;
    font-weight: 600;
  }

  .employee-info {
    display: flex;
    align-items: center;

    .avatar {
      width: 50px;
      height: 50px;
      border-radius: 50%;
      margin-right: 15px;
    }

    div {
      div {
        margin-bottom: 5px;
        color: #666;

        &:last-child {
          margin-bottom: 0;
        }
      }
    }
  }
}

.no-wrap {
  display: flex;
  flex-wrap: wrap;
}

::v-deep .ivu-form-inline .ivu-input-wrapper,
.ivu-form-inline .ivu-select {
  width: 200px;
}
</style>
