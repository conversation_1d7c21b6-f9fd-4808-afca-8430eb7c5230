<template>
  <Modal
    :value="value"
    :title="row.physio_name"
    width="850px"
    @on-visible-change="changeVisible"
    footer-hide
    class="modal-wrapper"
  >
    <Form inline :label-width="0" @submit.native.prevent @keyup.enter.native="onSearch">
      <div class="no-wrap">
        <FormItem>
          <Input v-model="queryFormData.order_code" placeholder="订单号" style="width: 180px" />
        </FormItem>
        <FormItem>
          <Input v-model="queryFormData.name_mobile" placeholder="客户姓名/手机号" style="width: 180px" />
        </FormItem>

        <FormItem>
          <DatePicker
            type="daterange"
            clearable
            format="yyyy-MM-dd"
            placeholder="支付时间"
            v-model="pay_times"
            style="width: 180px"
            @on-change="times => handleTimeChange(times, 'pay_time_st', 'pay_time_et')"
          ></DatePicker>
        </FormItem>

        <FormItem>
          <store-search
            ref="store"
            v-model="queryFormData.clinic_id"
            :isClearable="true"
            placeholder="所属诊所"
            style="width: 180px"
          ></store-search>
        </FormItem>

        <FormItem>
          <DatePicker
            type="daterange"
            clearable
            format="yyyy-MM-dd"
            placeholder="分帐时间"
            v-model="checkin_times"
            style="width: 180px"
            @on-change="times => handleTimeChange(times, 'checkin_time_st', 'checkin_time_et')"
          ></DatePicker>
        </FormItem>
        <FormItem>
          <DatePicker
            v-model="create_time"
            type="daterange"
            clearable
            format="yyyy-MM-dd"
            placeholder="记录时间"
            style="width: 180px"
            @on-change="times => handleTimeChange(times, 'st', 'et')"
          ></DatePicker>
        </FormItem>

        <FormItem style="text-align: left">
          <Button type="primary" @click="onSearch" style="margin-right: 10px">筛选</Button>
          <Button type="default" @click="onResetSearch" style="margin-right: 10px">重置</Button>
        </FormItem>
      </div>
    </Form>

    <Table :data="list" :columns="modalColumns" :loading="tableLoading" :height="320">
      <template v-slot:order_code="{ row }">
        <KLink
          :to="{
            path: '/purchase/rst-order/detail',
            query: { orderid: row.order_id, orderType: 'shop_order' }
          }"
          target="_blank"
          >{{ row.order_code }}</KLink
        >
      </template>
      <template v-slot:money="{ row }">
        <div>订单金额：￥{{ row.origin_money }}</div>
        <div>实收金额：￥{{ row.payment_money }}</div>
      </template>
      <template v-slot:user_info="{ row }">
        <div>注册名：{{ row.user_info?.nickname }}</div>
        <div>姓名：{{ row.user_info?.real_name }}</div>
        <div>手机号：{{ row.user_info?.mobile }}</div>
      </template>

      <template v-slot:time="{ row }">
        <div>下单时间：{{ row.order_time }}</div>
        <div>支付时间：{{ row.pay_time }}</div>
      </template>
      <template v-slot:physio_info="{ row }">
        <div>{{ row.physio_info?.level }} / {{ row.physio_info?.ratio_text }}</div>
      </template>
      <!-- <template v-slot:commission_origin_money="{ row }">
        <span v-text-format.number="row.commission_origin_money"></span>
      </template> -->
      <template v-slot:commission_real_money="{ row }">
        <span v-text-format.number="row.commission_real_money"></span>
      </template>
      <!-- 付款金额 -->
      <template slot-scope="{ row }" slot="order_amount">
        <div>¥{{ row.order_amount || '0.00' }}</div>
      </template>

      <!-- 分账金额 -->
      <template slot-scope="{ row }" slot="settle_amount">
        <div>+ ¥{{ row.settle_amount || '0.00' }}</div>
      </template>

      <!-- 结算流水号 -->
      <template slot-scope="{ row }" slot="batch_divide_code">
        <div>{{ row.batch_divide_code || '-' }}</div>
      </template>

      <template slot-scope="{ row }" slot="checkin_time">
        <div>{{ row.checkin_time }}</div>
      </template>
      <template slot-scope="{ row }" slot="create_time">
        <div>{{ row.create_time || '-' }}</div>
      </template>
      <template slot-scope="{ row }" slot="clinic_name">
        <div>{{ row.clinic_name || '-' }}</div>
      </template>
    </Table>

    <div class="block_20"></div>

    <KPage
      :total="total"
      :pageSizeOpts="[10, 20, 50]"
      :page-size.sync="+queryFormData.pageSize"
      :current.sync="+queryFormData.page"
      @on-change="handleCurrentChange"
      @on-page-size-change="handleSizeChange"
      style="text-align: center"
    />
  </Modal>
</template>

<script>
import storeSearch from '../../../rst/skill/components/store-search.vue';
import moment from 'moment';

let init_query_form_data = {
  page: 1,
  pageSize: 10,
  order_code: '', // 订单号
  batch_divide_code: '', // 结算流水号
  st: '', // 记录时间
  et: '', // 记录时间
  settle_st: '', // 结算时间
  settle_et: '', // 结算时间
  divide_status: '', // 交易状态
  name_mobile: '', //客户姓名/手机号
  pay_time_st: '', //支付时间
  pay_time_et: '', //支付时间-截止
  order_time_st: '', //下单时间
  order_time_et: '', //下单时间-截止
  checkin_time_st: '', //核销时间
  checkin_time_et: '' //核销时间-截止
  // physio_id: '', // 理疗师id
};

export default {
  name: 'modal',
  mixins: [],

  components: { storeSearch },

  props: {
    value: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      default: 'title'
    },
    // 选中的item
    row: {
      type: Object,
      default: () => {}
    },
    // tab类型
    currentModalType: {
      type: String,
      default: ''
    }
  },

  data() {
    return {
      queryFormData: { ...init_query_form_data },

      modalColumns: [
        { title: '订单号', slot: 'order_code', align: 'center', width: 100 },
        { title: '名称', key: 'card_name', align: 'center', width: 100 },
        { title: '金额', slot: 'money', align: 'center', width: 150 },
        { title: '客户信息', slot: 'user_info', align: 'center', width: 150 },
        { title: '时间', slot: 'time', align: 'center', width: 220 },
        { title: '所属诊所', slot: 'clinic_name', align: 'center', width: 220 },
        { title: '按实收金额记账（元）', slot: 'commission_real_money', align: 'center', width: 150 },
        { title: '分帐时间', slot: 'checkin_time', align: 'center', width: 150 },
        { title: '记录时间', slot: 'create_time', align: 'center', width: 150 }
      ],

      type_desc: [
        { desc: '全部', id: '' },
        { desc: '待结算', id: '1' },
        { desc: '可结算', id: '2' },
        { desc: '结算中', id: '3' },
        { desc: '已结算', id: '4' }
      ], // tab type
      optionsList: [],
      // 如果当前需求页面有时间组件，并且需要限制选择范围
      disabledTime: {
        disabledDate(date) {
          return date && date.valueOf() > Date.now();
        }
      },
      times: [], // 交易时间
      settle_times: [], // 结算时间

      list: [],
      tableLoading: false,
      total: 0,
      divide_status_count_hash: {},
      pay_times: [],
      order_times: [],
      checkin_times: [],
      create_time: []
    };
  },

  computed: {},

  watch: {},

  created() {},

  mounted() {},

  destroyed() {},

  methods: {
    /**
     * 搜索
     * */
    onSearch: function () {
      this.queryFormData.page = 1;
      this.getList();
    },

    /**
     * 重置
     * */
    onResetSearch: function () {
      this.queryFormData = { ...init_query_form_data };
      this.pay_times = [];
      this.order_times = [];
      this.checkin_times = [];

      this.getList();
    },

    /**
     * 状态切换
     * */
    onStatusChange: function (status) {
      this.queryFormData.page = 1;
      this.queryFormData.divide_status = status;
      this.getList();
    },

    handleSizeChange(val) {
      this.queryFormData.page = 1;
      this.queryFormData.pageSize = val;
      this.getList();
      this.scrollTop();
    },
    handleCurrentChange(val) {
      this.queryFormData.page = val;
      this.getList();
    },

    /**
     * 表格回归顶部
     * */
    scrollTop() {
      let overflowY = this.$el.getElementsByClassName('ivu-table-body')[0];
      if (!overflowY) {
        return;
      }
      overflowY.scrollTop = 0;
    },

    /**
     * 关闭弹窗
     * */
    closeModal() {
      this.queryFormData = { ...init_query_form_data };
      this.pay_times = [];
      this.order_times = [];
      this.checkin_times = [];
      this.scrollTop();
      this.$emit('input', false);
    },

    /**
     * 弹窗变化
     * */
    changeVisible(flag) {
      if (flag) {
        const startDate = moment(this.row.month).startOf('month').format('YYYY-MM-DD');
        const endDate = moment(this.row.month).endOf('month').format('YYYY-MM-DD');
        this.create_time = [startDate, endDate];
        this.queryFormData.st = startDate;
        this.queryFormData.et = endDate;
        // this.checkin_times = [startDate, endDate];
        // this.queryFormData.checkin_time_st = startDate;
        // this.queryFormData.checkin_time_et = endDate;
        this.onSearch();
      } else {
        this.closeModal();
      }
    },

    /**
     * 当前需求如果有时间选项，用当前方法处理同步formData数据
     * */
    handleTimeChange(times, startTime = 'st', endTime = 'et') {
      if (times) {
        this.queryFormData[startTime] = times[0];
        this.queryFormData[endTime] = times[1];
      } else {
        this.queryFormData[startTime] = '';
        this.queryFormData[endTime] = '';
      }
    },

    /**
     * @description: 获取列表
     * */
    getList() {
      this.list = [];
      this.tableLoading = true;
      let params = {
        ...this.queryFormData,
        physio_id: this.row?.physio_id,
        member_id: this.row?.member_id
      };
      this.$api
        .getAssetsList(params)
        .then(res => {
          this.list = res.list;
          this.total = res.total;
          this.divide_status_count_hash = res.divide_status_count_hash;
        })
        .finally(() => (this.tableLoading = false));
    }
  }
};
</script>

<style scoped lang="less">
::v-deep .ivu-modal-body {
  max-height: 550px;
  min-height: 550px;
  overflow-y: auto;
}

.cursor {
  cursor: pointer;
}

.mb10 {
  margin-bottom: 10px;
}

.no-wrap {
  display: flex;
  flex-wrap: wrap;
}

::v-deep .ivu-form-inline .ivu-input-wrapper,
.ivu-form-inline .ivu-select {
  width: 200px;
}
</style>
