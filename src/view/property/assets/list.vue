<template>
  <div class="company-wrapper">
    <Form inline @submit.native.prevent @keyup.enter.native="onSearch">
      <Row>
        <FormItem>
          <div class="flex">
            <span>统计范围：</span>
            <clinic-search
              ref="store"
              v-model="queryFormData.clinic_id"
              placeholder="所属诊所"
              :isClearable="true"
              :showAll="false"
            ></clinic-search>
          </div>
        </FormItem>

        <FormItem>
          <div class="flex">
            <span>时间筛选：</span>
            <!--  -->
            <DatePicker
              type="month"
              :clearable="false"
              placeholder="时间"
              v-model="queryFormData.month"
              @on-change="onMonthChange"
            ></DatePicker>
          </div>
        </FormItem>

        <FormItem>
          <div class="flex">
            <Input v-model="queryFormData.name_mobile" placeholder="员工姓名/手机号" clearable />
          </div>
        </FormItem>

        <FormItem style="text-align: left">
          <div class="flex">
            <Button class="mr10" type="primary" @click="onSearch">筛选</Button>
            <Button class="mr10" type="default" @click="onResetSearch">重置</Button>
          </div>
        </FormItem>
      </Row>
    </Form>

    <!-- 统计卡片区域 -->
    <div class="stats-cards-wrapper">
      <!-- <div class="stats-card">
        <div class="card-header">
          <span class="card-title">待结算金额(元)</span>
          <Tooltip max-width="400" theme="dark" placement="top" class="custom-tooltip ml6">
            <div slot="content">
              <div>{{ tip.pend_waiting_tip }}</div>
            </div>
            <p class="flex flex-item-center cursor">
              <Icon type="ios-help-circle" color="rgb(159, 166, 193)" size="16" />
            </p>
          </Tooltip>
        </div>
        <div class="card-amount">￥{{ formatAmount(totalStats.pend_settle_money) }}</div>
      </div> -->

      <div class="stats-card">
        <div class="card-header">
          <span class="card-title">当月总业绩(元)</span>
          <Tooltip max-width="300" theme="dark" placement="top" class="custom-tooltip ml6">
            <div slot="content">
              <div>{{ tip.monthly_income_money_tip }}</div>
            </div>
            <p class="flex flex-item-center cursor">
              <Icon type="ios-help-circle" color="rgb(159, 166, 193)" size="16" />
            </p>
          </Tooltip>
        </div>
        <div class="card-amount">￥{{ formatAmount(totalStats.monthly_income_money) }}</div>
      </div>

      <div class="stats-card">
        <div class="card-header">
          <span class="card-title">当月营收金额(元)</span>
          <Tooltip max-width="300" theme="dark" placement="top" class="custom-tooltip ml6">
            <div slot="content">
              <div>{{ tip.settle_order_money_tip }}</div>
            </div>
            <p class="flex flex-item-center cursor">
              <Icon type="ios-help-circle" color="rgb(159, 166, 193)" size="16" />
            </p>
          </Tooltip>
        </div>
        <div class="card-amount">￥{{ formatAmount(totalStats.settle_order_money) }}</div>
      </div>
    </div>

    <div class="table-wrapper">
      <Table :columns="tableCols" :data="list" :loading="tableLoading" :height="$store.state.app.clientHeight - 228">
        <template v-slot:staff_info="{ row }">
          <div class="flex flex-item-align">
            <img
              :src="
                row.physio_avatar
                  | imageStyle('B.w300', 'http://img-sn-i01s-cdn.rsjxx.com/rsjxx/2023/1220/151459_81411.png')
              "
              class="avatar"
            />

            <div style="text-align: left">
              <div class="flex cell-info">
                <div>姓名：{{ row.physio_name }}</div>
                <div v-if="row.role_name.length !== 0">
                  <Tag v-for="item in row.role_name || []" :key="item.roleid" type="border">{{ item.name }}</Tag>
                </div>
                <div v-else>-</div>
              </div>
              <div>手机号：{{ row.physio_mobile || '-' }}</div>
              <div>身份证号：{{ row.physio_idcard }}</div>
            </div>
          </div>
        </template>

        <!--        职级/系数-->
        <template v-slot:mdp_level_name="{ row }">
          <span v-if="row.mdp_level_name && row.mdp_fixed_factor">
            {{ row.mdp_level_name }} / {{ row.mdp_fixed_factor }}
          </span>
          <span v-else>-</span>
        </template>
        <!--        评价系数-->
        <template v-slot:appraise_fixed_factor="{ row }">
          <span v-if="row.is_gen_fixed_factor == 1"> {{ row.appraise_fixed_factor }}</span>
          <span v-else class="color-999">次月后生成</span>
        </template>

        <!-- 营收 -->
        <!-- <template v-slot:order_payment_money="{ row }">
          <a @click="openAccountingPage(row, -1)">￥{{ row.order_payment_money || '0' }}</a>
        </template> -->

        <!-- 预估 -->
        <template v-slot:esti_commission_payment_money="{ row }">
          <a @click="openAccountingPage(row, -1)">￥{{ row.esti_commission_payment_money || '0' }}</a>
        </template>

        <!-- 实际-->
        <template v-slot:settle_payment_money="{ row }">
          <span v-if="row.is_gen_fixed_factor == 0" class="color-999">次月后生成</span>
          <a v-else @click="openAccountingPage(row, -1)">￥{{ row.settle_payment_money || '0' }}</a>
        </template>
      </Table>
      <div class="block_20"></div>
      <KPage
        :current="+queryFormData.page"
        :page-size="+queryFormData.pageSize"
        :total="total"
        style="text-align: center"
        @on-change="handleCurrentChange"
        @on-page-size-change="handleSizeChange"
      />
    </div>
  </div>
</template>

<script>
import search from '@/mixins/search';
import S from 'utils/util';
import renderHeader from '@/mixins/renderHeader';
import clinicSearch from '@/components/clinic-search/k-clinic-search_data.vue';
import {
  pend_pay_tip,
  settle_order_money_tip,
  monthly_income_money_tip,
  pend_waiting_tip,
  jz_estimated_tip,
  jz_actual_tip
} from './components/tip';

const init_query_form_data = {
  page: 1,
  pageSize: 20,
  name: '',
  mobile: '',
  wallet_code: '',
  bind_status: '',
  settle_times: '',
  month: '',
  clinic_id: ''
};

export default {
  name: 'list',
  mixins: [search, renderHeader],
  components: { clinicSearch },
  data() {
    return {
      apiName: 'getMonthlyAssetsList',
      disabledTime: {
        disabledDate(date) {
          return date && date.valueOf() > Date.now();
        }
      },
      queryFormData: {
        ...init_query_form_data
      },

      tip: {
        pend_pay_tip,
        settle_order_money_tip,
        monthly_income_money_tip,
        pend_waiting_tip
      },

      tableCols: [
        { title: '员工信息', slot: 'staff_info', align: 'center', width: 280 },
        // {title: 'MDP身份', key: 'mdp_role_name', slot: 'mdp_role_name', align: 'center'},
        { title: '月份', key: 'month', align: 'center' },
        { title: '职级/系数', key: 'mdp_level_name', slot: 'mdp_level_name', align: 'center' },
        { title: '评价系数', slot: 'appraise_fixed_factor', align: 'center' },
        // {title: '门店角色', key: 'role_name', slot: 'role_name', align: 'center'},
        {
          title: '预估记账业绩',
          renderHeader: (h, params) => this._renderHeader(h, params, jz_estimated_tip, 300),
          slot: 'esti_commission_payment_money',
          align: 'center'
        },
        {
          title: '实际记账业绩',
          renderHeader: (h, params) => this._renderHeader(h, params, jz_actual_tip, 300),
          slot: 'settle_payment_money',
          align: 'center'
        }
        // {
        //   title: '当月营收金额(元)',
        //   renderHeader: (h, params) => this._renderHeader(h, params, settle_order_money_tip),
        //   slot: 'order_payment_money',
        //   align: 'center'
        // },
        // {
        //   title: '结算金额(元)',
        //   renderHeader: (h, params) => this._renderHeader(h, params, pend_pay_tip),
        //   slot: 'settle_payment_money',
        //   align: 'center'
        // }
        // {
        //   title: '待结算金额(元)',
        //   renderHeader: (h, params) => this._renderHeader(h, params, pend_waiting_tip),
        //   slot: 'pend_settle_payment_money',
        //   align: 'center'
        // }
        // { title: '按实收金额记账（元）', slot: 'commission_payment_money', align: 'center' }
        // { title: '来源信息', slot: 'source_info', align: 'center', width: 200 }
      ],
      statusDesc: [],
      staffVisible: false,
      bindOptionList: [
        { id: '1', desc: '绑定' },
        { id: '2', desc: '未绑定' }
      ],
      currentRow: {},
      currentModalType: '',
      bindVisible: false,
      settle_times: '',
      totalStats: {
        commission_origin_money: 0,
        commission_payment_money: 0,
        settlement_money: 0
      },
      statistics: {}
    };
  },
  computed: {},
  watch: {},
  created() {
    this.queryFormData = S.merge(this.queryFormData, this.$route.query);
    const now = new Date();
    const month = now.getFullYear() + '-' + (now.getMonth() + 1 < 10 ? '0' : '') + (now.getMonth() + 1);
    init_query_form_data.month = month;
    this.queryFormData.month = month;

    // 默认选中第一个诊所
    this.$api
      .getCommonClinicList({
        page: 1,
        pageSize: 1
      })
      .then(res => {
        const id = res.list[0]?.id;
        init_query_form_data.clinic_id = id;
        this.queryFormData.clinic_id = id;
        sessionStorage.setItem('store_list', JSON.stringify(this.clinic_list));
        this.submitQueryForm(true);
      });
  },
  mounted() {},

  filters: {},
  methods: {
    onMonthChange(val) {
      this.queryFormData.month = val;
    },

    handlerListData(data) {
      /**
       *
       "pend_settle_money": "813.64",
       "settle_money": "1174.64",
       "settle_order_money": "7342"
       *
       */

      this.totalStats = data.statistics;
    },
    onResetSearch() {
      this.queryFormData = { ...init_query_form_data };
      console.log('🚀 ~ list.vue:282 ~ onResetSearch ~  this.queryFormData---->', this.queryFormData);
      this.submitQueryForm();
    },
    changeComStatus({ status, id }) {
      let params = {
        status: status === 'ON' ? 'OFF' : 'ON',
        id
      };
      this.$api.changeStatus(params).then(
        res => {
          this.$Message.success(`${params.status === 'ON' ? '启用' : '禁用'}成功`);
          this.submitQueryForm(true);
        },
        err => this.$Message.error(err.errmsg)
      );
    },

    /**
     * 打开记账详情页面
     * */
    openAccountingPage(row, settle_status) {
      // const employeeData = encodeURIComponent(JSON.stringify(row));
      // this.$router.push({
      //   path: '/property/assets/accounting',
      //   query: {
      //     employeeData,
      //     accountingType
      //   }
      // });

      const employeeData = encodeURIComponent(JSON.stringify(row));
      this.$router.push({
        path: '/property/assets/accounting',
        query: {
          employeeData,
          clinic_id: this.queryFormData.clinic_id,
          settle_status
        }
      });
    },
    showBindModal(row) {
      this.bindVisible = true;
      this.currentRow = row;
    },
    /**
     * 格式化金额：千分位 + 保留2位小数
     */
    formatAmount(amount) {
      if (!amount && amount !== 0) {
        return '0.00';
      }

      // 确保是数字类型
      const num = parseFloat(amount);
      if (isNaN(num)) {
        return '0.00';
      }

      // 保留2位小数并添加千分位分隔符
      return num.toFixed(2).replace(/\B(?=(\d{3})+(?!\d))/g, ',');
    }
  },
  beforeRouteUpdate(to, from, next) {
    this.queryFormData = S.merge(init_query_form_data, to.query);
    this.loadList();
    next();
  }
};
</script>

<style lang="less" scoped>
.avatar {
  width: 29px;
  height: 29px;
  min-width: 29px;
  min-height: 29px;
  border-radius: 50%;
  margin-right: 10px;
}

::v-deep .ivu-form-item {
  margin-bottom: 16px;

  label {
    vertical-align: middle;
  }
}

.table-wrapper {
  .table-fun {
    //text-align: right;
    padding: 10px 0;
  }
}

.custom-tooltip {
  .helpIcon {
    width: 14px;
    height: 14px;
    color: rgb(159, 166, 193);
    cursor: pointer;
  }
}

.flex {
  display: flex;
}

.flex-item-center {
  align-items: center;
}

.ml6 {
  margin-left: 6px;
}

.cursor {
  cursor: pointer;
}

.stats-cards-wrapper {
  display: flex;
  gap: 20px;
  margin-bottom: 20px;

  .stats-card {
    flex: 1;
    background: #fff;
    border: 1px solid #e8eaec;
    border-radius: 4px;
    padding: 20px;
    box-shadow: 0 1px 6px rgba(0, 0, 0, 0.1);

    .card-header {
      display: flex;
      align-items: center;
      margin-bottom: 15px;

      .card-title {
        font-size: 14px;
        color: #666;
        font-weight: 500;
      }
    }

    .card-amount {
      font-size: 24px;
      font-weight: 600;
      color: #2d8cf0;
    }
  }
}

.cell-info {
  display: flex;
  align-items: center;
  gap: 4px;
}
</style>
