<template>
  <div class="detail-wrapper">
    <h3 class="f-title">订单详情</h3>
    <div class="basic-info">
      <div class="basic-info-item">
        <span class="item-label">交易订单号</span>
        <span class="item-content">{{ echoData.code || '-' }}</span>
      </div>

      <div class="basic-info-item">
        <span class="item-label">交易时间</span>
        <span class="item-content">{{ echoData.created_at || '-' }}</span>
      </div>

      <div class="basic-info-item">
        <span class="item-label">业务类型</span>
        <span class="item-content">{{ echoData.mch_bill_type_text || '-' }}</span>
      </div>

      <div class="basic-info-item">
        <span class="item-label">收支金额</span>
        <span class="item-content">
          {{ echoData.amount ? `${echoData.wallet_bill_type === '2' ? '-' : '+'} ￥${echoData.amount}` : '-' }}
        </span>
      </div>

      <div class="basic-info-item">
        <span class="item-label">收支类型</span>
        <span class="item-content">{{ echoData.wallet_bill_type_desc || '-' }}</span>
      </div>

      <div class="basic-info-item">
        <span class="item-label">对方名称</span>
        <span class="item-content">{{ echoData.to_mch_name || '-' }}</span>
      </div>

      <!-- <div class="basic-info-item"> -->
      <!--   <span class="item-label">对方账户</span> -->
      <!--   <span class="item-content">{{ echoData.to_account_no || '-' }}</span> -->
      <!-- </div> -->

      <div class="basic-info-item">
        <span class="item-label">处理状态</span>
        <span class="item-content">{{ echoData.status_desc || '-' }}</span>
      </div>
      <!-- <div class="basic-info-item"> -->
      <!--   <span class="item-label">附言</span> -->
      <!--   <span class="item-content">{{ echoData.note || '-' }}</span> -->
      <!-- </div> -->
      <!-- <div class="basic-info-item"> -->
      <!--   <span class="item-label">备注</span> -->
      <!--   <span class="item-content">{{ echoData.remark || '-' }}</span> -->
      <!-- </div> -->
    </div>
    <div style="height: 35px">
      <div class="fixed-bottom-wrapper">
        <back-button></back-button>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'detail',
  components: {},
  mixins: [],
  props: {},
  data() {
    return {
      echoData: {}
    };
  },
  computed: {},
  watch: {},
  created() {
    this.init();
  },
  mounted() {},
  methods: {
    init() {
      let code = this.$route.query.code;
      if (code) {
        this.getBlanceInfo(code);
      }
    },

    // 返回
    back() {
      this.$router.back();
    },

    /* api-详情 */
    getBlanceInfo(code) {
      this.$api
        .getBlanceInfo({
          code
        })
        .then(res => {
          this.echoData = res;
        })
        .catch(err => {
          this.$Message.error(err.errmsg);
        });
    }
  },
  filters: {}
};
</script>

<style lang="less" scoped>
.detail-wrapper {
  .basic-info {
    border: 1px solid #b7b7b7;

    .basic-info-item {
      display: flex;
      line-height: 22px;
      border-bottom: 1px solid #b7b7b7;

      &:last-of-type {
        border-bottom: none;
      }

      text-align: center;

      .item-label {
        width: 180px;
        border-right: 1px solid #b7b7b7;
        background: #e6e6e6;
      }

      .item-content {
        flex: 1;
        text-align: left;
        text-indent: 30px;
      }
    }
  }
}
</style>

<style lang="less" scoped>
.mt20 {
  margin-top: 20px;
}
</style>
