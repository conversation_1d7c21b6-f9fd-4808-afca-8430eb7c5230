<template>
  <Modal
    :value="value"
    width="480"
    title="充值提醒"
    :mask-closable="false"
    :closable="false"
    :header-hide="true"
    class-name="vertical-center-modal auth-remind-modal"
  >
    <div class="content">
      <p>请务必再次确认，使用主体认证的银行账号 {{ cardNum }} 进行账户充值</p>
    </div>
    <!--      树家-->
    <div slot="footer">
      <Button @click="closeModal" type="primary">我知道了</Button>
    </div>
  </Modal>
</template>

<script>
export default {
  name: 'RechargeRemindModal',
  mixins: [],
  components: {},
  props: {
    value: {
      type: Boolean,
      default: false
    },
    cardNum: {
      type: String,
      default: ''
    }
  },

  data() {
    return {};
  },

  computed: {},

  watch: {},

  created() {},

  mounted() {},

  methods: {
    closeModal(){
      this.$emit('input', false);

    },
  },

  destroyed() {}
};
</script>

<style scoped lang="less">
//::v-deep .auth-remind-modal {
//  left: 210px;
//}
////::v-deep .ivu-modal-header {
////  border-bottom: none !important;
////}
//::v-deep .ivu-modal-mask {
//  left: 210px;
//}
.content {
  font-size: 14px;
  padding: 10px 20px 20px;
}
</style>
