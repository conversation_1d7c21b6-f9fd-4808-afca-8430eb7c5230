<template>
  <div class="page-flex" :style="`height:${$store.state.app.clientHeight - 200}px`" v-show="pageLoad">
    <div>
      <Alert type="warning" v-if="isyztopen !== '1'">
        为了保障资金安全，需要先通过主体认证，方可使用。
        <Button size="small" type="primary" @click="$router.push('/setting/subject-auth/index')">去认证</Button>
        <!-- <template slot="desc">Content of prompt. Content of prompt. Content of prompt. Content of prompt. </template> -->
      </Alert>
      <el-descriptions
        title="名称信息"
        :labelStyle="{ display: 'inline-block', textAlign: 'right', width: '100px' }"
        :column="1"
      >
        <el-descriptions-item label="主体名称">{{ echoData.company_name || '-' }}</el-descriptions-item>
        <el-descriptions-item label="名称">{{ echoData.clinic_name || '-' }}</el-descriptions-item>
        <el-descriptions-item label="账户可用余额"
          ><span v-text-format.number="echoData.balance"></span>
        </el-descriptions-item>
      </el-descriptions>

      <!-- <div class="flex label-item"> -->
      <!--   <div class="label">收款账户：</div> -->
      <!--   <div class="value">{{ echoData.wallet_name || '-' }}</div> -->
      <!-- </div> -->

      <!-- <div class="flex label-item"> -->
      <!--   <div class="label">店铺名称 ：</div> -->
      <!--   <div class="value">{{ echoData.clinic_name || '-' }}</div> -->
      <!-- </div> -->

      <!--     <div class="flex label-item"> -->
      <!--       <div class="label">银联钱包账号：</div> -->
      <!--       <div class="value"> -->
      <!--         {{ echoData.wallet_id || '-' }} -->
      <!--        <span class="tip ml20">公对公账户 仅展示</span>-->
      <!--       </div> -->
      <!--     </div> -->
      <div class="flex label-item ml20">
        <!-- <Button type="primary" class="ml20" @click="topUpFunc">充值</Button> -->
        <!-- <div class="label"> -->
        <!-- </div> -->
        <!-- <div class="value"></div> -->
      </div>

      <el-descriptions
        :column="1"
        :colon="false"
        :labelStyle="{ display: 'inline-block', textAlign: 'right', width: '100px' }"
        style="margin-top: 40px"
        v-if="isyztopen === '1'"
      >
        <template v-slot:title>
          <p class="desc-title">
            &nbsp;请务必使用
            <span class="strong-tips">
              <span style="font-size: 18px">主体认证的银行账号</span>{{ echoData?.bank_no?.bank_acct_no }}</span
            >
            进行账户充值；
            <span style="color: red">请勿使用其他银行卡转账，否则将无法成功充值</span>
          </p>
        </template>
        <el-descriptions-item label="收款户名:">
          <span v-if="echoData.payee_name"> {{ echoData.payee_name }} </span>
          <span v-else> - </span>
        </el-descriptions-item>
        <el-descriptions-item label="收款银行:">
          <span v-if="echoData.payee_bank"> {{ echoData.payee_bank }} </span>
          <span v-else> - </span>
        </el-descriptions-item>
        <el-descriptions-item label="收款账号:">
          <span v-if="echoData.payee_bank_no">
            {{ echoData.payee_bank_no }}
          </span>
          <span v-else> - </span>
        </el-descriptions-item>
        <el-descriptions-item label="联行号:">
          <span v-if="echoData.interbank_no"> {{ echoData.interbank_no }}</span>
          <span v-else> - </span>
        </el-descriptions-item>
        <el-descriptions-item label="">
          <Button type="primary" v-copy="getCopyInfo" @click="remindVisible = true">复制全部</Button>
        </el-descriptions-item>
      </el-descriptions>

      <!-- <div style="font-weight: bold; margin-bottom: 10px"> -->
      <!--   请使用 {{ echoData?.bank_no?.bank_acct_no }} 的账号进行汇款转账 -->
      <!-- </div> -->
      <!-- <div class="flex label-item card-item"> -->
      <!--   <div class="label">主体名称</div> -->
      <!--   <div class="value">{{ echoData.payee_name || '-' }}</div> -->
      <!-- </div> -->
      <!-- <div class="flex label-item card-item"> -->
      <!--   <div class="label">收款开户行</div> -->
      <!--   <div class="value">{{ echoData.payee_bank || '-' }}</div> -->
      <!-- </div> -->
      <!-- <div class="flex label-item card-item"> -->
      <!--   <div class="label">收款卡号</div> -->
      <!--   <div class="value">{{ echoData.payee_bank_no || '-' }}</div> -->
      <!-- </div> -->
      <!---->
      <!-- <div class="cardGrayModel"> -->
      <!--   <div>1、充值流程：</div> -->
      <!--   请使用{{ echoData?.bank_no?.bank_acct_no }}卡号进行汇款，商户确认汇款入账后会往您的商户钱包进行充值。 -->
      <!-- </div> -->
    </div>

    <div class="cardGray" v-if="isyztopen === '1'">
      <div>1、充值流程：</div>
      <p>请使用{{ echoData?.bank_no?.bank_acct_no }}卡号进行汇款，平台确认汇款入账后会往您的钱包进行充值。</p>

      <div>2、充值后，为什么收支明细没有记录？</div>
      <p>汇款入账后成功才会产生汇款记录。可能系统会延迟，若超过一天任未显示，请与平台或省公司联系。</p>
    </div>
    <Modal
      v-model="visible"
      title="汇款信息"
      width="430px"
      class-name="tips-modal vertical-center-modal"
      transfer
      footer-hide
    >
    </Modal>
    <recharge-remind-modal v-model="remindVisible" :card-num="echoData.bank_no.bank_acct_no"></recharge-remind-modal>
  </div>
</template>

<script>
import RechargeRemindModal from './RechargeRemindModal.vue';

export default {
  name: 'list',
  components: { RechargeRemindModal },
  mixins: [],
  props: {},
  data() {
    return {
      echoData: {
        payee_bank: '',
        payee_name: '',
        balance: '',
        clinic_name: '',
        company_name: '',
        bank_no: { bank_acct_no: '', interbank_no: '', payee_bank_no: '' }
      },
      visible: false,
      isyztopen: '',
      remindVisible: false,
      pageLoad: false
    };
  },
  computed: {
    getCopyInfo() {
      return (
        '收款账户：' +
        this.echoData.payee_name +
        '\n' +
        '收款银行：' +
        this.echoData.payee_bank +
        '\n' +
        '收款账号：' +
        this.echoData.payee_bank_no +
        '\n' +
        '   联行号：' +
        this.echoData.interbank_no
      );
    }
  },
  watch: {},
  created() {
    this.getRechargeInfo();
  },
  mounted() {},
  methods: {
    getIsyztopen() {
      this.$api.getIsyztopen().then(res => {
        this.isyztopen = res.is_yzt_open;
        this.pageLoad = true;
      });
    },
    // api-获取充值详情
    getRechargeInfo() {
      this.$api.getRechargeInfo().then(
        res => {
          this.echoData = res.recharge;
          this.getIsyztopen();
        },
        rej => this.$Message.error(rej.errmsg)
      );
    },
    topUpFunc() {
      if (this.isyztopen === '0') {
        this.$Message.error('当前因未认证，暂不支持充值');
        return;
      }
      console.log(234234);
      this.visible = true;
    }
  },
  filters: {}
};
</script>

<style lang="less" scoped>
.label-item {
  line-height: 30px;
  margin-bottom: 10px;
}

.label {
  width: 100px;
  min-width: 100px;
  text-align: right;
}

.value {
}

.tip {
  color: orange;
}

.ml20 {
  margin-left: 20px;
}

.page-flex {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.cardGray {
  background: #f5f5f5;
  padding: 10px 20px;
  margin-top: 40px;

  div {
    font-weight: bold;
    margin-bottom: 5px;
  }

  p {
    color: #868686;
  }
}

.cardGrayModel {
  .cardGray;
  margin-top: 0px;
  background: #fff;
  color: #c6c6c6;
}

.card-item {
  background: #f7f8fa;
  padding: 6px 0px;

  .label {
    padding-right: 10px;
  }
}

.desc-title {
  line-height: 26px;
}

::v-deep .strong-tips {
  color: red;
  font-size: 24px;
  background: blanchedalmond;
}

::v-deep .el-descriptions__body {
  background-color: #f9fbfb;
  padding-top: 10px;
}
</style>
