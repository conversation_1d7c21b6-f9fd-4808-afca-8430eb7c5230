<template>
  <div>
    <Form :label-width="0" @submit.native.prevent @keyup.enter.native="onSearch" inline>
      <Row>
        <Col>
          <FormItem>
            <DatePicker
              type="daterange"
              :options="dateOptions"
              v-model="timeRange"
              clearable
              @on-change="times => handleTimeChange(times)"
              placeholder="交易时间"
              style="width: 180px"
            ></DatePicker>
          </FormItem>
        </Col>
        <Col>
          <FormItem>
            <Select v-model="queryFormData.status" placeholder="充值状态" clearable style="width: 180px">
              <Option value="">全部状态</Option>
              <Option v-for="(desc, status) in statusDesc" :key="desc.id" :value="desc.id">{{ desc.desc }}</Option>
            </Select>
          </FormItem>
        </Col>

        <Col>
          <FormItem>
            <Button type="primary" @click="initList">筛选</Button>
            <Dvd /><Dvd />
            <Button class="space6" @click="onResetSearch">重置</Button>
          </FormItem>
        </Col>
      </Row>
    </Form>

    <div class="tips" style="margin-top: -12px; margin-bottom: 4px; color: #999">
      充值记录可能存在5-15分钟延迟情况
    </div>

    <Table :loading="tableLoading" :columns="tableCols" :data="list" :height="$store.state.app.clientHeight - 314">
      <template v-slot:created_at="{ row }">{{ row.created_at || '-' }}</template>
      <template v-slot:amount_string="{ row }"> ￥{{ row.amount_string }} </template>
      <template v-slot:note="{ row }">{{ row?.extend?.remark || '-' }}</template>
      <template v-slot:pay_bank_acct_no="{ row }">{{ row?.extend?.recharge_extend?.pay_bank_acct_no || '-' }}</template>
    </Table>

    <div class="block_20"></div>

    <KPage
      :total="total"
      :page-size="+queryFormData.pageSize"
      :current="+queryFormData.page"
      @on-change="handleCurrentChange"
      @on-page-size-change="handleSizeChange"
      style="text-align: center"
    />
  </div>
</template>

<script>
import search from '@/mixins/search';
import S from '@/utils/util';
import moment from 'moment';

let init_query_form_data = {
  page: 1,
  pageSize: 20,
  status: '',
  st: moment().subtract(30, 'days').format('YYYY-MM-DD'),
  et: moment().format('YYYY-MM-DD')
};
export default {
  name: 'list',
  components: {},
  mixins: [search],
  props: {},
  data() {
    return {
      statusDesc: [],
      queryFormData: { ...init_query_form_data },
      apiName: 'getRechargeList',
      isMultipleTabs: true,

      dateOptions: {
        disabledDate(time) {
          return time.getTime() > Date.now();
        },
        shortcuts: [
          {
            text: '今天',
            value() {
              const end = new Date();
              const start = new Date();
              console.log('-> %c end  === %o', 'font-size: 15px;color: green;', end);
              // const start = new Date (moment().subtract(1, 'weeks').unix()*1000)
              // console.log("-> %c start  === %o", "font-size: 15px;color: green;", start)
              return [start, end];
            }
          },
          {
            text: '昨天',
            value() {
              const end = new Date();
              const start = new Date();
              console.log('-> %c end  === %o', 'font-size: 15px;color: green;', end);
              // const start = new Date (moment().subtract(1, 'weeks').unix()*1000)
              // console.log("-> %c start  === %o", "font-size: 15px;color: green;", start)
              start.setTime(start.getTime() - 3600 * 1000 * 24);
              return [start, start];
            }
          },
          {
            text: '近七天',
            value() {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 6);
              return [start, end];
            }
          },
          {
            text: '近三十天',
            value() {
              const start = moment().subtract(30, 'days').toDate();
              const end = moment().toDate();
              // start.setTime(start.getTime() - 3600 * 1000 * 24 * 29);
              return [start, end];
            }
          }
        ]
      },
      timeRange: [],
      tableCols: [
        // {
        //   title: '商户订单号',
        //   key: 'mch_order_code',
        //   align: 'center',
        //   render: (h, { row }) => h('span', {}, row.mch_order_code || '-')
        // },
        // {
        //   title: '商户流水号',
        //   key: 'mch_bill_code',
        //   align: 'center',
        //   render: (h, { row }) => h('span', {}, row.mch_bill_code || '-')
        // },
        {
          title: '交易流水号',
          key: 'code',
          align: 'center',
          render: (h, { row }) => h('span', {}, row.code || '-')
        },
        // { title: '银行账号', key: 'to_account_no', align: 'center' },
        {
          title: '付款卡号',
          slot: 'pay_bank_acct_no',
          align: 'center',
          render: (h, { row }) => h('span', {}, row.extend?.recharge_extend?.pay_bank_acct_no || '-')
        },
        // {
        //   title: '收款卡号',
        //   key: 'pay_bank_acct_no',
        //   align: 'center',
        //   render: (h, { row }) => h('span', {}, row.extend?.recharge_extend?.receive_bank_acct_no || '-')
        // },
        { title: '交易时间', slot: 'created_at', align: 'center' },
        { title: '充值金额（元）', slot: 'amount_string', align: 'center' },
        {
          title: '交易类型',
          key: 'mch_bill_type_text',
          align: 'center',
          render: (h, { row }) => h('span', {}, row.mch_bill_type_text || '-')
        },
        { title: '充值状态', key: 'status_desc', align: 'center' },
        {
          title: '附言',
          slot: 'note',
          align: 'center',
          render: (h, { row }) => h('span', {}, row.extend?.recharge_extend?.recharge_memo || '-')
        }
      ],
      list: [],
      tableLoading: false
    };
  },
  computed: {},
  watch: {},

  created() {},
  mounted() {
    this.init();
  },
  methods: {
    init() {
      this.getBlanceOptions();
    },

    onResetSearch() {
      this.queryFormData = { ...init_query_form_data };
      this.timeRange = [];
      this.initList();
    },

    //  api-获取枚举
    getBlanceOptions() {
      this.$api
        .getBlanceOptions()
        .then(
          res => {
            this.statusDesc = S.descToArrHandle(res.statusDesc);
            this.queryFormData = S.merge(this.queryFormData, this.$route.query);
            this.loadList();
            this.getTimeRange();
          },
          rej => this.$Message.error(rej.errmsg)
        )
        .catch(() => {});
    },
    initList() {
      this.queryFormData = S.merge(this.queryFormData, this.$route.query);
      this.getTimeRange();
      this.loadList();
    }
  },
  filters: {}
};
</script>

<style lang="less" scoped>
.tip {
  margin: 0 10px 10px;
  color: #ccc;
}
</style>
