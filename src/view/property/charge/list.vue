<template>
  <div style="height: 100%">
    <Tabs :value="currentComponentName" @on-click="changeTabs">
      <TabPane v-for="item in componentsTabs" :key="item.componentName" :label="item.label" :name="item.componentName">
      </TabPane>
    </Tabs>
    <component :ref="currentComponentName" :is="currentComponentName"></component>
  </div>
</template>

<script>
import Charge from './components/charge';
import Record from './components/record';
export default {
  name: 'list',
  components: {
    Charge,
    Record
  },
  mixins: [],
  props: {},
  data() {
    return {
      currentComponentName: '',
      componentsTabs: [
        { componentName: 'Charge', label: '充值' },
        { componentName: 'Record', label: '充值记录' }
      ]
    };
  },
  computed: {},
  watch: {},
  created() {
    const { tab } = this.$route.query;
    if (tab) {
      this.currentComponentName = tab;
    } else {
      this.init();
    }
  },
  mounted() {},
  methods: {
    init() {
      this.currentComponentName = this.componentsTabs[0].componentName;
    },
    changeTabs(itemName) {
      this.currentComponentName = itemName;
      this.$router.push({
        query: {
          ...this.$route.query,
          tab: itemName
        }
      });
    }
  },
  filters: {},
  beforeRouteUpdate(to, from, next) {
    // todo 点击菜单栏tab重置为第一个
    this.$nextTick(() => {
      const { tab } = this.$route.query;
      if (tab) {
        this.currentComponentName = tab;
      } else {
        this.init();
      }
    });
    next();
  }
};
</script>

<style lang="less" scoped></style>
