<template>
  <div>
    <table class="ks-table ks-table-goods">
      <tr>
        <td colspan="2" class="table_tt">对账单信息</td>
      </tr>
      <tr>
        <th width="90">交易订单号</th>
        <td>
          <span>{{ StatementInfo.trans_bill_code || '-' }}</span>
        </td>
      </tr>
      <tr>
        <th>商户订单号</th>
        <td>{{ StatementInfo.mch_order_code || '-' }}</td>
      </tr>
      <tr>
        <th>交易时间</th>
        <!-- <td>{{ StatementInfo.trade_time | data_format('YYYY-MM-DD HH:mm:ss') }}</td> -->
        <td>{{ StatementInfo.trade_at || '-' }}</td>
      </tr>
      <tr>
        <th>交易类型</th>
        <td>{{ StatementInfo.type_text || '-' }}</td>
      </tr>
      <tr>
        <th>业务类型</th>
        <td>{{ StatementInfo.biz_type_text || '-' }}</td>
      </tr>
      <tr>
        <th>金额</th>
        <td>￥{{ StatementInfo.amount }}</td>
      </tr>
      <tr>
        <th>交易手续费</th>
        <td>
          <span class="">￥{{ StatementInfo.fee || '-' }}</span>
        </td>
      </tr>
      <tr>
        <th>对方名称</th>
        <td>{{ StatementInfo.to_mch_name || '-' }}</td>
      </tr>
      <tr>
        <th>对方账户</th>
        <td>
          <span>{{ StatementInfo.to_mch_bank_no || '-' }}</span>
        </td>
      </tr>
      <tr>
        <th>处理状态</th>
        <td>
          <span>{{ StatementInfo.status_text || '-' }}</span>
        </td>
      </tr>
      <tr>
        <th>附言</th>
        <td>
          <span>{{ StatementInfo.note || '-' }}</span>
        </td>
      </tr>
      <tr>
        <th>备注</th>
        <td>
          <span>{{ StatementInfo.remark || '-' }}</span>
        </td>
      </tr>
    </table>
    <div class="fixed-bottom-wrapper">
      <back-button></back-button>
    </div>
  </div>
</template>

<script>
/* eslint-disable */
import S from '@/utils/util'; // Some commonly used tools
/* eslint-disable */

export default {
  name: 'detail',
  components: {},
  data() {
    return {
      StatementInfo: {}
    };
  },

  created() {
    this.getDetail();
  },

  methods: {
    getDetail() {
      const id = this.$route.query.id;
      this.$api.getStatementInfo({ id }).then(res => {
        console.log('-> %c res  === %o', 'font-size: 15px;color: green;', res);
        this.StatementInfo = res;
      });
    }
  }
};
</script>

<style lang="less" scoped>
.text-center {
  text-align: center;
}
.record-table {
  td {
    text-align: center;
  }
}
</style>
