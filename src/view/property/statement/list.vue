<template>
  <div>
    <Form :label-width="0" @submit.native.prevent @keyup.enter.native="onSearch" inline>
      <FormItem label="">
        <Input type="text" placeholder="交易流水号/商户订单号" v-model="queryFormData.keywords" style="width: 180px" />
      </FormItem>
      <FormItem label="">
        <DatePicker
          type="daterange"
          v-model="timeRange"
          @on-change="times => handleTimeChange(times)"
          style="width: 180px"
          :options="dateOptions"
          placeholder="交易时间"
        ></DatePicker>
      </FormItem>
      <FormItem label="">
        <Select v-model="queryFormData.wallet_bill_type" placeholder="收支类型" style="width: 180px">
          <Option v-for="(item, status) in symbolDesc" :key="item.kw" :value="item.id">{{ item.desc }}</Option>
        </Select>
      </FormItem>
      <!-- <FormItem label=""> -->
      <!--   <Select v-model="queryFormData.type" placeholder="交易类型" style="width: 180px;"> -->
      <!--     <Option v-for="(item, status) in typeDesc" :key="item.kw" :value="item.id">{{ item.desc }}</Option> -->
      <!--   </Select> -->
      <!-- </FormItem> -->
      <FormItem>
        <Button type="primary" @click="onSearch">筛选</Button>
        <Dvd /><Dvd />
        <Button type="default" @click="downloadExcel(queryFormData)">导出</Button>
        <Dvd /><Dvd />
        <Button class="space6" @click="onResetSearch">重置</Button>
      </FormItem>
    </Form>

    <Table :loading="tableLoading" :columns="tableCols" :data="list" :height="$store.state.app.clientHeight - 235">
      <template v-slot:trade_time="{ row }">
        {{ row.trade_time | data_format('YYYY-MM-DD HH:mm:ss') }}
      </template>
      <template slot-scope="{ row }" slot="amount">
        <div v-if="row.amount">
          <div v-if="row.type === '2'">
            <span
              ><span style="color: red">-{{ row.amount }}</span></span
            >
          </div>
          <div v-else>
            <span
              ><span style="color: green">+{{ row.amount }}</span></span
            >
          </div>
        </div>
        <div v-else>-</div>
        <!-- {{ row.amount ? `￥${row.wallet_bill_type === '2' ? '-' : '+'}${row.amount}` : '-' }} -->
      </template>
      <template v-slot:trade_code="{ row }">
        {{ row.trade_code || '-' }}
      </template>
      <template v-slot:status_text="{ row }">
        <status-text :status="row.status">{{ row.status_text }}</status-text>
      </template>
      <template v-slot:fee="{ row }"> ￥{{ row.fee }} </template>
      <template slot-scope="{ row }" slot="operate">
        <KLink :to="{ path: '/property/statement/detail', query: { id: row.wallet_bill_code } }">详情</KLink>
      </template>
    </Table>

    <div class="block_20"></div>

    <KPage
      :total="total"
      :current="+queryFormData.page"
      :page-size="+queryFormData.pageSize"
      @on-change="handleCurrentChange"
      @on-page-size-change="handleSizeChange"
      style="text-align: center"
    />
  </div>
</template>

<script>
/* eslint-disable */
import S from '@/utils/util'; // Some commonly used tools
import io from 'utils/request'; // Http request
import search from '@/mixins/search'; // Search mixin
import moment from 'moment';
/* eslint-disable */
import downloadExcel from '@/mixins/downloadExcel';

let init_query_form_data = {
  page: 1,
  pageSize: 20,
  trade_code: '', //交易订单号
  biz_type: '', //业务类型
  type: '', //交易类型
  status: '', //状态
  symbol: '', //收支符号
  st: moment().subtract(30, 'days').format('YYYY-MM-DD'),
  et: moment().format('YYYY-MM-DD')
};
export default {
  name: 'list',
  components: {},
  mixins: [downloadExcel, search],
  data() {
    return {
      apiName: 'getStatementList',
      downloadApiName: 'exportStatementList',
      queryFormData: { ...init_query_form_data },
      timeRange: [], // 账单时间
      tableCols: [
        { title: '交易流水号', key: 'trans_bill_code', width: 250, align: 'center' },
        {
          title: '订单号',
          key: 'mch_order_code',
          align: 'center',
          render: (h, { row }) => h('span', {}, row.mch_order_code || '-')
        },
        { title: '交易时间', key: 'trade_at', align: 'center' },
        // { title: '交易类型', key: 'type_text',align: 'center' },
        {
          title: '业务类型',
          key: 'mch_order_type_text',
          align: 'center',
          render: (h, { row }) => h('span', {}, row.mch_order_type_text || '-')
        },
        { title: '收支类型', key: 'type_desc', align: 'center' },
        { title: '交易金额（元）', slot: 'amount', align: 'center' },
        // { title: '交易手续费', slot: 'fee',align: 'center' },
        {
          title: '对方名称',
          key: 'to_mch_name',
          align: 'center',
          render: (h, { row }) => h('span', {}, row.to_mch_name || '-')
        },
        // {
        //   title: '对方账户',
        //   key: 'to_mch_bank_no',
        //   align: 'center',
        //   render: (h, { row }) => h('span', {}, row.to_mch_bank_no || '-')
        // },
        // { title: '处理状态', slot: 'status_text', align: 'center' },
        {
          title: '金额结余（元）',
          key: 'total_balance',
          align: 'center',
          render: (h, { row }) => h('span', {}, row.total_balance ? `￥${row.total_balance}` : '' || '-')
        }
        // { title: '备注', key: 'remark', align: 'center', render: (h, { row }) => h('span', {}, row.remark || '-') }
        // { title: '操作', slot: 'operate', width: 90, align: 'center' }
      ],
      tableLoading: false,
      list: [],
      sourceDesc: [],
      total: 0,
      users: {},
      statusDesc: [],
      typeDesc: [],
      symbolDesc: [],
      dateOptions: {
        disabledDate(time) {
          return time.getTime() > Date.now();
        },
        shortcuts: [
          {
            text: '今天',
            value() {
              const end = new Date();
              const start = new Date();
              console.log('-> %c end  === %o', 'font-size: 15px;color: green;', end);
              // const start = new Date (moment().subtract(1, 'weeks').unix()*1000)
              // console.log("-> %c start  === %o", "font-size: 15px;color: green;", start)
              return [start, end];
            }
          },
          {
            text: '昨天',
            value() {
              const end = new Date();
              const start = new Date();
              console.log('-> %c end  === %o', 'font-size: 15px;color: green;', end);
              // const start = new Date (moment().subtract(1, 'weeks').unix()*1000)
              // console.log("-> %c start  === %o", "font-size: 15px;color: green;", start)
              start.setTime(start.getTime() - 3600 * 1000 * 24);
              return [start, start];
            }
          },
          {
            text: '近七天',
            value() {
              const end = new Date();
              const start = new Date();
              console.log('-> %c end  === %o', 'font-size: 15px;color: green;', end);
              // const start = new Date (moment().subtract(1, 'months').unix()*1000)
              // console.log("-> %c start  === %o", "font-size: 15px;color: green;", start)
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 6);
              return [start, end];
            }
          },
          {
            text: '近三十天',
            value() {
              const start = moment().subtract(30, 'days').toDate();
              const end = moment().toDate();
              // start.setTime(start.getTime() - 3600 * 1000 * 24 * 29);
              return [start, end];
            }
          }
        ]
      }
    };
  },

  created() {
    this.queryFormData = S.merge(this.queryFormData, this.$route.query);
    this.getOptions();
    this.submitQueryForm(true);
  },

  watch: {},

  methods: {
    changeTime(times, type) {
      this.queryFormData.st = times && times[0] ? times[0] : '';
      this.queryFormData.et = times && times[1] ? times[1] : '';
    },
    onResetSearch: function () {
      this.timeRange = [];
      this.queryFormData = { ...init_query_form_data };
      this.submitQueryForm();
    },

    getOptions() {
      this.$api.getBalanceOptions().then(data => {
        this.statusDesc = data.statusDesc;
        this.typeDesc = S.descToArrHandle(data.typeDesc);
        this.symbolDesc = S.descToArrHandle(data.walletBillTypeDesc);
        console.log('-> %c this.symbolDesc  === %o', 'font-size: 15px;color: green;', this.symbolDesc);
      });
    }
  },

  beforeRouteUpdate: function (to, from, next) {
    // S.log(to.params['nav-stack-key-dir'], 'nav-stack-key-dir')
    this.queryFormData = S.merge(init_query_form_data, to.query);
    this.getTimeRange();
    this.loadList();
    next();
  }
};
</script>

<style lang="less">
.order_attrs_table {
  width: 100%;
  border-spacing: 0;
  border-collapse: collapse;
}

.order_attrs_table td {
  padding: 5px 5px;
  border-color: transparent;
}
</style>
<style lang="less" scoped>
::v-deep .ivu-form-item {
  margin-bottom: 12px;
}
</style>
