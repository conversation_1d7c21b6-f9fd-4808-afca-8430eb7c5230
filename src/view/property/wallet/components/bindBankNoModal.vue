<template>
  <Modal
    :value="visible"
    title="结算信息"
    :mask-closable="false"
    width="600px"
    @on-cancel="cancel"
    @on-visible-change="changeVisible"
  >
    <div>
      <Form :label-width="100" style="margin-right: 50px" label-colon>
        <div>
          <FormItem label="姓名" required>
            <Input :value="row.name" disabled placeholder="请输入姓名" />
          </FormItem>
          <FormItem label="手机号" required>
            <Input :value="row.mobile" disabled placeholder="请输入手机号" />
          </FormItem>
          <FormItem label="身份证" required>
            <Input :value="row.idcard" disabled placeholder="请输入身份证" />
          </FormItem>
          <FormItem label="银行卡卡号" required>
            <Input
              v-model="queryFormData.bank_no"
              placeholder="请输入银行卡卡号"
              @input="val => (queryFormData.bank_no = val.replace(/\s+/g, ''))"
            />
          </FormItem>
        </div>
      </Form>
    </div>
    <div slot="footer">
      <Button @click="cancel">取消</Button>
      <Button type="primary" @click="submitForm" :loading="submitLoading">确定</Button>
    </div>
  </Modal>
</template>

<script>
const initFormData = {
  bank_no: ''
};

export default {
  name: 'bindBankNoModal',
  mixins: [],
  components: {},
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    id: {
      type: String,
      default: ''
    },
    row: {
      type: Object,
      default: () => {}
    }
  },

  data() {
    return {
      queryFormData: { ...initFormData },
      submitLoading: false
    };
  },

  computed: {},

  watch: {},

  created() {},

  mounted() {},

  methods: {
    changeVisible(val) {
      if (val) {
        this.queryFormData.bank_no = this.row.bank_no;
      } else {
        this.formData = { ...initFormData }; // 重置数据
      }
    },
    cancel() {
      this.$emit('update:visible', false);
    },
    submitForm() {
      if (!this.queryFormData.bank_no) {
        this.$Message.error('请输入银行卡卡号');
        return;
      }
      this.bindRstMemberWalletBank();
    },
    bindRstMemberWalletBank() {
      let params = {
        id: this.row.id,
        bank_no: this.queryFormData.bank_no
      };
      console.log('=>(detail.vue:241) params', params);
      this.$api
        .bindRstMemberWalletBank(params)
        .then(res => {
          console.log('=>(detail.vue:242) res', res);
          this.$Message.success('银行卡更换成功');
          this.cancel();
          this.$emit('refresh');
        })
        .catch(err => this.$Message.error(err.errmsg));
    }
  },

  destroyed() {}
};
</script>

<style scoped lang="less"></style>
