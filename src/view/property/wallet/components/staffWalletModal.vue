<template>
  <Modal
    :value="value"
    :title="row.name"
    width="900px"
    @on-visible-change="changeVisible"
    footer-hide
    class="modal-wrapper"
  >
    <Form inline :label-width="0" @submit.native.prevent @keyup.enter.native="onSearch">
      <div class="no-wrap">
        <FormItem>
          <Input v-model="queryFormData.order_code" placeholder="订单号" />
        </FormItem>

        <FormItem>
          <store-search
            ref="store"
            v-model="queryFormData.clinic_id"
            :isClearable="true"
            placeholder="来源诊所"
          ></store-search>
        </FormItem>

        <FormItem>
          <Input v-model="queryFormData.batch_divide_code" placeholder="结算流水号" />
        </FormItem>

        <FormItem>
          <DatePicker
            type="daterange"
            clearable
            format="yyyy-MM-dd"
            placeholder="结算时间"
            v-model="settle_times"
            @on-change="times => handleTimeChange(times, 'settle_st', 'settle_et')"
          ></DatePicker>
        </FormItem>

        <FormItem style="text-align: left">
          <Button type="primary" @click="onSearch" style="margin-right: 10px">筛选</Button>
          <Button type="default" @click="onResetSearch" style="margin-right: 10px">重置</Button>
        </FormItem>
      </div>
    </Form>

    <div class="panel-nav flex flex-item-between">
      <div>
        <a
          class="nav"
          :class="{ active: queryFormData.divide_status === type_item.id }"
          v-for="(type_item, type_index) in type_desc"
          @click.prevent.capture="onStatusChange(type_item.id)"
          :key="type_index + 'type'"
          >{{ type_item.desc }}
          <Tag v-if="!!type_item.id">{{ divide_status_count_hash[type_item.id] }}</Tag>
        </a>
      </div>
    </div>
    <Table :data="list" :columns="modalColumns" :loading="tableLoading" :height="320">
      <template v-slot:order_code="{ row }">
        <KLink
          :to="{
            path: '/purchase/rst-order/detail',
            query: { orderid: row.relate_info?.order_id, orderType: 'shop_order' }
          }"
          target="_blank"
          >{{ row.relate_info?.order_code }}</KLink
        >
      </template>
      <template v-slot:amount="{ row }"> ￥{{ row.amount }} </template>
      <!-- 付款金额 -->
      <template slot-scope="{ row }" slot="order_amount">
        <div>¥{{ row.order_amount || '0.00' }}</div>
      </template>

      <!-- 分账金额 -->
      <template slot-scope="{ row }" slot="settle_amount">
        <div>+ ¥{{ row.settle_amount || '0.00' }}</div>
      </template>

      <!-- 结算流水号 -->
      <template slot-scope="{ row }" slot="batch_divide_code">
        <div>{{ row.batch_divide_code || '-' }}</div>
      </template>

      <!-- 结算时间 -->
      <template slot-scope="{ row }" slot="settle_at">
        <div>{{ row.settle_at || '-' }}</div>
      </template>
      <!-- 结算时间 -->
      <template slot-scope="{ row }" slot="source_info">
        <div>合伙人：{{ row.cp_name || '-' }}</div>
        <div>诊所：{{ row.clinic_name || '-' }}</div>
      </template>
    </Table>

    <div class="block_20"></div>

    <KPage
      :total="total"
      :pageSizeOpts="[10, 20, 50]"
      :page-size.sync="+queryFormData.pageSize"
      :current.sync="+queryFormData.page"
      @on-change="handleCurrentChange"
      @on-page-size-change="handleSizeChange"
      style="text-align: center"
    />
  </Modal>
</template>

<script>
import storeSearch from '../../../rst/skill/components/store-search.vue';

let init_query_form_data = {
  page: 1,
  pageSize: 10,
  order_code: '', // 订单号
  batch_divide_code: '', // 结算流水号
  st: '', // 交易时间
  et: '', // 交易时间
  settle_st: '', // 结算时间
  settle_et: '', // 结算时间
  divide_status: '' // 交易状态
};

export default {
  name: 'modal',
  mixins: [],

  components: { storeSearch },

  props: {
    value: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      default: 'title'
    },
    // 选中的item
    row: {
      type: Object,
      default: () => {}
    },
    // tab类型
    currentModalType: {
      type: String,
      default: ''
    }
  },

  data() {
    return {
      queryFormData: { ...init_query_form_data },

      modalColumns: [
        { title: '订单号', slot: 'order_code', align: 'center' },
        { title: '业绩提成', slot: 'amount', align: 'center' },
        { title: '结算状态', key: 'divide_status_desc', align: 'center', width: 80 },
        { title: '结算流水号', slot: 'batch_divide_code', align: 'center' },
        { title: '结算时间', slot: 'settle_at', align: 'center', width: 160 },
        { title: '来源信息', slot: 'source_info', align: 'center', width: 160 },
        { title: '分账类型', key: 'mch_bill_type_desc', align: 'center', width: 160 }
      ],

      type_desc: [
        { desc: '全部', id: '' },
        { desc: '待结算', id: '1' },
        { desc: '可结算', id: '2' },
        { desc: '结算中', id: '3' },
        { desc: '已结算', id: '4' }
      ], // tab type
      optionsList: [],
      // 如果当前需求页面有时间组件，并且需要限制选择范围
      disabledTime: {
        disabledDate(date) {
          return date && date.valueOf() > Date.now();
        }
      },
      times: [], // 交易时间
      settle_times: [], // 结算时间

      list: [],
      tableLoading: false,
      total: 0,
      divide_status_count_hash: {}
    };
  },

  computed: {},

  watch: {},

  created() {},

  mounted() {},

  destroyed() {},

  methods: {
    /**
     * 搜索
     * */
    onSearch: function () {
      this.queryFormData.page = 1;
      this.getList();
    },

    /**
     * 重置
     * */
    onResetSearch: function () {
      this.queryFormData = { ...init_query_form_data };
      this.times = [];
      this.settle_times = [];
      this.getList();
    },

    /**
     * 状态切换
     * */
    onStatusChange: function (status) {
      this.queryFormData.page = 1;
      this.queryFormData.divide_status = status;
      this.getList();
    },

    handleSizeChange(val) {
      this.queryFormData.page = 1;
      this.queryFormData.pageSize = val;
      this.getList();
      this.scrollTop();
    },
    handleCurrentChange(val) {
      this.queryFormData.page = val;
      this.getList();
    },

    /**
     * 表格回归顶部
     * */
    scrollTop() {
      let overflowY = this.$el.getElementsByClassName('ivu-table-body')[0];
      if (!overflowY) {
        return;
      }
      overflowY.scrollTop = 0;
    },

    /**
     * 关闭弹窗
     * */
    closeModal() {
      this.queryFormData = { ...init_query_form_data };
      this.times = [];
      this.settle_times = [];
      this.scrollTop();
      this.$emit('input', false);
    },

    /**
     * 弹窗变化
     * */
    changeVisible(flag) {
      if (flag) {
        this.queryFormData.divide_status = this.currentModalType;
        this.onSearch();
      } else {
        this.closeModal();
      }
    },

    /**
     * 当前需求如果有时间选项，用当前方法处理同步formData数据
     * */
    handleTimeChange(times, startTime = 'st', endTime = 'et') {
      if (times) {
        this.queryFormData[startTime] = times[0];
        this.queryFormData[endTime] = times[1];
      } else {
        this.queryFormData[startTime] = '';
        this.queryFormData[endTime] = '';
      }
    },

    /**
     * @description: 获取列表
     * */
    getList() {
      this.list = [];
      this.tableLoading = true;
      let params = {
        wallet_code: this.row.wallet_code,
        ...this.queryFormData
      };
      this.$api
        .getRstMemberBalanceList(params)
        .then(res => {
          this.list = res.list;
          this.total = res.total;
          this.divide_status_count_hash = res.divide_status_count_hash;
        })
        .finally(() => (this.tableLoading = false));
    }
  }
};
</script>

<style scoped lang="less">
::v-deep .ivu-modal-body {
  max-height: 550px;
  min-height: 550px;
  overflow-y: auto;
}

.cursor {
  cursor: pointer;
}

.mb10 {
  margin-bottom: 10px;
}

.no-wrap {
  display: flex;
  flex-wrap: wrap;
}

::v-deep .ivu-form-inline .ivu-input-wrapper,
.ivu-form-inline .ivu-select {
  width: 200px;
}
</style>
