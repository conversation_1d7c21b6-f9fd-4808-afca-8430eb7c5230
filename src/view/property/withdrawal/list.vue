<template>
  <div>
    <Tabs v-model="currentTab" @on-click="changeTabs">
      <TabPane label="提现" name="cash"></TabPane>
      <TabPane label="提现记录" name="cashDetail"></TabPane>
    </Tabs>
    <withdrawal v-if="currentTab==='cash'"></withdrawal>
    <withdrawal-record v-else></withdrawal-record>
  </div>
</template>

<script>
/* eslint-disable */
import S from '@/utils/util' // Some commonly used tools
/* eslint-disable */
import Withdrawal from './components/Withdrawal.vue'
import WithdrawalRecord from './components/WithdrawalRecord.vue'

const init_relate_form_data = {
  editOrderId: '',
  relateVisible: false,
  relateStaffIds: [],
  classType: '',
}
export default {
  name: 'cash-list',
  components: {
    WithdrawalRecord, Withdrawal
  },
  mixins: [],
  data() {
    return {
      relatedModalFormData: {
        ...init_relate_form_data,
      },
      currentTab: 'cash',
    }
  },

  created() {
    this.init()
  },

  methods: {
    init(){
      const {tab} =this.$route.query
      if(tab){
        this.currentTab = tab
      }else {
        this.currentTab = 'cash'
      }
    },
    changeTabs (itemName) {
      this.currentTab = itemName
      this.$router.push({
        query: {
          ...this.$route.query,
          tab: itemName
        }
      })
    },
  },

  beforeRouteUpdate(to,from,next){
    // todo 点击菜单栏tab重置为第一个
    this.$nextTick(() => {
      console.log("-> this.currentTab", this.currentTab);
      this.init()
      // this.currentTab = 'cash'
    })
    next()
  }
}
</script>

<style lang="less" scoped>
</style>
