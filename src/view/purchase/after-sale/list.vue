<template>
  <div>
    <div class="f_order-wrapper">
      <Form class="form-warpper_left" inline @submit.native.prevent @keyup.enter.native="onSearch">
        <Row>
          <FormItem>
            <Input type="text" v-model="queryFormData.keyword" placeholder="售后编号/采购单号" />
          </FormItem>

          <FormItem>
            <div class="flex">
              <surning-search
                ref="nursing-search"
                placeholder="请输入搜索申请主体"
                v-model="queryFormData.ent_code"
                :type="queryFormData.ent_type"
                :typeList="typeList"
                @getType="type => (queryFormData.ent_type = type)"
                :isClearable="true"
                :company_id="queryFormData.company_id"
                :showOutside="true"
                :show-type="true"
              ></surning-search>
            </div>
          </FormItem>

          <FormItem>
            <clinic-type-search
              v-model="queryFormData.clinic_type"
              :clinic_direct_type="queryFormData.clinic_direct_type"
              @directChange="directChange"
            ></clinic-type-search>
          </FormItem>

          <FormItem>
            <Select v-model="queryFormData.refund_type" style="" placeholder="售后类型" clearable>
              <Option :value="item.id" v-for="(item, index) in refundTypeDesc" :key="index">{{ item.desc }}</Option>
            </Select>
          </FormItem>
          <FormItem>
            <Select v-model="queryFormData.order_type" style="" placeholder="采购单类型" clearable>
              <Option :value="item.id" v-for="(item, index) in orderTypeDesc" :key="index">{{ item.desc }}</Option>
            </Select>
          </FormItem>
        </Row>
        <Row>
          <FormItem>
            <Select v-model="queryFormData.change_state" style="" placeholder="换货情况" clearable>
              <Option :value="item.id" v-for="(item, index) in changeStateDesc" :key="index">{{ item.desc }}</Option>
            </Select>
          </FormItem>
          <FormItem>
            <DatePicker
              type="daterange"
              clearable
              format="yyyy-MM-dd"
              placeholder="创建时间"
              v-model="createTimeRange"
              @on-change="times => handleTimeChange(times, 'create_st', 'create_et')"
              class="time-range"
            ></DatePicker>
          </FormItem>

          <FormItem>
            <DatePicker
              type="daterange"
              clearable
              format="yyyy-MM-dd"
              placeholder="完成时间"
              v-model="finishTimeRange"
              @on-change="times => handleTimeChange(times, 'finished_st', 'finished_et')"
              class="time-range"
            ></DatePicker>
          </FormItem>
          <FormItem>
            <Input type="text" v-model="queryFormData.goods_name" placeholder="商品名称" />
          </FormItem>
          <FormItem>
            <Button type="primary" class="space6" @click="onSearch">筛选</Button>
            <Button class="space6" @click="onResetSearch">重置</Button>
            <Button class="space6" :loading="downloadLoading" type="default" @click="downloadExcel(queryFormData)"
              >导出</Button
            >
          </FormItem>
        </Row>
      </Form>
    </div>

    <div class="table-wrapper">
      <div class="panel-nav">
        <a class="nav" :class="{ active: !$route.query.status }" @click.prevent.capture="onStatusChange('')"> 全部 </a>
        <a
          class="nav"
          v-for="(item, index) in statusDesc"
          :class="{ active: $route.query.status == item.id }"
          :key="index"
          @click.prevent.capture="onStatusChange(item.id)"
        >
          {{ item.desc }}
          <Tag :color="getTagColor(item.id)">{{ status_count[item.id] }}</Tag>
        </a>
      </div>
      <Table
        :loading="tableLoading"
        :columns="tableCols"
        :data="list"
        stripe
        :height="$store.state.app.clientHeight - 390"
      >
        <template slot-scope="{ row }" slot="ent_info">
          <div>名称：{{ row.name || '-' }}</div>
          <div>类型：{{ row.ent_type_text }}</div>
          <template v-if="row.ent_type === 'CLI'">
            <div>
              诊所类型：
              <span v-if="row.clinic_direct_type_text"
                >{{ row.clinic_direct_type_text }} / {{ row.clinic_type_text }}</span
              >
              <span v-else>-</span>
            </div>
          </template>
        </template>
        <template slot="organization_name" slot-scope="{ row }">
          {{ row.organization_name || '-' }}
        </template>
        <template slot-scope="{ row }" slot="purchase_info">
          <div style="text-align: left">
            <div>
              单号：
              <KLink
                v-if="row.source === 'PMS'"
                :to="{ path: '/purchase/clinic/detail', query: { order_code: row.order_code } }"
                target="_blank"
              >
                {{ row.order_code }}
              </KLink>
              <KLink
                v-else-if="row.source !== 'PMS'"
                :to="{ path: '/purchase/order/detail', query: { pur_code: row.order_code } }"
                target="_blank"
              >
                {{ row.order_code }}
              </KLink>
              <span v-else>-</span>
            </div>
            <div>类型：{{ row.purchase_type_text }}</div>
          </div>
        </template>
        <template slot-scope="{ row }" slot="goods_name">
          <div style="text-align: left">
            <div>
              <Tooltip max-width="200" :content="row.goods_name">
                <div class="ecs ecs-2">商品：{{ row.goods_name }}</div>
              </Tooltip>
            </div>
            <div v-for="field in row.labels" :key="field.key">{{ field.key }}：{{ field.value || '-' }}</div>
            <!--            <div>数量：{{ row.num }}</div>-->
            <!--            <div>退款金额：¥ {{ row.cli_receive_money }}</div>-->
            <!--            <div>省公司退款：¥ {{ row.com_refund_money }}</div>-->
          </div>
        </template>

        <template slot-scope="{ row }" slot="create_time">
          {{ row.create_time | date_format }}
        </template>

        <template slot-scope="{ row }" slot="finished_time">
          {{ row.finished_time | date_format }}
        </template>

        <template slot-scope="{ row }" slot="action">
          <div class="action-box">
            <a v-if="row.status == 'COM_AUDIT'" @click="jump(row, 'audit')">审核</a>
            <a v-if="canExpress(row)" type="primary" @click="jump(row)">填写退货物流</a>
            <a v-if="canEditBtn(row)" @click="jump(row)">撤销申请</a>
            <a @click="jump(row)">详情</a>
          </div>
        </template>
      </Table>
    </div>
    <div class="block_20"></div>

    <KPage
      :total="total"
      :page-size="+queryFormData.pageSize"
      :current="+queryFormData.page"
      @on-change="handleCurrentChange"
      @on-page-size-change="handleSizeChange"
      style="text-align: center"
    />
  </div>
</template>

<script>
import S from 'utils/util'; // Some commonly used tools
import search from '@/mixins/search';
import surningSearch from '@/components/surning-search/surning-search';
import renderHeader from '@/mixins/renderHeader';
import downloadExcel from '@/mixins/downloadExcel';
import clinicTypeSearch from '@/components/remote-search/clinic-type-search.vue';
let init_query_form_data = {
  page: 1,
  pageSize: 20,
  keyword: '', // 申请单号/采购单号
  goods_name: '', // 商品名称
  status: '', // 状态
  cp_id: '', // 合伙人ID
  refund_type: '', // 退款类型
  ent_code: '',
  ent_type: '',
  order_type: '', // 采购单类型
  change_state: '', //换货情况
  clinic_type: '', // 诊所类型
  clinic_direct_type: '', // 诊所子类型
  create_st: '',
  create_et: '',
  finished_st: '',
  finished_et: ''
};

export default {
  name: 'list',
  components: {
    surningSearch,
    clinicTypeSearch
  },
  mixins: [search, renderHeader, downloadExcel],

  data() {
    return {
      queryFormData: { ...init_query_form_data },
      apiName: 'getOrderRefundList',
      tableCols: [
        { title: '售后单号', key: 'apply_code', align: 'left', minWidth: 80 },
        { title: '申请主体', slot: 'ent_info', align: 'left', minWidth: 200 },
        { title: '营业执照名称', slot: 'organization_name', align: 'center', width: 120 },
        { title: '采购单信息', slot: 'purchase_info', align: 'left', minWidth: 180 },
        { title: '售后类型', key: 'refund_type_text', align: 'left', minWidth: 80 },
        {
          title: '换货情况',
          key: 'change_state_desc',
          align: 'center',
          minWidth: 100,
          renderHeader: (h, params) => this._renderHeader(h, params, '系统暂不支持换货功能，这里仅起到标记作用。')
        },
        { title: '退款商品', slot: 'goods_name', align: 'left', minWidth: 200 },
        { title: '状态', key: 'status_text', align: 'left', minWidth: 120 },
        { title: '创建时间', slot: 'create_time', align: 'left', width: 140 },
        { title: '完成时间', slot: 'finished_time', align: 'left', width: 140 },
        { title: '操作', slot: 'action', align: 'center', width: 120, fixed: 'right' }
      ],
      typeList: [
        { label: '诊所', value: 'CLI' },
        { label: '直营运营中心', value: 'OPC' },
        { label: '外部公司', value: 'OUTSIDE' },
        { label: '个人客户', value: 'PERSON_C' }
      ],
      tableLoading: false,
      list: [],
      total: 0,
      statusDesc: [], // 发货状态
      status_count: {
        WAIT_AUDIT: 0,
        WAIT_EDIT: 0,
        WAIT_PAY: 0,
        WAIT_SHIP: 0,
        HAS_SHIP: 0,
        FINISHED: 0,
        CLOSED: 0
      },
      typeDesc: [], // 订单类型
      refundTypeDesc: [], // 售后类型枚举
      orderTypeDesc: [],
      changeStateDesc: [], // 换货情况
      downloadApiName: 'getOrderrefundExportList',

      createTimeRange: [],
      finishTimeRange: []
    };
  },
  computed: {
    getTagColor(type) {
      return type => {
        switch (type) {
          case 'SUP_AUDIT': // 待供应商审核
          case 'PLAT_AUDIT': // 待平台审核
          case 'WAIT_RETURN': // 待买家退货
          case 'WAIT_RECEIVE': // 待商家收货
          case 'WAIT_REFUND': // 等待退款
            return 'warning';
          case 'WAIT_SHIP': // 待发货
            return 'primary';
          case 'COM_AUDIT': // 待发货
            return 'error';
          case 'HAS_SHIP': // 已发货
          case 'FINISHED': // 已完成
            return 'success';
          default: // 已取消
            return 'default';
        }
      };
    },
    canEditBtn() {
      return row => {
        let statusList = ['PLAT_AUDIT', 'WAIT_RETURN'];
        let typeList = ['OPC_OWN', 'OPC_PERSONAL', 'OPC_OUTSIDE'];
        return typeList.includes(row.order_type) && statusList.includes(row.status);
      };
    },
    canExpress() {
      return row => {
        let statusList = ['WAIT_RETURN'];
        let typeList = ['OPC_OWN', 'OPC_PERSONAL', 'OPC_OUTSIDE'];
        return typeList.includes(row.order_type) && statusList.includes(row.status);
      };
    }
  },
  created() {
    this.getOrderRefundOptions();
    this.queryFormData = S.merge(this.queryFormData, this.$route.query);
    this.submitQueryForm(true);
  },

  methods: {
    directChange(val) {
      this.$set(this.queryFormData, 'clinic_direct_type', val);
    },
    // 进入详情
    jump(row, action) {
      let query = {
        apply_code: row.apply_code,
        action
      };
      if (row.source === 'PMS') {
        this.$router.push({
          path: '/purchase/after-sale/online-detail',
          query
        });
      } else {
        this.$router.push({
          path: '/purchase/after-sale/offline-detail',
          query
        });
      }
    },
    onStatusChange(status) {
      console.log('-> status', status);
      this.queryFormData.page = 1;
      this.queryFormData.status = status;
      this.submitQueryForm();
    },
    onResetSearch() {
      this.queryFormData = { ...init_query_form_data };
      this.timeRange = [];
      this.createTimeRange = [];
      this.finishTimeRange = [];
      this.submitQueryForm();
      this.$refs['nursing-search'].clear();
    },
    handlerListData(data) {
      this.status_count = data.status_count;
    },

    getOrderRefundOptions() {
      this.$api
        .getOrderRefundOptions()
        .then(res => {
          this.statusDesc = S.descToArrHandle(res.statusDesc);
          this.refundTypeDesc = S.descToArrHandle(res.refundTypeDesc);
          this.orderTypeDesc = S.descToArrHandle(res.orderTypeDesc);
          this.changeStateDesc = S.descToArrHandle(res.changeStateDesc);
        })
        .catch(err => this.$Message.error(err.errmsg));
    },

    goOrderDetail(row) {
      this.$router.push({ path: '/purchase/clinic/detail', query: { order_code: row.order_code } });
    }
  },
  beforeRouteUpdate(to, from, next) {
    this.queryFormData = S.merge(init_query_form_data, to.query);
    this.getTimeRange('create_st', 'create_et', 'createTimeRange');
    this.getTimeRange('finished_st', 'finished_et', 'finishTimeRange');
    this.loadList();
    next();
  }
};
</script>

<style lang="less" scoped>
::v-deep .ivu-tooltip {
  display: flex;
}
.action-box {
  a {
    display: block;
  }
}
</style>
