<template>
  <div class="detail-wrapper">
    <div class="block-header"><span>订单详情</span></div>
    <div class="basic-info">
      <div class="basic-info-item">
        <span class="item-label">售后单号</span>
        <span class="item-content">{{ echoData.apply_code || '-' }}</span>
      </div>

      <div class="basic-info-item">
        <span class="item-label">售后状态</span>
        <span class="item-content"
          >{{ echoData.status_text || '-' }}
          <span
            v-if="
              echoData.status === 'COM_REJECT_CLOSE' ||
              echoData.status === 'SUP_REJECT_CLOSE' ||
              echoData.status === 'PLAT_REJECT_CLOSE'
            "
            style="color: red"
            >(驳回原因：{{ echoData.reject_reason }})</span
          >
        </span>
      </div>

      <div class="basic-info-item">
        <span class="item-label">采购单号</span>
        <span class="item-content">
          <KLink
            v-if="echoData.order_code"
            :to="{ path: '/purchase/order/detail', query: { pur_code: echoData.order_code } }"
            target="_blank"
            >{{ echoData.order_code }}</KLink
          >
          <span v-else>-</span>
        </span>
      </div>

      <div class="basic-info-item">
        <span class="item-label">采购单类型</span>
        <span class="item-content">{{ echoData.purchase_type_text || '-' }}</span>
      </div>
      <div class="basic-info-item">
        <span class="item-label flex flex-item-center">开票状态</span>
        <div class="item-content">
          <div v-for="(item, index) in echoData.invoice_list" :key="index">
            {{ item.desc }}: {{ item.status_text }}
            <span v-show="item.status === 'FINISHED' || item.status === 'PART_FINISHED'"
              >（{{ item.type_text }} 编号：{{ item.invoice_no }}）</span
            >
          </div>
          <div v-show="echoData.invoice_list && echoData.invoice_list.length === 0">-</div>
        </div>
      </div>

      <div class="basic-info-item">
        <span class="item-label">申请主体</span>
        <span class="item-content">{{ echoData.name || '-' }}</span>
      </div>
      <div class="basic-info-item" v-if="echoData.ent_type === 'CLI'">
        <span class="item-label">诊所类型</span>
        <span class="item-content">
          <span v-if="echoData.clinic_direct_type_text"
            >{{ echoData.clinic_direct_type_text }}/{{ echoData.clinic_type_text }}</span
          >
          <span v-else>-</span>
        </span>
      </div>
      <div class="basic-info-item">
        <span class="item-label">营业执照名称</span>
        <span class="item-content">{{ echoData.organization_name || '-' }}</span>
      </div>
      <div class="basic-info-item">
        <span class="item-label">主体编号</span>
        <span class="item-content">{{ echoData.code || '-' }}</span>
      </div>

      <div class="basic-info-item">
        <span class="item-label">主体类型</span>
        <span class="item-content">{{ echoData.ent_type_text || '-' }}</span>
      </div>

      <div class="basic-info-item">
        <span class="item-label">负责人</span>
        <span class="item-content">{{ echoData.leading_person || '-' }}</span>
      </div>

      <div class="basic-info-item">
        <span class="item-label">负责人手机号</span>
        <span class="item-content">{{ echoData.leading_mobile || '-' }}</span>
      </div>

      <div class="basic-info-item">
        <span class="item-label">申请类型</span>
        <span class="item-content"
          >{{ echoData.refund_type_text || '-' }}
          <span v-if="echoData.refund_type === 'GM' && echoData.delivery_way"
            >({{ echoData.delivery_way === 'HAS' ? '已收到货' : '未收到货' }})</span
          >
        </span>
      </div>

      <div class="basic-info-item">
        <span class="item-label">退款金额</span>
        <span class="item-content">¥ {{ echoData.refund_money }}</span>
      </div>
      <div class="basic-info-item" v-if="is_com_refund(echoData.order_type)">
        <span class="item-label">直营运营中心退款金额</span>
        <span class="item-content">¥ {{ echoData.com_refund_money }}</span>
      </div>
      <div class="basic-info-item">
        <span class="item-label">上海树家退款金额</span>
        <span class="item-content">¥ {{ echoData.sj_refund_money }}</span>
      </div>

      <div class="basic-info-item" v-if="showExchange">
        <span class="item-label"
          >换货情况
          <Tooltip
            class="item"
            effect="dark"
            content="系统暂不支持换货功能，这里仅起到标记作用。"
            maxWidth="200"
            placement="top"
          >
            <Icon type="md-help-circle" class="cursor-pointer" color="" size="16"></Icon> </Tooltip
        ></span>
        <span class="item-content">{{ echoData.change_state_desc || '-' }}</span>
      </div>

      <div class="basic-info-item">
        <span class="item-label">售后原因</span>
        <span class="item-content">{{ echoData.refund_reason || '-' }}</span>
      </div>

      <div class="basic-info-item">
        <span class="item-label">售后说明</span>
        <span class="item-content">{{ echoData.refund_desc || '-' }}</span>
      </div>

      <div class="basic-info-item">
        <span class="item-label flex flex-item-center">附件</span>
        <span class="item-content">
          <viewer
            v-if="echoData.refund_images.length"
            :images="echoData.refund_images"
            class="flex flex-item-align"
            style="margin: 10px 10px 10px 30px"
          >
            <img
              style="width: 40px; height: 40px; margin-right: 10px"
              v-for="(item, index) in echoData.refund_images"
              :key="index"
              :src="item"
              alt="附件"
              class="image cursor-pointer scale"
            />
          </viewer>
          <div v-else>-</div>
        </span>
      </div>

      <div class="basic-info-item" v-if="echoData.refund_type !== 'M'">
        <span class="item-label flex flex-item-center">退货地址信息</span>
        <div class="item-content">
          <div>收货人：{{ echoData.consignee || '-' }}</div>
          <div>联系电话：{{ echoData.mobile || '-' }}</div>
          <div>收货地址：{{ echoData.address_text || '-' }}</div>
        </div>
      </div>

      <div class="basic-info-item">
        <span class="item-label">申请时间</span>
        <span class="item-content">{{ echoData.create_time | date_format }}</span>
      </div>

      <div class="basic-info-item">
        <span class="item-label">完成时间</span>
        <span class="item-content">{{ echoData.finished_time | date_format }}</span>
      </div>
    </div>

    <div class="goods-list">
      <div class="panel-nav flex flex-item-between">
        <div>
          <a
            :class="{ active: activeGoodsType === item.type }"
            v-for="item in goodsTypeList"
            :key="item.type"
            class="nav"
            @click.prevent.capture="goodsTabChange(item.type)"
          >
            {{ item.name }}
          </a>
        </div>
      </div>

      <div class="table-wrapper">
        <Table
          v-show="activeGoodsType === 0"
          :columns="tableColumns"
          :data="goodsList"
          :loading="tableLoading"
          :show-summary="true"
          :summary-method="handleSummary"
          stripe
          border
        >
          <template slot-scope="{ row, index }" slot="img">
            <viewer :images="[row.img]" v-if="row.img" class="flex flex-item-center">
              <p v-if="row.img" class="flex flex-item-center">
                <img style="width: 60px; height: auto; margin: 10px 0" :src="row.img | imageStyle" class="image" />
              </p>
            </viewer>
            <span v-else>-</span>
          </template>

          <!-- 类型 -->
          <template slot="type_text" slot-scope="{ row }">
            {{ row.type_text || '-' }}
          </template>
          <!-- 规格 -->
          <template slot="spec" slot-scope="{ row }">
            {{ row.spec || '-' }}
          </template>
          <!-- 单位 -->
          <template slot="unit" slot-scope="{ row }">
            {{ row.unit || '-' }}
          </template>
          <!-- 单位 -->
          <template slot="supplier" slot-scope="{ row }">
            {{ row.supplier || '-' }}
          </template>
          <!-- 销售商 -->
          <template slot="seller" slot-scope="{ row }">
            <span v-if="row.seller">{{ row.seller }}</span>
            <span v-else>-</span>
          </template>

          <!-- 采购单价 -->
          <template slot="goods_price" slot-scope="{ row }">
            <span v-if="row.goods_price">￥{{ row.goods_price }}</span>
            <span v-else>-</span>
          </template>
          <!-- 退款金额 -->
          <template slot="refund_money" slot-scope="{ row }">
            <span v-if="row.refund_money">￥{{ row.refund_money }}</span>
            <span v-else>-</span>
          </template>
          <!-- 省公司退款金额 -->
          <template slot="com_refund_money" slot-scope="{ row }">
            <span v-if="row.com_refund_money">￥{{ row.com_refund_money }}</span>
            <span v-else>-</span>
          </template>
          <!-- 省公司退款金额 -->
          <template slot="sj_refund_money" slot-scope="{ row }">
            <span v-if="row.sj_refund_money">￥{{ row.sj_refund_money }}</span>
            <span v-else>-</span>
          </template>
        </Table>
        <!-- 退货物流 -->
        <Table
          v-show="activeGoodsType === 1 && echoData.refund_type !== 'M'"
          :columns="logisticsColumns"
          :data="logisticsList"
          :loading="tableLoading"
          border
        >
          <!-- 物流单号 -->
          <template slot="express_desc" slot-scope="{ row }">
            <div class="flex flex-item-between flex-item-align" style="text-align: left; padding: 0 12px">
              <div v-if="row.express_desc.length > 0">
                <div v-for="(item, index) in row.express_desc" :key="index">
                  {{ item.express_name }}：{{ item.express_no }}
                </div>
              </div>
              <a v-if="canEditExpress()" @click="editReturnLogistics">修改退货物流</a>
            </div>
            <div v-show="row.express_desc.length === 0 && !canEditExpress()">-</div>
          </template>
          <!-- 备注与凭证 -->
          <template v-slot:remark_images="{ row }">
            <div style="text-align: left">
              <span>{{ row.prove_remark }}</span>
              <viewer
                :images="[row.prove_images]"
                v-if="row.prove_images"
                class="flex flex-wrap"
                style="text-align: left; padding-bottom: 10px"
              >
                <img
                  style="width: 40px; height: 40px; margintop: 10px"
                  v-for="(prove_images_item, prove_images_index) in row.prove_images"
                  :key="'refund_images' + prove_images_index"
                  :src="prove_images_item | imageStyle"
                  class="mr10 cursor-pointer"
                />
              </viewer>
            </div>
            <span v-show="!row.prove_remark && row.prove_images.length === 0">-</span>
          </template>
          <!-- 录入时间 -->
          <template slot="prove_time" slot-scope="{ row }">
            {{ row.prove_time | date_format }}
          </template>
        </Table>
      </div>
    </div>

    <!--  操作记录  -->
    <div class="goods-list-title mt20">
      <h3 class="f-title title">操作记录</h3>
    </div>
    <Table :columns="operatorColumns" :data="operatorRecordData" border>
      <template slot="create_time" slot-scope="{ row }">
        {{ row.create_time | date_format }}
      </template>
      <template slot="source" slot-scope="{ row }">
        {{ row.source || '-' }}
      </template>
      <template slot="operator_name" slot-scope="{ row }">
        <span v-if="row.operator_name"
          >{{ row.operator_name }}<span v-if="row.operator_role_name">({{ row.operator_role_name }})</span></span
        >
        <span v-else>-</span>
      </template>
      <template slot="content_describe" slot-scope="{ row }">
        {{ row.content_describe }}
      </template>
    </Table>

    <div class="fixed-bottom-wrapper">
      <!--      <Button class="mr10" @click="$router.back()">返回</Button>-->
      <back-button class="mr10"></back-button>
      <Button
        class="mr10"
        v-if="$route.query.action === 'audit' && echoData.status === 'COM_AUDIT'"
        type="error"
        @click="refuseVisible = true"
        >审核驳回
      </Button>
      <Button
        class="mr10"
        v-if="$route.query.action === 'audit' && echoData.status === 'COM_AUDIT'"
        type="primary"
        @click="pass"
        >审核通过
      </Button>
      <Button class="mr10" v-if="canEditBtn()" type="default" @click="revocation">撤销售后</Button>
      <Button class="mr10" v-if="canExpress()" type="primary" @click="entryExpress">商品已寄回，填写退货物流</Button>
    </div>

    <!--  审核驳回  -->
    <refuse-modal v-model="refuseVisible" @ok="refuseEvent"></refuse-modal>
    <!--  审核通过  -->
    <!-- <confirm-modal :confirmVisible.sync="confirmVisible" content="确定要通过审核吗？" content-text="" @ok="passEvent"> -->
    <confirm-modal
      :confirmVisible.sync="confirmVisible"
      content="温馨提示"
      content-text="确定要通过审核吗？"
      @ok="passEvent"
    >
      <template v-slot:header>
        <div class="flex flex-item-end">
          <a @click="refundModalVisible = true">退款去向说明</a>
        </div>
      </template>
      <div v-if="showExchange">
        <KWidget label="换货情况" required style="margin-top: 20px; align-items: center">
          <RadioGroup v-model="change_state" class="radio-wrapper">
            <Radio label="NONE">无需换货</Radio>
            <Radio label="NEED">换货</Radio>
          </RadioGroup>
        </KWidget>
        <KWidget :label-width="40" style="margin-top: 20px">
          <div style="color: #999" class="flex">
            <div>注：</div>
            <div>
              <div>1 系统暂不支持换货功能，这里仅起到标记作用。</div>
              <div>
                2
                选择无需换货或换货，将决定部分主体的退款是否退到其预付款余额。具体规则，请点击查看右上角的“退款去向说明”。
              </div>
            </div>
          </div>
        </KWidget>
      </div>
    </confirm-modal>
    <!--  退货物流  -->
    <return-logistics
      v-model="returnLogisticsVisible"
      :apply_code="echoData.apply_code"
      :packs="returnLogisticsList"
      :delivery_way="echoData.delivery_way"
      @refresh="getOrderRefundDetail"
    ></return-logistics>
    <!--  撤销售后  -->
    <confirm-modal
      :confirmVisible.sync="revocationVisible"
      content="撤销售后"
      content-text="确定要撤销售后吗"
      @ok="revocationOrderRefund"
    ></confirm-modal>
    <!--    温馨提示-->
    <tips-modal
      v-model="tipsVisible"
      :show-cancel="false"
      confirm-text="好的"
      :contentText="contentText"
      @onOk="okTips"
    >
    </tips-modal>
    <refund-explain-modal v-model="refundModalVisible"></refund-explain-modal>
  </div>
</template>

<script>
// import { CodeToText, regionData, TextToCode } from '@/utils/chinaMap';
import renderHeader from '@/mixins/renderHeader';
import refuseModal from '@/components/refuseModal/refuseModal';
import confirmModal from '@/components/confirmModal/confirmModal';
import ReturnLogistics from './components/return-logistics';
import TipsModal from '@/components/confirmModal/TipsModal';
import { $operator } from '@/utils/operation';
import refundExplainModal from '../components/refundExplainModal.vue';
const init_order_form_data = {
  remark: '',
  consignee_info: {
    consignee: '',
    mobile: '',
    address: {
      prov: {
        name: '',
        code: ''
      },
      city: {
        name: '',
        code: ''
      },
      county: {
        name: '',
        code: ''
      },
      detail: ''
    }
  }
};
export default {
  name: 'detail',
  components: {
    refuseModal,
    confirmModal,
    ReturnLogistics,
    TipsModal,
    refundExplainModal
  },
  mixins: [renderHeader],
  data() {
    return {
      formData: {
        ...init_order_form_data
      },
      echoData: {
        apply_code: '',
        apply_ent_type: '',
        apply_ent_type_text: '',
        cli_receive_money: '',
        code: '',
        com_refund_money: '',
        company_name: '',
        create_time: '',
        finished_time: '',
        leading_mobile: '',
        leading_person: '',
        name: '',
        order_code: '',
        purchase_type_text: '',
        refund_desc: '',
        refund_reason: '',
        refund_type: '',
        refund_type_text: '',
        status: '',
        status_text: '',
        refund_images: [],
        change_state_desc: ''
      }, // 基础信息

      // 售后商品
      tableColumns: [],
      tableCommonColumns: [
        { title: '编号', key: 'sku_code', align: 'center' },
        { title: '商品名', key: 'goods_name', align: 'center' },
        { title: '介绍图', slot: 'img', key: 'img', align: 'center', minWidth: 40 },
        { title: '类型', slot: 'type_text', key: 'type_text', align: 'center' },
        { title: '规格', slot: 'spec', key: 'spec', align: 'center' },
        { title: '单位', slot: 'unit', key: 'unit', align: 'center' },
        { title: '供应商', slot: 'supplier', key: 'supplier', align: 'center' },
        { title: '销售商', slot: 'seller', key: 'seller', align: 'center' },
        { title: '采购单价', key: 'goods_price', slot: 'goods_price', align: 'center' },
        { title: '售后数量', key: 'num', align: 'center', showTotal: true }
      ],
      tableTypeColumns: [
        {
          title: '退款金额',
          slot: 'refund_money',
          key: 'refund_money',
          align: 'center',
          showTotal: true,
          isMoney: true,
          type: 'all'
        },
        {
          title: '直营运营中心退款金额',
          slot: 'com_refund_money',
          key: 'com_refund_money',
          align: 'center',
          showTotal: true,
          isMoney: true,
          type: ['OPC_OWN', 'OPC_PERSONAL', 'OPC_OUTSIDE']
        },
        {
          title: '上海树家退款金额',
          slot: 'sj_refund_money',
          key: 'sj_refund_money',
          align: 'center',
          showTotal: true,
          isMoney: true,
          type: 'all'
        }
      ],
      goodsList: [],
      tableLoading: false,

      // 退货物流
      logisticsColumns: [
        { title: '售后商品', key: 'goods_text', align: 'center' },
        { title: '物流单号', slot: 'express_desc', align: 'center' },
        { title: '备注与凭证', slot: 'remark_images', align: 'center' },
        { title: '录入时间', slot: 'prove_time', align: 'center' }
      ],
      logisticsList: [],
      tipVisible: false,

      // 操作记录
      operatorColumns: [
        { title: '时间', slot: 'create_time', align: 'center' },
        { title: '操作主体', slot: 'source', align: 'center' },
        { title: '操作人', slot: 'operator_name', align: 'center' },
        { title: '操作记录', slot: 'content_describe', align: 'center' }
      ],
      operatorRecordData: [],

      refuseVisible: false, // 审核驳回弹窗
      confirmVisible: false, // 审核通过弹窗
      activeGoodsType: 0,
      goodsTypeList: [{ type: 0, name: '售后商品' }],
      goodsTypeAllList: [
        { type: 0, name: '售后商品' },
        { type: 1, name: '退货物流' }
      ],
      returnLogisticsVisible: false,
      apply_code: '',
      returnLogisticsList: [],
      revocationVisible: false,
      tipsVisible: false,
      contentText: '',
      change_state: '', //
      orderType: '',
      refundModalVisible: false
    };
  },
  computed: {
    showExchange() {
      let orderTypes = ['CLI_ADDITION', 'OPC_OWN', 'OPC_PERSONAL', 'OPC_OUTSIDE'];
      return orderTypes.includes(this.orderType);
    },
    /**
     * 采购单类型 - 诊所开业采购单
     * const TYPE_CLI_OPENING = 'CLI_OPENING';
     * 采购单类型 - 榕小家开业采购单
     * const TYPE_RXJ_OPENING = 'RXJ_OPENING';
     * 采购单类型 - 诊所二次采购单
     * const TYPE_CLI_ADDITION = 'CLI_ADDITION';
     * 采购单类型 - 榕小家二次采购单
     * const TYPE_RXJ_ADDITION = 'RXJ_ADDITION';
     * 采购单类型 - 省公司采购自用采购单(省公司自采单, 没有诊所价格)
     * const TYPE_OPC_OWN = 'OPC_OWN';
     * 采购单类型 - 省公司对个人销售(含秒杀，会议等活动)采购单[商品售价支持修改]
     * const TYPE_OPC_PERSONAL = 'OPC_PERSONAL';
     * 采购单类型 - 省公司对外部公司销售采购单
     * const TYPE_OPC_PERSONAL = 'OPC_OUTSIDE';
     * 采购单类型 - 活动秒杀
     * const TYPE_PROMOTION_ACT_SECKILL = 'P_ACT_SECKILL';
     * 采购单类型 - 会议秒杀
     * const TYPE_PROMOTION_MEET_SECKILL = 'P_M_SECKILL';
     * 采购单类型 - 会议门票
     * const TYPE_PROMOTION_MEETING = 'P_MEETING';
     */
    // 是否有省公司退款金额
    is_com_refund() {
      return status => {
        // 存在省公司退款的单子类型
        let com_type = ['OPC_PERSONAL', 'OPC_OUTSIDE', 'OPC_OWN', 'P_MEETING_OPC'];
        return com_type.includes(status); // 会议门票为省公司代采时展示
      };
    },
    canEditBtn() {
      return () => {
        let statusList = ['PLAT_AUDIT', 'WAIT_RETURN'];
        let typeList = ['OPC_OWN', 'OPC_PERSONAL', 'OPC_OUTSIDE'];
        return typeList.includes(this.echoData.order_type) && statusList.includes(this.echoData.status);
      };
    },
    canExpress() {
      return () => {
        let statusList = ['WAIT_RETURN'];
        let typeList = ['OPC_OWN', 'OPC_PERSONAL', 'OPC_OUTSIDE'];
        return typeList.includes(this.echoData.order_type) && statusList.includes(this.echoData.status);
      };
    },
    canEditExpress() {
      return () => {
        let statusList = ['WAIT_RECEIVE'];
        let typeList = ['OPC_OWN', 'OPC_PERSONAL', 'OPC_OUTSIDE'];
        return typeList.includes(this.echoData.order_type) && statusList.includes(this.echoData.status);
      };
    }
  },
  watch: {},
  created() {},
  mounted() {
    console.log(this.$refs.refundPoptip);
    this.$router.onReady(() => {
      this.tableColumns = this.tableCommonColumns;
      this.getOrderRefundDetail();
    });
  },
  methods: {
    refuseEvent(reason) {
      this.review(0, reason);
    },
    passEvent() {
      if (!this.change_state && this.showExchange) {
        this.$Message.error('请选择是否换货');
        return;
      }
      this.review(1);
    },
    pass() {
      this.confirmVisible = true;
    },
    getOrderRefundDetail() {
      let params = { apply_code: this.$route.query.apply_code };
      console.log('=>(detail.vue:241) params', params);
      this.$api
        .getOrderRefundDetail(params)
        .then(res => {
          this.echoData = res.order;
          this.goodsList = res.list;
          this.operatorRecordData = res.log;
          this.logisticsList = res.packs;
          this.typeCountComputed();
          this.orderType = res.order.order_type;
          // 如果当前状态为退货退款，追加上退货物流的tab
          if (this.echoData.refund_type !== 'M') {
            this.goodsTypeList = this.goodsTypeAllList;
          } else {
            this.goodsTypeList = [this.goodsTypeAllList[0]];
          }
        })
        .catch(err => this.$Message.error(err.errmsg));
    },
    // 合计
    handleSummary({ columns, data }) {
      const sums = {};
      columns.forEach((column, index) => {
        const key = column.key;
        if (index === 0) {
          sums[key] = {
            key,
            value: '合计'
          };
        } else {
          if (column.showTotal) {
            const values = data.map(item => Number(item[key] || 0));
            if (!values.every(value => isNaN(value))) {
              const v = values.reduce((prev, curr) => {
                // const value = Number( curr ).toFixed( 2 )
                if (!isNaN(curr)) {
                  return $operator.add(Number(prev), Number(curr));
                } else {
                  return $operator.add(Number(prev), 0);
                }
              }, 0);
              sums[key] = {
                key,
                value: `${column.isMoney ? '￥' : ''}${v}`
              };
            } else {
              sums[key] = {
                key,
                value: ''
              };
            }
          } else {
            sums[key] = {
              key,
              value: ''
            };
          }
        }
      });
      console.log('-> sums', sums);
      return sums;
    },

    review(status, reason) {
      let params = {
        apply_code: this.$route.query.apply_code,
        status
      };
      if (!status) {
        params.reason = reason;
      } else {
        if (!this.change_state && this.showExchange) {
          this.$Message.error('请选择是否换货');
          return;
        }
        params.change_state = this.change_state;
      }
      this.$api
        .changeOrderRefundAudit(params)
        .then(res => {
          if (status) {
            this.$Message.success('审核通过');
          } else {
            this.$Message.success('审核驳回');
          }
          this.change_state = '';
          this.confirmVisible = false;
          this.getOrderRefundDetail();
        })
        .catch(err => this.$Message.error(err.errmsg));
    },

    goOrderDetail() {
      this.$router.push({ path: '/purchase/clinic/detail', query: { order_code: this.echoData.order_code } });
    },

    goodsTabChange(type) {
      this.activeGoodsType = type;
    },

    // 线下订单根据不同的类型，展示不同退款金额
    typeCountComputed() {
      let filterTableColumns = this.tableTypeColumns.filter(item => {
        if (item.type == 'all' || (Array.isArray(item.type) && item.type.includes(this.echoData.order_type))) {
          return true;
        }
      });
      this.tableColumns = [...this.tableCommonColumns, ...filterTableColumns];
    },

    revocationOrderRefund() {
      let params = {
        apply_code: this.echoData.apply_code
      };
      this.$api
        .revocationOrderRefund(params)
        .then(res => {
          this.$Message.success('撤销成功');
          this.revocationVisible = false;
          this.getOrderRefundDetail();
        })
        .catch(err => this.$Message.error(err.errmsg));
    },

    entryExpress() {
      if (this.echoData.apply_ent_type === 'OPC') {
        this.returnLogisticsVisible = true;
      } else {
        this.$Message.warning('当前售后由平台发起，无操作权限');
      }
    },

    editReturnLogistics() {
      if (this.echoData.apply_ent_type === 'PLAT') {
        this.$Message.warning('当前售后由平台发起，无操作权限');
      } else {
        if (this.echoData.status === 'WAIT_RETURN' || this.echoData.status === 'WAIT_RECEIVE') {
          // 待寄回 || 待收货
          this.returnLogisticsVisible = true;
          this.returnLogisticsList = this.logisticsList;
        } else if (this.echoData.status === 'WAIT_REFUND' || this.echoData.status === 'FINISHED') {
          // 待退款 || 已完成
          this.contentText = '商家已确认收到退货商品，无法修改物流';
          this.tipsVisible = true;
        } else {
          // 其余状态不可修改
          this.contentText = '订单当前状态不可修改退货物流';
          this.tipsVisible = true;
        }
      }
    },

    okTips() {
      this.tipsVisible = false;
    },

    revocation() {
      if (this.echoData.apply_ent_type === 'OPC') {
        this.revocationVisible = true;
      } else {
        this.$Message.warning('当前售后由平台发起，无操作权限');
      }
    }
  },
  beforeDestroy() {}
};
</script>

<style lang="less" scoped>
.detail-wrapper {
  padding-bottom: 50px;

  .basic-info {
    border: 1px solid #b7b7b7;

    .basic-info-item {
      display: flex;
      line-height: 22px;
      border-bottom: 1px solid #b7b7b7;

      &:last-of-type {
        border-bottom: none;
      }

      text-align: center;

      .item-label {
        width: 180px;
        border-right: 1px solid #b7b7b7;
        background: #e6e6e6;
      }

      .item-content {
        flex: 1;
        text-align: left;
        text-indent: 30px;
      }
    }
  }

  .goods-list {
    padding-top: 20px;

    .title {
      padding-bottom: 0 !important;
    }

    .goods-list-title {
      display: flex;
      align-items: center;
      margin-bottom: 10px;
    }
  }
}
</style>

<style lang="less" scoped>
.mt20 {
  margin-top: 20px;
}

.mr10 {
  margin-right: 10px;
}

.default-title {
  color: #333;
  cursor: pointer;
  font-size: 12px;
  line-height: 21px;
}

.active-title {
  color: #155bd4;
  font-size: 16px;
  line-height: 18px !important;
  //border-bottom: 1px solid #155BD4;
}
</style>
