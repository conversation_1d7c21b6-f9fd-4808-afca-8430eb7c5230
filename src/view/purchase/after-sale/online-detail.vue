<template>
  <div class="detail-wrapper">
    <div class="block-header"><span>订单详情</span></div>
    <div class="basic-info">
      <div class="basic-info-item">
        <span class="item-label">售后单号</span>
        <span class="item-content">{{ echoData.apply_code || '-' }}</span>
      </div>

      <div class="basic-info-item">
        <span class="item-label">售后状态</span>
        <span class="item-content"
          >{{ echoData.status_text || '-' }}
          <span
            v-if="
              echoData.status === 'COM_REJECT_CLOSE' ||
              echoData.status === 'SUP_REJECT_CLOSE' ||
              echoData.status === 'PLAT_REJECT_CLOSE'
            "
            style="color: red"
            >(驳回原因：{{ echoData.reject_reason }})</span
          >
        </span>
      </div>

      <div class="basic-info-item">
        <span class="item-label">采购单号</span>
        <span class="item-content">
          <KLink
            v-if="echoData.order_code"
            :to="{ path: '/purchase/clinic/detail', query: { order_code: echoData.order_code } }"
            target="_blank"
            >{{ echoData.order_code }}</KLink
          >
          <span v-else>-</span>
        </span>
      </div>

      <div class="basic-info-item">
        <span class="item-label">采购单类型</span>
        <span class="item-content">{{ echoData.purchase_type_text || '-' }}</span>
      </div>

      <div class="basic-info-item">
        <span class="item-label flex flex-item-center">开票状态</span>
        <div class="item-content">
          <div v-for="(item, index) in echoData.invoice_list" :key="index">
            {{ item.desc }}: {{ item.status_text }}
            <span v-show="item.status === 'FINISHED' || item.status === 'PART_FINISHED'"
              >（{{ item.type_text }} 编号：{{ item.invoice_no }}）</span
            >
          </div>
          <div v-show="echoData.invoice_list && echoData.invoice_list.length === 0">-</div>
        </div>
      </div>

      <div class="basic-info-item">
        <span class="item-label">申请主体</span>
        <span class="item-content">{{ echoData.name || '-' }}</span>
      </div>
      <div class="basic-info-item" v-if="echoData.ent_type === 'CLI'">
        <span class="item-label">诊所类型</span>
        <span class="item-content">
          <span v-if="echoData.clinic_direct_type_text"
            >{{ echoData.clinic_direct_type_text }}/{{ echoData.clinic_type_text }}</span
          >
          <span v-else>-</span>
        </span>
      </div>
      <div class="basic-info-item">
        <span class="item-label">营业执照名称</span>
        <span class="item-content">{{ echoData.organization_name || '-' }}</span>
      </div>
      <div class="basic-info-item">
        <span class="item-label">主体编号</span>
        <span class="item-content">{{ echoData.code || '-' }}</span>
      </div>

      <div class="basic-info-item">
        <span class="item-label">主体类型</span>
        <span class="item-content">{{ echoData.ent_type_text || '-' }}</span>
      </div>

      <div class="basic-info-item">
        <span class="item-label">负责人</span>
        <span class="item-content">{{ echoData.leading_person || '-' }}</span>
      </div>

      <div class="basic-info-item">
        <span class="item-label">负责人手机号</span>
        <span class="item-content">{{ echoData.leading_mobile || '-' }}</span>
      </div>

      <div class="basic-info-item">
        <span class="item-label">申请类型</span>
        <span class="item-content"
          >{{ echoData.refund_type_text || '-' }}
          <span v-if="echoData.refund_type === 'GM' && echoData.delivery_way"
            >({{ echoData.delivery_way === 'HAS' ? '已收到货' : '未收到货' }})</span
          >
        </span>
      </div>

      <div class="basic-info-item">
        <span class="item-label">退款金额</span>
        <span class="item-content">¥ {{ echoData.cli_receive_money }}</span>
      </div>
      <!-- <div class="basic-info-item" v-if="showExchange">
			<span class="item-label"
				>换货情况
				<Tooltip
					class="item"
					effect="dark"
					content="系统暂不支持换货功能，这里仅起到标记作用。"
					maxWidth="200"
					placement="top"
				>
					<Icon type="md-help-circle" class="cursor-pointer" color="" size="16"></Icon> </Tooltip
			></span>
			<span class="item-content">{{ echoData.change_state_desc || '-' }}</span>
		</div> -->
      <div class="basic-info-item">
        <span class="item-label">售后原因</span>
        <span class="item-content">{{ echoData.refund_reason || '-' }}</span>
      </div>

      <div class="basic-info-item">
        <span class="item-label">售后说明</span>
        <span class="item-content">{{ echoData.refund_desc || '-' }}</span>
      </div>

      <div class="basic-info-item">
        <span class="item-label flex flex-item-center">附件</span>
        <span class="item-content">
          <viewer
            v-if="echoData.refund_images.length"
            :images="echoData.refund_images"
            class="flex flex-item-align"
            style="margin: 10px 10px 10px 30px"
          >
            <img
              style="width: 40px; height: 40px; margin-right: 10px"
              v-for="(item, index) in echoData.refund_images"
              :key="index"
              :src="item"
              alt="附件"
              class="image cursor-pointer scale"
            />
          </viewer>
          <div v-else>-</div>
        </span>
      </div>

      <div class="basic-info-item" v-if="echoData.refund_type !== 'M'">
        <span class="item-label flex flex-item-center">退货地址信息</span>
        <div class="item-content">
          <div>收货人：{{ echoData.consignee || '-' }}</div>
          <div>联系电话：{{ echoData.mobile || '-' }}</div>
          <div>收货地址：{{ echoData.address_text || '-' }}</div>
        </div>
      </div>

      <div class="basic-info-item">
        <span class="item-label">申请时间</span>
        <span class="item-content">{{ echoData.create_time | date_format }}</span>
      </div>

      <div class="basic-info-item">
        <span class="item-label">完成时间</span>
        <span class="item-content">{{ echoData.finished_time | date_format }}</span>
      </div>
    </div>

    <div class="goods-list">
      <div class="panel-nav flex flex-item-between">
        <div>
          <a
            :class="{ active: activeGoodsType === item.type }"
            v-for="item in goodsTypeList"
            :key="item.type"
            class="nav"
            @click.prevent.capture="goodsTabChange(item.type)"
          >
            {{ item.name }}
          </a>
        </div>
      </div>

      <div class="table-wrapper">
        <Table
          v-show="activeGoodsType === 0"
          :columns="tableColumns"
          :data="goodsList"
          :loading="tableLoading"
          :show-summary="true"
          :summary-method="handleSummary"
          stripe
          border
        >
          <template slot-scope="{ row, index }" slot="img">
            <viewer :images="[row.img]" v-if="row.img" class="flex flex-item-center">
              <p v-if="row.img" class="flex flex-item-center">
                <img style="width: 60px; height: auto; margin: 10px 0" :src="row.img | imageStyle" class="image" />
              </p>
            </viewer>
            <span v-else>-</span>
          </template>

          <!-- 规格 -->
          <template slot="spec" slot-scope="{ row }">
            {{ row.spec || '-' }}
          </template>
          <!-- 销售商 -->
          <template slot="seller" slot-scope="{ row }">
            <span v-if="row.seller">{{ row.seller }}</span>
            <span v-else>-</span>
          </template>

          <!-- 采购单价 -->
          <template slot="goods_price" slot-scope="{ row }">
            <span v-if="row.goods_price">￥{{ row.goods_price }}</span>
            <span v-else>-</span>
          </template>
          <!-- 退款金额 -->
          <template slot="cli_receive_money" slot-scope="{ row }">
            <span v-if="row.cli_receive_money">￥{{ row.cli_receive_money }}</span>
            <span v-else>-</span>
          </template>
          <!-- 省公司退款金额 -->
          <template slot="com_refund_money" slot-scope="{ row }">
            <span v-if="row.com_refund_money">￥{{ row.com_refund_money }}</span>
            <span v-else>-</span>
          </template>
        </Table>
        <!-- 退货物流 -->
        <Table
          v-show="activeGoodsType === 1 && echoData.refund_type !== 'M'"
          :columns="logisticsColumns"
          :data="logisticsList"
          :loading="tableLoading"
          border
        >
          <!-- 物流单号 -->
          <template slot="express_desc" slot-scope="{ row }">
            <div style="text-align: left; padding: 0 12px">
              <div v-for="(item, index) in row.express_desc" :key="index">
                {{ item.express_name }}：{{ item.express_no }}
              </div>
            </div>
          </template>

          <!-- 备注与凭证 -->
          <template v-slot:remark_images="{ row }">
            <div style="text-align: left">
              <span>{{ row.prove_remark }}</span>
              <viewer
                :images="[row.prove_images]"
                v-if="row.prove_images"
                class="flex flex-wrap"
                style="text-align: left; padding-bottom: 10px"
              >
                <img
                  style="width: 40px; height: 40px; margintop: 10px"
                  v-for="(prove_images_item, prove_images_index) in row.prove_images"
                  :key="'refund_images' + prove_images_index"
                  :src="prove_images_item | imageStyle"
                  class="mr10 cursor-pointer"
                />
              </viewer>
            </div>
            <span v-show="!row.prove_remark && row.prove_images.length === 0">-</span>
          </template>

          <!-- 录入时间 -->
          <template slot="prove_time" slot-scope="{ row }">
            {{ row.prove_time | date_format }}
          </template>
        </Table>
      </div>
    </div>

    <!--  操作记录  -->
    <div class="goods-list-title mt20">
      <h3 class="f-title title">操作记录</h3>
    </div>
    <Table :columns="operatorColumns" :data="operatorRecordData" border>
      <template slot="create_time" slot-scope="{ row }">
        {{ row.create_time | date_format }}
      </template>
      <template slot="source" slot-scope="{ row }">
        {{ row.source || '-' }}
      </template>
      <template slot="operator_name" slot-scope="{ row }">
        <span v-if="row.operator_name"
          >{{ row.operator_name }}<span v-if="row.operator_role_name">({{ row.operator_role_name }})</span></span
        >
        <span v-else>-</span>
      </template>
      <template slot="content_describe" slot-scope="{ row }">
        {{ row.content_describe }}
      </template>
    </Table>

    <div class="fixed-bottom-wrapper">
      <!--      <Button class="mr10" @click="$router.back()">返回</Button>-->
      <back-button class="mr10"></back-button>
      <Button
        class="mr10"
        v-if="$route.query.action === 'audit' && echoData.status === 'COM_AUDIT'"
        type="error"
        @click="refuseVisible = true"
        >审核驳回
      </Button>
      <Button
        class="mr10"
        v-if="$route.query.action === 'audit' && echoData.status === 'COM_AUDIT'"
        type="primary"
        @click="pass"
        >审核通过
      </Button>
    </div>

    <!--  审核驳回  -->
    <refuse-modal v-model="refuseVisible" @ok="refuseEvent"></refuse-modal>
    <!--  审核通过  -->
    <!-- <confirm-modal :confirmVisible.sync="confirmVisible" content-text="" @ok="passEvent"> -->
    <confirm-modal
      :confirmVisible.sync="confirmVisible"
      content="温馨提示"
      content-text="确定要通过审核吗？"
      @ok="passEvent"
    >
      <!-- <KWidget label="换货情况" required style="margin-top: 20px; align-items: center" v-if="!showExchange">
			<RadioGroup v-model="change_state" class="radio-wrapper">
				<Radio label="NONE">无需换货</Radio>
				<Radio label="NEED">换货</Radio>
			</RadioGroup>
		</KWidget>
		<KWidget :label-width="40" style="margin-top: 20px">
			<span style="color: #999"> 注：系统暂不支持换货功能，这里仅起到标记作用。 </span>
		</KWidget> -->
    </confirm-modal>
  </div>
</template>

<script>
// import { CodeToText, regionData, TextToCode } from '@/utils/chinaMap';
import renderHeader from '@/mixins/renderHeader';
import refuseModal from '@/components/refuseModal/refuseModal';
import confirmModal from '@/components/confirmModal/confirmModal';
import { $operator } from '@/utils/operation';

const init_order_form_data = {
  remark: ''
};
export default {
  name: 'detail',
  components: {
    refuseModal,
    confirmModal
  },
  mixins: [renderHeader],
  data() {
    return {
      formData: {
        ...init_order_form_data
      },
      echoData: {
        apply_code: '',
        apply_ent_type: '',
        apply_ent_type_text: '',
        cli_receive_money: '',
        code: '',
        com_refund_money: '',
        company_name: '',
        create_time: '',
        finished_time: '',
        leading_mobile: '',
        leading_person: '',
        name: '',
        order_code: '',
        purchase_type_text: '',
        refund_desc: '',
        refund_reason: '',
        refund_type: '',
        refund_type_text: '',
        status: '',
        status_text: '',
        refund_images: [],
        purchaseType: ''
      }, // 基础信息
      // 售后商品
      tableColumns: [
        { title: '编号', key: 'sku_code', align: 'center' },
        { title: '商品名', key: 'goods_name', align: 'center' },
        { title: '介绍图', slot: 'img', key: 'img', align: 'center' },
        { title: '类型', key: 'type_text', align: 'center' },
        { title: '规格', slot: 'spec', align: 'center', minWidth: 100 },
        { title: '单位', key: 'unit', align: 'center' },
        { title: '供应商', key: 'supplier', align: 'center' },
        { title: '销售商', slot: 'seller', align: 'center', minWidth: 40 },
        { title: '采购单价', slot: 'goods_price', align: 'center', minWidth: 40 },
        { title: '售后数量', key: 'num', align: 'center' },
        { title: '退款金额', slot: 'cli_receive_money', key: 'cli_receive_money', align: 'center' }
      ],
      goodsList: [],
      tableLoading: false,

      // 退货物流
      logisticsColumns: [
        { title: '售后商品', key: 'goods_text', align: 'center' },
        { title: '物流单号', slot: 'express_desc', align: 'center' },
        { title: '备注与凭证', slot: 'remark_images', align: 'center' },
        { title: '录入时间', slot: 'prove_time', align: 'center' }
      ],
      logisticsList: [],
      tipVisible: false,

      // 操作记录
      operatorColumns: [
        { title: '时间', slot: 'create_time', align: 'center' },
        { title: '操作主体', slot: 'source', align: 'center' },
        { title: '操作人', slot: 'operator_name', align: 'center' },
        { title: '操作记录', slot: 'content_describe', align: 'center' }
      ],
      operatorRecordData: [],

      refuseVisible: false, // 审核驳回弹窗
      confirmVisible: false, // 审核通过弹窗
      activeGoodsType: 0,
      goodsTypeList: [{ type: 0, name: '售后商品' }],
      goodsTypeAllList: [
        { type: 0, name: '售后商品' },
        { type: 1, name: '退货物流' }
      ],
      change_state: '' //换货情况
    };
  },
  computed: {
    // showExchange() {
    //   let orderTypes = ['RXJ_ADDITION', 'CLI_ADDITION', 'OPC_OWN', 'OPC_PERSONAL'];
    //   return orderTypes.includes(this.orderType);
    // }
  },
  watch: {},
  created() {},
  mounted() {
    this.$router.onReady(() => {
      this.getOrderRefundDetail();
    });
  },
  methods: {
    refuseEvent(reason) {
      this.review(0, reason);
    },
    passEvent() {
      this.review(1);
    },
    pass() {
      this.confirmVisible = true;
    },
    getOrderRefundDetail() {
      let params = { apply_code: this.$route.query.apply_code };
      console.log('=>(detail.vue:241) params', params);
      this.$api
        .getOrderRefundDetail(params)
        .then(res => {
          this.echoData = res.order;
          this.goodsList = res.list;
          this.operatorRecordData = res.log;
          this.logisticsList = res.packs;

          // 如果当前状态为退货退款，追加上退货物流的tab
          if (this.echoData.refund_type !== 'M') {
            this.goodsTypeList = this.goodsTypeAllList;
          } else {
            this.goodsTypeList = [this.goodsTypeAllList[0]];
          }
        })
        .catch(err => this.$Message.error(err.errmsg));
    },
    // 合计
    handleSummary({ columns, data }) {
      const sums = {};
      columns.forEach((column, index) => {
        const key = column.key;
        if (index === 0) {
          sums[key] = {
            key,
            value: '合计'
          };
          return;
        }
        let whiteList = [9, 10, 11];
        if (!whiteList.includes(index)) {
          sums[key] = {
            key,
            value: ''
          };
          return;
        }
        const values = data.map(item => Number(item[key]));
        if (!values.every(value => isNaN(value))) {
          const v = values.reduce((prev, curr) => {
            const value = Number(curr).toFixed(2);
            if (!isNaN(value)) {
              return $operator.add(prev, curr);
            } else {
              return prev;
            }
          }, 0);
          sums[key] = {
            key,
            value: index === 9 ? v : `¥ ${v}` // 售后数量不加¥
          };
        } else {
          sums[key] = {
            key,
            value: ''
          };
        }
      });
      return sums;
    },

    review(status, reason) {
      let params = {
        apply_code: this.$route.query.apply_code,
        status
      };
      if (!status) {
        params.reason = reason;
      } else {
        // if (!this.change_state && !this.showExchange) {
        //   this.$Message.error('请选择是否换货');
        //   return;
        // }
        params.change_state = this.change_state;
      }

      this.$api
        .changeOrderRefundAudit(params)
        .then(res => {
          if (status) {
            this.$Message.success('审核通过');
          } else {
            this.$Message.success('审核驳回');
          }
          this.getOrderRefundDetail();
          this.change_state = '';
          this.confirmVisible = false;
        })
        .catch(err => this.$Message.error(err.errmsg));
    },

    goOrderDetail() {
      this.$router.push({ path: '/purchase/clinic/detail', query: { order_code: this.echoData.order_code } });
    },

    goodsTabChange(type) {
      this.activeGoodsType = type;
    }
  },
  beforeDestroy() {}
};
</script>

<style lang="less" scoped>
.detail-wrapper {
  padding-bottom: 50px;

  .basic-info {
    border: 1px solid #b7b7b7;

    .basic-info-item {
      display: flex;
      line-height: 22px;
      border-bottom: 1px solid #b7b7b7;

      &:last-of-type {
        border-bottom: none;
      }

      text-align: center;

      .item-label {
        width: 180px;
        border-right: 1px solid #b7b7b7;
        background: #e6e6e6;
      }

      .item-content {
        flex: 1;
        text-align: left;
        text-indent: 30px;
      }
    }
  }

  .goods-list {
    padding-top: 20px;

    .title {
      padding-bottom: 0 !important;
    }

    .goods-list-title {
      display: flex;
      align-items: center;
      margin-bottom: 10px;
    }
  }
}
</style>

<style lang="less" scoped>
.mt20 {
  margin-top: 20px;
}

.mr10 {
  margin-right: 10px;
}

.default-title {
  color: #333;
  cursor: pointer;
  font-size: 12px;
  line-height: 21px;
}

.active-title {
  color: #155bd4;
  font-size: 16px;
  line-height: 18px !important;
  //border-bottom: 1px solid #155BD4;
}
</style>
