<template>
  <div class="detail-wrapper">
    <h3 class="f-title">诊所采购单详情</h3>
    <div class="basic-info">
      <!-- <div class="basic-info-item" style="justify-content: center;background: #cccccc;">
			采购单信息
		</div> -->
      <div class="basic-info-item">
        <span class="item-label">采购单号</span>
        <span class="item-content">{{ echoData.cli_order_code }}</span>
      </div>

      <div class="basic-info-item">
        <span class="item-label">交易号</span>
        <span class="item-content">{{ echoData.trade_code }}</span>
      </div>

      <div class="basic-info-item">
        <span class="item-label">采购单类型</span>
        <span class="item-content">{{ echoData.type_text }}</span>
      </div>

      <div class="basic-info-item">
        <span class="item-label">采购单状态</span>
        <span class="item-content" style="text-indent: 0; margin-left: 30px"
          >{{ echoData.status_text
          }}<span v-if="echoData.reject_reason" class="reject-reason"
            >( 驳回原因：{{ echoData.reject_reason }} )</span
          ></span
        >
      </div>

      <div class="basic-info-item">
        <span class="item-label">采购主体编号</span>
        <span class="item-content">{{ echoData.ent_info.ent_code || '-' }}</span>
      </div>

      <div class="basic-info-item">
        <span class="item-label">采购主体</span>
        <span class="item-content">{{ echoData.ent_info.name || '-' }}</span>
      </div>
      <div class="basic-info-item" v-if="echoData.ent_info.ent_type === 'CLI'">
        <span class="item-label">诊所类型</span>
        <span class="item-content">
          <span v-if="echoData.clinic_direct_type_text && echoData.clinic_type_text">
            {{ echoData.clinic_direct_type_text }} / {{ echoData.clinic_type_text }}
          </span>
          <span v-else>-</span>
        </span>
      </div>

      <div class="basic-info-item">
        <span class="item-label">营业执照名称</span>
        <span class="item-content">{{ echoData.organization_name || '-' }}</span>
      </div>

      <div class="basic-info-item">
        <span class="item-label">统一社会信用代码</span>
        <span class="item-content">{{ echoData.organization_code || '-' }}</span>
      </div>

      <div class="basic-info-item">
        <span class="item-label">负责人</span>
        <span class="item-content">{{ echoData.leading_person }}</span>
      </div>

      <div class="basic-info-item">
        <span class="item-label">负责人电话</span>
        <span class="item-content">{{ echoData.leading_mobile }}</span>
      </div>

      <div class="basic-info-item">
        <span class="item-label">采购单总件数</span>
        <span class="item-content">{{ echoData.num }}</span>
      </div>

      <div class="basic-info-item">
        <span class="item-label">采购单生成时间</span>
        <span class="item-content">{{ echoData.cli_create_time }}</span>
      </div>

      <div class="basic-info-item">
        <span class="item-label">采购单支付时间</span>
        <span class="item-content">{{ echoData.cli_paid_at }}</span>
      </div>

      <div class="basic-info-item">
        <span class="item-label">采购单支付金额</span>
        <span class="item-content">
          <span v-if="echoData.cli_payment_fee">￥{{ echoData.cli_payment_fee }}</span>
          <span v-else></span>
        </span>
      </div>

      <!-- <div class="basic-info-item">
			<span class="item-label">采购单下单时间</span>
			<span class="item-content">{{ echoData.cli_create_time }}</span>
		</div> -->
      <div class="basic-info-item">
        <span class="item-label">供应商应收收入</span>
        <span class="item-content">
          ￥{{ echoData.money_info.sup_income }}
          <span
            v-if="
              Number(echoData.money_info.sup_gu_fee) &&
              echoData.money_info.sup_income != 0 &&
              echoData.money_info.sup_income != '-'
            "
            >（含银联手续费：￥{{ echoData.money_info.sup_gu_fee }}）</span
          >
        </span>
      </div>

      <div class="basic-info-item">
        <span class="item-label">供应商发货状态</span>
        <span class="item-content">{{ echoData.ship_status_text }}</span>
      </div>

      <div class="basic-info-item">
        <span class="item-label">收件人姓名</span>
        <span class="item-content">{{ echoData.consignee || '-' }}</span>
      </div>

      <div class="basic-info-item">
        <span class="item-label">收件人手机号</span>
        <span class="item-content">{{ echoData.mobile || '-' }}</span>
      </div>

      <div class="basic-info-item">
        <span class="item-label">收货地址</span>
        <span class="item-content">{{ echoData.address || '-' }}</span>
      </div>

      <div class="basic-info-item">
        <span class="item-label">诊所入库状态</span>
        <span class="item-content">{{ echoData.store_status_text }}</span>
      </div>

      <div class="basic-info-item">
        <span class="item-label">签收状态</span>
        <span class="item-content">{{ echoData.sign_status_desc || '-' }}</span>
      </div>

      <div class="basic-info-item">
        <span class="item-label">订单备注</span>
        <span class="item-content">{{ echoData.remark || '-' }}</span>
      </div>
    </div>
    <div class="goods-list">
      <div class="panel-nav flex flex-item-between custom-sticky" style="top: 56px">
        <div>
          <a
            :class="{ active: activeGoodsType === item.type }"
            v-for="item in goodsTypeList"
            :key="item.type"
            class="nav"
            @click.prevent.capture="goodsTabChange(item.type)"
          >
            {{ item.name }}
          </a>
        </div>
      </div>

      <div class="table-wrapper">
        <Table
          v-show="activeGoodsType == 0"
          :row-class-name="rowClassName"
          class="tc-table-style sticky-table-head"
          :columns="tableColumns"
          :data="goods_items"
          row-key="sku_code"
        >
          <template slot-scope="{ row }" slot="generic_name">
            <span v-if="row.is_taocan === '1'" class="tc-tag">套餐</span>{{ row.generic_name }}
          </template>

          <template slot-scope="{ row, index }" slot="img">
            <viewer :images="[row.img]" v-if="row.img" class="flex flex-item-center">
              <img style="width: 60px; height: auto; margin: 10px 0" :src="row.img | imageStyle" class="image" />
            </viewer>
            <span v-else>-</span>
          </template>

          <template slot-scope="{ row }" slot="prod_spec">
            <span v-if="row.is_taocan === '1'">-</span>
            <span v-else>{{ row.prod_spec || '-' }}</span>
          </template>
          <template slot-scope="{ row }" slot="supplier_name">
            <!-- 套餐子集不重复展示供应商-->
            <span v-if="row.isChild">-</span>
            <span v-else>{{ row.supplier_name || '-' }}</span>
          </template>
          <template slot-scope="{ row }" slot="seller">
            <!-- 套餐子集不重复展示经销商-->
            <span v-if="row.isChild">-</span>
            <span v-else>{{ row.seller || '-' }}</span>
          </template>

          <template slot-scope="{ row, index }" slot="clinic_price">
            <span>￥{{ row.clinic_price }}</span>
          </template>

          <template slot-scope="{ row, index }" slot="com_price">
            <span>￥{{ row.com_price }}</span>
          </template>
          <template slot-scope="{ row, index }" slot="num"> <span v-if="row.isChild">x</span>{{ row.num }} </template>

          <!--					<template slot="prod_date" slot-scope="{row}">-->
          <!--						<p>{{ row.prod_date || date_format('YYYY-MM-DD') }}</p>-->
          <!--					</template>-->

          <!--					<template slot="expire_date" slot-scope="{row}">-->
          <!--						<p>{{ row.expire_date || date_format('YYYY-MM-DD') }}</p>-->
          <!--					</template>-->
        </Table>
        <Table v-show="activeGoodsType == 1" :columns="refundApplyColumns" :data="refundApplyData" border>
          <template slot-scope="{ row, index }" slot="goods">
            <div v-for="(item, goods_key) in row.list" :key="goods_key">
              {{ item.goods_name }}*{{ item.num }} <span v-show="goods_key + 1 < row.list.length">,</span>
            </div>
          </template>

          <template slot-scope="{ row }" slot="refund_reason">
            <span>{{ row.refund_reason || '-' }}</span>
          </template>

          <template slot-scope="{ row }" slot="refund_desc">
            <span>{{ row.refund_desc || '-' }}</span>
          </template>

          <template slot-scope="{ row, index }" slot="create_time">
            <span>{{ row.create_time | date_format }}</span>
          </template>

          <template slot-scope="{ row }" slot="action">
            <KLink
              :to="{ path: '/purchase/after-sale/online-detail', query: { apply_code: row.apply_code } }"
              target="_blank"
            >
              详情
            </KLink>
          </template>
        </Table>

        <!-- 开票信息 -->
        <Table
          v-show="activeGoodsType == 3"
          :columns="invoiceColumns"
          :data="invoiceList"
          :loading="tableLoading"
          border
        >
          <!-- 申请主体 -->
          <template slot="applicant" slot-scope="{ row }">
            <div>{{ row.applicant.applicant || '-' }}</div>
          </template>
          <!-- 金额 -->
          <template slot="amount" slot-scope="{ row }">
            <div>{{ row.amount ? `￥${row.amount}` : '-' }}</div>
          </template>
          <!-- 发票内容 -->
          <template slot="content" slot-scope="{ row }">
            <span v-if="row.drawer_type === '3'">
              {{ row.content }}
            </span>
            <span v-else>
              <a @click="checkGoodsDetail(row)">{{ row.content }}</a>
            </span>
          </template>
          <!-- 发票编号 -->
          <template slot="invoice_no" slot-scope="{ row }">
            <div>{{ row.invoice_no || '-' }}</div>
          </template>
          <!-- 发票预览 -->
          <template slot="preview" slot-scope="{ row }">
            <a
              v-if="(row.nuonuo && row.nuonuo.c_paper_pdf_url?.length) || (row.nuonuo && row.nuonuo.c_url?.length)"
              @click="previewInvoice(row)"
              >点击预览</a
            >

            <span v-else>-</span>
          </template>
        </Table>
      </div>
    </div>

    <div class="logistics-information" v-if="express_list.length && activeGoodsType === 2">
      <!-- <h3 class="f-title">物流信息</h3> -->
      <!-- <div class="flex flex-warp"> -->
      <!--   <div class="logistics-info-box" v-for="(item, index) in express_list" :key="item.express_no"> -->
      <!--     <div class="flex flex-item-between"> -->
      <!--       <h4 class="title">{{ `包裹${index + 1}：${item.express_name}` }}</h4> -->
      <!--       <Button type="default" size="small" class="radius-btn" @click="toLogisticsDetail(item)">包裹详情</Button> -->
      <!--     </div> -->
      <!--     <div class=""> -->
      <!--       <span class="logistics-label">单号：</span> -->
      <!--       <span class="logistics-content">{{ item.express_no || '-' }}</span> -->
      <!--     </div> -->
      <!--     <div> -->
      <!--       <span class="logistics-label">时间：</span> -->
      <!--       <span class="logistics-content">{{ item.time }}</span> -->
      <!--     </div> -->
      <!--     <div class="info-item flex"> -->
      <!--       <span class="logistics-label min36" v-if="item.remark">备注：</span> -->
      <!--       <span class="logistics-content wrap" v-if="item.remark">{{ item.remark }}</span> -->
      <!--     </div> -->
      <!--   </div> -->
      <!-- </div> -->

      <Table :columns="expressCols" :data="express_list" border :span-method="handleSpan">
        <template slot="express_no" slot-scope="{ row }">
          <div>
            <div>
              {{ row.express_name || '-' }}
              {{ row.express_no }}
            </div>
          </div>
        </template>

        <template slot-scope="{ row }" slot="action">
          <a v-if="row.normal_express === '1'" @click="toLogisticsDetail(row)" style="margin-right: 10px">查看物流</a>
          <span v-else>-</span>
          <a @click="confirmExpress(row)" v-if="echoData.can_sign === '1' && row.can_sign === '1'">确认收货</a>
        </template>
      </Table>
    </div>

    <div class="pay-wrapper" v-if="echoData.status === 'HAS_PAY' && echoData.com_pay_status == 'WAIT'">
      <h3 class="f-title">支付方式</h3>
      <div class="pay-content flex flex-item-between">
        <div class="left">
          <RadioGroup v-model="payType">
            <Radio label="GUNION" :disabled="default_pay_type === 'YZT'"
              ><span class="radio-label">银联钱包扣款</span
              ><span class="money">（当前余额：￥{{ echoData.balance }}）</span></Radio
            >
            <Radio label="YZT" :disabled="default_pay_type === 'GUNION'"
              ><span class="radio-label">云直通钱包扣款</span
              ><span class="money">（当前余额：￥{{ echoData.yzt_balance }}）</span></Radio
            >
          </RadioGroup>
        </div>
        <div class="right">
          <p>
            待支付总额（出厂价）：<span class="balance">￥{{ echoData.money_info.payment_fee }}</span>
          </p>
        </div>
      </div>
    </div>

    <!--  操作记录  -->
    <div class="goods-list-title mt20">
      <h3 class="f-title title">操作记录</h3>
    </div>
    <Table :columns="operatorColumns" :data="operatorRecordData" border>
      <template slot="create_time" slot-scope="{ row }">
        {{ row.create_time | date_format }}
      </template>
      <template slot="operator_source" slot-scope="{ row }">
        {{ row.operator_source || '-' }}
      </template>
      <template slot="operator_name" slot-scope="{ row }">
        <span v-if="row.operator_name"
          >{{ row.operator_name }}<span v-if="row.operator_role_name">({{ row.operator_role_name }})</span></span
        >
        <span v-else>-</span>
      </template>
      <template slot="content" slot-scope="{ row }">
        {{ row.content }}
      </template>
    </Table>
    <div class="fixed-bottom-wrapper">
      <back-button></back-button>
      <Button
        type="primary"
        style="margin: 0 20px"
        @click="pay"
        v-if="echoData.status === 'HAS_PAY' && echoData.com_pay_status == 'WAIT'"
        >立即支付
      </Button>
    </div>

    <!-- Modal -->
    <div class="modal">
      <Modal v-model="payVisible" :mask-closable="false" class-name="vertical-center-modal" title=" " width="400px">
        <div v-if="hasEnoughMoney">
          <h3 class="modal-title">是否确认用余额抵扣</h3>
          <p style="font-size: 13px">确认用余额抵扣后，订单将流转给供应商进行发货处理</p>
        </div>
        <div v-else>
          <h3 class="modal-title">当前钱包余额不足</h3>
          <p style="font-size: 13px">请先向银联钱包账户充值。确保余额充足再进行购买订单</p>
        </div>
        <div slot="footer">
          <div v-if="hasEnoughMoney">
            <Button @click="payVisible = false">我再想想</Button>
            <Button type="primary" @click="confirmPayment" :loading="payLoading">确认支付</Button>
          </div>
          <Button v-else type="primary" @click="payVisible = false"> 我知道了</Button>
        </div>
      </Modal>
    </div>

    <!-- 物流详情 -->
    <kLogisticsProgress
      v-model="logisticsVisible"
      :is-logistics-detail="false"
      :express_detail="progress_express_detail"
    />
    <tips-modal
      v-model="tipsVisible"
      :show-cancel="true"
      @onOk="agreeRefund"
      @onCancel="cancelHandler"
      cancel-text="查看售后申请"
      confirm-text="好的"
      contentText="诊所已提交采购单售后申请，省公司暂时无法支付"
    >
      <div slot="cancel">查看售后申请</div>
      <!--      <div slot="footer">-->
      <!--        <Button :to="{path: '/purchase/after-sale/detail', query: {apply_code: refundApplyCode, action: 'audit'}}" target="_blank">查看售后申请111</Button>-->
      <!--      </div>-->
    </tips-modal>

    <!-- 商品明细 -->
    <GoodsModal :goods-visible.sync="goodsVisible" :goodsDetails="goodsDetails"></GoodsModal>
  </div>
</template>

<script>
import search from '@/mixins/search';
import KLogisticsDetail from '@/components/k-logistics-detail';
import kLogisticsProgress from '@/components/k-logistics-progress/k-logistics-progress';
import renderHeader from '@/mixins/renderHeader';
import tipsModal from '@/components/confirmModal/TipsModal';
import global_config from '@/config';
import { date_format } from 'utils/filters';
// import purchaseModal from './components/purchase-modal'
export default {
  name: 'detail',
  mixins: [search, renderHeader],
  components: {
    kLogisticsProgress,
    GoodsModal: () => import('@/view/finance/invoice/components/GoodsDetail'),
    tipsModal
  },
  data() {
    return {
      logisticsVisible: false, // 物流弹窗详情
      progress_express_detail: [],

      echoData: {
        com_outlay: '', //省公司支出金额
        status: '', // 是否需要支付
        com_pay_status: '', // 省公司是否支付了
        cli_order_code: '', // 采购单号
        trade_code: '', // 交易单号
        status_text: '', // 采购单状态
        // purchase_money: '', // 采购单总金额
        ent_info: {
          ent_code: '',
          ent_type: '',
          name: '' //采购主体
        }, // 诊所编号
        organization_name: '',
        organization_code: '',
        leading_person: '', // 诊所负责人
        leading_mobile: '', //诊所负责人电话
        num: '', //采购单总件数
        cli_create_time: '', // 采购单下单时间
        order_code: '', // 付款单号
        paid_at: '', //支付时间
        payment_fee: '', // 省公司待支付
        money_info: '', // 金额
        ship_status_text: '', // 供应商发货状态
        consignee: '', // 收件人姓名
        mobile: '', // 收件手机号
        address: '', // 收件人地址
        store_status_text: '', // 诊所入库状态
        sign_status_desc: '',
        remark: '', // 订单备注
        balance: '', // 当前余额
        auto_pay: '', // 是否需要校验
        yzt_balance: '',
        partner_name: ''
      },
      // allow_yzt: '',// 是否可以使用云直通支付
      default_pay_type: '', // 默认支付方式
      // hasEnoughMoney: true, // 是否有足够金额支付
      tableColumns: [
        // {title: '序号', type: 'index', align: 'center'},
        { title: '编号', key: 'sku_code', align: 'center' },
        { title: '商品名', slot: 'generic_name', align: 'center' },
        { title: '介绍图', slot: 'img', key: 'img', align: 'center' },
        { title: '类型', key: 'prod_type_text', align: 'center' },
        { title: '规格', slot: 'prod_spec', align: 'center' },
        { title: '单位', key: 'pack_unit', align: 'center' },
        { title: '供应商', slot: 'supplier_name', align: 'center' },
        {
          title: '销售商',
          slot: 'seller',
          align: 'center',
          renderHeader: (h, params) => this._renderHeader(h, params, '诊所采购时货品金额部分，发票由销售商开具')
        },
        // {title: '批号', slot: 'batch_code', align: 'center'},
        // {title: '生产日期', slot: 'prod_date', align: 'center'},
        // {title: '过期日期', slot: 'expire_date', align: 'center'},
        { title: '采购单价', slot: 'clinic_price', align: 'center' },
        { title: '供应商供货单价', slot: 'com_price', align: 'center', minWidth: 20 },
        { title: '采购数量', slot: 'num', align: 'center' },
        { title: '已发数量', key: 'shipped_num', align: 'center' },
        { title: '待发数量', key: 'can_ship_num', align: 'center' }
      ],
      goods_items: [], // 商品列表数据
      payType: 'GUNION', // 支付方式选择
      payVisible: false, // 支付误操作弹窗
      payLoading: false, // 确认支付得loading
      express_list: [], // 物流信息
      order_code: '',
      orderType: '',
      activeGoodsType: 0,
      goodsTypeList: [
        { type: 0, name: '商品清单' }
        // {type: 1, name: '售后明细'},
      ],
      typeList: [
        { name: '商品清单', type: 0, sort: 0 },
        { name: '包裹物流', type: 2, sort: 1 },
        { name: '售后明细', type: 1, sort: 4 },
        { name: '开票信息', type: 3, sort: 3 }
      ],
      refundApplyColumns: [
        { title: '售后单号', key: 'apply_code', align: 'center' },
        { title: '售后类型', key: 'refund_type_text', align: 'center' },
        { title: '退款商品', slot: 'goods', align: 'center' },
        { title: '退款金额', key: 'cli_receive_money', align: 'center' },
        { title: '省公司退款', key: 'com_refund_money', align: 'center' },
        // { title: '供应商退款', key: 'pack_unit', align: 'center' },
        {
          title: '换货情况',
          key: 'change_state_desc',
          align: 'center',
          renderHeader: (h, params) => this._renderHeader(h, params, '系统暂不支持换货功能，这里仅起到标记作用。'),
          isExchange: true
        },
        { title: '售后原因', slot: 'refund_reason', align: 'center' },
        { title: '售后说明', slot: 'refund_desc', align: 'center' },
        { title: '状态', key: 'status_text', align: 'center' },
        { title: '申请时间', slot: 'create_time', align: 'center' },
        { title: '操作', slot: 'action', align: 'center' }
      ],
      refundApplyData: [],
      // 操作记录
      operatorColumns: [
        { title: '时间', slot: 'create_time', align: 'center' },
        { title: '操作主体', slot: 'operator_source', align: 'center' },
        { title: '操作人', slot: 'operator_name', align: 'center' },
        { title: '操作记录', slot: 'content', align: 'center' }
      ],
      expressCols: [
        {
          title: '发货单号',
          align: 'center',
          render: (h, { row }) => h('span', {}, row.ship_code || '-')
        },
        {
          title: '商品',
          align: 'center',
          render: (h, { row }) => h('span', {}, row.goods_text || '-')
        },
        {
          title: '物流单号',
          slot: 'express_no',
          align: 'center'
        },
        {
          title: '操作',
          slot: 'action',
          align: 'center'
        },
        {
          title: '签收状态',
          align: 'center',
          render: (h, { row }) => h('span', {}, row.is_sign === '1' ? '已签收' : '未签收' || '-')
        },
        {
          title: '签收时间',
          align: 'center',
          render: (h, { row }) => h('span', {}, date_format(row.sign_time) || '-')
          // render: (h, { row }) => h('span', {}, row.express_no || '-')
        },
        {
          title: '签收方式',
          key: 'code',
          align: 'center',
          renderHeader: (h, params) =>
            h(
              'div',
              {
                style: {
                  display: 'flex',
                  alignItem: 'center'
                }
              },
              [
                h(
                  'Tooltip',
                  {
                    props: {
                      theme: 'dark',
                      placement: 'top'
                    }
                  },
                  [
                    h('Icon', {
                      props: {
                        type: 'md-help-circle'
                      },
                      style: {
                        cursor: 'pointer',
                        fontSize: '16px',
                        marginRight: '4px'
                      }
                    }),
                    h(
                      'div',
                      {
                        slot: 'content',
                        class: 'text-inner',
                        style: {
                          maxWidth: '500px'
                        }
                      },
                      [
                        h('div', {}, '物流签收：物流快递单签收。'),
                        h('div', {}, '手动签收：点击“确认收货”按钮，完成签收。'),
                        h('div', {}, '入库签收：手动完成入库操作，系统自动签收。'),
                        h('div', {}, '超时签收：商品出库后7天，系统自动签收。'),
                        h('div', {}, '无需签收：当商品的发货方式为自提或无需发货时，无需签收。')
                      ]
                    )
                  ]
                ),
                params.column.title
              ]
            ),
          render: (h, { row }) => h('span', {}, row.sign_type_desc || '-')
        },
        {
          title: '物流回传/录入时间',
          key: 'code',
          align: 'center',
          render: (h, { row }) => h('span', {}, date_format(row.ship_time) || '-')
        }
      ],
      operatorRecordData: [],
      tipsVisible: false,
      refundApplyCode: '', // 支付时存在诊所售后单

      // 开票信息
      invoiceColumns: [
        { title: '序号', type: 'index', key: 'index', align: 'center' },
        { title: '申请主体', slot: 'applicant', align: 'center' },
        { title: '开票方', key: 'drawer', align: 'center' },
        { title: '申请时间', key: 'create_time', align: 'center' },
        { title: '开票时间', key: 'finished_at', align: 'center' },
        { title: '状态', key: 'status_text', align: 'center' },
        { title: '金额', slot: 'amount', align: 'center' },
        { title: '发票内容', slot: 'content', align: 'center' },
        { title: '发票编号', slot: 'invoice_no', align: 'center' },
        { title: '发票预览', slot: 'preview', align: 'center' }
      ],
      invoiceList: [],
      goodsVisible: false,
      goodsDetails: [] // 详情明细
    };
  },
  computed: {
    hasEnoughMoney() {
      const { balance, payment_fee, auto_pay } = this.echoData;
      if (auto_pay == 1) {
        return true;
      } else {
        if (Number(balance) < Number(payment_fee)) {
          return false;
        } else {
          return true;
        }
      }
    },
    showExchange() {
      let orderTypes = ['RXJ_ADDITION', 'CLI_ADDITION', 'OPC_OWN', 'COM_PERSONAL'];
      return orderTypes.includes(this.orderType);
    }
  },
  watch: {},
  created() {
    this.setShowTypeList(this.typeList[3]);
    // 根据单号获取采购商品数据
    if (this.$route.query.order_code) {
      this.getDetail();
      this.getOrderRefundApplyList();
      this.getPurchaseOperationLogList();
      this.getInvoicePurchasedetail();
    }
  },
  mounted() {},
  methods: {
    previewInvoice(row) {
      const urls = row.nuonuo.c_paper_pdf_url.length ? row.nuonuo.c_paper_pdf_url : row.nuonuo.c_url;
      if (urls.length === 1) {
        const a = document.createElement('a');
        a.href = urls[0];
        a.target = '_blank';
        a.download = '';
        a.style.display = 'none';
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        return;
      }
      this.previewInvoiceUrls = urls;
      this.invoicePreviewVisible = true;
    },
    /**
     * @description:  开票相关
     */
    checkGoodsDetail(row) {
      this.goodsDetails = row.goods;
      this.goodsVisible = true;
    },
    // * api 采购订单开票详情
    getInvoicePurchasedetail() {
      let params = {
        order_code: this.$route.query.order_code
      };
      this.$api.getInvoicePurchasedetail(params).then(
        res => {
          this.invoiceList = res.list;
        },
        rej => {
          this.$Message.error(rej.errmsg);
        }
      );
    },
    /* 查看包裹详情 */
    toLogisticsDetail(item) {
      console.log(item);
      this.progress_express_detail = [item];
      this.logisticsVisible = true;
    },

    // 显示误操作弹窗
    pay() {
      this.payVisible = true;
    },
    // 确认支付
    confirmPayment() {
      this.payLoading = true;
      let params = {
        order_code: this.order_code,
        pay_type: this.payType
      };
      this.$api.postPay(params).then(
        res => {
          this.payVisible = false;
          this.payLoading = false;
          if (res.type === 'REFUND_APPLY') {
            this.tipsVisible = true;
            this.refundApplyCode = res.apply_code;
            return;
          }
          this.$Message.success('支付成功');
          // 支付成功，重新拉取状态
          this.getDetail();
        },
        rej => {
          this.$Message.error(rej.errmsg);
          this.payLoading = false;
        }
      );
    },
    // *api 获取诊所采购单详情
    getDetail() {
      this.$api.getClinicOrderDetail({ cli_order_code: this.$route.query.order_code }).then(
        ({ list, order, express_info }) => {
          this.echoData.status = order.status;
          this.echoData.com_pay_status = order.com_pay_status;

          this.echoData.cli_order_code = order.cli_order_code;
          this.echoData.trade_code = order.trade_code;
          this.echoData.status_text = order.status_text;
          this.echoData.paid_at = order.paid_at;
          this.echoData.ent_info = order.ent_info;
          this.echoData.clinic_name = order.clinic_name;
          this.echoData.organization_name = order.organization_name;
          this.echoData.organization_code = order.organization_code;
          this.echoData.clinic_name = order.clinic_name;

          this.echoData.leading_person = order.leading_person;
          this.echoData.leading_mobile = order.leading_mobile;
          this.echoData.num = order.num;

          // this.echoData.purchase_money = order.money_info.purchase_money
          this.echoData.cli_create_time = order.cli_create_time;
          this.echoData.order_code = order.order_code;
          this.echoData.cli_paid_at = order.cli_paid_at;
          this.echoData.com_outlay = order.money_info.com_outlay;
          this.echoData.cli_payment_fee = order.money_info.cli_payment_fee;
          this.echoData.money_info = order.money_info;
          this.echoData.paid_at = order.paid_at;
          this.echoData.ship_status_text = order.ship_status_text;

          this.echoData.consignee = order.consignee_info.consignee;
          this.echoData.mobile = order.consignee_info.mobile;
          let header = order.consignee_info.address;
          this.echoData.address =
            `${header.prov.name}${header.city.name}${header.county.name || ''}${header.detail}` || '';
          this.echoData.store_status_text = order.store_status_text;
          this.echoData.remark = order.remark;
          this.echoData.sign_status_desc = order.sign_status_desc;
          this.echoData.balance = order.balance;
          this.echoData.auto_pay = order.auto_pay;
          this.echoData.type_text = order.type_text;
          this.echoData.yzt_balance = order.yzt_balance;
          this.echoData.clinic_direct_type_text = order.clinic_direct_type_text;
          this.echoData.clinic_type_text = order.clinic_type_text;
          this.echoData.reject_reason = order.reject_reason;
          this.echoData.partner_name = order.partner_name;
          // this.allow_yzt = order.allow_yzt;
          this.default_pay_type = order.default_pay_type;
          this.payType = order.default_pay_type;
          list.forEach(item => {
            if (item.is_taocan === '1') {
              item.attrs.forEach(item => (item.isChild = true));
              item.children = item.attrs;
              item._showChildren = true;
            }
          });
          this.goods_items = list;

          if (express_info?.length) {
            console.log('innnn2222222');
            this.express_list = express_info;
            this.setShowTypeList(this.typeList[1]);
            // this.goodsTypeList.push(this.typeList[2]);
            // this.goodsTabChange('express_list');
            // this.express_list = packs;
          }
          this.order_code = order.order_code;
          this.orderType = order.type;
          if (!this.showExchange) {
            console.log(2131232);
            this.refundApplyColumns = this.refundApplyColumns.filter(item => !item.isExchange);
          }
        },
        rej => {
          this.$Message.error(rej.errmsg);
        }
      );
    },
    setShowTypeList(setItemObj) {
      console.log('=>(detail.vue:898) setItemObj', setItemObj);
      let index = this.goodsTypeList.findIndex(item => item.type === setItemObj.type);
      if (index === -1) {
        this.goodsTypeList.push(setItemObj);
      }
      this.goodsTypeList.sort((a, b) => a.sort - b.sort);
      console.log('=>(detail.vue:905) goodsTypeList', this.goodsTypeList);
    },
    handleSummary({ columns, data }) {
      console.log('-> data', data);
      const sums = {};
      columns.forEach((column, index) => {
        const key = column.key;
        if (index === 0) {
          sums[key] = {
            key,
            value: '合计'
          };
          return;
        }
        let whiteList = [1, 2, 3, 4, 5, 6, 7, 8, 9];
        if (!whiteList.includes(index)) {
          sums[key] = {
            key,
            value: ''
          };
          // return
        }
        const values = data.map(item => Number(item[key]));
        if (!values.every(value => isNaN(value))) {
          const v = values.reduce((prev, curr) => {
            const value = Number(curr);
            if (!isNaN(value)) {
              return prev + curr;
            } else {
              return prev;
            }
          }, 0);
          if (key === 'clinic_price' || key === 'com_price' || key === 'num') {
            if (key === 'num') {
              sums[key] = {
                key,
                value: v
              };
            } else {
              sums[key] = {
                key,
                value: v.toFixed(2)
              };
            }
          } else {
            sums[key] = {
              key,
              value: ''
            };
          }
        } else {
          sums[key] = {
            key,
            value: ''
          };
        }
      });
      return sums;
    },

    goodsTabChange(type) {
      this.activeGoodsType = type;
    },
    getOrderRefundApplyList() {
      let params = { order_code: this.$route.query.order_code };
      // let params = {order_code: 'PCO230316164230766561'}
      console.log('=>(detail.vue:241) params', params);
      this.$api
        .getOrderRefundApplyList(params)
        .then(res => {
          this.refundApplyData = res.list;
          if (this.refundApplyData.length > 0) {
            this.setShowTypeList(this.typeList[0]);
            this.setShowTypeList(this.typeList[2]);
            // this.goodsTypeList = [
            //   { type: 0, name: '采购商品详情' },
            //   { type: 1, name: '售后明细' }
            // ];
          }
          console.log('=>(detail.vue:242) res', res);
        })
        .catch(err => {
          this.$Message.error(err.errmsg);
        });
    },

    getPurchaseOperationLogList() {
      let params = { order_code: this.$route.query.order_code };
      console.log('=>(detail.vue:241) params', params);
      this.$api
        .getPurchaseOperationLogList(params)
        .then(res => {
          console.log('=>(detail.vue:242) res', res);
          this.operatorRecordData = res.list;
        })
        .catch(err => this.$Message.error(err.errmsg));
    },

    agreeRefund() {
      this.tipsVisible = false;
    },
    cancelHandler() {
      window.open(
        `${process.env.VUE_APP_BASE_URL}purchase/after-sale/online-detail?apply_code=${this.refundApplyCode}&action=audit`
      );
      // this.$router.push({path: '/purchase/after-sale/detail', query: {apply_code: this.refundApplyCode, action: 'audit'}})
    },
    // 给表格行设置样式
    rowClassName(row, index) {
      if (row.children && row.children.length) {
        return 'ivu-table-row-tree';
      }
      if (row.isChild) {
        return 'ivu-table-row-tree-child';
      }
    },
    handleSpan({ row, column, rowIndex, columnIndex }) {
      // 合并单元格起始位置
      let firstBiginColsIndex = 0;
      // 合并单元格数量
      let firstColsNum = 1;
      // 合并单元格起始位置
      let secondBiginColsIndex = 0;
      // 合并单元格数量
      let secondColsNum = 1;

      // 一级单元格合并
      // [0,1,2]代表从第一行开始合并三行
      // [2,3,4,5]代表从第三行开始合并四行
      let includeFirstIndex = [];
      this.express_list.forEach((item, index) => {
        if (item.ship_unique === row.ship_unique) {
          includeFirstIndex.push(index);
        }
      });
      if (includeFirstIndex.includes(rowIndex)) {
        firstBiginColsIndex = includeFirstIndex[0];
        firstColsNum = includeFirstIndex.length;
      }
      // 二级单元格合并
      let includeSecondIndex = [];
      this.express_list.forEach((item, index) => {
        if (item.goods_unique === row.goods_unique) {
          includeSecondIndex.push(index);
        }
      });
      if (includeSecondIndex.includes(rowIndex)) {
        secondBiginColsIndex = includeSecondIndex[0];
        secondColsNum = includeSecondIndex.length;
      }

      //---------------------------------------------------------------------这部分代码不用动-
      //合并一级单元格
      if (firstColsNum + firstBiginColsIndex > this.express_list.length) {
        console.error(new Error('合并列数超出表格列数'));
        return;
      }

      if (columnIndex === 0 && rowIndex === firstBiginColsIndex) {
        return [firstColsNum, 1];
      } else {
        if (columnIndex === 0 && rowIndex > firstBiginColsIndex && rowIndex < firstColsNum + firstBiginColsIndex) {
          return [0, 0];
        }
      }

      //合并二级单元格
      if (secondColsNum + secondBiginColsIndex > this.express_list.length) {
        console.error(new Error('合并列数超出表格列数'));
        return;
      }

      if (columnIndex === 1 && rowIndex === secondBiginColsIndex) {
        return [secondColsNum, 1];
      } else {
        if (columnIndex === 1 && rowIndex > secondBiginColsIndex && rowIndex < secondColsNum + secondBiginColsIndex) {
          return [0, 0];
        }
      }
    }
  }
};
</script>

<style lang="less" scoped>
.detail-wrapper {
  padding-bottom: 50px;

  .basic-info {
    border: 1px solid #b7b7b7;

    .basic-info-item {
      display: flex;
      line-height: 22px;
      border-bottom: 1px solid #b7b7b7;

      &:last-of-type {
        border-bottom: none;
      }

      text-align: center;

      .item-label {
        width: 180px;
        border-right: 1px solid #b7b7b7;
        background: #e6e6e6;
      }

      .item-content {
        flex: 1;
        text-align: left;
        text-indent: 30px;
      }
    }
  }

  .goods-list {
    padding-top: 20px;
  }

  .logistics-information {
    margin-bottom: 30px;

    .logistics-info-box {
      padding: 12px 12px 6px;
      border: 1px solid #e7e7e7;
      width: 260px;
      margin: 12px;
      background: rgba(225, 225, 225, 0.3);

      .title {
        font-size: 15px;
        font-weight: 600;
        line-height: 22px;
      }

      .info-item {
        margin-bottom: 20px;
      }

      .foot-tip {
        line-height: 20px;
      }
    }
  }
}

.table-wrapper {
  position: relative;

  ::v-deep .ivu-table-wrapper {
    overflow: unset;
  }
}

.sticky-table-head {
  ::v-deep .ivu-table {
    overflow: unset;

    .ivu-table-header {
      position: sticky;
      top: 94px;
      z-index: 3;
    }
  }
}

.pay-wrapper {
  margin-bottom: 40px;
  margin-top: 20px;

  .pay-content {
    margin: 10px 0;

    .radio-label {
      font-size: 14px;
    }

    .balance {
      font-size: 20px;
    }
  }
}

.wrap {
  white-space: pre-wrap;
}

.min36 {
  min-width: 36px;
}

.a-btn {
  color: #333;

  &:hover {
    color: #447cdd;
  }
}
</style>

<style lang="less" scoped>
.radius-btn {
  border-radius: 30px;
  font-size: 12px;
}

.modal-title {
  text-align: center;
  margin-bottom: 15px;
  margin-top: -30px;
  font-size: 16px;
}

::v-deep .ivu-modal-footer {
  border-top: none;
}

::v-deep .ivu-modal-header {
  border-bottom: none;
}

::v-deep .vertical-center-modal {
  display: flex;
  align-items: center;
  justify-content: center;

  .ivu-modal {
    top: -10vh;
  }
}

.logistics-information {
  margin-bottom: 30px;

  .logistics-info-box {
    padding: 12px 12px 6px;
    border: 1px solid #e7e7e7;
    width: 260px;
    margin: 12px;
    background: rgba(225, 225, 225, 0.3);

    .title {
      font-size: 15px;
      font-weight: 600;
      line-height: 22px;
    }

    .info-item {
      margin-bottom: 20px;
    }

    .foot-tip {
      line-height: 20px;
    }
  }
}

.default-title {
  color: #333;
  cursor: pointer;
  font-size: 12px;
  line-height: 21px;
}

.active-title {
  color: #155bd4;
  font-size: 16px;
  line-height: 18px !important;
  //border-bottom: 1px solid #155BD4;
}

.reject-reason {
  color: red;
}
</style>

<style>
.ivu-tooltip-inner {
  max-width: 400px;
}
</style>
