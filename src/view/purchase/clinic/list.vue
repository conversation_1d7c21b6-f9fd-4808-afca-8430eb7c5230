<template>
  <div class="f_order-wrapper">
    <Form inline @submit.native.prevent @keyup.enter.native="onSearch">
      <Row>
        <FormItem>
          <Input v-model="queryFormData.goods_name" placeholder="请输入商品名称" clearable />
        </FormItem>
        <FormItem>
          <Input v-model="queryFormData.order_code" placeholder="请输入采购单号/交易号" clearable />
        </FormItem>
        <FormItem>
          <surningSearch
            ref="nursing-search"
            v-model="queryFormData.ent_code"
            @getType="type => (queryFormData.ent_type = type)"
            :isClearable="true"
          ></surningSearch>
        </FormItem>
        <FormItem>
          <license-search
            ref="license-search"
            v-model="queryFormData.ent_code_organ"
            :type="queryFormData.ent_type_organ"
            :isClearable="true"
            :company_id="queryFormData.company_id"
            :showType="false"
            @getType="type => (queryFormData.ent_type_organ = type)"
          ></license-search>
        </FormItem>
        <FormItem>
          <Select v-model="queryFormData.purchase_type" placeholder="请选择采购单类型" clearable>
            <Option v-for="item in purchaseTypeDesc" :key="item.id" :value="item.id">{{ item.desc }}</Option>
          </Select>
        </FormItem>
      </Row>
      <Row>
        <FormItem>
          <supplier-search
            ref="supplier-search"
            v-model="queryFormData.supplier_id"
            :isClearable="true"
          ></supplier-search>
        </FormItem>
        <FormItem>
          <clinic-type-search
            v-model="queryFormData.clinic_type"
            :clinic_direct_type="queryFormData.clinic_direct_type"
            @directChange="directChange"
          ></clinic-type-search>
        </FormItem>
        <FormItem>
          <DatePicker
            type="daterange"
            clearable
            :options="disabledTime"
            format="yyyy-MM-dd"
            placeholder="请选择采购时间"
            v-model="timeRange"
            @on-change="times => handleTimeChange(times)"
            class="time-range"
          ></DatePicker>
        </FormItem>
        <FormItem>
          <Select v-model="queryFormData.sign_status" placeholder="请选择签收状态" clearable>
            <Option v-for="item in signStatusDesc" :key="item.id" :value="item.id">{{ item.desc }}</Option>
          </Select>
        </FormItem>
      </Row>
      <Row>
        <FormItem style="text-align: left">
          <Button class="mr10" type="primary" @click="onSearch">筛选</Button>
          <Button class="mr10" type="default" @click="onResetSearch">重置</Button>
          <Dropdown trigger="click" @on-click="exportExcel">
            <Button :loading="downloadLoading">
              导出
              <Icon type="ios-arrow-down" />
            </Button>
            <DropdownMenu slot="list">
              <DropdownItem name="COM_ORDER_DETAILS">导出订单明细</DropdownItem>
              <DropdownItem name="COM_ORDER_GOODS">导出商品明细</DropdownItem>
            </DropdownMenu>
          </Dropdown>
        </FormItem>
      </Row>
    </Form>
    <div class="table-wrapper">
      <div class="panel-nav">
        <a :class="{ active: !queryFormData.list_status }" class="nav" @click.prevent.capture="onStatusChange('')">
          全部
        </a>
        <a
          :class="{ active: $route.query.list_status === item.id }"
          v-for="item in listStatusDescV2"
          :key="item.id"
          @click.prevent.capture="onStatusChange(item.id)"
        >
          {{ item.desc }}
          <Tag :color="getTagColor(item.id)">{{ list_count[item.id] }}</Tag>
        </a>
      </div>
      <Table :columns="tableCols" :data="list" :loading="tableLoading" :height="$store.state.app.clientHeight - 430">
        <!-- 采购金额 -->
        <template slot-scope="{ row, index }" slot="money">
          <span v-if="row.money">￥{{ row.money }}</span>
          <span v-else></span>
        </template>
        <template slot-scope="{ row, index }" slot="ent_info">
          <div>名称：{{ row.ent_info.ent_name }}</div>
          <div>类型：{{ row.ent_info.ent_type === 'RXJ' ? '养疗馆' : '诊所' }}</div>
        </template>

        <template slot="cli_type" slot-scope="{ row }">
          <div v-if="row.clinic_direct_type_text && row.clinic_type_text">
            {{ row.clinic_direct_type_text }} / {{ row.clinic_type_text }}
          </div>
          <div v-else>-</div>
        </template>
        <template slot="organization_name" slot-scope="{ row }">
          {{ row.organization_name || '-' }}
        </template>
        <template slot="sign_status_desc" slot-scope="{ row }">
          <div>
            <div>
              {{ row.sign_status_desc || '-' }}
            </div>
          </div>
        </template>

        <!-- 收入金额 -->
        <template slot-scope="{ row, index }" slot="com_income">
          <span>￥{{ row.com_income }}</span>
        </template>
        <template slot="action" slot-scope="{ row }">
          <a @click="toDetail(row)" v-if="row.status === 'HAS_PAY' && row.com_pay_status === 'WAIT'">去支付</a>
          <a @click="toDetail(row)" style="margin-left: 10px">详情</a>
        </template>
      </Table>
      <div class="block_20"></div>
      <KPage
        :current="+queryFormData.page"
        :page-size="+queryFormData.pageSize"
        :total="total"
        style="text-align: center"
        @on-change="handleCurrentChange"
        @on-page-size-change="handleSizeChange"
      />
    </div>
  </div>
</template>

<script>
import S from 'utils/util';
import search from '@/mixins/search';
import download from '@/mixins/downloadExcel';
import renderHeader from '@/mixins/renderHeader';
import surningSearch from '@/components/surning-search/surning-search';
import licenseSearch from '@/components/license-search/license-search';
import supplierSearch from '@/components/supplier-search/supplier-search';
import ClinicTypeSearch from '../../../components/remote-search/clinic-type-search.vue';

const init_query_form_data = {
  page: 1,
  pageSize: 20,
  goods_name: '', // 商品名称
  order_code: '', // 订单编号
  sign_status: '',
  // status: '', // 下拉数据
  // clinic_id: '', // 诊所id
  cp_id: '', // 诊所id
  list_status: '', // 标签数据
  ent_code: '', //采购主体
  ent_type: '', //采购主体类型
  purchase_type: '', // 采购单类型
  st: '',
  et: '',
  clinic_direct_type: 'DIGITIZE',
  clinic_type: '',
  ent_code_organ: '',
  ent_type_organ: '',
  supplier_id: '',
  r: ''
};
export default {
  name: 'list',
  mixins: [search, renderHeader, download],
  components: { ClinicTypeSearch, surningSearch, licenseSearch, supplierSearch },
  data() {
    return {
      apiName: 'getClinicOrderList',
      downloadApiName: 'getReportUrl',
      queryFormData: { ...init_query_form_data },
      clinic_list: [], // 诊所枚举
      listStatusDescV2: [],
      tableCols: [
        { title: '采购单号', key: 'cli_order_code', align: 'center', width: 100 },
        { title: '交易号', key: 'trade_code', align: 'center', width: 100 },
        { title: '进货商品', key: 'goods_names', align: 'center', tooltip: true, width: 100 },
        { title: '供应商', render: (h, { row }) => h('span', {}, row.supplier.name), align: 'center', width: 100 },
        {
          title: '销售商',
          minWidth: 100,
          key: 'seller',
          align: 'center',
          renderHeader: (h, params) => this._renderHeader(h, params, '诊所采购时货品金额部分，发票由销售商开具')
        },
        { title: '采购数量', key: 'num', align: 'center', width: 100 },
        { title: '采购金额', slot: 'money', align: 'center', width: 100 },
        { title: '采购主体', slot: 'ent_info', width: 160 },
        { title: '诊所类型', slot: 'cli_type', align: 'center', width: 140 },
        { title: '营业执照名称', slot: 'organization_name', align: 'center', width: 100 },
        { title: '采购下单时间', key: 'create_time', align: 'center', width: 140 },
        { title: '采购单类型', key: 'type_text', align: 'center', width: 100 },
        { title: '采购单状态', key: 'status_text', align: 'center', width: 100 },
        { title: '入库状态', key: 'store_status_text', align: 'center', width: 100 },
        {
          title: '签收状态',
          // slot: 'sign_status_desc',
          align: 'center',
          render: (h, { row }) => h('span', {}, row.sign_status_desc || '-'),
          width: 100
        },
        // {title: '付款状态', key: 'pay_status', align: 'center'},
        { title: '操作', slot: 'action', align: 'center', width: 100, fixed: 'right' }
      ],
      list_count: {},
      isAutoPay: 'OFF',
      purchaseTypeDesc: [],
      signStatusDesc: [],
      disabledTime: {
        disabledDate(date) {
          return date && date.valueOf() > Date.now();
        }
      },
      tableLoading: false,
      downloadLoading: false,
      directTypeDesc: [],
      typeDesc: []
    };
  },
  computed: {
    getTagColor(type) {
      return type => {
        switch (type) {
          case 'WAIT_AUDIT': // 待审核
          case 'CLI_W_PAY': // 诊所待付款
          case 'COM_W_PAY': // 省公司待付款
            return 'warning';
          case 'WAIT_SHIP': // 待发货
            return 'primary';
          case 'WAIT_EDIT': // 待发货
            return 'error';
          case 'HAS_SHIP': // 已发货
          case 'FINISHED': // 已完成
            return 'success';
          default: // 已取消
            return 'default';
        }
      };
    }
  },
  watch: {},
  created() {
    this.queryFormData = S.merge(this.queryFormData, this.$route.query);
    this.submitQueryForm(true);
    // 获取状态列表枚举需求delete
    this.getClinicOrderOptions();
  },
  mounted() {},
  methods: {
    loadList() {
      this.tableLoading = true;
      this.$api[this.apiName](this.queryFormData)
        .then(data => {
          console.log(data, 'data');
          this.total = +data.total;
          this.list = data.list;
          this.list_count = data.list_count;
          this.isAutoPay = data.auto_pay;
        })
        .catch(error => {
          this.$Message.error(error.errmsg);
        })
        .finally(() => {
          this.tableLoading = false;
        });
    },
    // 跳转到诊所采购单的详情
    toDetail(row) {
      let query = {
        order_code: row.cli_order_code,
        pay_status: row.com_pay_status
      };
      this.$router.push({
        path: '/purchase/clinic/detail',
        query
      });
    },
    onStatusChange(status) {
      console.log('-> status', status);
      this.queryFormData.page = 1;
      this.queryFormData.list_status = status;
      this.submitQueryForm();
    },
    onResetSearch() {
      this.queryFormData = { ...init_query_form_data };
      this.submitQueryForm();
      this.timeRange = [];
      // this.$refs['clinic-search'].clear()
      this.$refs['nursing-search'].clear();
      this.$refs['license-search'].clear();
      this.$refs['supplier-search'].clear();
    },
    // 获取状态-需求delete
    getClinicOrderOptions() {
      this.$api.getClinicOrderOptions().then(res => {
        // this.clinic_list = res.clinic_list
        this.listStatusDescV2 = S.descToArrHandle(res.listStatusDescV2);
        this.purchaseTypeDesc = S.descToArrHandle(res.purchaseTypeDesc);
        this.signStatusDesc = S.descToArrHandle(res.signStatusDesc);
      });
    },
    // 获取诊所列表
    getClinicList() {
      let params = {
        name: '',
        status: '',
        page: '',
        pageSize: ''
      };
      this.$api.getClinicList(params).then(res => {
        this.clinic_list = res.list;
      });
    },
    changeAutoPay() {
      return new Promise(resolve => {
        this.$Modal.confirm({
          title: `确认${this.isAutoPay === 'ON' ? '关闭' : '开启'}`,
          content: `<p>是否确认${this.isAutoPay === 'ON' ? '关闭' : '开启'}“诊所银联采购单”的一键支付</p>`,
          onOk: () => {
            this.changeSelfAutoPay();
          }
        });
      });
    },
    changeSelfAutoPay() {
      let act = this.isAutoPay === 'ON' ? 'OFF' : 'ON';
      let params = { act };
      console.log('=>(detail.vue:241) params', params);
      this.$api
        .changeSelfAutoPay(params)
        .then(res => {
          this.$Message.success(`${act === 'ON' ? '开启' : '关闭'}成功`);
          this.loadList();
        })
        .catch(err => this.$Message.error(err.errmsg));
    },
    // 导出
    exportExcel(exportType) {
      let queryParams = {
        ...this.queryFormData
      };
      delete queryParams.r;
      delete queryParams.page;
      delete queryParams.pageSize;
      delete queryParams['nav-stack-key'];
      console.log('-> %c queryParams  === %o', 'font-size: 15px;color: green;', queryParams);
      const isQueryParamsEmpty = Object.values(queryParams).every(param => {
        return !param;
      });
      console.log('-> %c isQueryParamsEmpty  === %o', 'font-size: 15px;color: green;', isQueryParamsEmpty);
      console.log('-> %c isQueryParamsEmpty  === %o', 'font-size: 15px;color: green;', isQueryParamsEmpty);
      if (isQueryParamsEmpty) {
        this.$Message.error('请设置筛选条件后导出');
        return;
      }
      let params = {
        ...queryParams,
        type: exportType
      };
      this.downloadExcel(params);
    },
    directChange(val) {
      this.$set(this.queryFormData, 'clinic_direct_type', val);
    }
  },
  beforeRouteUpdate(to, from, next) {
    // S.log(to.params['nav-stack-key-dir'], 'nav-stack-key-dir')
    this.queryFormData = S.merge(init_query_form_data, to.query);
    this.getTimeRange();
    this.loadList();
    next();
  }
};
</script>

<style lang="less" scoped>
.auto-box {
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-sizing: border-box;
  height: 25px;
  line-height: 32px;
  margin-bottom: 7px;
  margin-top: -10px;

  ::v-deep .ivu-checkbox-inner {
    border-radius: 50%;
  }
}
</style>
