<template>
  <div>
    <Modal
      :mask-closable="false"
      :value="showModal"
      :title="title"
      width="800px"
      @on-visible-change="closeModal"
      @on-ok="submitForm('refundModal')"
    >
      <div slot="footer" class="modal-foot">
        <Button @click="closeModal(false)">取消</Button>
        <Button type="primary" :loading="loading" @click="submitForm('refundModal')">确定</Button>
      </div>
      <div class="purchase-modal-wrapper">
        <div class="modal-header flex flex-item-between flex-item-align">
          <span>退款商品明细：</span>
          <Select
            v-model="select_good"
            filterable
            placeholder="添加未发货商品"
            style="width: 200px"
            @on-select="selectGoods"
          >
            <Option v-for="(item, index) in goods_list" :key="index" :value="item.spu_code">{{ item.name }}</Option>
          </Select>
        </div>
        <div class="table-wrapper">
          <Table
            :columns="info.is_opc_pay ? tableColumns_op_order : tableColumns_cli_order"
            :data="select_goods_list"
            :summary-method="handleSummary"
            border
            show-summary
          >
            <template slot="wait_num" slot-scope="{ row, index }">
              <InputNumber
                v-model="select_goods_list[index].wait_num"
                :min="0"
                :max="Number(row.wait_purchase_num)"
                :precision="0"
                placeholder="输入退款数量"
                @on-change="e => changeNum(e, index, row)"
              />
            </template>
            <template slot="action" slot-scope="{ row, index }">
              <a style="color: red" @click="deleteGood(index)">删除</a>
            </template>
          </Table>
        </div>
        <Form ref="refundModal" :label-width="100" label-colon>
          <div>
            <Row>
              <Col span="18">
                <FormItem label="养疗馆" v-if="info.ent_type == 'RXJ'">
                  <p>{{ info.rxj_name || '-' }}</p>
                </FormItem>

                <FormItem label="诊所" v-else>
                  <p>{{ info.clinic_name || '-' }}</p>
                </FormItem>
              </Col>
              <Col span="18">
                <!-- 二者用同个字段 -->
                <FormItem label="直营运营中心退款金额" v-if="info.is_opc_pay" :label-width="140">
                  <p><span v-if="cli_refund_price">￥</span>{{ cli_refund_price || '-' }}</p>
                </FormItem>

                <FormItem label="诊所退款金额" v-else>
                  <p><span v-if="cli_refund_price">￥</span>{{ cli_refund_price || '-' }}</p>
                </FormItem>
              </Col>
              <Col span="18">
                <FormItem label="退款原因" required>
                  <Input v-model.trim="info.refund_info" autosize type="textarea" />
                </FormItem>
              </Col>
              <Col span="18">
                <FormItem label="备注">
                  <Input v-model="info.remark" autosize type="textarea" />
                </FormItem>
              </Col>
            </Row>
          </div>
        </Form>
      </div>
    </Modal>
  </div>
</template>

<script>
import S from 'utils/util';
import { $operator } from 'utils/operation';
export default {
  name: 'purchase-modal',
  props: {
    showModal: {
      type: Boolean,
      default: false
    },
    orderInfo: {
      type: Object,
      default: () => {
        return {
          clinic_name: '',
          cli_price: '',
          com_price: '',
          items: [],
          ent_type: res.ent_type,
          ent_type_text: res.ent_type_text,
          rxj_info: res.rxj_info
        };
      }
    },
    goodsList: {
      type: Array,
      default: () => []
    },
    title: {
      type: String,
      default: '申请退款'
    }
  },
  data() {
    return {
      select_good: '', // 未发货商品列表
      loading: false,
      tableColumns_cli_order: [
        { title: '商品编号', key: 'spu_code', align: 'center' },
        { title: '商品', key: 'name', align: 'center' },
        { title: '诊所进货单价', key: 'single_cli_price', align: 'center' },
        { title: '待采购数', key: 'wait_purchase_num', align: 'center' },
        { title: '退款数量', key: 'wait_num', slot: 'wait_num', align: 'center', width: 100 },
        { title: '诊所退款金额', key: 'tab_cli_price', align: 'center' },
        { title: '操作', slot: 'action', align: 'center' }
      ],
      tableColumns_op_order: [
        { title: '商品编号', key: 'spu_code', align: 'center' },
        { title: '商品', key: 'name', align: 'center' },
        { title: '诊所进货单价', key: 'single_com_price', align: 'center' },
        { title: '待采购数', key: 'wait_purchase_num', align: 'center' },
        { title: '退款数量', key: 'wait_num', slot: 'wait_num', align: 'center', width: 100 },
        { title: '直营运营中心退款金额', key: 'tab_com_price', align: 'center' },
        { title: '操作', slot: 'action', align: 'center' }
      ],
      select_goods_list: [],
      info: {},
      copy_info: {},
      cli_refund_price: 0,
      path: this.$router.history.current.path
    };
  },
  computed: {
    goods_list() {
      let good_arr = this._.cloneDeep(this.goodsList).filter(
        item => Number(item.wait_purchase_num) > Number(item.refund_wait_num)
      );
      let resultArr = [];
      good_arr.map(item => {
        if (Number(item.wait_purchase_num) > Number(item.refund_wait_num)) {
          resultArr.push({
            ...item,
            num: Number(item.num),
            // wait_num: Number(item.wait_purchase_num)- Number(item.refund_wait_num),
            wait_num: null,
            wait_num_copy: Number(item.wait_purchase_num) - Number(item.refund_wait_num)
          });
        }
      });
      return resultArr;
    }
  },
  watch: {
    orderInfo: {
      handler(val) {
        console.log('val', val);
        this.info = this._.cloneDeep(val);
        this.copy_info = this._.cloneDeep(val);
        this.select_goods_list = this.changeType(this.info.items);
      }
    }
    // select_goods_list: {
    //   deep: true,
    //   handler (val) {
    //     if (!val.length) return
    //     val.forEach( (item, index) => {
    //       this.select_goods_list[index].tab_cli_price = S.mathMul(Number(item.single_cli_price), Number(item.wait_num))
    //       this.select_goods_list[index].tab_com_price = S.mathMul(Number(item.single_com_price), Number(item.wait_num))
    //     } )
    //   }
    // }
  },
  created() {},
  mounted() {},
  methods: {
    changeType(items) {
      if (!items || !items.length) {
        return [];
      }
      return items.map(item => {
        const toast_wait_num = Number(item.wait_purchase_num) - Number(item.refund_wait_num);
        return {
          ...item,
          wait_num: Number(item.wait_num),
          wait_num_copy: toast_wait_num,
          tab_cli_price:
            item.wait_num == toast_wait_num
              ? item.wait_refund_price.cli
              : S.mathMul(Number(item.single_cli_price), Number(item.wait_num)),
          tab_com_price:
            item.wait_num == toast_wait_num
              ? item.wait_refund_price.com
              : S.mathMul(Number(item.single_com_price), Number(item.wait_num))
        };
      });
    },
    // 待退款数量是否存在0或者空
    hasNumEmpty() {
      let hasNumFlag = this.select_goods_list.some(item => {
        if (Number(item.wait_num) < 1) {
          this.$Message.error(`${item.name}得数量必须大于0`);
        }
        return Number(item.wait_num) < 1;
      });
      return hasNumFlag;
    },
    submitForm(name) {
      // 是否有商品
      if (!this.select_goods_list.length) {
        this.$Message.error('请先添加未发货商品');
        return;
      }

      // 商品得退款数量是否存在且大于0
      if (this.hasNumEmpty()) {
        return;
      }

      // 是否填写了退款原因
      if (this.info.refund_info === '') {
        this.$Message.error('请填写退款原因');
        return;
      }

      this.postRefundEdit();
    },
    changeNum(e, index, row) {
      this.select_goods_list[index].wait_num = e;
      if (e === row.wait_num_copy) {
        this.select_goods_list[index].tab_cli_price = this.select_goods_list[index].wait_refund_price.cli;
        this.select_goods_list[index].tab_com_price = this.select_goods_list[index].wait_refund_price.com;
      } else {
        this.select_goods_list[index].tab_cli_price = $operator.multiply(
          e,
          this.select_goods_list[index].single_cli_price
        );
        this.select_goods_list[index].tab_com_price = $operator.multiply(
          e,
          this.select_goods_list[index].single_com_price
        );
      }
    },
    deleteGood(index) {
      this.select_goods_list.splice(index, 1);
      if (!this.select_goods_list.length) {
        this.cli_refund_price = 0;
      }
    },
    // 合计
    handleSummary({ columns, data }) {
      console.log('1');
      const sums = {};
      columns.forEach((column, index) => {
        const key = column.key;
        if (index === 0) {
          sums[key] = {
            key,
            value: '合计'
          };
          return;
        }
        const values = data.map(item => Number(item[key]));
        if (!values.every(value => isNaN(value))) {
          const v = values.reduce((prev, curr) => {
            const value = Number(curr);
            if (!isNaN(value)) {
              return prev + curr;
            } else {
              return prev;
            }
          }, 0);
          if (index === 5 || index === 6 || index === 7) {
            sums[key] = {
              key,
              value: S.mathAdd(v, 0)
            };
            if (index === 5) {
              this.cli_refund_price = S.mathAdd(v, 0);
            }
            if (index === 7) {
              this.com_refund_price = S.mathAdd(v, 0);
            }
          } else {
            sums[key] = {
              key,
              value: ''
            };
          }
        } else {
          sums[key] = {
            key,
            value: ''
          };
        }
      });
      return sums;
    },
    selectGoods(val) {
      this.handleSelectGood(val);
    },
    handleSelectGood(val) {
      let isExit = this.select_goods_list.findIndex(item => item.spu_code === val.value);
      if (isExit > -1) {
        this.$Message.error('请勿重复添加');
        return;
      }
      const addItem = this.goods_list.filter(item => item.spu_code === val.value);
      const copy_item = this._.cloneDeep(addItem);
      copy_item.map(item => {
        item.tab_cli_price = 0;
        item.tab_com_price = 0;
      });
      this.select_goods_list = this.select_goods_list.concat(copy_item);
    },
    //关闭弹窗
    closeModal(show) {
      if (!show) {
        this.$emit('update:showModal', show);
        this.info = {};
        this.select_goods_list = [];
        this.select_good = '';
      } else {
        this.info = this._.cloneDeep(this.copy_info);
        this.select_goods_list = this.changeType(this.info.items);
      }
    },
    handleListOf() {
      let listof = {};
      this.select_goods_list.forEach(item => {
        listof = { ...listof, ...{ [item.spu_code]: item.wait_num } };
      });
      return JSON.stringify(listof);
    },
    // 提交退款申请
    postRefundEdit() {
      let params = {
        id: this.$route.query.id,
        listof: this.handleListOf(),
        refund_info: this.info.refund_info,
        remark: this.info.remark,
        pack_order_id: this.$route.query.pack_order_id
      };
      this.loading = true;
      this.$api
        .postRefundEdit(params)
        .then(
          res => {
            this.$Message.success('提交成功');
            if (this.path === '/purchase/product/detail') {
              this.$parent.getDetail();
            } else {
              this.$parent.getRefundInfo();
            }
            this.closeModal(false);
          },
          rej => this.$Message.error(rej.errmsg)
        )
        .finally(() => (this.loading = false));
    }
  }
};
</script>

<style lang="less" scoped>
.table-wrapper {
  padding: 10px 0 20px;
}

.spu-box {
  flex-wrap: wrap;

  .spu-items {
    align-items: center;
    width: 50%;
    margin: 10px 0;
  }
}
</style>
<style lang="less" scoped>
.spu-modal {
  .ivu-modal {
    .ivu-modal-body {
      min-height: 400px;
      max-height: 500px;
      overflow-y: auto;
    }
  }

  .ivu-input-number-input {
    text-align: center;
  }
}
::v-deep .ivu-form-item {
  margin-bottom: 10px !important;
}
p {
  margin: 0;
}
</style>
