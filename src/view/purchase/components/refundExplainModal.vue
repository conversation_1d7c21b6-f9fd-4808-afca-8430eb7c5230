<template>
  <Modal
    :value="value"
    width="800px"
    :title="title"
    :footer-hide="true"
    :mask-closable="false"
    :lock-scroll="true"
    @on-visible-change="changeVisible"
  >
    <div class="content">
      <div class="header-text">一 非开业采购单的售后：</div>
      <div class="text-box mb12">
        <p class="text mb12">
          <span class="bold">1 选择换货，</span
          >对于退给诊所/养疗馆/个人客户/外部公司的款项，当审核通过后，直接退到其预付款余额中。
        </p>
        <p class="text">
          <span class="bold">2 选择无需换货，</span
          >对于退给诊所/养疗馆/个人客户/外部公司的款项，当审核通过后，财务线下操作，退到其对公户，不退到其预付款余额。
        </p>
        <p class="tip mb12">
          （当审核通过后，系统将款项自动退到预付款余额，然后再自动从预付款余额中退出，所以会产生这两条关于此诊所/养疗馆/个人客户/外部公司的预付款余额交易明细，相当于没退到预付款余额）
        </p>
        <p class="text">
          3 无论选择换货或无需换货，<span class="bold">对于退给省公司或直营运营中心的款项，</span
          >当审核通过后，直接退到其预付款余额中。
        </p>
      </div>

      <div class="header-text">二 开业采购单的售后：</div>
      <div class="text-box mb12">
        <p class="text mb12">
          1 如果首采包中状态为已采购的商品发生售后，那么仅商品退到首采包中，商品状态变为待采购，预付款余额无变化。
        </p>
        <p class="text">
          2 如果首采包中状态为待采购的商品发生售后，款项退到预付款余额中。<span class="tip">
            （如果当初首采包是诊所支付的，那么就退到诊所预付款余额；如果当初首采包是合伙人支付的，那么就退到合伙人预付款余额）
          </span>
        </p>
      </div>

      <div class="header-text">三 数字化诊所的历史采购单的售后：</div>
      <div class="text-box">
        <p class="text mb12">
          <span class="bold">当非数字化诊所转为数字化诊所之后，</span>发生以下任意一种情况时，无论是否选择换货，<span
            class="bold"
            >对于省公司退给诊所的款项，</span
          >当审核通过后，财务线下操作，退到其对公户，不退到其预付款余额。<span class="bold"
            >对于上海树家退给省公司的款项，</span
          >依然还是退到省公司的预付款余额。
        </p>
        <p class="text mb12">1 转型前创建的采购单，在转型之后，此采购单发生了售后。</p>
        <p class="text mb12">
          2 转型前创建的首采包，且当初首采包是诊所支付的，在转型之后，首采包中状态为待采购的商品发生售后。
        </p>
        <p class="text">3 转型前创建的采购售后单，且采购售后单的财务初审还没审核通过，在转型之后，财务初审通过了。</p>
      </div>
    </div>
  </Modal>
</template>

<script>
export default {
  name: 'refundExplainModal',
  mixins: [],

  components: {},
  props: {
    value: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      default: '退款去向说明'
    }
  },

  data() {
    return {};
  },

  computed: {},

  watch: {},

  created() {},

  mounted() {},

  destroyed() {},

  methods: {
    changeVisible(visible) {
      if (!visible) {
        this.closeModal();
      }
    },

    closeModal() {
      this.$emit('input', false);
    }
  }
};
</script>

<style scoped lang="less">
::v-deep .ivu-modal-body {
  max-height: 500px;
  min-height: 300px;
  overflow-y: auto;
}

::v-deep .ivu-modal-header-inner {
  font-weight: bold;
  font-size: 16px;
  color: #333333;
  line-height: 24px;
}

.content {
  .header-text {
    font-weight: 600;
    font-size: 15px;
    color: #333333;
    line-height: 24px;
    margin-bottom: 12px;
  }

  .text-box {
    padding: 16px;
    background: #f9fafb;
    border-radius: 4px;

    .text {
      font-weight: 400;
      font-size: 14px;
      color: #333333;
      line-height: 22px;
    }

    .tip {
      font-weight: 400;
      font-size: 12px;
      color: #999999;
      line-height: 18px;
    }
  }
}

.mb12 {
  margin-bottom: 12px;
}
.bold {
  font-weight: bold;
}
</style>
