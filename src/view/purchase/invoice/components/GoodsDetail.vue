<template>
  <Modal
    :value="goodsVisible"
    :title="title"
    width="680px"
    @on-visible-change="changeVisible"
    footer-hide
    :styles="{maxHeight: '400px',minHeight:'200px'}"
    class="goods-modal"
  >
    <!-- 搜索条件 -->
    <div style="text-align: right" class="mb10" v-if="!isGoodsDetails">
      <Input v-model="queryFormData.order_code" placeholder="输入订单编号筛选"
             clearable @keyup.enter.native="onSearch" @on-clear="onSearch"
             style="width: 180px">
        <Icon type="ios-search" slot="suffix" class="cursor" @click="onSearch" />
      </Input>
      <dvd></dvd>
      <Button type="primary" @click="onSearch">搜索</Button>
    </div>
    <Table :data="list" :columns=" isGoodsDetails ? detailsGoodsCols : goodsCols" :loading="tableLoading" :height="isGoodsDetails ? 490 : 410">
      <!-- 规格 -->
      <template slot-scope="{row}" slot="spec">
        <div>{{ row.spec || '-' }}</div>
      </template>

      <!-- 单位 -->
      <template slot-scope="{row}" slot="unit">
        <div>{{ row.unit || '-' }}</div>
      </template>

      <!-- 数量 -->
      <template slot-scope="{row}" slot="num">
        <div>{{ row.num || '-' }}</div>
      </template>

      <!-- 单价 -->
      <template slot-scope="{row}" slot="price">
        <div>{{ row.price ? `￥${row.price}` : '-' }}</div>
      </template>

      <!-- 金额 -->
      <template slot-scope="{row}" slot="total_fee">
        <div>{{ row.total_fee ? `￥${row.total_fee}` : '-' }}</div>
      </template>
    </Table>

    <div class="block_20"></div>

    <KPage
      v-if="!isGoodsDetails"
      :current.sync="+queryFormData.page"
      :page-size.sync="+queryFormData.pageSize"
      :total="total"
      style="text-align: center"
      @on-change="handleCurrentChange"
      @on-page-size-change="handleSizeChange"
    />
  </Modal>
</template>

<script>
import renderHeader from '@/mixins/renderHeader'

let init_query_from_data = {
  page: 1,
  pageSize: 10,
  order_code: '', // 输入订单编号筛选
}

export default {
  name: 'GoodsDetail',
  mixins: [renderHeader],

  components: {},

  props: {
    id: {
      type: String,
      default: ''
    },
    goodsVisible: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      default: '商品明细'
    },
    // 采购订单codes的合集
    orderCodes: {
      type: Object,
      default: () => {}
    },
    /**
     * @description: 商品详情数据
     * @note 正常数据由发票id走接口获取，开票结果里面的数据由 goodsDetails 传递进来
     * */
    goodsDetails: {
      type: Array,
      default: () => [],
    },
  },

  data() {
    return {
      queryFormData: {...init_query_from_data},

      detailsGoodsCols: [
        { title: '名称', key: 'name', align: 'center' },
        { title: '规格', slot: 'spec', align: 'center' },
        { title: '单位', slot: 'unit', align: 'center' },
        { title: '数量', slot: 'num', align: 'center' },
        {
          title: '单价',
          slot: 'price',
          align: 'center',
          renderHeader: (h, params) => this._renderHeader(h, params, '此处的单价特指“货品金额”，不包含“平台服务费”'),
        },
        { title: '金额', slot: 'total_fee', align: 'center' },
      ],

      goodsCols: [
        { title: '订单编号', key: 'order_code', align: 'center', width: 100 },
        { title: '名称', key: 'name', align: 'center' },
        { title: '规格', slot: 'spec', align: 'center' },
        { title: '单位', slot: 'unit', align: 'center' },
        { title: '数量', slot: 'num', align: 'center' },
        { title: '单价', slot: 'price', align: 'center', },
        { title: '金额', slot: 'total_fee', align: 'center' },
      ],
      list: [],
      tableLoading: false,
      total: 0,
    }
  },

  computed: {
    // 是否有商品详情的数据传递进来，如有则表示不走搜索接口
    isGoodsDetails () {
      if ( this.goodsDetails.length ) {
        this.list = this.goodsDetails
        return true
      }else{
        return false
      }
    }
  },

  watch: {
  },

  created() {
  },

  mounted() {
  },

  destroyed() {
  },

  methods: {
    onSearch: function () {
      this.queryFormData.page = 1
      this.get()
    },

    // 获取发票商品详情
    get: function () {
      let orderCodes=''
      if(this?.orderCodes){
        orderCodes = Object?.keys(this?.orderCodes)?.join(',')||[];
      }
      let params = {
        id: this.id,
        trade_flow_ids: orderCodes,
        ...this.queryFormData
      }
      this.tableLoading = true
      // id:可以不传，ent_type：不需要传
      this.$api.getPurInvoiceGoodsdetails(params).then(data => {
        this.list = data.list
        this.total = data.total
      }, rej => {
        this.$Message.error(rej.errmsg)
      }).finally( () => {
        this.tableLoading = false
      })
    },

    handleSizeChange( val ) {
      this.queryFormData.page = 1
      this.queryFormData.pageSize = val
      this.get()
      this.scrollTop()
    },
    handleCurrentChange( val ) {
      this.queryFormData.page = val
      this.get()
      this.scrollTop()
    },

    scrollTop(){
      let overflowY = this.$el.getElementsByClassName("ivu-table-body")[0];
      if (!overflowY) {
        return;
      }
      overflowY.scrollTop = 0;
    },

    closeModal() {
      this.queryFormData = {...init_query_from_data}
      this.$emit( 'update:goodsVisible', false )
    },
    changeVisible( flag ) {
      if ( flag ) {
        if (!this.isGoodsDetails) {
          this.get();
        }
      }else{
        this.closeModal()
      }
    },
  },
}
</script>

<style scoped lang="less">
.goods-modal{
  ::v-deep .ivu-modal-content{
    height: 600px;
  }
}
.cursor {
  cursor: pointer;
}
.mb10 {
  margin-bottom: 10px;
}
</style>
