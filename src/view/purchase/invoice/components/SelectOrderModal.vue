<template>
  <Modal
    :value="modalValue"
    title="选择订单"
    :mask-closable="false"
    @on-visible-change="changeVisible"
    :width="750"
  >
    <div>
      <div class="mb10">
        <Alert type="warning">发货时间从2023年3月1日到当前的所有负数开票金额和对应的正数开票金额必须被选择</Alert>
      </div>
      <div class="flex flex-item-l-end">
        <div>
          <!-- 订单类型 -->
          <Select ref="type" clearable v-model="queryFormData.type" @on-change="selectChange()" style="width:150px" placeholder="选择订单类型">
            <Option v-for="item in purchaseTypeDesc" :value="item.id" :key="item.id">{{ item.desc }}</Option>
          </Select>
          <dvd></dvd>

          <Input v-model="queryFormData.order_code" placeholder="输入订单编号筛选"
              clearable @keyup.enter.native="onSearch" @on-clear="onSearch"
              style="width: 180px">
            <Icon type="ios-search" slot="suffix" @click="onSearch" />
          </Input>
        </div>
        <dvd />
        <Button type="primary" @click="onSearch">搜索</Button>
      </div>
    </div>
    <div class="block_10"></div>
    <div style="position: relative; height: 345px; overflow: auto;">
      <Table ref="selection" height="280"
          @on-select="onSelect" @on-select-cancel="onSelectCancel"
          @on-select-all="onSelectAll" @on-select-all-cancel="onSelectAllCancel"
          :columns="tableCols" :data="list" :loading="tableLoading">
        <!-- 金额 -->
        <template slot-scope="{row}" slot="amount">
          <span> {{ row.amount ? `￥${row.amount}` : '' }} </span>
        </template>
      </Table>

      <div class="block_20"></div>

      <KPage
        v-if="total > 0"
        :current.sync="+queryFormData.page"
        :page-size.sync="+queryFormData.pageSize"
        :total="total"
        style="text-align: center"
        @on-change="handleCurrentChange"
        @on-page-size-change="handleSizeChange"
      />

    </div>
    <div slot="footer">
      <div v-if="Object.keys(selected_items).length > 0" style="display: inline-block;" class="lr15 text-muted">
        已选择<span class="text-error">{{Object.keys(selected_items).length}}</span>订单
        <dvd/>
        <dvd/>
        <dvd/>
        <span>总金额：￥<span v-html="totalAmount"></span></span>
      </div>
      <Button @click="changeVisible(false)">取消</Button>
      <Button type="primary" @click="onConfirm">确定</Button>
    </div>
  </Modal>
</template>

<script>
/* eslint-disable */
import S from '@/utils/util' // Some commonly used tools
import * as runtime from '@/utils/runtime' // Runtime information
import {debounce} from "lodash"
import { $operator } from '@/utils/operation';
/* eslint-disable */

let init_query_from_data = {
  page: 1,
  pageSize: 10,
  type: 'ALL', // 类型,默认全部
  order_code: '',
}

export default {
  name: "order",
  model: {
    prop: 'modalValue',
    event: 'input'
  },
  props: {
    modalValue: {
      type: Boolean,
      default: false
    },
    checkedList: {
      type: Array,
      default (){
        return []
      }
    },
    type: {
      type: String,
      default: ''
    },
  },
  data() {
    return {
      queryFormData: {...init_query_from_data},
      totalAmount:'',
      tableCols: [
        { type: 'selection', width: 60},
        { title: '订单编号', key: 'order_code', align: 'center'},
        { title: '订单类型', key: 'type_text', align: 'center'},
        { title: '可开票金额', slot: 'amount', align: 'center', width: 100},
        { title: '开票方', key: 'drawer', align: 'center'},
      ],
      tableLoading: false,
      list: [],
      total: 0,
      purchaseTypeDesc: [], // 订单类型
      selected_items: {}, // 勾选的订单

      redInvoiceList: [], // 红票的以及对应蓝票的数据
    }
  },

  computed: {
  },

  watch: {
    selected_items(oldVal){
      this.itemDebonce(oldVal)
    }
  },

  mounted () {
  },

  methods: {
    init () {
      // 获取所有订单类型
      this.getPurInvoiceOption()
      this.get()
      // 回显已经勾选的数据
      this.onSelectAll(this.checkedList)
    },

    itemDebonce:debounce(function(val){
      if(Object?.keys(val)?.length){
        // 线下采购交易id合集
        let joinin_trade_flow_ids = ''
        for(let item in val) {
          joinin_trade_flow_ids+=val[item].trade_flow_ids+','
        }
        joinin_trade_flow_ids.substring(0,joinin_trade_flow_ids.length-1)

        this.$api.getInvoiceAmount({joinin_trade_flow_ids}).then(res=>{
          let mount=''
          if(Math.sign(Number(res.red_amount))<0){
            mount=`${res.amount}(<span style='color:red'>¥${res.red_amount}</span>)`
          }else{
            mount=res.amount
          }
          this.totalAmount= mount
        })
      }
    },500),

    selectChange () {
      this.queryFormData.page = 1
      this.get()
    },

    close () {
      this.clearQuery()
      this.$emit('input', false)
    },

    changeVisible (show) {
      this.scrollTop()
      if ( show ) {
        this.init()
      }else {
        this.close()
      }
    },

    onSearch: function () {
      this.queryFormData.page = 1
      this.get()
    },

    handleSizeChange( val ) {
      this.queryFormData.page = 1
      this.queryFormData.pageSize = val
      this.get()
      this.scrollTop()
    },
    handleCurrentChange( val ) {
      this.queryFormData.page = val
      this.get()
      this.scrollTop()
    },

    scrollTop(){
      let overflowY = this.$el.getElementsByClassName("ivu-table-body")[0];
      if (!overflowY) {
        return;
      }
      overflowY.scrollTop = 0;
    },

    onSelect: function (selection, row) {
      this.$set(this.selected_items, row.trade_flow_ids, row)
    },

    onSelectAll: function (selection) {
      selection.forEach(item => {
        if (!item._disabled) {
          this.$set(this.selected_items, item.trade_flow_ids, item)
        }
      })
    },

    onSelectCancel: function (selection, row) {
      this.$delete(this.selected_items, row.trade_flow_ids)
    },

    onSelectAllCancel: function (selection) {
      for(let item in this.list) {
        if ( !this.list[item]._disabled ) {
          this.$delete(this.selected_items, this.list[item].trade_flow_ids)
        }
      }
    },

    onConfirm: function () {
      if ( Object.keys(this.selected_items).length == 0 ) {
        this.$Message.error('请先选择订单')
        return false
      }

      this.$emit('on-selected',this.sortRedInvoiceList())
      this.close()
    },

    // 为了保持红票以及对应的蓝票数据两两对应，将其从对象中删除再进行追加抛出
    sortRedInvoiceList () {
      this.redInvoiceList.forEach(item => {
        this.$delete(this.selected_items, item.trade_flow_ids)
      })
      return [...Object.values(this.selected_items), ...this.redInvoiceList]
    },

    get: function () {
      // 借助订单搜索的时机，同步搜索红票订单
      this.getRedinvopurchaseorder().then( res => {
        this.tableLoading = true
        this.$api.getPurInvoicePurchaseorder(this.queryFormData).then(data => {
          this.list = this.handler(data.list)
          this.total = data.total
          this.tableLoading = false
        }, rej => {
          this.$Message.error(rej.errmsg)
        })
      } )
    },

    // 处理初始化勾选
    handler: function (list) {
      for(let item in list) {
        list[item].plat = this.queryFormData.type
        for(let select_item in this.selected_items) {
          if (list[item].trade_flow_ids== this.selected_items[select_item].trade_flow_ids) {
            list[item]['_checked'] = true // 选中已选项
            if ( this.selected_items[select_item].disabled ) {
              list[item]['_disabled'] = true // 选中已选项
            }
          }
        }
      }
      return list
    },

    clearQuery: function () {
      this.queryFormData = {...init_query_from_data}
      this.queryFormData.page = 1
      this.list = []
      this.total = 0
      this.selected_items = {}
    },

    // 获取枚举值
    getPurInvoiceOption() {
      this.$api.getPurInvoiceOption().then(res => {
        this.purchaseTypeDesc = S.descToArrHandle(res.purchaseTypeDesc)
      });
    },

    /**
     * @description: 获取所有的红票订单,红票对应的蓝票也要默认被勾选上
     * */
    getRedinvopurchaseorder () {
      return this.$api.getRedinvopurchaseorder().then( res => {
        this.redInvoiceList = res.list
        res.list.forEach( item => {
          this.$set(this.selected_items, item.trade_flow_ids, {...item, disabled: true})
        } )
        return new Promise( resolve => resolve() )
      } )
    }
  },
}
</script>

<style lang="less" scoped>
::v-deep .ivu-modal{
  top: 20%;
}
.mb10 {
  margin-bottom: 10px;
}
</style>
