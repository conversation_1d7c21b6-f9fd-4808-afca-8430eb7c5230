/**
 * @description： 当前页面为模拟的发票数据，由于发票数据较少，可以以下数据进行状态的观察，
 * 							  如果发票流程有变更，需要同步变更当前的数据，否则可能会出现问题
 *
 * 第一版： 开票成功 ｜ 开票失败
 *
 *
 * 第二版： 开票成功 ｜ 开票失败 ｜ 红票
 *
 * 第三版：2023.05.29版本（is_cli_joinin === 1）  开票成功 ｜ 开票失败 ｜ 红票 ｜ 蓝票 (红票｜蓝票三种状态： 1：待标记 2：待审核3：已驳回)
 *      针对于走到诺诺的票(is_cli_joinin === 0):
 *          开票结果里面，当前状态为被驳回，即单子的状态为  REJECTED,开票结果只展示开票成功的数据
 *      状态：
 *        开票失败： FAIL
 *        待标记： WAIT
 *        待审核： AUDIT_WAIT
 *        已驳回： REJECTED
 *      开票数据合并 【...开票成功，...开票失败，...蓝票，...红票】分别由四个数组进行维护
 *      平台对于线下订单（is_cli_joinin === 1）的发票数据只有查看权限，不可标记，重新申请等操作
 *      诺诺中没有蓝票展示，2023.05.29诊所｜养疗馆｜城市合伙人｜省公司过来的数据，诊所等走的是省公司开票，省公司走的是第三方开票，蓝票不可标记
 * */

// is_cli_joinin === 1 2023.05.29新版发票数据
export let cli_joinin_res = {
  order_from: 'COM',
  invoice_id: '534',
  invoice: {
    bank: '招商银行',
    reg_mobile: '***********',
    reg_address_text: '河北省唐山市路南区1212',
    bank_account: '6222021106002329799',
    organization_name: '上海诊所********',
    organization_code: '***********',
    reg_address: {
      city: {
        code: '130200',
        name: '唐山市'
      },
      prov: {
        code: '130000',
        name: '河北省'
      },
      county: {
        code: '130202',
        name: '路南区'
      },
      detail: '1212'
    }
  },
  applicant: {
    applicant: '上海诊所********',
    name: '九夏',
    mobile: '***********',
    email: '<EMAIL>',
    address: {
      city: {
        code: '120100',
        name: '天津市'
      },
      prov: {
        code: '120000',
        name: '天津市'
      },
      county: {
        code: '120103',
        name: '河西区'
      },
      detail: '12'
    },
    address_text: '天津市天津市河西区12'
  },
  rejected_desc: '',
  update_time: '2023-05-30 19:34:22',
  amount: '20.00',
  red_amount: '-200',
  type: '1',
  type_desc: '普通电子发票',
  status: 'FINISHED',
  status_desc: '已开票',
  blue_status: 'WAIT',
  blue_status_desc: '开票处理中',
  red_status: 'WAIT',
  red_status_desc: '开票处理中',
  invoice_details: [
    {
      id: '620',
      type: 'RED',
      type_desc: '红票',
      amount: '-200.00',
      invoice_no: '1212',
      invoice_code: '',
      serial_no: '',
      nuonuo: {
        c_url: 'http://img-sn-i01s-cdn.rsjxx.com/rsjxx/2023/0530/193420_11430.jpeg',
        c_imgUrls: null,
        c_jpg_url: null,
        c_paper_pdf_url: ''
      },
      goods: [
        {
          name: '公共区域座椅（铁木文V字椅+常规）',
          spec: '',
          unit: '',
          price: '200',
          total_fee: '-200',
          num: '-1'
        }
      ]
    },
    {
      id: '620',
      type: 'RED',
      type_desc: '红票',
      amount: '-200.00',
      invoice_no: '1212',
      invoice_code: '',
      serial_no: '',
      nuonuo: {
        c_url: 'http://img-sn-i01s-cdn.rsjxx.com/rsjxx/2023/0530/193420_11430.jpeg',
        c_imgUrls: null,
        c_jpg_url: null,
        c_paper_pdf_url: ''
      },
      goods: [
        {
          name: '公共区域座椅（铁木文V字椅+常规）',
          spec: '',
          unit: '',
          price: '200',
          total_fee: '-200',
          num: '-1'
        }
      ]
    },
    {
      id: '620',
      type: 'RED',
      type_desc: '红票',
      amount: '-200.00',
      invoice_no: '1212',
      invoice_code: '',
      serial_no: '',
      nuonuo: {
        c_url: 'http://img-sn-i01s-cdn.rsjxx.com/rsjxx/2023/0530/193420_11430.jpeg',
        c_imgUrls: null,
        c_jpg_url: null,
        c_paper_pdf_url: ''
      },
      goods: [
        {
          name: '公共区域座椅（铁木文V字椅+常规）',
          spec: '',
          unit: '',
          price: '200',
          total_fee: '-200',
          num: '-1'
        }
      ]
    }
  ],
  fail_invoice: [
    {
      is_submit: '0',
      goods: [
        {
          name: '服务费',
          spec: '',
          unit: '',
          price: '',
          total_fee: '300',
          num: ''
        }
      ],
      amount: '300',
      rejected_desc: '[410701]金税盘处于汇总期，不能开票！',
      status: 'FAIL',
      status_desc: '开票失败'
    }
  ],
  red_invoice: [
    {
      detail_id: '621',
      type: 'RED',
      status: 'WAIT',
      status_desc: '开票处理中',
      type_desc: '红票1',
      amount: '20.00',
      nuonuo: {
        c_url: null,
        c_imgUrls: null,
        c_jpg_url: null,
        c_paper_pdf_url: ''
      },
      goods: [
        {
          name: '毛巾浴巾组合',
          spec: '',
          unit: '',
          price: '20',
          total_fee: '20',
          num: '1'
        }
      ]
    },
    {
      detail_id: '621',
      type: 'RED',
      status: 'WAIT',
      status_desc: '开票处理中',
      type_desc: '红票2',
      amount: '20.00',
      nuonuo: {
        c_url: null,
        c_imgUrls: null,
        c_jpg_url: null,
        c_paper_pdf_url: ''
      },
      goods: [
        {
          name: '毛巾浴巾组合',
          spec: '',
          unit: '',
          price: '20',
          total_fee: '20',
          num: '1'
        }
      ]
    }
  ],
  blue_invoice: [
    {
      detail_id: '621',
      type: 'BLUE',
      status: 'WAIT',
      status_desc: '开票处理中',
      type_desc: '蓝票',
      amount: '20.00',
      nuonuo: {
        c_url: null,
        c_imgUrls: null,
        c_jpg_url: null,
        c_paper_pdf_url: ''
      },
      goods: [
        {
          name: '毛巾浴巾组合',
          spec: '',
          unit: '',
          price: '20',
          total_fee: '20',
          num: '1'
        }
      ]
    },
    {
      detail_id: '621',
      type: 'BLUE',
      status: 'WAIT',
      status_desc: '开票处理中',
      type_desc: '蓝票',
      amount: '20.00',
      nuonuo: {
        c_url: null,
        c_imgUrls: null,
        c_jpg_url: null,
        c_paper_pdf_url: ''
      },
      goods: [
        {
          name: '毛巾浴巾组合',
          spec: '',
          unit: '',
          price: '20',
          total_fee: '20',
          num: '1'
        }
      ]
    }
  ],
  ent_type: 'CLI',
  create_time: '2023-05-29 19:15:32',
  finished_at: '2023-05-30 19:34:22',
  order_details: [
    {
      uuid: null,
      order_code: 'C2301432404222217164788',
      order_type: 'CLI_ADDITION',
      type_text: '诊所二次采购单',
      amount: '-200.00',
      drawer: 'TEST-榕树家黑龙江省公司',
      trade_flow_ids: '2702'
    },
    {
      uuid: null,
      order_code: 'C2301432405121912128527',
      order_type: 'CLI_ADDITION',
      type_text: '诊所二次采购单',
      amount: '20.00',
      drawer: 'TEST-榕树家黑龙江省公司',
      trade_flow_ids: '2655'
    }
  ],
  date: '',
  is_cli_joinin: '1'
};
export let nuonuo_invoice_res = {
  order_from: 'CLINIC',
  invoice_id: '27',
  invoice: {
    bank: '招商银行',
    reg_mobile: '***********',
    reg_address_text: '北京市北京市朝阳区3333',
    bank_account: '6222021106002329799',
    organization_name: '哈尔滨市榕树家中医诊所有限公司',
    organization_code: '91230110MA1F5C6X0F',
    reg_address: {
      city: {
        code: '110100',
        name: '北京市'
      },
      prov: {
        code: '110000',
        name: '北京市'
      },
      county: {
        code: '110105',
        name: '朝阳区'
      },
      detail: '3333'
    }
  },
  applicant: {
    applicant: 'T榕树家中医诊所三合路店银联下',
    name: '张英ID',
    mobile: '***********',
    email: '<EMAIL>',
    address: {
      city: {
        code: '',
        name: ''
      },
      prov: {
        code: '',
        name: ''
      },
      county: {
        code: '',
        name: ''
      },
      detail: ''
    },
    address_text: ''
  },
  rejected_desc: '',
  update_time: '2022-07-28 10:24:34',
  amount: '106.36',
  red_amount: '0',
  type: '1',
  type_desc: '普通电子发票',
  status: 'FINISHED',
  status_desc: '已开票',
  blue_status: '',
  blue_status_desc: null,
  red_status: '',
  red_status_desc: null,
  invoice_details: [
    {
      id: '620',
      type: 'RED',
      type_desc: '红票',
      amount: '-200.00',
      invoice_no: '1212',
      invoice_code: '',
      serial_no: '',
      nuonuo: {
        c_url: 'http://img-sn-i01s-cdn.rsjxx.com/rsjxx/2023/0530/193420_11430.jpeg',
        c_imgUrls: null,
        c_jpg_url: null,
        c_paper_pdf_url: ''
      },
      goods: [
        {
          name: '公共区域座椅（铁木文V字椅+常规）',
          spec: '',
          unit: '',
          price: '200',
          total_fee: '-200',
          num: '-1'
        }
      ]
    }
  ],
  fail_invoice: [
    {
      is_submit: '0',
      goods: [
        {
          name: '平台技术服务费',
          spec: '',
          unit: '',
          price: '',
          total_fee: '-100',
          num: ''
        },
        {
          name: '平台技术服务费',
          spec: '',
          unit: '',
          price: '',
          total_fee: '50',
          num: ''
        },
        {
          name: '平台技术服务费',
          spec: '',
          unit: '',
          price: '',
          total_fee: '50',
          num: ''
        },
        {
          name: '平台技术服务费',
          spec: '',
          unit: '',
          price: '',
          total_fee: '50',
          num: ''
        }
      ],
      amount: '50',
      rejected_desc: '[410701]金税盘处于汇总期，不能开票！；该发票未开具',
      status: 'FAIL',
      status_desc: '开票失败'
    }
  ],
  red_invoice: [
    {
      detail_id: '621',
      type: 'RED',
      status: 'WAIT',
      status_desc: '开票处理中',
      type_desc: '红票1',
      amount: '20.00',
      nuonuo: {
        c_url: null,
        c_imgUrls: null,
        c_jpg_url: null,
        c_paper_pdf_url: ''
      },
      goods: [
        {
          name: '毛巾浴巾组合',
          spec: '',
          unit: '',
          price: '20',
          total_fee: '20',
          num: '1'
        }
      ]
    },
    {
      detail_id: '621',
      type: 'RED',
      status: 'WAIT',
      status_desc: '开票处理中',
      type_desc: '红票2',
      amount: '20.00',
      nuonuo: {
        c_url: null,
        c_imgUrls: null,
        c_jpg_url: null,
        c_paper_pdf_url: ''
      },
      goods: [
        {
          name: '毛巾浴巾组合',
          spec: '',
          unit: '',
          price: '20',
          total_fee: '20',
          num: '1'
        }
      ]
    }
  ],
  blue_invoice: [],
  ent_type: 'CLI',
  create_time: '2022-07-26 17:36:20',
  finished_at: '2022-07-28 10:24:34',
  order_details: [
    {
      uuid: null,
      order_code: 'PCO220726175456431061',
      order_type: 'CLI_PUR',
      type_text: '平台（银联）采购订单',
      amount: '106.36',
      drawer: '海南树家（平台）',
      trade_flow_ids: ''
    }
  ],
  date: '',
  is_cli_joinin: '0'
};
