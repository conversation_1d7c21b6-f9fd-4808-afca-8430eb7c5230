<template>
  <Modal
    :value="value"
    :title="title"
    width="680px"
    @on-visible-change="changeVisible"
    :styles="{ maxHeight: '400px', minHeight: '200px' }"
    class="goods-modal"
  >
    <div class="content">
      <p>
        订单
        <span v-for="code_item in change_codes" :key="code_item">{{ code_item }}；</span>
        发生了新的发货或售后，所以您已选择的订单发生了变化。
      </p>
    </div>

    <div slot="footer">
      <Button type="primary" @click="confirm">确定</Button>
    </div>
  </Modal>
</template>

<script>
export default {
  name: 'orderTipModal',
  mixins: [],

  components: {},

  props: {
    value: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      default: '订单变化通知'
    },
    change_codes: {
      type: Array,
      default: []
    }
  },

  data() {
    return {};
  },

  computed: {},

  watch: {},

  created() {},

  mounted() {},

  destroyed() {},

  methods: {
    closeModal() {
      this.$emit('input', false);
    },
    changeVisible(flag) {
      if (!flag) {
        this.closeModal();
      }
    },
    confirm() {
      this.closeModal();
      this.$emit('confirm')
    }
  }
};
</script>

<style scoped lang="less">
::v-deep .ivu-modal-body {
  min-height: 200px;
  max-height: 200px;
  overflow-y: auto;
}
.content {
  font-size: 15px;
}
</style>
