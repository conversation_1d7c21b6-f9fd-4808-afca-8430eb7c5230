<template>
  <div class="invoice-container">
    <Form :model="formData" ref="formData" label-position="right" :label-width="100" :rules="ruleValidate">
      <div class="block-header">申请方信息</div>
      <Row>
        <Col :span="14" v-if="$route.query.id">
          <FormItem label="状态:" class="mb10">
            <div class="item-lineHeight" :style="{ color: formData.status == 'PART_FINISHED' ? 'red' : '' }">
              {{ formData.status_desc || '-' }}
              <span v-if="formData.status == 'REJECTED'"
                >(<span style="color:red">驳回原因：{{ formData.rejected_desc || '-' }}</span
                >)</span
              >
            </div>
          </FormItem>
        </Col>

        <Col :span="14">
          <FormItem label="申请方:" class="mb10">
            {{ partnerName }}
          </FormItem>
        </Col>
        <Col :span="14">
          <FormItem label="收票人姓名:" prop="name">
            <Input v-model="formData.name"></Input>
          </FormItem>
        </Col>
        <Col :span="14">
          <FormItem label="收票人手机:" prop="mobile">
            <Input v-model="formData.mobile"></Input>
          </FormItem>
        </Col>
        <Col :span="14">
          <FormItem label="收票邮箱:" prop="email">
            <Input v-model="formData.email"></Input>
          </FormItem>
        </Col>
        <Col :span="14">
          <FormItem label="收票地址:" prop="address" class="mb10">
            <div class="flex">
              <div class="addressBox" style="width: 55%; padding-top: 1px; ">
                <el-cascader
                  v-model="selectedAddress"
                  :options="options"
                  clearable
                  popper-class="address-com-small"
                  placeholder="请选择收件地址"
                  size="small"
                  style="width: 100%;"
                  @change="regionChange"
                >
                </el-cascader>
              </div>
              <div class="ml10" style="width: 45%">
                <Input v-model="formData.address.detail" placeholder="详细地址"></Input>
              </div>
            </div>
          </FormItem>
        </Col>
      </Row>

      <div class="block-header">发票内容</div>
      <Row>
        <Col :span="14">
          <FormItem label="发票类型:" class="mb10" prop="type">
            <RadioGroup v-model="formData.type">
              <Radio :label="item.id" v-for="item in invoiceType" :key="item.id">{{ item.desc }}</Radio>
            </RadioGroup>
          </FormItem>
        </Col>
        <Col :span="14">
          <FormItem label="发票金额:" required class="mb0">
            <span class="mr10" v-if="order_list.length">￥<span v-html="totalAmount"></span></span>
            <a @click="showOrderVisible" v-if="order_list.length">重选订单</a>
            <a @click="showOrderVisible" v-else>选择订单</a>

            <Table ref="selection" :columns="orderColumns" :data="showOrderList()" v-if="order_list.length">
              <!-- 金额 -->
              <template slot-scope="{ row }" slot="amount">
                <span> {{ row.amount ? `￥${row.amount}` : '' }} </span>
              </template>

              <template slot-scope="{ row, index }" slot="action">
                <p v-if="isRedInvoiceOrder(row.order_code)">-</p>
                <a @click="deleteOrder(index)" v-else>删除</a>

              </template>
            </Table>
          </FormItem>
        </Col>
        <Col :span="14">
          <FormItem required class="mb10" v-if="order_list.length > this.split_num">
            <div class="border-arrow" style="text-align: center;">
              <a @click="foldOrExpand('expand')" v-if="showOrderList().length <= this.split_num">
                展开
                <Icon type="ios-arrow-down" />
              </a>
              <a @click="foldOrExpand('fold')" v-else>
                收起
                <Icon type="ios-arrow-up" />
              </a>
            </div>
          </FormItem>
        </Col>
        <Col :span="14">
          <FormItem label="发票内容:" class="mb10">
            <a v-if="order_list.length" @click="goodsDetail()">商品明细</a>
            <p v-else>-</p>
          </FormItem>
        </Col>
      </Row>
      <div class="block-header flex flex-align-center">
        发票信息
        <k-link class="ml10" :to="{ path: '/setting/Invoice' }" target="_blank">修改发票信息</k-link>
        <Icon type="md-refresh" size="18" class="ml10 cursor" @click="getInvoiceInfo()" />
      </div>

      <div class="block">
        <Spin v-if="spinLoading"></Spin>
        <Row v-else>
          <Col :span="14">
            <FormItem label="发票抬头:" class="mb10">
              {{ this.formData.invoice.organization_name || '-' }}
            </FormItem>
          </Col>
          <Col :span="14">
            <FormItem label="公司税号:" class="mb10">
              {{ this.formData.invoice.organization_code || '-' }}
            </FormItem>
          </Col>
          <Col :span="14">
            <FormItem label="注册地址:" class="mb10">
              {{ this.formData.invoice.reg_address_text || '-' }}
            </FormItem>
          </Col>
          <Col :span="14">
            <FormItem label="注册电话:" class="mb10">
              {{ this.formData.invoice.reg_mobile || '-' }}
            </FormItem>
          </Col>
          <Col :span="14">
            <FormItem label="开户银行:" class="mb10">
              {{ this.formData.invoice.bank || '-' }}
            </FormItem>
          </Col>
          <Col :span="14">
            <FormItem label="银行账户:" class="mb10">
              {{ this.formData.invoice.bank_account || '-' }}
            </FormItem>
          </Col>
        </Row>
      </div>
    </Form>
    <div class="block_40"></div>

    <div class="fixed-bottom-wrapper">
      <back-button></back-button>
      <dvd />
      <dvd />
      <dvd />
      <Button type="primary" @click="submit()" :loading="saveBtnLoading">提交</Button>
    </div>

    <!-- 商品明细 -->
    <goods-modal :goods-visible.sync="goodsVisible" :order-codes="order_codes"></goods-modal>

    <select-order-modal v-model="orderVisible" @on-selected="onSelected" :checkedList="order_list"></select-order-modal>

    <!-- 订单变动通知 -->
    <order-tip-modal v-model="tipModalVisible" :change_codes="change_codes" @confirm="tipConfirm"></order-tip-modal>

    <!-- 发票信息未完善提示 -->
    <invoice-info-tip v-model="invoiceInfoVisible"></invoice-info-tip>
  </div>
</template>

<script>
import { getCpName } from '@/utils/runtime';
import { cloneDeep, debounce } from 'lodash';
import { CodeToText, regionData } from '@/utils/chinaMap';
import selectOrderModal from './components/SelectOrderModal';
import orderTipModal from './components/orderTipModal';
import invoiceInfoTip from '@/components/InvoiceModal/invoice-info-tip.vue';
export default {
  name: 'edit',
  components: {
    selectOrderModal,
    orderTipModal,
    invoiceInfoTip,
    GoodsModal: () => import('./components/GoodsDetail')
  },

  props: {},

  data() {
    const validateMobile = (rule, value, callback) => {
      if (value === '') {
        callback(new Error('请输入正确的手机号码'));
      } else {
        const reg = /^1[3456789]\d{9}$/;
        if (!reg.test(value)) {
          callback(new Error('请输入正确的手机号码'));
        }
        callback();
      }
    };

    const validateEmail = (rule, value, callback) => {
      let reg = /^\w+((-\w+)|(\.\w+))*\@[A-Za-z0-9]+((\.|-)[A-Za-z0-9]+)*\.[A-Za-z0-9]+$/;
      if (!reg.test(value)) {
        callback(new Error('请输入正确的邮箱'));
      } else {
        callback();
      }
    };

    return {
      saveBtnLoading: false,
      // 发票类型(1：普通电子发票，2:普通纸质发票,3：增值税专用纸质发票)
      invoiceType: [
        { id: '1', desc: '普通电子发票' },
        { id: '2', desc: '普通纸质发票' }
      ],
      totalAmount: '',
      // invoice里面统一放置发票信息
      formData: {
        invoice: {
          organization_name: '', // 发票抬头
          organization_code: '', // 公司税号
          reg_address_text: '', // 注册地址
          reg_mobile: '', // 注册电话
          bank: '', // 开户银行
          bank_account: '' // 银行账号
        },
        name: '',
        mobile: '',
        email: '',
        type: '1', // 发票类型
        //收票地址
        address: {
          prov: {
            name: '',
            code: ''
          },
          city: {
            name: '',
            code: ''
          },
          county: {
            name: '',
            code: ''
          },
          detail: ''
        }
      },
      ruleValidate: {
        name: [{ required: true, message: '请输入收票人姓名', trigger: 'blur' }],
        mobile: [
          { required: true, message: '请输入收票人电话', trigger: 'change' },
          { required: true, validator: validateMobile }
        ],
        email: [
          { required: true, message: '请输入正确的邮箱', trigger: 'change' },
          { required: true, validator: validateEmail }
        ],
        type: [{ required: true, message: '请选择发票类型', trigger: 'change' }]
      },
      options: regionData,
      selectedAddress: [],
      orderVisible: false,
      orderType: '',
      orderColumns: [
        { title: '订单编号', key: 'order_code', align: 'center' },
        { title: '订单类型', key: 'type_text', align: 'center' },
        { title: '金额', slot: 'amount', align: 'center' },
        { title: '开票方', key: 'drawer', align: 'center' },
        { title: '操作', slot: 'action', algin: 'center', width: 60 }
      ], // 选中订单columns
      order_list: [], // 选中订单数据

      // 商品明细
      goodsVisible: false,
      order_codes: {}, // 采购订单codes合集
      spinLoading: false,

      redCodesObj: {}, // 红票以及对应蓝票的合集
      showType: 'fold', // 默认折叠
      split_num: 5, // 折叠数量
      tipModalVisible: false,
      change_codes: [], // 发生异动的数据

      invoiceInfoVisible: false,
    };
  },

  computed: {
    partnerName () {
      return getCpName()
    },
    // 是否是红票以及对应的蓝票,对应的票不可以被删除
    isRedInvoiceOrder() {
      return order_code => {
        return this.redCodesObj?.hasOwnProperty(order_code);
      };
    },
  },
  watch: {
    order_list(oldVal) {
      this.getRedinvopurchaseorder().then(() => {
        this.itemDebonce(oldVal)
      });
    }
  },

  created() {},
  mounted() {
    this.init();
  },
  methods: {
    // 初始化数据
    init() {
      let id = this.$route.query.id;
      if (id) {
        this.getPurInvoiceDetail(id);
      } else {
        // 初始化创建时，通过回显接口回显上次开票得用户
        this.echoInvoiceApplicant();
      }
      // 获取发票信息
      this.getInvoiceInfo();
    },

    itemDebonce (val) {
      if(Object?.keys(val)?.length){
        // 线下采购交易id合集
        let joinin_trade_flow_ids = ''
        for(let item in val) {
          joinin_trade_flow_ids+=val[item].trade_flow_ids+','
        }
        joinin_trade_flow_ids.substring(0,joinin_trade_flow_ids.length-1)

        this.$api.getInvoiceAmount({joinin_trade_flow_ids}).then(res=>{
          let mount=''
          if(Math.sign(Number(res.red_amount))<0){
            mount=`${res.amount}(<span style='color:red'>¥${res.red_amount}</span>)`
          }else{
            mount=res.amount
          }
          this.totalAmount= mount
        })
      }
    },

    // 发票详情
    getPurInvoiceDetail(id) {
      let params = {
        id: this.$route.query.id
      };
      this.$api.getPurInvoiceDetail(params).then(res => {
        this.order_list = res.order_details;

        // 驳回状态
        this.formData.status = res.status;
        this.formData.rejected_desc = res.rejected_desc;
        this.formData.status_desc = res.status_desc;

        this.formData.name = res.applicant.name;
        this.formData.mobile = res.applicant.mobile;
        this.formData.email = res.applicant.email;
        if (res.applicant.address.county && res.applicant.address.county.code) {
          this.selectedAddress = [
            res.applicant.address.prov.code,
            res.applicant.address.city.code,
            res.applicant.address.county.code
          ];
        } else {
          this.selectedAddress = [res.applicant.address.prov.code, res.applicant.address.city.code];
        }
        this.formData.address.detail = res.applicant.address.detail;
        this.formData.address = res.applicant.address;
        this.formData.type = res.type;
      });
    },
    //地区选择
    regionChange(address) {
      if (address.length) {
        const province = {
          name: CodeToText[address[0]],
          code: address[0]
        };
        const city = {
          name: CodeToText[address[1]],
          code: address[1]
        };
        const area = {
          name: CodeToText[address[2]],
          code: address[2]
        };
        this.formData.address.prov = province;
        this.formData.address.city = city;
        this.formData.address.county = area;
      } else {
        this.formData.address.prov = { name: '', code: '' };
        this.formData.address.city = { name: '', code: '' };
        this.formData.address.county = { name: '', code: '' };
      }
    },

    tipConfirm() {
      this.orderVisible = true;
    },

    showOrderVisible() {
      this.getRedinvopurchaseorder().then(res => {
        this.order_list = res.replace;
        if (res.change_codes?.length > 0) {
          this.change_codes = res.change_codes;
          this.tipModalVisible = true;
        } else {
          this.orderVisible = true;
        }
      });
    },

    // 展开/收起
    foldOrExpand(type = 'fold') {
      this.showType = type
      this.showOrderList()
    },
    // 展示订单数据
    showOrderList() {
      let copyOrderList = cloneDeep(this.order_list);
      let returnOrderList = copyOrderList;
      if (this.showType === 'fold') {
        returnOrderList = copyOrderList.splice(0, this.split_num);
      }
      return returnOrderList;
    },

    onSelected(item) {
      this.order_list = item;
    },

    deleteOrder(index) {
      this.$delete(this.order_list, index);
    },

    goodsDetail() {
      this.order_codes = this.getShipOrderCodeIds();
      this.goodsVisible = true;
    },

    back() {
      this.$router.push('/finance/invoice/list');
    },

    submit() {
      this.$refs['formData'].validate(valid => {
        if (valid) {
          if (!this.order_list.length) {
            this.$Message.error('请选择订单');
            return;
          }
          this.checkInvoiceStatus();
        }
      });
    },

    // 获取选中订单的ship_code_ids
    getShipOrderCodeIds() {
      let codes = {};
      this.order_list.forEach(item => {
        this.$set(codes, item.trade_flow_ids, item.order_code);
      });
      return codes || {};
    },

    // 获取选中订单的codes合集
    getOrderCodes() {
      let codes = [];
      this.order_list.forEach(item => {
        let obj = {
          [item.order_code]: item.trade_flow_ids
        };
        codes.push(obj);
      });
      return codes || {};
    },

    // 获取发票信息
    getInvoiceInfo() {
      this.spinLoading = true;
      this.$api
        .getInvoiceInfo()
        .then(res => {
          this.formData.invoice.organization_name = res.organization_name;
          this.formData.invoice.organization_code = res.organization_code;

          let reg_address_text = '';
          if (res.invoice?.reg_address?.county?.code) {
            let invoice = res.invoice?.reg_address;
            reg_address_text = `${invoice.prov.name}${invoice.city.name}${invoice.county.name}${invoice.detail}`;
          }

          this.formData.invoice.reg_address_text = reg_address_text;
          this.formData.invoice.reg_mobile = res.invoice?.reg_mobile;
          this.formData.invoice.bank = res.invoice?.bank;
          this.formData.invoice.bank_account = res.invoice?.bank_account;
        })
        .finally(() => (this.spinLoading = false));
    },

    // 回显开票人信息
    echoInvoiceApplicant() {
      this.$api.echoInvoiceApplicant().then(
        res => {
          this.formData.name = res.applicant.name;
          this.formData.mobile = res.applicant.mobile;
          this.formData.email = res.applicant.email;
          if (res.applicant.address.county && res.applicant.address.county.code) {
            this.selectedAddress = [
              res.applicant.address.prov.code,
              res.applicant.address.city.code,
              res.applicant.address.county.code
            ];
          } else {
            this.selectedAddress = [res.applicant.address.prov.code, res.applicant.address.city.code];
          }
          this.formData.address.detail = res.applicant.address.detail;
          this.formData.address = res.applicant.address;
        },
        err => this.$Message.error(err.errmsg)
      );
    },

    // 获取发票申请的参数
    handleParams() {
      let params = {
        id: this.$route.query.id || '',
        type: this.formData.type,
        name: this.formData.name,
        mobile: this.formData.mobile,
        email: this.formData.email,
        address: { ...this.formData.address },
        order_code: this.getOrderCodes()
      };

      console.log(params);
      return params;
    },

    checkInvoiceStatus() {
      this.saveBtnLoading = true
      this.$api.checkInvoiceStatus().then(
        res => {
          if (res.not_pass !== '1') {
            this.createInvoiceGenerate()
          }else{
            this.invoiceInfoVisible = true
          }
        },
        rej => this.$Message.error(rej.errmsg)
      ).finally( () => this.saveBtnLoading = false);
    },

    // 发票申请
    createInvoiceGenerate() {
      this.saveBtnLoading = true;
      let params = this.handleParams();
      console.log(params);
      this.$api
        .createInvoiceGenerate(params)
        .then(
          res => {
            this.$Message.success('申请开票成功');
            this.$router.push('/purchase/invoice/list');
          },
          rej => {
            this.$Message.error(rej.errmsg);
          }
        )
        .finally(() => (this.saveBtnLoading = false));
    },

    /**
     * @description: 获取所有的红票订单,红票对应的蓝票也要默认被勾选上
     * */
    getRedinvopurchaseorder() {
      let params = {
        items: this.order_list
      }
      return this.$api.getRedinvopurchaseorder(params).then(res => {
        res.list.forEach(item => {
          this.$set(this.redCodesObj, item.order_code, item);
        });
        return new Promise(resolve => resolve(res));
      });
    }
  }
};
</script>

<style lang="less" scoped>
::v-deep .invoice-container {
  .page-title {
    font-size: 16px;
    font-weight: 700;
    padding-bottom: 10px;
    margin-bottom: 20px;
  }
}

::v-deep .el-cascader {
  .el-cascader-panel {
    font-size: 12px;
  }

  .el-input {
    .el-input__inner {
      border: 1px solid #bcc3d7;
      border-radius: 2px;
      padding-left: 7px;
      font-size: 12px;
    }
  }
}

.mb10 {
  margin-bottom: 10px;
}

.mb0 {
  margin-bottom: 0px;
}

.ml10 {
  margin-left: 10px;
}

.cursor {
  cursor: pointer;
}

.mr10 {
  margin-right: 10px;
}

.block {
  position: relative;
}

.border-arrow {
  // border: 1px solid #d7d9de;
  background: #fafafa;

  a {
    display: inline-block;
    width: 100%;
    height: 100%;
  }
}

.ivu-spin {
  height: 300px;
  width: 50%;
  position: absolute;
  display: flex;
  justify-content: center;
  align-items: center;
}
</style>
