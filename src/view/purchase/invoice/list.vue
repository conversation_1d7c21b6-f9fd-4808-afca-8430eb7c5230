<template>
  <div>
    <div class="search-box" style="margin-top: 10px">
      <Form inline :label-width="0" @submit.native.prevent @keyup.enter.native="onSearch" label-colon>
        <Row>
          <Col>
            <FormItem>
              <Input placeholder="关联订单号" v-model="queryFormData.order_code" clearable/>
            </FormItem>
          </Col>
          <Col>
            <FormItem>
              <Input placeholder="申请单号" v-model="queryFormData.apply_code" clearable/>
            </FormItem>
          </Col>
          <Col>
            <FormItem>
              <DatePicker
                  type="daterange"
                  placement="bottom-end"
                  @on-change="times => handleTimeChange(times)"
                  placeholder="申请时间"
                  v-model="timeRange"
                  clearable
                  style="width: 200px"
              ></DatePicker>
            </FormItem>
          </Col>

          <Col>
            <FormItem style="text-align: left">
              <Button type="primary" class="mr10" @click="onSearch">筛选</Button>
              <Button type="default" @click="onResetSearch">重置</Button>
            </FormItem>
          </Col>
        </Row>
      </Form>
      <div class="table-wrapper">
        <div class="table-func  flex flex-item-between">
          <div class="panel-nav">
            <a
                class="nav"
                :class="{ active: $route.query.status == '' || !$route.query.status }"
                @click.prevent.capture="onStatusChange('')"
            >
              全部
            </a>
            <!--            <a-->
            <!--              class="nav"-->
            <!--              v-for="(status_item, status_index) in statusDesc"-->
            <!--              :key="'status' + status_index"-->
            <!--              :class="{ active: $route.query.status == status_item }"-->
            <!--              @click.prevent.capture="onStatusChange(status_item)"-->
            <!--            >-->
            <!--              {{ status_item.desc }}-->
            <!--            </a>-->

            <!--            <a class="nav" :class="{active: $route.query.status == 'AUDIT_WAIT'}"-->
            <!--               @click.prevent.capture="onStatusChange('AUDIT_WAIT')">-->
            <!--              待审核-->
            <!--            </a>-->
            <a class="nav" :class="{active: $route.query.status == 'WAIT'}"
               @click.prevent.capture="onStatusChange('WAIT')">
              待开票
            </a>
            <a class="nav" :class="{active: $route.query.status == 'FINISHED'}"
               @click.prevent.capture="onStatusChange('FINISHED')">
              已开票
            </a>
            <a class="nav" :class="{active: $route.query.status == 'REJECTED'}"
               @click.prevent.capture="onStatusChange('REJECTED')">
              已驳回
            </a>
          </div>
          <div class="table-btn flex">
            <Button :loading="downloadLoading" @click="exportInvoiceDetai" class="mr-12">导出明细</Button>
            <Button type="primary" :loading="applyInvoiceLoading" @click="checkInvoiceStatus">申请开票</Button>
          </div>
        </div>
        <Table :loading="tableLoading" :columns="tableCols" :data="list" :height="$store.state.app.clientHeight - 300">
          <!-- 申请方 -->
          <template slot-scope="{ row }" slot="apply">
            <div>
              <div class="flex table-slot">
                <span class="label">申请方:</span>
                <span class="value">{{ row.applicant.applicant || '-' }}</span>
              </div>

              <div class="flex table-slot">
                <span class="label">申请人:</span>
                <span class="value">{{ row.applicant.name || '-' }}</span>
              </div>

              <div class="flex table-slot">
                <span class="label">手机号:</span>
                <span class="value">{{ row.applicant.mobile || '-' }}</span>
              </div>

              <div class="flex table-slot">
                <span class="label">接收邮箱:</span>
                <span class="value">{{ row.applicant.email || '-' }}</span>
              </div>
            </div>
          </template>

          <!-- 开票方 -->
          <template slot-scope="{ row }" slot="drawer">
            <div>{{ row.drawer || '-' }}</div>
          </template>

          <!-- 发票信息 -->
          <template slot-scope="{ row }" slot="invoiceInfo">
            <div>
              <div class="flex table-slot">
                <span class="label">发票类型:</span>
                <span class="value">{{ row.type || '-' }}</span>
              </div>

              <div class="flex table-slot">
                <span class="label">发票内容:</span>
                <span class="value" v-if="row.drawer_type === '3'">{{ row.content || '-' }}</span>
                <a v-else @click="checkGoodsDetail(row.id)">{{ row.content }}</a>
              </div>

              <div class="flex table-slot">
                <span class="label">申请金额:</span>
                <span class="value"
                >{{
                    row.amount ? `￥${row.amount}` : '-'
                  }}<span v-if="Math.sign(row.red_amount) === -1"
                  >(<span style="color: red;">¥{{ getRedMount(row, 'red_amount') }}</span
                  >)</span
                  ></span
                >
              </div>

              <div class="flex table-slot">
                <span class="label">开票成功:</span>
                <!-- <span class="value">{{ row.amount_invoiced ? `￥${row.amount_invoiced}` : '-' }}</span> -->
                <span class="value"
                >{{
                    row.amount_invoiced ? `￥${row.amount_invoiced}` : '-'
                  }}<span v-if="Math.sign(row.red_amount_invoiced) === -1"
                  >(<span style="color: red;">¥{{ getRedMount(row, 'red_amount_invoiced') }}</span
                  >)</span
                  ></span
                >
              </div>

              <div class="flex table-slot error" v-if="Number(row.fail_amount) > 0">
                <span class="label">开票失败:</span>
                <span class="value">{{ row.fail_amount ? `￥${row.fail_amount}` : '-' }}</span>
              </div>
            </div>
          </template>

          <template slot-scope="{ row }" slot="content">
            <div>
              <span v-if="row.drawer_type === '3'">
                {{ row.content }}
              </span>
              <span v-else>
                <a @click="checkGoodsDetail(row.id)">{{ row.content }}</a>
              </span>
            </div>
          </template>

          <!-- 开票信息 -->
          <template slot-scope="{ row }" slot="billingInfo">
            <div>
              <div class="flex table-slot">
                <span class="label">发票抬头:</span>
                <span class="value">{{ row.invoice.organization_name || '-' }}</span>
              </div>
              <div class="flex table-slot">
                <span class="label">公司税号:</span>
                <span class="value">{{ row.invoice.organization_code || '-' }}</span>
              </div>
              <div class="flex table-slot">
                <span class="label">注册地址:</span>
                <span class="value">{{ (row.invoice && row.invoice.reg_address_text) || '-' }}</span>
              </div>
              <div class="flex table-slot">
                <span class="label">注册电话:</span>
                <span class="value">{{ (row.invoice && row.invoice.reg_mobile) || '-' }}</span>
              </div>
              <div class="flex table-slot">
                <span class="label">开户银行:</span>
                <span class="value">{{ (row.invoice && row.invoice.bank) || '-' }}</span>
              </div>
              <div class="flex table-slot">
                <span class="label">银行账号:</span>
                <span class="value">{{ (row.invoice && row.invoice.bank_account) || '-' }}</span>
              </div>
            </div>
          </template>

          <!-- 蓝票状态 -->
          <template slot-scope="{ row }" slot="blue_status_desc">
            {{ row.blue_status_desc || '-' }}
          </template>

          <!-- 红票状态 -->
          <template slot-scope="{ row }" slot="red_status_desc">
            {{ row.red_status_desc || '-' }}
          </template>

          <template slot-scope="{ row }" slot="action">
            <div class="flex flex-item-center">
              <a v-if="row.status === 'REJECTED'" @click="goCreate(row.id)">修改</a>
              <a style="margin-left: 6px;" @click="toDetail(row)">详情</a>
            </div>
          </template>
        </Table>
        <div class="block_20"></div>
        <KPage
            :current.sync="+queryFormData.page"
            :page-size.sync="+queryFormData.pageSize"
            :total="total"
            style="text-align: center"
            @on-change="handleCurrentChange"
            @on-page-size-change="handleSizeChange"
        />
      </div>

      <!-- 商品明细 -->
      <goods-modal :goods-visible.sync="goodsVisible" :id="checkedId"/>
      <!-- 发票信息未完善提示 -->
      <invoice-info-tip
          v-model="invoiceInfoVisible"
          :tips-content="invoiceTipsContent"
          :tipsType="invoiceTipsType"
      ></invoice-info-tip>
    </div>
  </div>
</template>

<script>
import S from '@/utils/util';
import search from "@/mixins/search";
import downloadExcel from '@/mixins/downloadExcel';
import invoiceInfoTip from '@/components/InvoiceModal/invoice-info-tip.vue';

const init_query_form_data = {
  page: 1,
  pageSize: 20,
  st: '',
  et: '',
  status: '',
  apply_code: '', // 申请单号
  order_code: '', // 关联订单号
  r: ''
};
export default {
  name: 'list',
  mixins: [search, downloadExcel],
  components: {
    invoiceInfoTip,
    GoodsModal: () => import('./components/GoodsDetail')
  },
  data() {
    return {
      apiName: 'getPurInvoiceList',
      downloadApiName: 'getPurInvoiceExport', // 导出列表
      timeRange: [],
      queryFormData: {
        ...init_query_form_data
      },
      tableCols: [
        {title: '申请单号', key: 'apply_code', align: 'center', minWidth: 80},
        {title: '申请方', slot: 'apply', align: 'center', width: 170},
        {title: '开票方', slot: 'drawer', align: 'center', minWidth: 80},
        {title: '发票信息', slot: 'invoiceInfo', align: 'center', width: 190},
        {title: '开票信息', slot: 'billingInfo', align: 'center', width: 240},
        {title: '蓝票状态', slot: 'blue_status_desc', align: 'center', minWidth: 80},
        {title: '红票状态', slot: 'red_status_desc', align: 'center', minWidth: 80},
        {title: '申请时间', key: 'create_time', align: 'center', minWidth: 80},
        {title: '开票时间', key: 'finished_at', align: 'center', minWidth: 80},
        {title: '操作', slot: 'action', align: 'center', width: 100}
      ],
      statusDesc: [], // 状态数据
      list: [],
      applicantList: [], // 申请方list
      // 商品明细
      goodsVisible: false,
      checkedId: '',

      invoiceInfoVisible: false,
      applyInvoiceLoading: false,
      invoiceTipsContent: '',
      invoiceTipsType: '',
    };
  },
  computed: {
    getRedMount() {
      return (row, key) => {
        if (Math.sign(row[key]) === -1) {
          return row[key];
        }
      };
    }
  },
  watch: {},
  created() {
    this.queryFormData = S.merge(this.queryFormData, this.$route.query);
    this.submitQueryForm(true);
  },
  mounted() {
    this.init();
  },
  methods: {
    // 初始化数据
    init() {
      this.getPurInvoiceOption();
    },

    // 导出明细
    exportInvoiceDetai() {
      this.downloadExcel(this.queryFormData);
    },

    checkGoodsDetail(id) {
      this.checkedId = id;
      this.goodsVisible = true;
    },

    onStatusChange(status) {
      this.queryFormData.page = 1;
      this.queryFormData.status = status;
      this.submitQueryForm();
    },

    onResetSearch() {
      this.queryFormData = {...init_query_form_data};
      this.timeRange = [];
      this.submitQueryForm();
    },

    checkInvoiceStatus() {
      this.applyInvoiceLoading = true;
      this.$api
          .checkInvoiceStatus()
          .then(
              res => {
                console.log('%c=>(list.vue:349) res', 'font-size: 18px;color: #FF7043 ;', res);
                // type = 1请填写..、2请修改..、3财务审核中..、4客服审核中..

                if (res.not_pass !== '1') {
                  this.goCreate();
                } else {
                  this.invoiceTipsContent = res.msg;
                  this.invoiceTipsType = res.type;
                  this.invoiceInfoVisible = true;
                }
              },
              rej => this.$Message.error(rej.errmsg)
          )
          .finally(() => (this.applyInvoiceLoading = false));
    },

    // 申请开票
    goCreate(id = '') {
      this.$router.push({
        path: '/purchase/invoice/edit',
        query: {
          id
        }
      });
    },

    // 详情
    toDetail({id}) {
      this.$router.push({
        path: '/purchase/invoice/detail',
        query: {
          id
        }
      });
    },

    // 获取枚举值
    getPurInvoiceOption() {
      this.$api.getPurInvoiceOption().then(res => {
        this.statusDesc = res.statusDesc;
      });
    }
  },
  beforeRouteUpdate(to, from, next) {
    this.queryFormData = S.merge(init_query_form_data, to.query);
    this.getTimeRange()
    this.loadList();
    next();
  }
};
</script>

<style lang="less" scoped>
.table-slot {
  font-size: 12px;

  .label {
    width: 64px;
    text-align: right;
    margin-right: 4px;
  }

  .value {
    text-align: left;
    flex: 1;
  }
}

.error {
  color: red;
}

.auto-box {
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-sizing: border-box;
  height: 25px;
  line-height: 32px;
  margin-bottom: 7px;
  margin-top: -10px;
}
</style>
