<template>
  <div>
    <Modal :value="value" width="680" :title="title" @on-visible-change="changeVisible" :mask-closable="false">
      <Form ref="externalCompanyForm" :model="formData" :rules="rules" :label-width="labelWidth" :label-colon="true">
        <FormItem label="外部公司名称" prop="name">
          <Input v-model="formData.name" :maxlength="30" show-word-limit placeholder="输入外部公司名称"></Input>
        </FormItem>

        <FormItem label="营业执照名称" prop="organization_name">
          <Input v-model="formData.organization_name" placeholder="输入营业执照名称"></Input>
        </FormItem>

        <FormItem label="统一社会信用代码" prop="organization_code">
          <Input
            v-model="formData.organization_code"
            :disabled="isOrgCodeDisabled"
            placeholder="请输入18位统一社会信用代码"
            maxlength="18"
            show-word-limit
          ></Input>
        </FormItem>

        <FormItem label="联系人">
          <Input v-model="formData.leading_person" placeholder="输入联系人姓名"></Input>
        </FormItem>

        <FormItem label="联系电话">
          <Input :maxlength="11" type="number" v-model="formData.leading_mobile" placeholder="输入联系人手机号"></Input>
        </FormItem>

        <FormItem label="公司地址">
          <div class="addWrap">
            <div class="addressBox" style="width: 55%">
              <el-cascader
                v-model="selectedAddress"
                :options="regionData"
                clearable
                placeholder="请选择外部公司地址"
                size="small"
                popper-class="address-com"
                style="width: 100%"
                @change="regionChange"
              >
              </el-cascader>
            </div>

            <div class="addressInput ml10">
              <Input v-model="formData.address_other" placeholder="详细地址"></Input>
            </div>
          </div>
        </FormItem>

        <FormItem label="营业执照">
          <Picture v-model="formData.organization_images" :limit="9" />
        </FormItem>
      </Form>
      <div slot="footer">
        <Button type="text" @click="handleCancel">取消</Button>
        <Button type="primary" :loading="submitLoading" @click="handleOk">保存</Button>
      </div>
    </Modal>
  </div>
</template>

<script>
import { CodeToText, regionData } from '@/utils/chinaMap/index';
import Picture from '@/components/upload/picture/index';
import { cloneDeep } from 'lodash';

export default {
  name: 'CreateExternalCompany',
  components: {
    Picture
  },
  model: {
    prop: 'value',
    event: 'changeVisible'
  },
  props: {
    labelWidth: {
      type: Number,
      default: 140
    },
    value: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      default: '新增外部公司'
    },
    ecCompanyName: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      formData: {
        name: '',
        organization_name: '',
        organization_code: '',
        leading_person: '',
        leading_mobile: '',
        prov_name: '',
        prov_code: '',
        city_name: '',
        city_code: '',
        county_name: '',
        county_code: '',
        address_other: '',
        organization_images: [],
        cp_id: ''
      },
      selectedAddress: [],
      options: [],
      rules: {
        name: [
          { required: true, message: '请输入外部公司名称', trigger: 'blur' },
          { min: 2, max: 30, message: '长度在 2 到 30 个字符', trigger: 'blur' }
        ],
        organization_name: [{ required: true, message: '输入营业执照名称', trigger: 'blur' }],
        organization_code: [
          { required: true, message: '输入18位统一社会信用代码', trigger: 'blur' },
          { type: 'string', pattern: /^[^oO]*$/, message: '统一社会信用代码不能包含字母o', trigger: 'blur' }
        ]
      },
      regionData: regionData,
      isOrgCodeDisabled: false,
      submitLoading: false,
      isEdit: true // 城市合伙人是否可以编辑
    };
  },
  methods: {
    regionChange(address) {
      if (address.length) {
        const province = {
          value: CodeToText[address[0]],
          key: address[0]
        };
        const city = {
          value: CodeToText[address[1]],
          key: address[1]
        };
        const area = {
          value: CodeToText[address[2]],
          key: address[2]
        };
        this.formData.prov_name = province.value;
        this.formData.prov_code = province.key;
        this.formData.city_name = city.value;
        this.formData.city_code = city.key;
        this.formData.county_name = area.value;
        this.formData.county_code = area.key;
      } else {
        this.formData.prov_name = '';
        this.formData.prov_code = '';
        this.formData.city_name = '';
        this.formData.city_code = '';
        this.formData.county_name = '';
        this.formData.county_code = '';
      }
    },
    handleOk() {
      this.$refs.externalCompanyForm.validate(valid => {
        if (valid) {
          if (this.formData.leading_mobile) {
            const reg = /^1[3456789]\d{9}$/;

            if (!reg.test(this.formData.leading_mobile)) {
              this.$Message.error('请输入正确的手机号码');
              return;
            }
          }
          if (this.formData.organization_code) {
            if (this.formData.organization_code.length < 18) {
              this.$Message.error('请输入18位统一社会信用代码');
              return;
            }
          }
          this.submitLoading = true;
          this.$api
            .editOutsideCompany(this.formData)
            .then(
              res => {
                this.$Message.success('创建外部公司成功');
                this.$emit('createSuccess', { name: this.formData.name, id: res.id });
                this.closeModal();
              },
              rej => {
                this.$Message.error(rej.errmsg);
                this.submitLoading = false;
              }
            )
            .catch(() => {
              this.submitLoading = false;
            });
        } else {
          this.submitLoading = false;
        }
      });
    },
    closeModal() {
      this.$refs.externalCompanyForm?.resetFields();
      this.$emit('close');
      this.$emit('changeVisible', false);
    },
    changeVisible(v) {
      if (v) {
        this.formData.name = cloneDeep(this.ecCompanyName);
      } else {
        this.closeModal();
      }
    },
    handleCancel() {
      this.closeModal();
    }
  }
};
</script>

<style lang="less" scoped></style>
