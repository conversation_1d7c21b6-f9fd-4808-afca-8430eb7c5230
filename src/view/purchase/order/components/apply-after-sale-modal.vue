<template>
  <div>
    <Modal :value="value" title="申请售后" width="980px" :mask-closable="false" @on-visible-change="changeVisible">
      <div class="modal-content">
        <Form ref="apply" :label-width="100" :label-colon="true" :model="formData">
          <FormItem label="售后类型" required>
            <RadioGroup v-model="formData.refund_type" @on-change="typeChange">
              <Radio label="M">仅退款</Radio>
              <Radio label="GM" :disabled="isAllBack">退货退款</Radio>
            </RadioGroup>
          </FormItem>

          <FormItem label="收货情况" required v-if="formData.refund_type == 'GM'">
            <RadioGroup v-model="formData.delivery_way">
              <Radio label="HAS">已收到货</Radio>
              <Radio label="NOT">未收到货</Radio>
            </RadioGroup>
          </FormItem>

          <FormItem label="售后商品明细" required :label-width="100">
            <div class="flex flex-item-end">
              <Select
                v-model="formData.goods"
                style="width: 280px"
                v-if="!isAllBack"
                :placeholder="moneyHeader.get(formData.refund_type)"
                @on-change="selectGood"
                transfer-class-name="apply-select"
              >
                <Option v-for="item in showOptionList" :key="item.spu_code" :value="item.spu_code" :label="item.name">
                  <div class="flex">
                    <span>{{ item.name }}</span>
                    <span style="font-size: 12px; color: #333333" v-show="item.spec_desc">
                      <span style="margin: 0 4px">-</span> {{ item.spec_desc }}</span
                    >
                  </div>
                </Option>
              </Select>
            </div>
          </FormItem>

          <Table
            :columns="goodsColumns"
            :data="showGoodList"
            :loading="goodsLoading"
            :show-summary="true"
            :summary-method="handleSummary"
            stripe
            border
            class="ml21 mb10"
          >
            <!--            省公司商品无单位，默认'-'展示-->
            <template slot="spec_desc" slot-scope="{ row }">
              {{ row.spec_desc || '-' }}
            </template>
            <!--        采购单价-->
            <template slot="pur_price" slot-scope="{ row }"> ¥ {{ getPurPrice(row.pur_price) }}</template>

            <!--            可退数量-->
            <template slot-scope="{ row, index }" slot="shipped_wait_num">
              {{ getMaxNum(index) }}
            </template>

            <!--            	退款数量-->
            <template slot-scope="{ index }" slot="addNum">
              <Input-Number
                :max="getMaxNum(index)"
                v-model="showGoodList[index].addNum"
                placeholder="请输入数量"
                :precision="0"
                @on-change="numberChange($event, index)"
                style="width: 64px"
                :disabled="isAllBack"
              />
            </template>

            <!--            个人退款金额-->
            <template slot-scope="{ row, index }" slot="personTotalPrice"> ¥ {{ row.personTotalPrice }}</template>

            <!--            省公司退款金额-->
            <template slot-scope="{ row, index }" slot="comTotalPrice"> ¥ {{ row.comTotalPrice }}</template>

            <template slot-scope="{ index }" slot="setting">
              <div>
                <a v-if="!isAllBack" style="color: red" @click="delCol(index)">删除</a>
                <span v-else>-</span>
              </div>
            </template>
          </Table>

          <FormItem label="个人退款金额" v-if="detail.type === 'OPC_PERSONAL'">
            ￥{{ personTotalRefundPrice }}
          </FormItem>
          <FormItem
            :label="detail.type === 'OPC_PERSONAL' ? '直营运营中心退款金额' : '退款金额'"
            :label-width="detail.type === 'OPC_PERSONAL' ? 140 : 100"
          >
            ￥{{ comTotalRefundPrice }}</FormItem
          >
          <FormItem label="换货情况" v-if="showChangeState">
            <div class="flex flex-item-between">
              <RadioGroup v-model="formData.change_state" class="radio-wrapper">
                <Radio label="NONE">无需换货</Radio>
                <Radio label="NEED">换货</Radio>
              </RadioGroup>
              <a @click="refundModalVisible = true">退款去向说明</a>
            </div>
            <template #label>
              <div class="flex flex-item-align" style="justify-content: flex-end">
                <span class="mgr-5"><span class="required">* </span>换货情况</span>
                <Tooltip class="item" effect="dark" maxWidth="200" placement="top">
                  <Icon type="md-help-circle" class="cursor-pointer" color="" size="18"></Icon>
                  <div slot="content">
                    <div>1.系统暂不支持换货功能，这里仅起到标记作用。</div>
                    <div>
                      2.选择无需换货或换货，将决定部分主体的退款是否退到其预付款余额。具体规则，请点击查看右上角的“退款去向说明”。
                    </div>
                  </div>
                </Tooltip>
                :
              </div>
            </template>
          </FormItem>
          <FormItem label="售后原因" required>
            <Select v-model="formData.refund_reason">
              <Option v-for="item in reasonOptions" :key="item.id" :value="item.id" :label="item.desc"></Option>
            </Select>
          </FormItem>

          <FormItem label="售后说明">
            <Input
              v-model="formData.refund_desc"
              type="textarea"
              :maxlength="200"
              :show-word-limit="true"
              :autosize="{ minRows: 3, maxRows: 5 }"
              placeholder="请说明诊所的退款原因"
            ></Input>
          </FormItem>

          <FormItem label="附件">
            <Picture v-model="formData.imgs" :limit="9" />
            <div>支持上传照片，单张图片大小不超过3.0M，最多9张</div>
          </FormItem>
        </Form>
      </div>

      <div slot="footer" class="edit-footer">
        <Button type="default" @click="closeModal">取消</Button>
        <Button type="primary" @click="confirm">确定</Button>
      </div>
    </Modal>

    <!--  提示框  -->
    <k-tip-modal v-model="tipVisible" text="售后申请提交成功，请等待审核"></k-tip-modal>
    <refund-explain-modal v-model="refundModalVisible"></refund-explain-modal>
  </div>
</template>

<script>
import Picture from '@/components/upload/picture';
import kTipModal from '@/components/k-tip-modal';
import S from '@/utils/util';
import { cloneDeep } from 'lodash';
import { $operator } from '@/utils/operation';
import refundExplainModal from '../../components/refundExplainModal.vue';
const init_form_data = {
  refund_type: 'M',
  delivery_way: 'HAS',
  goods: '',
  refund_reason: '',
  refund_desc: '',
  imgs: [],
  change_state: ''
};
export default {
  name: 'apply-after-sale',
  components: {
    Picture,
    kTipModal,
    refundExplainModal
  },
  mixins: [],
  props: {
    value: {
      type: Boolean,
      default: false
    },
    detail: {
      type: Object,
      default: () => {}
    },
    orderType: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      tipVisible: false,
      formData: cloneDeep(init_form_data),
      // 售后商品明细
      goodsColumns: [],
      goodsCommonColumns: [
        { title: '商品编号', key: 'spu_code', align: 'center' },
        { title: '商品类型', key: 'type_text', align: 'center' },
        { title: '商品', key: 'name', align: 'center' },
        { title: '规格', slot: 'spec_desc', align: 'center' },
        { title: '状态', key: 'shipped_status', align: 'center' },
        { title: '采购单价', slot: 'pur_price', align: 'center' },
        { title: '可退数量', key: 'shipped_wait_num', slot: 'shipped_wait_num', align: 'center' },
        { title: '退款数量', key: 'addNum', slot: 'addNum', align: 'center', showTotal: true },
        {
          title: '退款金额',
          key: 'comTotalPrice',
          slot: 'comTotalPrice',
          align: 'center',
          width: 100,
          showTotal: true,
          isMoney: true,
          //
          renderHeader: (h, params) => {
            return h('span', {}, `${this.detail.type === 'OPC_PERSONAL' ? '直营中心退款金额' : '退款金额'}`);
          }
        },
        { title: '操作', key: 'setting', slot: 'setting', align: 'center' }
      ],
      goodsTypeColumns: [
        {
          title: '个人退款',
          key: 'personTotalPrice',
          slot: 'personTotalPrice',
          align: 'center',
          width: 100,
          showTotal: true,
          isMoney: true,
          type: ['OPC_PERSONAL']
        }
      ],
      goodsList: [],
      showGoodList: [],
      optionList: [],
      showOptionList: [],
      goodsLoading: false,
      isAllBack: false,
      reasonOptions: [],
      // allPrice: null,
      moneyHeader: new Map([
        ['M', '添加未发货商品'],
        ['GM', '添加已发货商品']
      ]),
      refundModalVisible: false
    };
  },
  computed: {
    showChangeState() {
      // heihei
      let types = ['CLI_ADDITION', 'RXJ_ADDITION', 'OPC_OWN', 'OPC_PERSONAL', 'OPC_OUTSIDE'];
      return types.includes(this.orderType);
    },
    getMaxNum() {
      return function (index) {
        return Number(this.showGoodList[index]['can_apply_num_info'][this.formData['refund_type']]);
      };
    },
    comTotalRefundPrice() {
      return this.showGoodList.reduce((prev, item) => {
        return $operator.add(prev, item.comTotalPrice);
      }, 0);
    },
    personTotalRefundPrice() {
      return this.showGoodList.reduce((prev, item) => {
        return $operator.add(prev, item.personTotalPrice);
      }, 0);
    },
    getPurPrice() {
      return function (pur_price) {
        console.log('-> %c pur_price  ===    %o', 'font-size: 15px;color: #F56C6C ;', pur_price);
        console.log(
          '-> %c $operator.toPrecision(pur_price)  ===    %o',
          'font-size: 15px;color: #F56C6C ;',
          $operator.toPrecision(pur_price)
        );
        return `${$operator.toPrecision(pur_price)}`;
      };
    }
  },
  watch: {},
  created() {},
  mounted() {},
  methods: {
    // 初始化
    initData() {
      this.getPurchaseOrderGoods();
      this.getOrderRefundOptions();
      this.goodsColumns = cloneDeep(this.goodsCommonColumns);
      console.log('-> this.detail', this.detail);
      this.handleGoodsColumns();
    },
    // api - 获取退货商品列表
    getPurchaseOrderGoods() {
      this.$api
        .getPurchaseOrderGoods({ pur_code: this.detail.code })
        .then(res => {
          res.list.forEach(item => {
            item.addNum = null;
            item.personTotalPrice = 0;
            item.comTotalPrice = 0;
          });
          this.optionList = res.list;
          this.showOptionList = this.getOptionList(this.formData.refund_type);
        })
        .catch(err => {
          this.$Message.error(err.errmsg);
        });
    },

    // api - 枚举
    getOrderRefundOptions() {
      this.$api
        .getOrderRefundOptions()
        .then(res => {
          this.reasonOptions = S.descToArrHandle(res.refundReasonDesc);
        })
        .catch(err => this.$Message.error(err.errmsg));
    },

    // 通过类型调整列表展示
    handleGoodsColumns() {
      let filterTableColumns = this.goodsTypeColumns.filter(item => {
        if (item.type == 'all' || (Array.isArray(item.type) && item.type.includes(this.detail.type))) {
          return true;
        }
      });
      this.goodsColumns.splice(-2, 0, ...filterTableColumns);
    },
    typeChange(val) {
      this.formData.goods = '';
      this.showGoodList = [];
      this.showOptionList = this.getOptionList(val);
    },
    getOptionList(key) {
      let cloneOption = cloneDeep(this.optionList);
      return cloneOption.filter(item => Number(item.can_apply_num_info[key]) > 0) || [];
    },
    selectGood(value) {
      this.$nextTick(() => {
        this.formData.goods = '';
      });
      if (value) {
        let selectedGood = this.showOptionList.find(item => item.spu_code === value);
        selectedGood.addNum = null;
        selectedGood.totalPrice = 0;
        let isExist = this.showGoodList.some(item => item.spu_code === value);
        if (isExist) {
          this.$Message.error('该商品已添加');
          return;
        } else {
          this.showGoodList.push(selectedGood);
        }
      }
    },

    confirm() {
      if (this.validateFormData()) {
        this.applyOrderRefund();
      }
    },

    applyOrderRefund() {
      let params = {
        pur_code: this.detail.code,
        refund_type: this.formData.refund_type,
        refund_reason: this.formData.refund_reason,
        refund_desc: this.formData.refund_desc,
        refund_goods: this.handleRefundGoods(this.showGoodList),
        delivery_way: this.formData.refund_type === 'M' ? '' : this.formData.delivery_way,
        imgs: this.formData.imgs,
        change_state: this.formData.change_state
      };
      console.log('=>(detail.vue:241) params', params);
      this.$api
        .applyOrderRefund(params)
        .then(res => {
          this.$emit('applySuccess');
          console.log('=>(detail.vue:242) res', res);
        })
        .catch(err => this.$Message.error(err.errmsg));
    },
    handleSummary({ columns, data }) {
      const sums = {};
      columns.forEach((column, index) => {
        const key = column.key;
        if (index === 0) {
          sums[key] = {
            key,
            value: '合计'
          };
        } else {
          if (column.showTotal) {
            const values = data.map(item => Number(item[key] || 0));
            if (!values.every(value => isNaN(value))) {
              const v = values.reduce((prev, curr) => {
                // const value = Number( curr ).toFixed( 2 )
                if (!isNaN(curr)) {
                  return $operator.add(Number(prev), Number(curr));
                } else {
                  return $operator.add(Number(prev), 0);
                }
              }, 0);
              sums[key] = {
                key,
                value: `${column.isMoney ? '￥' : ''}${v}`
              };
            } else {
              sums[key] = {
                key,
                value: ''
              };
            }
          } else {
            sums[key] = {
              key,
              value: ''
            };
          }
        }
      });
      console.log('-> sums', sums);
      return sums;
    },

    numberChange(e, index) {
      this.showGoodList[index].addNum = e;
      this.calcPrice(index);
    },
    delCol(index) {
      this.showGoodList.splice(index, 1);
    },
    // 计算价格
    calcPrice(index) {
      const { pur_price, com_price, addNum } = this.showGoodList[index];
      if (com_price && addNum) {
        this.showGoodList[index].comTotalPrice = $operator.multiply(com_price, addNum);
      } else {
        this.showGoodList[index].comTotalPrice = 0;
      }
      // 省公司对个人订单 计算个人退款金额
      if (this.detail.type === 'OPC_PERSONAL') {
        if (pur_price && addNum) {
          this.showGoodList[index].personTotalPrice = $operator.multiply(pur_price, addNum);
        } else {
          this.showGoodList[index].personTotalPrice = 0;
        }
      }
    },
    // 校验
    validateFormData() {
      if (!this.formData.refund_type) {
        this.$Message.error('请选择收货类型');
        return false;
      }
      if (!this.showGoodList.length) {
        this.$Message.error('请选择售后商品');
        return false;
      }
      if (!this.formData.refund_reason) {
        this.$Message.error('请选择售后原因');
        return false;
      }
      if (this.showChangeState && !this.formData.change_state) {
        this.$Message.error('请选择换货状态');
        return false;
      }
      if (this.showGoodList.some(item => !item.addNum)) {
        this.$Message.error('商品退款数量不能为空');
        return false;
      }
      // let errorList = []
      // this.showGoodList.forEach(item => {
      //   if(!item.addNum){
      //     errorList.push(item.name)
      //   }
      // })
      // if(errorList.length > 0){
      //   let errorGoods = errorList.join(',')
      //   console.log("-> errorGoods", errorGoods);
      //   this.$Message.error(`${errorGoods} 退款数量不能为空`);
      //   return false
      // }
      return true;
    },

    handleRefundGoods(list) {
      console.log('-> list', list);
      return list.map(item => {
        return {
          spu_code: item.spu_code,
          num: item.addNum,
          refund_money: item.totalPrice
        };
      });
    },

    changeVisible(val) {
      if (val) {
        this.initData();
      } else {
        this.formData = { ...init_form_data };
        console.log('-> this.formData', this.formData);
        this.showGoodList = [];
        this.closeModal();
      }
    },
    // 关闭弹窗
    closeModal() {
      this.$emit('input', false);
    }
  },
  filters: {}
};
</script>
<style lang="less" scoped>
.required {
  color: #ed4014;
}
.radio-wrapper {
  display: inline-flex;
  align-items: center;
  line-height: 38px;
}
::v-deep .ivu-modal-body {
  max-height: 600px;
  min-height: 400px;
  overflow-y: scroll;

  &::-webkit-scrollbar {
    display: none;
  }

  .ivu-form-item {
    margin-bottom: 10px;
  }
}

.ml21 {
  margin-left: 21px;
}
</style>

<style lang="less">
.apply-select {
  .ivu-select-item {
    max-width: 282px !important;
    word-break: break-all !important;
    white-space: pre-wrap !important;
    line-height: 17px;
  }
}
</style>
