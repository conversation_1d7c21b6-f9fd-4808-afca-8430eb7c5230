<template>
  <div class="batch-modal-wrapper">
    <Modal
      :mask-closable="false"
      :value="batchVisible"
      title="设置商品采购规格"
      width="680px"
      class="spu-modal"
      @on-visible-change="visibleChange"
    >
      <div slot="footer" class="modal-foot">
        <div style="" class="flex flex-item-align flex-item-between">
          <div>
            已设置 <span style="color: green">{{ setUnit }}</span
            ><span v-show="isJiShanPurchase">/{{ editRow.pur_num }} </span>件
          </div>
          <div>
            <Button @click="cancelSet">取消</Button>
            <Button type="primary" @click="setSpu">确定</Button>
          </div>
        </div>
      </div>
      <div>
        <div class="flex flex-item-align spu-box">
          <div class="spu-items flex" v-for="(item, idx) in set_spu_list" :key="item.spu">
            <span style="min-width: 130px; text-align: left"> {{ item.name }}</span>
            <InputNumber
              v-model="item.num"
              placeholder="数量"
              controls-outside
              :precision="0"
              :min="0"
              size="small"
              style="width: 80px; margin-left: 20px"
            >
            </InputNumber>
          </div>
        </div>
      </div>
    </Modal>
  </div>
</template>

<script>
export default {
  name: 'batch-modal',
  props: {
    batchVisible: {
      type: Boolean,
      default: false,
    },
    editRow: {
      type: Object,
      default: () => {},
    },
    set_spu_list: {
      type: Array,
      default: () => [],
    },
    isJiShanPurchase: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {};
  },
  computed: {
    //共设置了多少件商品
    setUnit() {
      return this.set_spu_list.reduce((target, cur, arr) => {
        return target + Number(cur.num);
      }, 0);
    },
  },
  methods: {
    //取消设置
    cancelSet() {
      this.closeModal();
    },
    setSpu() {
      if(this.isJiShanPurchase && this.setUnit !== this.editRow.pur_num) {
        this.$Message.error('请设置正确的采购数量');
        return;
      }
      let specs_obj = {};
      const temList = this.set_spu_list.filter(item => item.num);
      const amount = temList.reduce((tar, cur) => {
        specs_obj[cur.spu] = cur.num;
        return tar + cur.num;
      }, 0);
      this.$emit('setSpu', {
        checked_spu_list: temList,
        specs: specs_obj,
        unit_amount: this.setUnit,
      });
      this.closeModal();
    },
    closeModal() {
      this.$emit('update:batchVisible', false);
    },
    changeSpuUnit(val, index) {
      console.log('-> val,index', val, index);
    },
    visibleChange(visible) {
      if (!visible) {
        this.closeModal();
      }
    },
  },
};
</script>

<style scoped lang="less">
.spu-box {
  flex-wrap: wrap;

  .spu-items {
    align-items: center;
    width: 50%;
    margin: 10px 0;
  }
}

.spu-modal {
  .ivu-modal {
    .ivu-modal-body {
      min-height: 400px;
      max-height: 500px;
      overflow-y: auto;
    }
  }

  .ivu-input-number-input {
    text-align: center;
  }
}
</style>
