<template>
  <div>
    <Form ref="purFormData" :model="formData" :label-width="110" label-colon :rules="ruleValidate">
      <Row>
        <Col span="8">
          <FormItem label="选择会议费活动" prop="promotion_id">
            <Select
              v-model="formData.promotion_id"
              transfer
              clearable
              :loading="searchLoading"
              filterable
              :remote-method="search"
              class="filterable-select"
              placeholder="请选择会议活动"
              @on-clear="clearPromotion"
              @on-query-change="getPromotionInfo"
              @on-select="selectPromotion"
            >
              <Option v-for="(item, index) in promotionList" :key="index" :value="item.id">{{ item.name }}</Option>
            </Select>
          </FormItem>
        </Col>
      </Row>

      <Row>
        <Col span="24">
          <FormItem label="参会人信息" prop="meeting_user_list">
            <div
              v-for="(item, index) in formData.meeting_user_list"
              :key="index"
              class="flex"
              style="margin-bottom: 12px"
            >
              <Input
                v-model="formData.meeting_user_list[index].name"
                class="normal-form-com"
                placeholder="请输入参会人真实姓名"
              />
              <Input
                v-model="formData.meeting_user_list[index].mobile"
                class="normal-form-com"
                placeholder="请输入参会人手机号"
              />
              <Select
                v-model="formData.meeting_user_list[index].sex"
                class="normal-form-com"
                placeholder="请选择参会人性别"
              >
                <Option value="1">男</Option>
                <Option value="2">女</Option>
              </Select>

              <el-select
                :ref="'comList' + index"
                style="margin-right: 12px"
                v-model="formData.meeting_user_list[index].opc_id"
                size="small"
                reserve-keyword
                :loading="comLoading"
                class="com-search"
                placeholder="请输入搜索直营运营中心"
                @change="selectCom(index)"
              >
                <el-option
                  v-for="item in formData.meeting_user_list[index].companyList"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
                />
              </el-select>
              <div class="action-icon" style="height: 32px; display: inline-flex; align-items: center">
                <svg-icon
                  v-if="index === 0"
                  icon-class="plus-circle"
                  style="font-size: 24px; cursor: pointer; margin-right: 6px"
                  @click="addMeetingUser"
                />
                <svg-icon
                  v-if="formData.meeting_user_list.length > 1"
                  icon-class="minus-circle"
                  style="font-size: 24px; cursor: pointer"
                  @click="deleteMeetingUser(index)"
                />
              </div>

              <!--                <Button type="primary" style="margin-right: 12px;" v-if="index===0" @click="addMeetingUser">添加</Button>-->
              <!--                <Button v-else @click="deleteMeetingUser(index)">移除</Button>-->
            </div>
          </FormItem>
        </Col>
      </Row>
      <Row>
        <Col span="24">
          <FormItem>
            <!-- 省公司会议门票建单-->
            <Table
              :data="meeting_goods_list"
              :columns="purchase_columns_meeting"
              :summary-method="handleSummary"
              border
              show-summary
            >
              <template slot="img" slot-scope="{ row, index }">
                <viewer v-if="row.img" :images="[row.img]" class="flex flex-item-center">
                  <img style="width: 80px; height: auto; max-height: 100px" :src="row.img | imageStyle" class="image" />
                </viewer>
                <span v-else>-</span>
              </template>
              <!--              规格-->
              <template slot="spu_list" slot-scope="{ row, index }"> -</template>
              <!--              采购数量-->
              <template slot="pur_num" slot-scope="{ row, index }">
                {{ row.pur_num }}
              </template>
              <!--            省公司进货合计  -->
              <template slot="com_amount" slot-scope="{ row, index }">
                {{ row.com_amount }}
              </template>
            </Table>
          </FormItem>
        </Col>
      </Row>

      <div class="block-header">支付信息</div>
      <Row>
        <Col span="14">
          <FormItem label="直营运营中心支付" prop="phonetic_code" :label-width="120">
            <div class="">
              <h3>¥ {{ getTotalAmount('com') }}</h3>
              <div class="com-pay-info">
                <div class="pay-item flex-item-align">
                  <span class="pay-label">预付款余额支付</span
                  ><span style="margin: 0 8px">(可用余额: {{ company_balance }}元)</span>
                  <Button
                    v-if="showRechargeButton('/finance/recharge/edit')"
                    class="ml-4"
                    size="small"
                    type="primary"
                    :to="{
                      path: '/finance/recharge/edit',
                      query: {
                        payer_type: 'OPC',
                        payer_id: companyWallet.ent_id
                      }
                    }"
                    target="_blank"
                  >
                    去充值
                  </Button>
                </div>
              </div>
            </div>
          </FormItem>
        </Col>
        <Col span="14">
          <FormItem label="备注" prop="remark">
            <Input v-model="formData.remark" autosize type="textarea" placeholder="请输入备注" />
          </FormItem>
        </Col>
        <Col span="14">
          <FormItem label="附件" prop="attach_images">
            <Picture
              v-model="formData.attach_images"
              :limit="9"
              :format="['jpg', 'jpeg', 'png', 'pdf', 'ofd']"
              accept=".pdf,.ofd,image/jpg,image/jpeg,image/png"
            />
          </FormItem>
        </Col>
      </Row>
    </Form>
  </div>
</template>

<script>
import { $operator } from 'utils/operation';
import { debounce } from 'lodash'; //计算方法
import S from 'utils/util';
import Picture from '@/components/upload/picture';

const init_wallet_info = {
  balance: 0,
  id: '',
  ent_type: '',
  ent_id: ''
};

export default {
  name: 'ClinicPurchase',
  components: { Picture },
  props: {
    sceneType: {
      type: String,
      default: 'P_MEETING'
    },
    isAdaptPriceCompany: {
      type: String,
      default: '0'
    },
    companyWallet: {
      type: Object,
      default: () => init_wallet_info
    },
    echoData: {
      type: Object,
      default: () => {}
    },
    purchase_scene_lists: {
      type: Array,
      default: () => []
    },
    showRechargeButton: {
      type: Function,
      default: () => {}
    }
  },
  data() {
    const validateMeetingUser = (rule, value, callback) => {
      callback();
    };
    return {
      formData: {
        type: 'P_MEETING',
        promotion_id: '',
        meeting_user_list: [
          {
            name: '', //姓名
            mobile: '', //手机号
            sex: '', //性别
            opc_id: '', //直营运营中心id
            id: '', //编辑是存在
            companyList: []
          }
        ],
        pay_info: {
          clinic: { wallet_amount: 0, offline_amount: 0, voucher_imgs: [] },
          company: { wallet_amount: 0, offline_amount: 0, voucher_imgs: [] },
          personal: { wallet_amount: 0, offline_amount: 0, voucher_imgs: [] },
          rxj: { wallet_amount: 0, offline_amount: 0, voucher_imgs: [] }
        },
        attach_images: []
      },
      ruleValidate: {
        promotion_id: [{ required: true, trigger: 'change', message: '请选择会议费活动' }],
        meeting_user_list: [{ required: true, trigger: 'change', validator: validateMeetingUser }]
      },
      clinicWallet: { ...init_wallet_info },
      searchLoading: false,
      promotionList: [],
      currentCompanyList: [],
      currentCompanyId: '',
      comLoading: false,
      meeting_goods_list: [],
      purchase_columns_meeting: [
        { title: '商品编号', key: 'spu_code', align: 'center', width: 75 },
        { title: '商品', key: 'name', align: 'center' },
        { title: '介绍图', slot: 'img', align: 'center', width: 120 },
        { title: '类型', key: 'type_text', align: 'center' },
        { title: '规格', key: 'spu_list', slot: 'spu_list', align: 'center', width: 50 },
        { title: '直营运营中心进货指导单价', key: 'com_price', align: 'center' },
        { title: '直营运营中心进货单价', key: 'com_ex_price', align: 'center', width: 90 },
        { title: '采购数量', slot: 'pur_num', key: 'pur_num', align: 'center', width: 90 },
        { title: '直营运营中心进货合计', slot: 'com_amount', key: 'com_amount', align: 'center' }
      ]
    };
  },
  computed: {
    // 计算总价
    getTotalAmount(type) {
      return type => {
        let list = this.meeting_goods_list;
        if (!list.length) return 0;
        return list.reduce((preVal, curVal, arr) => {
          if (type === 'cli') {
            return $operator.add(preVal, Number(curVal.cli_amount));
          } else if (type === 'com') {
            return $operator.add(preVal, Number(curVal.com_amount));
          } else if (type === 'per') {
            return $operator.add(preVal, Number(curVal.personal_amount));
          }
        }, 0);
      };
    },

    //获取线下支付总价
    getOfflinePayments(type) {
      return type => {
        if (type === 'cli') {
          if (this.clinicWallet.balance > this.getTotalAmount(type)) {
            return 0;
          } else {
            return $operator.subtract(this.getTotalAmount('cli'), this.clinicWallet.balance, 2);
          }
        } else if (type === 'com') {
          if (this.company_balance > this.getTotalAmount(type)) {
            return 0;
          } else {
            return $operator.subtract(this.getTotalAmount('com'), this.company_balance, 2);
          }
        } else if (type === 'per') {
          // 个人支付
          return this.getTotalAmount('per');
        } else if (type === 'rxj') {
          if (this.rxj_balance > this.getTotalAmount('cli')) {
            return 0;
          } else {
            return $operator.subtract(this.getTotalAmount('cli'), this.rxj_balance, 2);
          }
        }
      };
    },

    company_balance() {
      return this.companyWallet.balance;
    },

    //获取预支付总价 24/9/14 不用钱包设置上限，商品金额直接给后端处理
    getPrepayments(type) {
      return type => {
        if (type === 'cli' || type === 'rxj') {
          return this.getTotalAmount('cli');
        } else if (type === 'com') {
          return this.getTotalAmount('com');
        } else if (type === 'per') {
          return this.getTotalAmount('per');
        }
      };
    }
  },
  watch: {
    echoData: {
      handler(val) {
        console.log('=>(RxjPurchase.vue:495) val', val);
        if (S.isEmptyObject(val)) return;
        this.handleEchoData(val);
      },
      immediate: true
    }
  },
  mounted() {
    this.getPurchaseOptions();
    this.getPromotionInfo();
  },

  methods: {
    getPurchaseOptions() {
      this.$api
        .getPurchaseOptions()
        .then(res => {
          // 默认带出当前省公司
          this.formData.meeting_user_list[0].companyList = this.currentCompanyList = [
            {
              id: res.opc_id,
              name: res.opc_name
            }
          ];
          this.formData.meeting_user_list[0].opc_id = this.currentCompanyId = res.opc_id;
        })
        .catch(err => this.$Message.error(err.errmsg));
    },
    search() {},
    clearPromotion() {
      this.meeting_goods_list = [];
    },
    getPromotionInfo(query) {
      this.searchLoading = true;
      let params = {
        name: query,
        status: 'UNDERWAY',
        type: 'MEETING',
        with_info: 1
      };
      this.$api
        .getPromotionInfo(params)
        .then(res => {
          this.promotionList = res.list.filter(item => !!item.id);
        })
        .catch(err => this.$Message.error(err.errmsg))
        .finally(() => (this.searchLoading = false));
    },
    selectPromotion(val) {
      let curPromotion = this.promotionList.find(item => item.id === val.value);
      this.meeting_goods_list = curPromotion.meeting_attr.items.map(item => {
        item.pur_num = $operator.multiply(Number(item.buy_num), this.formData.meeting_user_list.length);
        return {
          ...item,
          num: item.buy_num, // 起购条件
          goods_code: item.goods_code,
          com_price: item.ori_com_price,
          personal_price: item.personal_price,
          spu_code: item.spu_code,
          type: item.type,
          type_text: item.type_text,
          com_ex_price: item.unit_price,
          com_amount: $operator.multiply(Number(item.unit_price), item.pur_num)
        };
      });
    },
    getCommonComList(query, index) {
      this.comLoading = true;
      let params = {
        name: query
      };
      this.$api
        .getCommonList(params)
        .then(res => {
          this.formData.meeting_user_list[index].companyList = res.list;
        })
        .catch(err => {
          this.$Message.error(err.errmsg);
        })
        .finally(() => (this.comLoading = false));
    },
    getCurCommonList(index) {
      let query = this.$refs['comList' + index][0].query;
      this.getCommonComList(query, index);
    },
    selectCom(index) {
      this.getCurCommonList(index);
    },
    addMeetingUser() {
      let emptyUser = {
        name: '', //姓名
        mobile: '', //手机号
        sex: '', //性别
        opc_id: this.currentCompanyId, //省公司id
        id: '', //编辑是存在
        companyList: this.currentCompanyList
      };
      this.formData.meeting_user_list.push(emptyUser);
      this.meeting_goods_list.forEach(item => {
        item.pur_num = $operator.multiply(Number(item.num), this.formData.meeting_user_list.length);
        item.com_amount = $operator.multiply(Number(item.com_ex_price), item.pur_num);
      });
    },
    deleteMeetingUser(index) {
      this.formData.meeting_user_list.splice(index, 1);
      this.meeting_goods_list.forEach(item => {
        item.pur_num = $operator.multiply(Number(item.num), this.formData.meeting_user_list.length);
        item.com_amount = $operator.multiply(Number(item.com_ex_price), item.pur_num);
      });
    },

    //合计
    handleSummary({ columns, data }) {
      const sums = {};
      columns.forEach((column, index) => {
        const key = column.key;
        if (index === 0) {
          sums[key] = {
            key,
            value: '合计'
          };
          return;
        }
        let whiteList = [7, 8];
        if (whiteList.indexOf(index) > -1) {
          const values = data.map(item => Number(item[key]));
          if (!values.every(value => isNaN(value))) {
            const v = values.reduce((prev, curr) => {
              const value = Number(curr);
              if (!isNaN(value)) {
                return S.mathAdd(prev, curr || 0);
              } else {
                return prev;
              }
            }, 0);
            sums[key] = {
              key,
              value: S.mathAdd(v || 0, 0)
            };
          } else {
            sums[key] = {
              key,
              value: '-'
            };
          }
        } else {
          sums[key] = {
            key,
            value: ''
          };
        }
      });
      return sums;
    },

    // 统一获取预付款余额数据 todo 不知道是否需要抛出，是否抛出更好？
    getMainWallet(ent_id, ent_type = 'COM') {
      this.$api.getMainWallet({ ent_id, ent_type }).then(res => {
        console.log('%c=>(create.vue:2229) res', 'font-size: 18px;color: #FF7043 ;', res);
        const walletMap = {
          COM: 'companyWallet',
          CLI: 'clinicWallet',
          RXJ: 'rxjWallet',
          PARTNER: 'partnerWallet',
          PERSON_C: 'personalWallet', // 个人
          OUTSIDE: 'outsideWallet' // 外部公司
        };
        this[walletMap[ent_type]] = res;
      });
    },

    // 提交(由父组件调用)
    validateForm() {
      console.log('=>(ClinicPurchase.vue:875) 子组件校验');
      this.$refs.purFormData.validate(valid => {
        if (valid) {
          if (!this.meeting_goods_list.length) {
            this.$Message.error('请先选择要采购的商品');
            return;
          }
          // 参会人校验

          if (!this.validateMeetingList()) {
            return;
          }

          // 区分编辑还是回显
          if (this.$route.query.pur_code) {
            this.formData.pur_code = this.$route.query.pur_code;
          }

          console.log('=>(ClinicPurchase.vue:908) 继续执行');

          let cloneFormData = this._.cloneDeep(this.formData);
          this.$delete(cloneFormData, 'pay_info');

          let meeting_user_list = this.formData.meeting_user_list.map(item => {
            return {
              name: item.name, //姓名
              mobile: item.mobile, //手机号
              sex: item.sex, //省公司id
              opc_id: item.opc_id, //性别
              id: item.id //编辑是存在}
            };
          });

          let params = {
            type: this.sceneType,
            ...cloneFormData,
            ...this.handleParamsPayInfo(),
            meeting_user_list: meeting_user_list
          };
          this.$emit('handleSubmitLoading', true);
          this.$api
            .saveAdditionPur(params)
            .then(
              res => {
                let currentInfo = this.purchase_scene_lists.filter(item => item.kw === this.formData.type);
                let title = currentInfo[0].label;
                this.$Message.success(`${this.$route.query.pur_code ? `编辑${title}单成功` : `创建${title}单成功`}`);
                this.$router.replace('/purchase/order/list');
              },
              err => this.$Message.error(err.errmsg)
            )
            .finally(() => {
              this.$emit('handleSubmitLoading', false);
            });
        } else {
          this.$Message.error('请完善表单信息');
        }
      });
    },
    // 校验参会人信息
    validateMeetingList() {
      let nameIndex = this.formData.meeting_user_list.findIndex(item => item.name === '');
      const reg = /^1[3456789]\d{9}$/;
      let mobileIndex = this.formData.meeting_user_list.findIndex(item => !reg.test(item.mobile));
      let sexIndex = this.formData.meeting_user_list.findIndex(item => item.sex === '');

      if (nameIndex !== -1) {
        this.$Message.error(`请填写参会人${nameIndex + 1}姓名`);
        return false;
      } else if (mobileIndex !== -1) {
        this.$Message.error(`请输入参会人${mobileIndex + 1}正确的手机号码`);
        return false;
      } else if (sexIndex !== -1) {
        this.$Message.error(`请选择参会人${sexIndex + 1}性别`);
        return false;
      }
      return true;
    },
    // 编辑回显
    handleEchoData(data) {
      console.log('%c=>(create.vue:1461) data', 'font-size: 18px;color: #FF7043 ;', data);
      this.formData.consignee_info = data.consignee_info;
      this.formData.rxj_code = data.rxj_code;
      this.formData.remark = data.remark;
      this.formData.attach_images = data.attach_images || [];
      this.formData.vip_id = data.vip_id;
      if (data.vip_id && data.vip_id !== '0') {
        this.getUseVipList({ id: data.vip_id });
      }

      this.meeting_goods_list = data.items.map(item => {
        let specsObj = {};
        if (item.specs.length) {
          item.specs.map(sub => {
            specsObj[sub.spu_code] = sub.num;
          });
          item.specsObj = specsObj;
        }
        item.checked_spu_list = item.spu_spec_list.filter(item => Number(item.num) > 0);
        return {
          ...item,
          cli_price: +item.guide_single_cli_price,
          cli_pur_price: +item.single_cli_price, //编辑的诊所价格
          com_price: +item.guide_single_com_price,
          com_ex_price: +item.single_com_price, //编辑的省公司价格
          personal_price: +item.guide_single_personal_price,
          personal_pur_price: +item.single_personal_price, //编辑得个人价格
          num: +item.base_num,
          pur_num: +item.num,
          cli_amount: item.cli_price,
          com_amount: item.com_price,
          personal_amount: Number(item.personal_price)
        };
      });
      this.formData.pay_info.clinic.voucher_imgs = data.pay_info.clinic.imgs;
      this.formData.pay_info.company.voucher_imgs = data.pay_info.company.imgs;
      this.formData.pay_info.personal.voucher_imgs = data.pay_info.personal.imgs;
      this.formData.pay_info.rxj.voucher_imgs = data.pay_info.rxj.imgs;
      console.log('%c [ data.type ]-1637', 'font-size:13px; background:#e07bf6; color:#ffbfff;', data.type);
      this.formData.promotion_id = data.promotion.id;
      this.formData.meeting_user_list = data.promotion.meeting_user_list;
      this.formData.meeting_user_list.forEach(item => {
        item.companyList = [{ id: item.opc_id, name: item.opc_name }];
      });
    },

    handleParamsPayInfo() {
      return {
        pay_info: {
          company: {
            wallet_amount: this.getPrepayments('com')
            // voucher_imgs: this.formData.pay_info.company.voucher_imgs,
            // offline_amount: this.getOfflinePayments('com'),
          }
        }
      };
    }
  }
};
</script>

<style lang="less" scoped>
.com-pay-info {
  background: #f8f9f9;
  padding: 0 12px;
  border-radius: 4px;
  line-height: normal;

  .price {
    color: #fb9a49;
    margin: 0 12px 0 12px;
  }

  .pay-item {
    display: flex;
    padding: 12px 0;

    //&:first-of-type {
    //  border-bottom: 1px solid #e5e5e5;
    //}

    //&:last-of-type {
    //  padding-bottom: 0;
    //}
  }

  .pay-label {
    width: 90px;
  }
}

.normal-form-com {
  margin-right: 12px;
  width: 160px;
}
</style>
