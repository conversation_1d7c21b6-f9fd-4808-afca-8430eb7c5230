<template>
  <div>
    <Form ref="purFormData" :model="formData" :label-width="110" label-colon :rules="ruleValidate">
      <Row>
        <Col span="8">
          <FormItem label="外部公司名称" prop="outside_id">
            <el-select
              ref="ecSelect"
              v-model="formData.outside_id"
              size="small"
              filterable
              clearable
              remote
              style="width: 100%"
              reserve-keyword
              placeholder="请输入外部公司名称"
              :remote-method="searchExternalCompany"
              @clear="clearOutsideCompany"
              @change="handleECSelect"
            >
              <el-option v-for="item in ECList" :key="item.id" :value="item.id" :label="item.name">
                {{ item.name }}
              </el-option>
              <template slot="empty">
                <div class="flex flex-item-between flex-item-align create-option" @click="createExtCompany">
                  <p class="flex flex-c">
                    <span style="font-size: 16px">{{ ecCompanyName }}</span>
                    <span class="tip">尚无该外部公司</span>
                  </p>
                  <a>创建外部公司</a>
                </div>
              </template>
            </el-select>
          </FormItem>
        </Col>
      </Row>
      <Row>
        <FormItem label="外部公司编号" class="item-text">
          {{ formData.outside_id || '-' }}
        </FormItem>
      </Row>
      <Row>
        <Col span="8">
          <FormItem label="采购商品">
            <Select
              ref="clinicSearch"
              v-model="goodsName"
              placeholder="请输入、搜索采购商品"
              clearable
              filterable
              :remote-method="searchSpuList"
              @on-select="selectGoods"
              @on-query-change="searchSpuList"
            >
              <Option v-for="item in goodsList" :key="item.value" :tag="item.jsonItem" :value="item.name">
                {{ item.name }}
              </Option>
            </Select>
          </FormItem>
        </Col>
        <Col :span="24">
          <FormItem>
            <!-- 直营运营中心对外部公司销售 -->
            <Table
              :data="select_goods_list"
              :columns="purchase_columns_outside"
              :summary-method="handleSummary"
              border
              show-summary
            >
              <template slot="img" slot-scope="{ row, index }">
                <viewer v-if="row.img" :images="[row.img]" class="flex flex-item-center">
                  <img style="width: 80px; height: auto; max-height: 100px" :src="row.img | imageStyle" class="image" />
                </viewer>
                <span v-else>-</span>
              </template>
              <template slot="com_ex_price" slot-scope="{ row, index }">
                <div v-if="row.can_edit_price === '1'">
                  <InputNumber
                    v-model="select_goods_list[index].com_ex_price"
                    :min="0"
                    :precision="2"
                    class="dis-ipt"
                    placeholder="直营运营中心进货单价"
                    :active-change="false"
                    style="width: 70px"
                    @on-change="e => changeComPrice(e, index)"
                  />
                </div>
                <div v-else>{{ row.com_ex_price }}</div>
              </template>
              <template slot="buy_info" slot-scope="{ row, index }">
                <span>{{ row.buy_info.num }}{{ row.buy_info.type === 'NUM' ? '件' : '倍' }}起购</span>
              </template>
              <!-- 进货单价 -->
              <template slot="personal_pur_price" slot-scope="{ row, index }">
                <InputNumber
                  v-model="select_goods_list[index].personal_pur_price"
                  :min="0"
                  :precision="2"
                  class="dis-ipt"
                  placeholder="进货单价"
                  :active-change="false"
                  style="width: 70px"
                  :disabled="row.can_edit_price !== '1'"
                  @on-change="e => changePersonalPrice(e, index)"
                />
              </template>

              <template slot="pur_num" slot-scope="{ row, index }">
                <InputNumber
                  v-model="select_goods_list[index].pur_num"
                  :min="0"
                  :precision="0"
                  placeholder="采购数量"
                  class="dis-ipt"
                  :disabled="!!row.spu_spec_list.length"
                  :active-change="false"
                  style="width: 70px"
                  @on-blur="e => changePurNum(e, index)"
                />
              </template>

              <!-- 个人采购合计 -->
              <template slot="personal_amount" slot-scope="{ row, index }">
                <InputNumber
                  v-model="select_goods_list[index].personal_amount"
                  :min="0"
                  :precision="2"
                  class="dis-ipt"
                  placeholder="外部公司采购合计"
                  :active-change="false"
                  style="width: 70px"
                  disabled
                  @on-change="e => changePersonalAmount(e, index)"
                />
              </template>
              <template slot="spu_list" slot-scope="{ row, index }">
                <div class="flex flex-item-center">
                  <div class="flex flex-c">
                    <div v-for="(item, index) in row.checked_spu_list" :key="index" class="flex">
                      {{ item.name + ' * ' + item.num }}
                    </div>
                  </div>
                </div>
                <div class="flex flex-item-center">
                  <a v-if="row.spu_spec_list.length" style="margin-top: 10px" @click="setBatch(row, index)">设置规格</a>
                  <span v-else>-</span>
                </div>
              </template>
              <template slot="com_amount" slot-scope="{ row, index }">
                <div v-if="row.can_edit_price === '1'">
                  <InputNumber
                    :value="select_goods_list[index].com_price_total || null"
                    :min="0"
                    :precision="2"
                    placeholder="合计"
                    class="dis-ipt"
                    :active-change="false"
                    style="width: 70px"
                    @on-change="e => changeComAmount(e, index)"
                  />
                </div>
                <span v-else> {{ getAmount('com', index) }} </span>
                <!--                {{ getAmount('com', index) }}-->
              </template>
              <template slot="action" slot-scope="{ row, index }">
                <a style="color: #e95151" @click="deleteGoods(index)">删除</a>
              </template>
            </Table>
          </FormItem>
        </Col>
      </Row>

      <Row v-if="withVipOutsideIds.includes(formData.outside_id)">
        <Col span="8">
          <FormItem label="关联会员用户">
            <Select v-model="formData.vip_id" filterable clearable @on-change="handleChangeVip">
              <Option v-for="vip in vipList" :key="vip.id" :value="vip.id">
                {{ vip.name }}
              </Option>
            </Select>
          </FormItem>
        </Col>
        <Col :span="24">
          <FormItem label="会员名称">
            <div>{{ getVipDesc }}</div>
          </FormItem>
        </Col>
      </Row>
      <Row>
        <Col span="8">
          <FormItem label="收件人姓名" v-if="formData.type !== 'P_MEETING'" prop="consignee_info.consignee">
            <Input v-model="formData.consignee_info.consignee" placeholder="请输入收件人姓名" />
          </FormItem>
        </Col>
      </Row>
      <Row>
        <Col span="8">
          <FormItem label="收件人手机号" v-if="formData.type !== 'P_MEETING'" prop="consignee_info.mobile">
            <Input v-model="formData.consignee_info.mobile" placeholder="请输入收件人手机号" />
          </FormItem>
        </Col>
      </Row>
      <Row v-if="formData.type !== 'P_MEETING'">
        <Col span="16" style="display: flex">
          <FormItem label="地址" class="flex-1" prop="consignee_info.prov.name">
            <el-cascader
              v-model="selectedAddress"
              :options="options"
              clearable
              placeholder="请选择地址"
              size="small"
              popper-class="address-com"
              style="width: 100%"
              @change="regionChange"
            >
            </el-cascader>
          </FormItem>
          <FormItem prop="consignee_info.detail" :label-width="20">
            <Input v-model="formData.consignee_info.detail" style="width: 220px" placeholder="详细地址" />
          </FormItem>
        </Col>
      </Row>

      <div class="block-header">支付信息</div>
      <Row>
        <Col span="14">
          <FormItem label="外部公司支付">
            <div>
              <h3>¥ {{ getTotalAmount('per') }}</h3>
              <div v-if="isShowAdvanceBox" class="com-pay-info">
                <div class="pay-item flex-item-align">
                  <span class="pay-label">预付款余额支付</span>
                  <span style="margin: 0 8px">(可用余额: {{ getAdvanceBalance('OUTSIDE')?.balance || 0 }}元)</span>
                  <Button
                    v-if="showRechargeButton('/finance/recharge/edit')"
                    class="ml-4"
                    size="small"
                    type="primary"
                    :to="{
                      path: '/finance/recharge/edit',
                      query: {
                        payer_type: 'OUTSIDE',
                        payer_id: getAdvanceBalance('OUTSIDE').ent_id
                      }
                    }"
                    target="_blank"
                  >
                    去充值
                  </Button>
                </div>
              </div>
            </div>
          </FormItem>
        </Col>

        <!-- 诊所采购的直营运营中心支付 -->
        <Col span="14">
          <FormItem label="直营运营中心支付" prop="phonetic_code" :label-width="120">
            <div class="">
              <h3>¥ {{ getTotalAmount('com') }}</h3>
              <div class="com-pay-info">
                <div class="pay-item flex-item-align">
                  <span class="pay-label">预付款余额支付</span
                  ><span style="margin: 0 8px">(可用余额: {{ company_balance }}元)</span>
                  <!--                  <span class="price">¥{{ getPrepayments('com') }}</span>-->
                  <Button
                    v-if="showRechargeButton('/finance/recharge/edit')"
                    class="ml-4"
                    size="small"
                    type="primary"
                    :to="{
                      path: '/finance/recharge/edit',
                      query: {
                        payer_type: 'OPC',
                        payer_id: companyWallet.ent_id
                      }
                    }"
                    target="_blank"
                  >
                    去充值
                  </Button>
                </div>
              </div>
            </div>
          </FormItem>
        </Col>
        <Col span="14">
          <FormItem label="备注" prop="phonetic_code">
            <Input v-model="formData.remark" autosize type="textarea" placeholder="请输入备注" />
          </FormItem>
        </Col>
        <Col span="14">
          <FormItem label="附件" prop="attach_images">
            <Picture
              v-model="formData.attach_images"
              :limit="9"
              :format="['jpg', 'jpeg', 'png', 'pdf', 'ofd']"
              accept=".pdf,.ofd,image/jpg,image/jpeg,image/png"
            />
          </FormItem>
        </Col>
      </Row>
    </Form>
    <batch-modal :edit-row="editRow" :batch-visible.sync="batchVisible" :set_spu_list="set_spu_list" @setSpu="setSpu" />
    <create-external-company
      v-model="createExternalVisible"
      :ec-company-name="ecCompanyName"
      @createSuccess="handleCreateSuccess"
      @close="searchExternalCompany"
    />
  </div>
</template>

<script>
import { $operator } from 'utils/operation';
import { debounce } from 'lodash'; //计算方法
import S from 'utils/util';
import { CodeToText, regionData } from '@/utils/chinaMap';
import batchModal from '../batch-modal.vue';
import CreateExternalCompany from '../CreateExternalCompany/index.vue';
import Picture from '@/components/upload/picture';

const init_wallet_info = {
  balance: 0,
  id: '',
  ent_type: '',
  ent_id: ''
};

export default {
  name: 'ClinicPurchase',
  components: { CreateExternalCompany, batchModal, Picture },
  props: {
    sceneType: {
      type: String,
      default: 'OPC_OUTSIDE'
    },
    isAdaptPriceCompany: {
      type: String,
      default: '0'
    },
    companyWallet: {
      type: Object,
      default: () => init_wallet_info
    },
    echoData: {
      type: Object,
      default: () => {}
    },
    purchase_scene_lists: {
      type: Array,
      default: () => []
    },
    showRechargeButton: {
      type: Function,
      default: () => {}
    }
  },
  data() {
    const validateMobile = (rule, value, callback) => {
      if (value === '') {
        callback(new Error('请输入正确的手机号码'));
      } else {
        const reg = /^1[3456789]\d{9}$/;
        if (!reg.test(value)) {
          callback(new Error('请输入正确的手机号码'));
        }
        callback();
      }
    };
    return {
      ECList: [],
      ecCompanyName: '', // 外部公司名称
      withVipOutsideIds: [],
      vipList: [],
      formData: {
        type: 'OPC_OUTSIDE',
        consignee_info: {
          mobile: '',
          prov: {
            code: '',
            name: ''
          },
          city: {
            code: '',
            name: ''
          },
          county: {
            code: '',
            name: ''
          },
          detail: '',
          consignee: ''
        },
        pay_info: {
          clinic: { wallet_amount: 0, offline_amount: 0, voucher_imgs: [] },
          company: { wallet_amount: 0, offline_amount: 0, voucher_imgs: [] },
          personal: { wallet_amount: 0, offline_amount: 0, voucher_imgs: [] },
          rxj: { wallet_amount: 0, offline_amount: 0, voucher_imgs: [] }
        },
        attach_images: []
      },
      createExternalVisible: false,
      ruleValidate: {
        'consignee_info.consignee': [{ required: true, message: '请填写收件人姓名', trigger: 'change' }],
        'consignee_info.mobile': [{ required: true, trigger: 'change', validator: validateMobile }],
        'consignee_info.detail': [{ required: true, trigger: 'change', message: '请输入详细地址' }],
        'consignee_info.prov.name': [{ required: true, type: 'string', trigger: 'change', message: '请选择地址' }],
        outside_id: [{ required: true, trigger: 'change', message: '请选择外部公司' }]
      },
      clinic_type_text: '', // 诊所子类型
      pur_main_name: '', //
      purchase_columns_outside: [
        { title: '商品编号', key: 'spu_code', align: 'center', width: 75 },
        { title: '商品', key: 'name', align: 'center' },
        { title: '介绍图', slot: 'img', align: 'center', width: 120 },
        { title: '类型', key: 'type_text', align: 'center' },
        { title: '规格', key: 'spu_list', slot: 'spu_list', align: 'center', width: 180 },
        { title: '外部公司采购指导单价', key: 'personal_price', align: 'center' },
        { title: '直营运营中心进货指导单价', key: 'com_price', align: 'center' },
        { title: '外部公司采购单价', slot: 'personal_pur_price', align: 'center', width: 90 },
        { title: '直营运营中心进货单价', slot: 'com_ex_price', align: 'center', width: 90 },
        { title: '起购条件', slot: 'buy_info', align: 'center' },
        { title: '采购数量', slot: 'pur_num', key: 'pur_num', align: 'center', width: 90 },
        { title: '外部公司采购合计', slot: 'personal_amount', key: 'personal_amount', width: 90, align: 'center' },
        { title: '直营运营中心进货合计', slot: 'com_amount', key: 'com_amount', align: 'center' },
        { title: '操作', slot: 'action', align: 'center' }
      ],
      isJiShan: false,
      goodsName: '',
      select_goods_list: [],
      goodsList: [],
      selectedAddress: [],
      clinicWallet: { ...init_wallet_info },
      outsideWallet: { ...init_wallet_info },
      options: regionData,
      editRow: {},
      batchVisible: false,
      set_spu_list: [],
      chooseClinic: false,
      personal_info: {}
    };
  },
  computed: {
    getVipDesc() {
      return this.personal_info.vip_type_desc?.join('、') || '-';
    },
    // 获取对应主体的预付款余额信息
    getAdvanceBalance() {
      return type => {
        switch (type) {
          case 'OUTSIDE':
            return this.outsideWallet; // 外部公司
          default:
            return {};
        }
      };
    },
    //计算单条总价
    getAmount(type, index) {
      return (type, index) => {
        const item = this.select_goods_list[index];
        if (type === 'cli') {
          return $operator.multiply(item.cli_pur_price, item.pur_num || 0, 2);
        } else if (type === 'com') {
          return $operator.multiply(item.com_ex_price, item.pur_num || 0, 2);
        } else if (type === 'per') {
          return $operator.multiply(item.personal_pur_price, item.pur_num || 0, 2);
        }
      };
    },

    // 计算总价
    getTotalAmount(type) {
      return type => {
        let list = this.select_goods_list;
        if (!list.length) return 0;
        return list.reduce((preVal, curVal, arr) => {
          if (type === 'cli') {
            return $operator.add(preVal, Number(curVal.cli_amount));
          } else if (type === 'com') {
            return $operator.add(preVal, Number(curVal.com_amount));
          } else if (type === 'per') {
            return $operator.add(preVal, Number(curVal.personal_amount));
          }
        }, 0);
      };
    },

    //是否展示支付信息
    showPayments() {
      return (type, isRxj) => {
        if (!this.formData.clinic_id && !isRxj) {
          return false;
        } else {
          if (type === 'cli') {
            return this.select_goods_list.some(item => {
              return Number(item.cli_amount) > 0;
            });
          } else if (type === 'com') {
            return this.select_goods_list.some(item => {
              return Number(item.com_amount) > 0;
            });
          }
          return true;
        }
      };
    },

    //获取线下支付总价
    getOfflinePayments(type) {
      return type => {
        if (type === 'cli') {
          if (this.clinicWallet.balance > this.getTotalAmount(type)) {
            return 0;
          } else {
            return $operator.subtract(this.getTotalAmount('cli'), this.clinicWallet.balance, 2);
          }
        } else if (type === 'com') {
          if (this.company_balance > this.getTotalAmount(type)) {
            return 0;
          } else {
            return $operator.subtract(this.getTotalAmount('com'), this.company_balance, 2);
          }
        } else if (type === 'per') {
          // 个人支付
          return this.getTotalAmount('per');
        } else if (type === 'rxj') {
          if (this.rxj_balance > this.getTotalAmount('cli')) {
            return 0;
          } else {
            return $operator.subtract(this.getTotalAmount('cli'), this.rxj_balance, 2);
          }
        }
      };
    },

    company_balance() {
      return this.companyWallet.balance;
    },

    //获取预支付总价 24/9/14 不用钱包设置上限，商品金额直接给后端处理
    getPrepayments(type) {
      return type => {
        if (type === 'cli' || type === 'rxj') {
          return this.getTotalAmount('cli');
        } else if (type === 'com') {
          return this.getTotalAmount('com');
        } else if (type === 'per') {
          return this.getTotalAmount('per');
        }
      };
    },

    // 是否展示预付款模块信息，
    isShowAdvanceBox() {
      return !!this.formData.outside_id;
    }
  },
  watch: {
    echoData: {
      handler(val) {
        console.log('=>(RxjPurchase.vue:495) val', val);
        if (S.isEmptyObject(val)) return;
        this.chooseClinic = true;
        this.handleEchoData(val);
      },
      immediate: true
    }
  },
  mounted() {
    this.searchExternalCompany('');
    this.searchSpuList('');
    this.getUseVipList();
    this.getPurchaseOptions();
  },

  methods: {
    searchExternalCompany: debounce(function (name) {
      console.log('%c=>(create.vue:2222) name', 'font-size: 18px;color: #FF7043 ;', name);
      this.ecCompanyName = name;
      let params = {
        page: 1,
        pageSize: 20,
        name,
        status: 'ENABLED'
      };
      this.$api.getExternalCompanyList(params).then(
        res => {
          // 获取用户数据
          this.ECList = res.list;
        },
        rej => this.$Message.error(rej.errmsg)
      );
    }, 600),
    clearOutsideCompany() {
      this.clearAddressInfo();
      this.searchExternalCompany('');
      this.resetGoodsList();
    },
    handleECSelect(val) {
      console.log('%c=>(create.vue:2271) val', 'font-size: 18px;color: #FF7043 ;', val);
      this.formData.outside_id = val;
      let params = {
        type: 'OPC_OUTSIDE',
        outside_id: val
      };
      this.getCommonAddress(params);
      this.resetGoodsList();
      // 获取钱包信息
      this.getMainWallet(val, 'OUTSIDE');
    },
    // 处理用户数据
    handleCreateSuccess(data) {
      console.log('%c=>(create.vue:2220) data', 'font-size: 18px;color: #FF7043 ;', data);
      this.ECList = [data];
      this.handleECSelect(data.id);
    },
    createExtCompany() {
      console.log('开始创建外部公司');
      this.$refs.ecSelect.blur();
      this.$nextTick(() => {
        this.createExternalVisible = true;
      });
    },
    // 获取地址
    getCommonAddress(params = {}) {
      this.$api.getAdditionCreatInfo(params).then(res => {
        this.formData.consignee_info = res.consignee_info;
        // 当切换label,如果毒药的收件人有信息则校验
        if (this.formData.consignee_info.mobile) {
          this.$refs.purFormData.validateField('consignee_info.consignee');
          this.$refs.purFormData.validateField('consignee_info.mobile');
        }
        if (res.consignee_info.county.code) {
          this.selectedAddress = [
            res.consignee_info.prov.code,
            res.consignee_info.city.code,
            res.consignee_info.county.code
          ];
          return;
        }
        if (res.consignee_info.city.code) {
          this.selectedAddress = [res.consignee_info.prov.code, res.consignee_info.city.code];
          return;
        }
        this.selectedAddress = [];
      });
    },

    getUseVipList(params = {}) {
      this.$api.getUseVipList(params).then(res => {
        this.vipList = res?.list || [];
        if (params.id) {
          this.personal_info = this.vipList.find(item => item.id == params.id);
        }
      });
    },

    handleChangeVip(val) {
      console.log('%c [ val ]-1343', 'font-size:13px; background:#5473e8; color:#98b7ff;', val);
      this.personal_info = this.vipList.find(item => item.id == val);
      console.log(
        '%c [  this.personal_info ]-1345',
        'font-size:13px; background:#8612b1; color:#ca56f5;',
        this.personal_info
      );
      this.getCommonAddress({
        type: 'OPC_OUTSIDE',
        vip_id: val
      });
    },

    selectClinic(data, isEcho) {
      const { id: clinic_id } = data;
      this.clinicWallet = {
        ...init_wallet_info
      };
      this.clinic_type_text = data.clinic_type_text;
      let type = this.formData.type;

      if (clinic_id || type) {
        this.chooseClinic = true;
        let params = {
          clinic_id: type == 'CLI_ADDITION' ? clinic_id : '',
          type: this.formData.type
        };
        this.$api.getAdditionCreatInfo(params).then(res => {
          // todo 暂时注释 随时删
          // 只有当诊所采购时，有钱包信息
          this.select_goods_list.map(item => {
            if (this.clinicWallet.balance <= 0) {
              item.cli_pur_price = Number(item.cli_price);
              item.com_ex_price = Number(item.com_price);
              item.canEdit = false;
            } else {
              item.canEdit = true;
            }
          });

          if (!isEcho) {
            this.resetGoodsList();
            this.formData.consignee_info = res.consignee_info;
            // 当切换label,如果毒药的收件人有信息则校验
            // if ( this.formData.consignee_info.mobile ) {
            //   this.$refs.purFormData.validateField('consignee_info.consignee')
            //   this.$refs.purFormData.validateField('consignee_info.mobile')
            // }
            if (res.consignee_info.county.code) {
              this.selectedAddress = [
                res.consignee_info.prov.code,
                res.consignee_info.city.code,
                res.consignee_info.county.code
              ];
              return;
            }
            if (res.consignee_info.city.code) {
              this.selectedAddress = [res.consignee_info.prov.code, res.consignee_info.city.code];
            } else {
              this.selectedAddress = [];
            }
          }
        });
      }
    },

    //选中商品
    // 如果是己善 就直接全部添加到商品列表
    selectGoods(item, isClear = true) {
      // console.log('-> item', item);
      isClear && this.$refs.clinicSearch.clearSingleSelect();
      this.goodsName = ''; // todo 待注释
      let goods_item = null;
      if (item.tag) {
        goods_item = JSON.parse(item.tag);
      } else {
        goods_item = JSON.parse(item.jsonItem);
      }
      const code_list = this.select_goods_list.map(item => item.spu_code);
      if (code_list.includes(goods_item.spu_code)) {
        this.$Message.error(`商品【${goods_item.name}】已存在,请勿重复添加`);
        return;
      }
      goods_item.cli_pur_price =
        goods_item.single_cli_price =
        goods_item.guide_single_cli_price =
          Number(goods_item.cli_price);
      goods_item.com_ex_price =
        goods_item.single_com_price =
        goods_item.guide_single_com_price =
          Number(goods_item.com_price);
      goods_item.personal_pur_price = Number(goods_item.personal_price);
      goods_item.com_amount = 0;
      goods_item.cli_amount = 0;
      goods_item.personal_amount = 0;
      goods_item.pur_num = !isClear ? +goods_item.num : null;
      goods_item.checked_spu_list = [];
      goods_item.canEdit = !!((this.clinicWallet.balance && this.formData.clinic_id) || !this.chooseClinic);
      this.select_goods_list.push(goods_item);
      this.calculateAmount(goods_item);
    },

    //合计
    handleSummary({ columns, data }) {
      const sums = {};
      columns.forEach((column, index) => {
        const key = column.key;
        if (index === 0) {
          sums[key] = {
            key,
            value: '合计'
          };
          return;
        }
        let whiteList = [10, 11, 12];
        if (whiteList.indexOf(index) > -1) {
          const values = data.map(item => Number(item[key]));
          if (!values.every(value => isNaN(value))) {
            const v = values.reduce((prev, curr) => {
              const value = Number(curr);
              if (!isNaN(value)) {
                return S.mathAdd(prev, curr || 0);
              } else {
                return prev;
              }
            }, 0);
            sums[key] = {
              key,
              value: S.mathAdd(v || 0, 0)
            };
          } else {
            sums[key] = {
              key,
              value: '-'
            };
          }
        } else {
          sums[key] = {
            key,
            value: ''
          };
        }
      });
      return sums;
    },

    changeCliPrice(val, index) {
      const types = ['CLI_ADDITION', 'RXJ_ADDITION', 'OPC_PERSONAL', 'OPC_OUTSIDE'];

      if (this.isAdaptPriceCompany === '1' && types.includes(this.formData.type)) {
        this.select_goods_list[index].com_ex_price = val;
      }
      this.select_goods_list[index].cli_amount = $operator.multiply(
        this.select_goods_list[index].pur_num || 0,
        this.select_goods_list[index].cli_pur_price || 0
      );
      this.calculateAmount(this.select_goods_list[index]);
    },

    changeComPrice(val, index) {
      console.log('-> %c val, index  ===    %o', 'font-size: 15px;color: #fa8c16 ;', val, index);

      const types = ['CLI_ADDITION', 'RXJ_ADDITION', 'OPC_PERSONAL', 'OPC_OUTSIDE'];
      if (this.isAdaptPriceCompany === '1' && types.includes(this.formData.type) && val) {
        if (['OPC_PERSONAL', 'OPC_OUTSIDE'].includes(this.formData.type)) {
          this.select_goods_list[index].personal_pur_price = val;
        } else {
          this.select_goods_list[index].cli_pur_price = val;
        }
      }
      if (!val) {
        this.select_goods_list[index].com_ex_price = null;
        this.select_goods_list[index].com_amount = 0;
      } else {
        this.select_goods_list[index].com_amount = $operator.multiply(
          this.select_goods_list[index].pur_num ?? 0,
          this.select_goods_list[index].com_ex_price ?? 0
        );
      }
      if (this.select_goods_list[index].can_edit_price === '1') {
        this.select_goods_list[index].com_price_total = this.select_goods_list[index].com_amount;
      }
      this.calculateAmount(this.select_goods_list[index]);
    },

    changePurNum(e, index) {
      console.log('-> %c e, index  ===    %o', 'font-size: 15px;color: #fa8c16 ;', e, index);
      let editItem = this.select_goods_list[index];
      let pur_num = this.select_goods_list[index].pur_num;
      if (!pur_num) {
        this.select_goods_list[index].pur_num = 0;
        this.calculateAmount(editItem);
        return;
      }
      const buyInfo = editItem.buy_info;
      // num为起购条件
      if (pur_num && pur_num < Number(buyInfo.num)) {
        this.select_goods_list[index].pur_num = Number(buyInfo.num);
        // console.log('-> %c editItem.pur_num  ===    %o', 'font-size: 15px;color: #F56C6C ;', editItem.pur_num);
        // console.log(this.select_goods_list);
        this.calculateAmount(editItem);
        return;
      }
      if (buyInfo.type === 'MUL') {
        if (pur_num % buyInfo.num !== 0) {
          editItem.pur_num = editItem.pur_num - (editItem.pur_num % buyInfo.num);
        }
      }
      this.calculateAmount(editItem);
    },

    calculateAmount(editItem) {
      // console.log('-> %c editItem  ===    %o', 'font-size: 15px;color: #F56C6C ;', editItem);
      if (editItem.can_edit_price === '1') {
        editItem.com_price_total = $operator.multiply(editItem.pur_num || 0, editItem.com_ex_price || 0);
      }
      editItem.cli_amount = $operator.multiply(editItem.pur_num || 0, editItem.cli_pur_price || 0);
      editItem.com_amount = $operator.multiply(editItem.pur_num || 0, editItem.com_ex_price || 0);
      editItem.personal_amount = $operator.multiply(editItem.pur_num || 0, editItem.personal_pur_price || 0);
    },

    //设置规格
    setBatch(row, index) {
      this.editIndex = index;
      this.editRow = row;
      this.set_spu_list = row.spu_spec_list.map(item => {
        if (row.checked_spu_list.length) {
          row.checked_spu_list.map(sub => {
            if (sub.spu === item.spu) {
              item.num = sub.num;
            }
          });
        }
        return { ...item, num: Number(item.num) || 0 };
      });
      this.batchVisible = true;
    },

    //设置某一件商品的spu_list
    setSpu({ unit_amount, specs, checked_spu_list }) {
      this.select_goods_list[this.editIndex].specsObj = specs;
      this.select_goods_list[this.editIndex].checked_spu_list = checked_spu_list;

      this.select_goods_list[this.editIndex].pur_num = unit_amount;
      this.select_goods_list[this.editIndex].cli_amount = $operator.multiply(
        unit_amount,
        this.select_goods_list[this.editIndex].cli_pur_price
      );
      this.select_goods_list[this.editIndex].personal_amount = $operator.multiply(
        unit_amount,
        this.select_goods_list[this.editIndex].personal_pur_price
      );
      this.select_goods_list[this.editIndex].com_amount = $operator.multiply(
        unit_amount,
        this.select_goods_list[this.editIndex].com_ex_price
      );
    },

    //远程搜索商品
    searchSpuList: debounce(function (keyword) {
      let params = {
        keyword: keyword,
        ent_type: this.formData.type === 'RXJ_ADDITION' ? 'RXJ' : '',
        pur_type: this.formData.type,
        outside_id: this.formData.outside_id
      };
      this.$api.getSpuList(params).then(res => {
        res.list.map(item => {
          item.jsonItem = JSON.stringify(item);
        });
        this.goodsList = res.list;
      });
    }, 600),

    // 统一获取预付款余额数据 todo 不知道是否需要抛出，是否抛出更好？
    getMainWallet(ent_id, ent_type = 'COM') {
      this.$api.getMainWallet({ ent_id, ent_type }).then(res => {
        console.log('%c=>(create.vue:2229) res', 'font-size: 18px;color: #FF7043 ;', res);
        const walletMap = {
          COM: 'companyWallet',
          CLI: 'clinicWallet',
          RXJ: 'rxjWallet',
          PARTNER: 'partnerWallet',
          PERSON_C: 'personalWallet', // 个人
          OUTSIDE: 'outsideWallet' // 外部公司
        };
        this[walletMap[ent_type]] = res;
      });
    },

    // todo 将来可能会移除此逻辑(非本期拆分todo)
    resetGoodsList() {
      this.select_goods_list = [];
      this.searchSpuList();
    },

    regionChange(address) {
      if (address.length) {
        const province = {
          name: CodeToText[address[0]],
          code: address[0]
        };
        const city = {
          name: CodeToText[address[1]],
          code: address[1]
        };
        const county = {
          name: CodeToText[address[2]],
          code: address[2]
        };
        this.formData.consignee_info.prov = province;
        this.formData.consignee_info.city = city;
        this.formData.consignee_info.county = county.code ? county : {};
      } else {
        this.formData.consignee_info.prov = {
          code: '',
          name: ''
        };
        this.formData.consignee_info.city = {
          code: '',
          name: ''
        };
        this.formData.consignee_info.county = {
          code: '',
          name: ''
        };
      }
      this.$refs.purFormData.validateField('consignee_info.prov.name');
    },
    // 提交(由父组件调用)
    validateForm() {
      console.log('=>(ClinicPurchase.vue:875) 子组件校验');
      this.$refs.purFormData.validate(valid => {
        if (valid) {
          if (!this.select_goods_list.length) {
            this.$Message.error('请先选择要采购的商品');
            return;
          }

          if (!this.handleSpecMap()) {
            return;
          }
          // 区分编辑还是回显
          if (this.$route.query.pur_code) {
            this.formData.pur_code = this.$route.query.pur_code;
          }

          console.log('=>(ClinicPurchase.vue:908) 继续执行');

          let cloneFormData = this._.cloneDeep(this.formData);
          this.$delete(cloneFormData, 'pay_info');
          let items = this.handleParamsItems();
          let params = {
            type: this.sceneType,
            ...cloneFormData,
            ...this.handleParamsPayInfo(),
            ...items
          };
          this.$emit('handleSubmitLoading', true);
          this.$api
            .saveAdditionPur(params)
            .then(
              res => {
                let currentInfo = this.purchase_scene_lists.filter(item => item.kw === this.formData.type);
                let title = currentInfo[0].label;
                this.$Message.success(`${this.$route.query.pur_code ? `编辑${title}单成功` : `创建${title}单成功`}`);
                this.$router.replace('/purchase/order/list');
              },
              err => this.$Message.error(err.errmsg)
            )
            .finally(() => {
              this.$emit('handleSubmitLoading', false);
            });
        } else {
          this.$Message.error('请完善表单信息');
        }
      });
    },
    // 校验商品规格选取，并处理上传格式
    handleSpecMap() {
      let unsetSkuProd = [];
      for (const item of this.select_goods_list) {
        if (Number(item.pur_num) <= 0) {
          this.$Message.error(`商品${item.name}数量不能为0`);
          return false; // 返回false表示校验失败
        }
        if (item.spu_spec_list.length && !item.checked_spu_list.length) {
          unsetSkuProd.push(item.name);
        }
      }
      if (unsetSkuProd.length) {
        this.$Message.error({
          content: `商品${unsetSkuProd.join('、')}未设置规格`,
          duration: 3
        });
        return false; // 返回false表示校验失败
      }
      let temObj = this.select_goods_list.filter(item => item.checked_spu_list.length);
      console.log('%c=>(create.vue:1886) temObj', 'font-size: 18px;color: #FF7043 ;', temObj);
      let spec_map = {};
      temObj.map(item => {
        spec_map[item.spu_code] = item.specsObj;
      });
      this.formData.spec_map = spec_map;
      return true; // 返回true表示校验通过
    },
    // 处理item得数据格式
    handleParamsItems() {
      return {
        items: this.select_goods_list.map(item => {
          let paramsObj = {
            spu_code: item.spu_code,
            num: item.pur_num,
            personal_price: item.personal_amount // 传个人采购合计
          };
          if (item.can_edit_price === '1') {
            paramsObj.com_price_total = item.com_price_total;
          } else {
            paramsObj.com_price = item.com_ex_price;
          }
          return paramsObj;
        })
      };
    },
    // 编辑回显
    handleEchoData(data) {
      console.log('%c=>(create.vue:1461) data', 'font-size: 18px;color: #FF7043 ;', data);
      this.formData.consignee_info = data.consignee_info;
      this.clinic_name = data.clinic_name;
      this.pur_main_name = data.pur_main_name;
      this.formData.clinic_id = data.clinic_id;
      this.formData.rxj_code = data.rxj_code;
      this.formData.remark = data.remark;
      this.formData.attach_images = data.attach_images || [];
      this.formData.vip_id = data.vip_id;
      if (data.vip_id && data.vip_id !== '0') {
        this.getUseVipList({ id: data.vip_id });
      }
      this.ECList = [
        {
          id: data.outside_id,
          name: data.pur_main_name
        }
      ];
      this.formData.outside_id = data.outside_id;

      this.formData.person_id = data.person_id;
      if (data.person_id !== '0') {
        this.personal_info.id = data.person_id;
        this.personal_info.mobile = data.person_mobile;
        this.$nextTick(() => {
          this.$refs.personalRef.localEcho({
            name: data.pur_main_name,
            id: data.person_id
          });
        });
      }

      let resList = data.items.map(item => {
        let specsObj = {};
        if (item.specs.length) {
          item.specs.map(sub => {
            specsObj[sub.spu_code] = sub.num;
          });
          item.specsObj = specsObj;
        }
        item.checked_spu_list = item.spu_spec_list.filter(item => Number(item.num) > 0);
        return {
          ...item,
          cli_price: +item.guide_single_cli_price,
          cli_pur_price: +item.single_cli_price, //编辑的诊所价格
          com_price: +item.guide_single_com_price,
          com_ex_price: +item.single_com_price, //编辑的直营运营中心价格
          personal_price: +item.guide_single_personal_price,
          personal_pur_price: +item.single_personal_price, //编辑得个人价格
          num: +item.base_num,
          pur_num: +item.num,
          cli_amount: item.cli_price,
          com_amount: item.com_price,
          personal_amount: Number(item.personal_price)
        };
      });
      if (this.formData.type !== 'P_MEETING') {
        if (data.consignee_info.county.code) {
          this.selectedAddress = [
            data.consignee_info.prov.code,
            data.consignee_info.city.code,
            data.consignee_info.county.code
          ];
        } else {
          this.selectedAddress = [data.consignee_info.prov.code, data.consignee_info.city.code];
        }
        this.select_goods_list = resList;
      } else {
        this.meeting_goods_list = resList;
      }
      this.formData.pay_info.clinic.voucher_imgs = data.pay_info.clinic.imgs;
      this.formData.pay_info.company.voucher_imgs = data.pay_info.company.imgs;
      this.formData.pay_info.personal.voucher_imgs = data.pay_info.personal.imgs;
      this.formData.pay_info.rxj.voucher_imgs = data.pay_info.rxj.imgs;
      console.log('%c [ data.type ]-1637', 'font-size:13px; background:#e07bf6; color:#ffbfff;', data.type);
      this.getMainWallet(data.outside_id, 'OUTSIDE');
      this.formData.promotion_id = data.promotion.id;
      this.formData.meeting_user_list = data.promotion.meeting_user_list;
      this.formData.meeting_user_list.forEach(item => {
        item.companyList = [{ id: item.company_id, name: item.company_name }];
      });
    },
    //删除商品
    deleteGoods(index) {
      this.select_goods_list.splice(index, 1);
    },

    handleParamsPayInfo() {
      return {
        pay_info: {
          personal: {
            // voucher_imgs: this.formData.pay_info.personal.voucher_imgs,
            // offline_amount: this.getOfflinePayments('per'),
            wallet_amount: this.getPrepayments('per')
          },
          company: {
            wallet_amount: this.getPrepayments('com')
            // voucher_imgs: this.formData.pay_info.company.voucher_imgs,
            // offline_amount: this.getOfflinePayments('com'),
          }
        }
      };
    },

    getPurchaseOptions() {
      this.$api
        .getPurchaseOptions()
        .then(res => {
          this.withVipOutsideIds = res?.withVipOutsideIds || [];
        })
        .catch(err => this.$Message.error(err.errmsg));
    },
    changePersonalPrice(val, index) {
      if (this.isAdaptPriceCompany === '1' && val) {
        this.select_goods_list[index].com_ex_price = val;
      }
      this.select_goods_list[index].personal_amount = $operator.multiply(
        this.select_goods_list[index].pur_num || 0,
        this.select_goods_list[index].personal_pur_price
      );
      this.calculateAmount(this.select_goods_list[index]);
    },
    changeComAmount(val, index) {
      this.select_goods_list[index].com_amount = this.select_goods_list[index].com_price_total = val;
      if (!val || !this.select_goods_list[index].pur_num) {
        this.select_goods_list[index].com_ex_price = 0;
      } else {
        this.select_goods_list[index].com_ex_price = $operator.divide(val, this.select_goods_list[index].pur_num, 4);
      }
    }
  }
};
</script>

<style lang="less" scoped>
.com-pay-info {
  background: #f8f9f9;
  padding: 0 12px;
  border-radius: 4px;
  line-height: normal;

  .price {
    color: #fb9a49;
    margin: 0 12px 0 12px;
  }

  .pay-item {
    display: flex;
    padding: 12px 0;

    //&:first-of-type {
    //  border-bottom: 1px solid #e5e5e5;
    //}

    //&:last-of-type {
    //  padding-bottom: 0;
    //}
  }

  .pay-label {
    width: 90px;
  }
}

.create-option {
  padding: 12px;
}
</style>
