<template>
  <Modal
    ref="customModal"
    :value="value"
    width="600px"
    :title="title"
    :footer-hide="false"
    :mask-closable="false"
    :lock-scroll="true"
    @on-visible-change="changeVisible"
  >
    <div class="content">
      <Form ref="personalRef" :model="formData" :rules="rules" :label-width="100" :label-colon="true">
        <FormItem label="个人客户名称" prop="name">
          <Input v-model="formData.name" :maxlength="30" show-word-limit placeholder="输入外部公司名称"></Input>
        </FormItem>

        <FormItem label="手机号" prop="mobile">
          <Input type="number" v-model="formData.mobile" placeholder="输入手机号"></Input>
        </FormItem>

        <FormItem label="个人客户类型" prop="custom_type" >
          <CheckboxGroup v-model="formData.custom_type" class="flex">
            <div class="flex flex-item-align" v-for="(item, index) in customTypeOptions" :key="index">
              <Checkbox :label="item.id" disabled>{{ item.desc }}</Checkbox>
            </div>
          </CheckboxGroup>
        </FormItem>

        <FormItem label="备注" prop="remark">
          <Input v-model="formData.remark" :maxlength="300" show-word-limit placeholder="请输入备注" type="textarea" :autosize="{minRows: 5,maxRows: 5}"></Input>
        </FormItem>

      </Form>
    </div>
    <div slot="footer">
      <Button @click="closeModal">取消</Button>
      <Button :loading="confirmLoading" type="primary" @click="confirm">确定</Button>
    </div>
  </Modal>
</template>

<script>
import S from 'utils/util';
import {cloneDeep} from 'lodash'
export default {
  name: 'personalCreate',
  mixins: [],

  components: {},
  props: {
    value: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      default: '创建个人客户'
    },
    personName: {
      type: String,
      default: ''
    },
  },

  data() {
    const validateMobile = (rule, value, callback) => {
      if (value === '') {
        callback(new Error('请输入正确的手机号码'))
      } else {
        const reg = /^1[3456789]\d{9}$/
        if (!reg.test(value)) {
          callback(new Error('请输入正确的手机号码'))
        }
        callback()
      }
    }

    return {
      confirmLoading: false,
      formData: {
        name: '',
        mobile: '',
        custom_type: ['PURCHASE'],
        remark: ''
      },
      rules: {
        name: [
          { required: true, message: '请输入个人客户名称', trigger: 'change' },
        ],
        mobile: [
          { required: true, message: '请输入手机号', trigger: 'change'},
          { required: true, validator: validateMobile }
        ],
        custom_type: [{ required: true, type: 'array', message: '请选择个人客户类型', trigger: 'change' }],
      },
      customTypeOptions: [],
    };
  },

  computed: {
  },

  watch: {},

  created() {},

  mounted() {},

  destroyed() {},

  methods: {
    changeVisible(visible) {
      if (visible) {
        this.getPCOptions()
        this.formData.name = cloneDeep(this.personName)
      }else{
        this.closeModal();
      }
    },

    clearData() {
      this.$refs.personalRef.resetFields()
    },

    closeModal() {
      this.clearData()
      this.$emit('close')
      this.$emit('input', false);
    },

    confirm() {
      this.$refs.personalRef.validate(valid => {
        if (valid) {
          this.submitLoading = true;
          this.$api.getPCEdit(this.formData).then(res => {
            this.$Message.success('创建个人客户成功');
            this.$emit('success', { name: this.formData.name, id: res.id });
            this.closeModal()
          }, rej => {
            this.$Message.error(rej.errmsg)
            this.submitLoading = false
          }).catch(() => {
            this.submitLoading = false
          })
        }
      });
    },

    getPCOptions() {
      this.$api.getPCOptions().then(
        res => {
          this.customTypeOptions = S.descToArrHandle(res.customTypeDesc || {})
        },
        err => this.$Message.error(err.errmsg)
      );
    },
  }
};
</script>

<style scoped lang="less">
::v-deep .radio-item {
  margin-top: -10px;
  margin-bottom: 10px;
  .ivu-form-item-error-tip {
    padding-top: 0px;
    margin-top: -4px;
  }
}
</style>

<style scoped lang="less">
.tip {
  color: #999;
}
::v-deep .ivu-modal-body {
  max-height: 500px;
  min-height: 300px;
  overflow-y: auto;
}
</style>
