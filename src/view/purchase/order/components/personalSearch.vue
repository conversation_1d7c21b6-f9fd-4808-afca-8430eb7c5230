<template>
  <div>
    <el-select
      size="small"
      v-model="personal_id"
      filterable
      clearable
      remote
      ref="pcSelect"
      style="width: 100%"
      reserve-keyword
      placeholder="请输入个人名称"
      @clear="clearPersion"
      @change="handlepcSelect"
      :remote-method="searchPerson"
    >
      <el-option v-for="item in personal_list" :key="item.id" :value="item.id" :label="item.name">
        {{ item.name }}
      </el-option>

      <template slot="empty">
        <div class="flex flex-item-between flex-item-align create-option" @click="createPerson">
          <p class="flex flex-c">
            <span style="font-size: 16px">{{ personName }}</span>
            <span class="tip">尚无该个人客户</span>
          </p>
          <a>创建个人客户</a>
        </div>
      </template>
    </el-select>
    <person-create v-model="personalCreateVisible" @success="createSuccess" @close="close" :personName="personName"></person-create>
  </div>
</template>

<script>
import {debounce} from 'lodash'
import personCreate from './personCreate.vue'
export default {
  name: 'personalSearch',
  components: {
    personCreate
  },
  mixins: [],
  data() {
    return {
      personal_id: '',
      personName: '',
      personal_list: [],
      personalCreateVisible: false,
    };
  },
  computed: {},
  watch: {},
  created() {},
  mounted() {},
  methods: {
    close () {
      this.searchPerson()
    },
    localEcho (info) {
      this.personal_list = [info]
      this.personal_id = info.id
    },
     createSuccess (info) {
       this.searchMethods(info.name, info.id)
    },
    clearPersion() {
      // 清除地址信息
      this.$emit('clearAddressInfo')
      // this.clearAddressInfo()
      // 默认搜索信息
      this.searchPerson('')
      // this.resetGoodsList()
      this.$emit('resetGoodsList')
    },
    // 个人不做地址回显示
    handlepcSelect(val) {
      this.personal_id = val
      let current_item = this.personal_list.filter(item => item.id === this.personal_id)
      this.$emit('getPersonalInfo', current_item[0] || {})
    },
    searchPerson: debounce(function (name, id) {
      console.log('searchPersonsearchPerson', name, id)
      this.searchMethods(name,id)
    }, 400),
    searchMethods (name, id) {
      this.personName = name
      let params = {
        name,
        id,
        status: 'ENABLED',
        type: 'PERSON'
      }
      this.$api.getPCList(params).then(
        res => {
          // 获取用户数据
          this.personal_list = res.list
          console.log('id', id)
          if (id) {
            this.handlepcSelect(id, true)
          }
        },
        rej => this.$Message.error(rej.errmsg)
      )
    },
    createPerson() {
      this.$refs.pcSelect.blur()
      this.$nextTick(() => {
        this.personalCreateVisible = true
      })
    },
  },
};
</script>

<style lang="less" scoped>
.create-option {
  padding: 12px;
}

::v-deep .el-input__inner {
  border-radius: 0px;
  border-color: #bbb;
  font-size: 12px;
  padding: 0 24px 0 8px;
}
</style>
