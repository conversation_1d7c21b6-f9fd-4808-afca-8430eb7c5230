<template>
	<Modal
			:mask-closable="false"
			:value="showModal"
			title="修改诊所采购单"
			width="800px"
			@on-visible-change="closeModal"
			@on-ok="submitForm('purchaseModal')">
		<div slot="footer" class="modal-foot">
			<Button @click="closeModal(false)">取消</Button>
			<Button type="primary" @click="submitForm('purchaseModal')">确定</Button>
		</div>
		<div class="purchase-modal-wrapper">
			<div class="modal-header flex flex-item-between flex-item-align">
				<span>发货明细：</span>
				<Select v-model="select_good" placeholder="请选择商品" style="width:200px" @on-select="selectGoods">
					<Option v-for="(item,index) in goods_items" :key="index" :value="item.spu_code">{{ item.name }}</Option>
				</Select>
			</div>
			<div class="table-wrapper">
				<Table :columns="tableColumns_order" :data="select_goods_list" :summary-method="handleSummary" border
				       show-summary>
					<template slot="unit" slot-scope="{row,index}">
						<InputNumber v-model="select_goods_list[index].unit" :min="0" :precision="0" placeholder="数量"
						             @on-change="(e)=>changeNum(e,index)"/>
					</template>
					<template slot="unbuilt_singular" slot-scope="{row,index}">
						{{row.wait_ship_num}}
					</template>
					<template slot="action" slot-scope="{row,index}">
						<a style="color:red" @click="deleteGood(index)">删除</a>
					</template>
				</Table>
			</div>
			<Form ref="purchaseModal" :label-width="100" :model="formData" :rules="ruleValidate" label-colon>
				<div>
					<Row>
						<Col span="18">
							<FormItem label="诊所" prop="generic_name">
								<Input v-model="formData.clinic_name"/>
							</FormItem>
						</Col>
						<Col span="18">
							<FormItem label="收件人姓名" prop="consignee_info.consignee">
								<Input v-model="formData.consignee_info.consignee" placeholder="收件人姓名"></Input>
							</FormItem>
						</Col>
						<Col span="18">
							<FormItem label="收件人手机号" prop="consignee_info.mobile">
								<Input v-model="formData.consignee_info.mobile" placeholder="收件人手机号"></Input>
							</FormItem>
						</Col>
						<Col span="18">
							<FormItem label="诊所地址" prop="bar_code" >
								<div class="addWrap">
									<div class="addressBox" style="width: 55%;">
<!--										<v-region v-model="selectedAddress" @values="regionChange"></v-region>-->
										<el-cascader
												v-model="selectedAddress"
												:options="options"
												clearable
												placeholder="请选择收件地址"
												size="small"
                        popper-class="address-com"
												style="width: 100%;"
												@change="regionChange">
										</el-cascader>
									</div>
									<div class="addressInput ml10">
										<Input v-model="formData.consignee_info.detail" placeholder="详细地址"></Input>
									</div>
								</div>
							</FormItem>
						</Col>
						<Col span="18">
							<FormItem label="备注" prop="phonetic_code">
								<Input v-model="formData.remark" autosize type="textarea"/>
							</FormItem>
						</Col>
					</Row>
				</div>
			</Form>
		</div>
	</Modal>
</template>

<script>
import S from 'utils/util'
import {CodeToText, regionData} from '@/utils/chinaMap'
export default {
	name: 'purchase-modal',
	props: {
		showModal: {
			type: Boolean,
			default: false,
		},
		orderInfo: {
			type: Object,
			default: ()=>{},
		},
		goods_items: {
			type: Array,
			default:()=>[]
		},
		pack_order_code:{
			type: String,
			default: ''
		}
	},
	data() {
		const init_form_data = {
			pack_order_code: '',
			pur_code: this.$route.query.pur_code,
			clinic_name: '',
			consignee_info: {
				city: {
					code: '',
					name: '',
				},
				county: {
					code: '',
					name: '',
				},
				prov: {
					code: '',
					name: '',
				},
				mobile: '',
				consignee: '',
				detail: '',
			},
			remark: '',
			item_list: [],
		}
		const validateMobile = (rule, value, callback) => {
			if (value === '') {
				callback(new Error('请输入正确的手机号码'))
			} else {
				const reg = /^1[3456789]\d{9}$/
				if (!reg.test(value)) {
					callback(new Error('请输入正确的手机号码'))
				}
				callback()
			}
		}
		return {
			options: regionData,
			select_good: '',
			selectedAddress: [],
			formData: {
				...init_form_data,
			},
			tableColumns_order: [
				{title: '商品编号', key: 'spu_code', align: 'center'},
				{title: '商品', key: 'name', align: 'center'},
				{title: '商品类型', key: 'type_text', align: 'center'},
				{title: '待采购数', slot: 'unbuilt_singular', align: 'center'},
				{title: '采购数量', key: 'unit', slot: 'unit', align: 'center'},
				{title: '操作', slot: 'action', align: 'center'},
			],
			ruleValidate: {
				'consignee_info.consignee': [{required: true, message: '请填写收件人人名', trigger: 'blur'}],
				'consignee_info.mobile': [{required: true, trigger: 'blur', validator: validateMobile}],
			},
			select_goods_list: [],
			goods_list: [],
			currentInfo: {},
			clinic_list: [],
			local_items: []
		}
	},
	computed: {},
	watch: {
		orderInfo: {
			handler(val) {
				console.log(S.isEmptyObject(val))
				if (val.items && val.items.length) {
					this.formData.consignee_info = val.consignee_info
					this.currentInfo = val
					this.clinic_list = [val.clinic_info]
					this.formData.remark = val.remark
					this.formData.clinic_name = val.clinic_name
					this.formData.pack_order_code = val.pack_order_code
					this.local_items = val.items
					console.log('-> goods_list', this.goods_list)
					// this.selectedAddress = {
					// 	city: val.consignee_info.city.code,
					// 	province: val.consignee_info.prov.code,
					// 	area: val.consignee_info.county.code,
					// 	town: null,
					// }
          if(val.consignee_info.length){
            if(val.consignee_info.county.code){
              this.selectedAddress = [val.consignee_info.prov.code, val.consignee_info.city.code, val.consignee_info.county.code]
            }else {
              this.selectedAddress = [val.consignee_info.prov.code, val.consignee_info.city.code]
            }
          }

				}
			},
		},
	},
	created() {
	},
	mounted() {
	},
	methods: {
		submitForm(name) {
			this.$refs[name].validate((valid) => {
				if (valid) {
					console.log(this.select_goods_list)
					if (!this.select_goods_list.length) {
						this.$Message.error('请选择要发货的商品')
						return
					}
					let isOk = true
					let goods_type = []
					const item_list = this.select_goods_list.map(item => {
						if (item.num <= 0) {
							this.$Message.error(`商品${item.name}数量不能为0`)
							isOk = false
						}
						if (item.unit >(+item.wait_ship_num)) {
							this.$Message.error(`商品${item.name}数量不能大于未建单数`)
							isOk = false
						}
						if(item)
							return {
								spu_code: item.spu_code,
								num: item.unit,
							}
					})
					if (!isOk) return
					this.formData.item_list = item_list
					this.formData.pack_order_code = this.pack_order_code
					this.$api.editPurchaseOrder(this.formData).then(res => {
						console.log(res)
						this.$Message.success('编辑采购单成功')
						this.closeModal(false)
						this.$parent.getDetail()
					}, err => this.$Message.error(err.errmsg))
				} else {
					this.$Message.error('请完善表单信息')
				}
			})
		},
		changeNum(e, index) {
			console.log('-> e', e)
			if (e) {
				this.select_goods_list[index].unit = e
				this.select_goods_list.sort()
			}
		},
		deleteGood(index) {
			console.log('-> index', index)
			this.select_goods_list.splice(index, 1)
		},
		handleSummary({columns, data}) {
			console.log('-> data', data)
			const sums = {}
			columns.forEach((column, index) => {
				const key = column.key
				if (index === 0) {
					sums[key] = {
						key,
						value: '合计',
					}
					return
				}
				if (index === 1 || index === 2 || index === 3) {
					sums[key] = {
						key,
						value: '',
					}
					return
				}
				const values = data.map(item => Number(item[key]))
				if (!values.every(value => isNaN(value))) {
					const v = values.reduce((prev, curr) => {
						const value = Number(curr)
						if (!isNaN(value)) {
							return prev + curr
						} else {
							return prev
						}
					}, 0)
					sums[key] = {
						key,
						value: v,
					}
				} else {
					sums[key] = {
						key,
						value: '',
					}
				}
			})
			return sums
		},
		//地区选择
		regionChange(address) {
			console.log('-> address', address)
			// const {province, city, area} = address
			// console.log(this.formData)
			if (address.length) {
				console.log(CodeToText[address[0]])
				const province = {
					value: CodeToText[address[0]],
					key: address[0],
				}
				const city = {
					value: CodeToText[address[1]],
					key: address[1],
				}
				const area = {
					value: CodeToText[address[2]],
					key: address[2],
				}
				console.log(province, city, area)
				this.formData.prov_name = province.value; this.formData.prov_code = province.key
				this.formData.city_name = city.value; this.formData.city_code = city.key
				this.formData.county_name = area.value, this.formData.county_code = area.key
			}else {
				(this.formData.prov_name = '', this.formData.prov_code = '')
				(this.formData.city_name = '', this.formData.city_code = '')
				(this.formData.county_name = '', this.formData.county_code = '')
			}
			console.log(this.formData)
		},
		selectGoods(val) {
			console.log(val)
			let isExit = this.select_goods_list.findIndex(item => item.spu_code === val.value)
			if (isExit > -1) {
				this.$Message.error('请勿重复添加')
				return
			}
			console.log(this.goods_items)
			const addItem = this.goods_items.filter((item) => item.spu_code === val.value)
			console.log('-> addItem', addItem)
			addItem[0].num = Number(addItem[0].num)
			addItem[0].unit = ''
			addItem[0].wait_ship_num = addItem[0].num - addItem[0].has_purchase_num
			this.select_goods_list = this.select_goods_list.concat(addItem)
			console.log(this.select_goods_list)
		},
		//关闭弹窗
		closeModal(show) {
			console.log('-> show', show)
			this.select_goods_list = []
			this.select_good = ''
			if(show){
				this.select_goods_list = this.local_items.map(item=>{
					item.unit = +item.wait_ship_num
					return {
						...item
					}
				})
			}
			if(!show){
				this.$emit('update:showModal', show)
			}
		},
	},
}
</script>

<style lang="less" scoped>
.table-wrapper {
	padding: 10px 0 30px;
}
</style>
<style lang="less">
.addWrap {
	display: flex;
	align-items: center;
	
	.addressBox {
		div.rg-select div.rg-select__el div.rg-select__content {
			padding: 0 30px 0px 15px !important;
			font-size: 12px;
		}
	}
	
	.addressInput {
		flex: 1;
	}
}
</style>
