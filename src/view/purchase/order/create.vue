<template>
  <div class="wrapper">
    <Form ref="purFormData" :model="formData" :label-width="110" label-colon :rules="ruleValidate">
      <div class="block-header">基本信息</div>

      <Row>
        <Col span="8" class="custom-formItem">
          <FormItem label="采购场景" required prop="type">
            <RadioGroup v-model="formData.type" class="flex" @on-change="purchaseClick">
              <Radio
                v-for="(item, index) in purchase_scene_lists"
                :key="index"
                :disabled="isPurchaseDisabled(item.kw)"
                :label="item.kw"
              >
                {{ item.label }}
              </Radio>
            </RadioGroup>
          </FormItem>
        </Col>
      </Row>

      <component
        :is="getComponentInfo.componentName"
        v-if="formData.type"
        :ref="getComponentInfo.ref"
        :scene-type="formData.type"
        :echo-data="echoData"
        :is-adapt-price-company="isAdaptPriceCompany"
        :company-wallet="companyWallet"
        :purchase_scene_lists="purchase_scene_lists"
        :show-recharge-button="showRechargeButton"
        @handleSubmitLoading="handleSubmitLoading"
      />
    </Form>
    <div class="fixed-bottom-wrapper">
      <Button @click="$router.replace('/purchase/order/list')">取消</Button>
      <Button type="primary" style="margin: 0 20px" :loading="submitLoading" @click="submitForm"> 确定 </Button>
    </div>
  </div>
</template>

<script>
import S from 'utils/util';
import { getUser } from '@/utils/runtime';
import util from '../../../utils/util';

const init_wallet_info = {
  balance: 0,
  id: '',
  ent_type: '',
  ent_id: ''
};
export default {
  name: 'Create',
  components: {
    ClinicPurchase: () => import('./components/createScene/ClinicPurchase.vue'),
    CompanyOwn: () => import('./components/createScene/CompanyOwn.vue'),
    CompanyPersonal: () => import('./components/createScene/CompanyPersonal.vue'),
    CompanyMeeting: () => import('./components/createScene/CompanyMeeting.vue'),
    CompanyOutside: () => import('./components/createScene/CompanyOutside.vue')
  },
  mixins: [],
  props: {},
  data() {
    return {
      purchase_scene_lists: [
        { label: '诊所采购', kw: 'CLI_ADDITION', componentName: 'ClinicPurchase', ref: 'CLI_ADDITION' },
        { label: '直营运营中心采购自用', kw: 'OPC_OWN', componentName: 'CompanyOwn', ref: 'OPC_OWN' },
        {
          label: '直营运营中心对个人销售(含秒杀)',
          kw: 'OPC_PERSONAL',
          componentName: 'CompanyPersonal',
          ref: 'OPC_PERSONAL'
        },
        { label: '直营运营中心会议门票', kw: 'P_MEETING', componentName: 'CompanyMeeting', ref: 'P_MEETING' },
        { label: '直营运营中心对外部公司销售', kw: 'OPC_OUTSIDE', componentName: 'CompanyOutside', ref: 'OPC_OUTSIDE' }
      ],
      submitLoading: false,
      formData: {
        type: '' // 采购场景
      },
      clinicWallet: {
        ...init_wallet_info
      },
      rxjWallet: {
        ...init_wallet_info
      },
      companyWallet: {
        ...init_wallet_info
      },
      partnerWallet: {
        ...init_wallet_info
      },
      personalWallet: {
        ...init_wallet_info
      },
      outsideWallet: {
        ...init_wallet_info
      },
      ruleValidate: {},
      isRsjCompany: 0,
      isAdaptPriceCompany: '0',

      echoData: {}
    };
  },
  computed: {
    showRechargeButton() {
      return path => {
        return util.rootPageCheck(path);
      };
    },

    // todo 拆分方法
    getComponentInfo() {
      return this.formData.type ? this.purchase_scene_lists.find(item => item.kw === this.formData.type) : {};
    },
    isPurchaseDisabled() {
      return kw => {
        return !!this.$route.query.pur_code || (this.isRsjCompany == 1 && (kw === 'OPC_OWN' || kw === 'P_MEETING'));
      };
    }
  },
  watch: {},

  created() {
    this.getPurchaseOptions();
    const { opc_id } = getUser();
    this.getMainWallet(opc_id, 'OPC');
  },
  mounted() {
    if (this.$route.query.pur_code) {
      this.$api.getPurchaseInfo({ pur_code: this.$route.query.pur_code }).then(({ info }) => {
        console.log('%c=>(create.vue:1371) info', 'font-size: 18px;color: #FF7043 ;', info);
        this.formData.type = info.type;
        this.echoData = info; // 子组件中监听echoData，做回显处理
      });
    } else {
      this.formData.type = 'CLI_ADDITION';
    }
  },

  methods: {
    purchaseClick(val) {
      console.log('%c=>(create.vue:1387) val', 'font-size: 18px;color: #FF7043 ;', val);
      this.formData.type = val;
    },

    //提交表单
    submitForm() {
      this.$refs[this.formData.type].validateForm();
    },

    // 统一获取预付款余额数据
    getMainWallet(ent_id, ent_type = 'OPC') {
      this.$api.getMainWallet({ ent_id, ent_type }).then(res => {
        console.log('%c=>(create.vue:2229) res', 'font-size: 18px;color: #FF7043 ;', res);
        const walletMap = {
          OPC: 'companyWallet',
          CLI: 'clinicWallet',
          RXJ: 'rxjWallet',
          PARTNER: 'partnerWallet',
          PERSON_C: 'personalWallet', // 个人
          OUTSIDE: 'outsideWallet' // 外部公司
        };
        this[walletMap[ent_type]] = res;
      });
    },

    getPurchaseOptions() {
      this.$api
        .getPurchaseOptions()
        .then(res => {
          this.isAdaptPriceCompany = res?.isAdaptPriceCompany || '0';
          this.isRsjCompany = res.isRsjCompany;
        })
        .catch(err => this.$Message.error(err.errmsg));
    },

    handleSubmitLoading(val) {
      this.submitLoading = val;
    }
  }
};
</script>
<style lang="less" scoped>
.wrapper {
  margin-bottom: 30px;
}

::v-deep {
  .ivu-table-cell {
    line-height: normal;
  }
}

::v-deep .custom-formItem {
  .ivu-form-item-content {
    width: 100%;
  }
}
</style>
