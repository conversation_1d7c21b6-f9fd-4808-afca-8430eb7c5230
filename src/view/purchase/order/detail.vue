<template>
  <div class="detail-wrapper">
    <h3 class="f-title">基础信息</h3>
    <div class="basic-info">
      <div class="basic-info-item" style="justify-content: center; background: #cccccc">采购单信息</div>
      <div class="basic-info-item">
        <span class="item-label">采购单号</span>
        <span class="item-content">{{ echoData.code }}</span>
      </div>

      <div class="basic-info-item">
        <span class="item-label">采购单状态</span>
        <span class="item-content"
          >{{ echoData.status_text }}
          <span style="color: red; margin-left: 6px" v-if="echoData.status_text === '已驳回'"
            >( 驳回原因：{{ echoData.reject_reason }} )</span
          >
        </span>
      </div>

      <div class="basic-info-item">
        <span class="item-label">采购主体</span>
        <span class="item-content">{{ echoData.pur_main_name }}</span>
      </div>
      <div class="basic-info-item" v-if="getCodeLabel">
        <span class="item-label">{{ getCodeLabel }}</span>
        <span class="item-content">{{ echoData.ent_code || '-' }}</span>
      </div>

      <div class="basic-info-item">
        <span class="item-label">主体类型</span>
        <span class="item-content">{{ echoData.ent_type_text || '-' }}</span>
      </div>

      <div class="basic-info-item" v-if="echoData.ent_type === 'CLI'">
        <span class="item-label">诊所类型</span>
        <span class="item-content">
          <span v-if="echoData.clinic_direct_type_text && echoData.clinic_type_text">
            {{ echoData.clinic_direct_type_text }} / {{ echoData.clinic_type_text }}
          </span>
          <span v-else>-</span>
        </span>
      </div>
      <div class="basic-info-item">
        <span class="item-label">营业执照名称</span>
        <span class="item-content">{{ orderInfo.organization_name || '-' }}</span>
      </div>
      <div v-if="orderInfo.type === 'OPC_PERSONAL' && orderInfo.ent_type === 'PERSON_C'" class="basic-info-item">
        <span class="item-label">会员名称</span>
        <span class="item-content">{{ orderInfo.vip_type_desc?.join('；') || '-' }}</span>
      </div>
      <div class="basic-info-item">
        <span class="item-label">采购单类型</span>
        <span class="item-content">{{ echoData.type_text }}</span>
      </div>
      <div class="basic-info-item" v-if="type === 'P_MEETING'">
        <span class="item-label">关联活动名称</span>
        <span class="item-content">{{ promotion.name }}</span>
      </div>
      <div class="basic-info-item" v-if="type === 'P_MEETING'">
        <span class="item-label flex flex-item-center">参会人</span>
        <span class="item-content">
          <div v-for="(item, index) in promotion.meeting_user_list" :key="index">
            参会人{{ index + 1 }}: {{ item.name }}({{ item.sex_text }}) {{ item.mobile }} {{ item.opc_name }}
          </div>
        </span>
      </div>

      <div class="basic-info-item" v-if="type !== 'P_MEETING'">
        <span class="item-label">收件人姓名</span>
        <span class="item-content">{{ echoData.consignee || '-' }}</span>
      </div>
      <div class="basic-info-item" v-if="type !== 'P_MEETING'">
        <span class="item-label">收件人手机号</span>
        <span class="item-content">{{ echoData.mobile || '-' }}</span>
      </div>
      <div class="basic-info-item" v-if="type !== 'P_MEETING'">
        <span class="item-label">收货地址</span>
        <span class="item-content">{{ echoData.address || '-' }}</span>
      </div>
      <div class="basic-info-item">
        <span class="item-label">下单时间</span>
        <span class="item-content">{{ echoData.create_time }}</span>
      </div>
      <div class="basic-info-item" v-if="echoData.status === 'WAIT_SHIP'">
        <span class="item-label">付款单号</span>
        <span class="item-content">{{ echoData.pack_order_code }}</span>
      </div>

      <div class="basic-info-item" v-if="type === 'OPC_PERSONAL' || type === 'OPC_OUTSIDE'">
        <span class="item-label">{{ type === 'OPC_OUTSIDE' ? '外部公司' : '个人' }}支付金额</span>
        <span class="item-content">￥{{ echoData.personal_price }}</span>
      </div>
      <div class="basic-info-item" v-if="type === 'OPC_PERSONAL' || type === 'OPC_OUTSIDE'">
        <span class="item-label flex flex-item-center">{{ type === 'OPC_OUTSIDE' ? '外部公司' : '个人' }}支付方式</span>
        <span
          class="item-content"
          v-if="echoData.pay_info && echoData.pay_info.personal && echoData.pay_info.personal.desc_list.length"
        >
          <span v-for="(item, ind) in echoData.pay_info.personal.desc_list" :key="ind">{{ item + '&nbsp;' }}</span>
          <br />
          <viewer
            v-if="echoData.pay_info.personal.imgs.length"
            :images="echoData.pay_info.personal.imgs"
            class="flex flex-item-align"
            style="margin: 10px 10px 10px 30px"
          >
            <img
              style="width: 45px; height: auto; margin-right: 10px"
              v-for="(item, index) in echoData.pay_info.personal.imgs"
              :key="index"
              :src="item | imageStyle"
              alt="支付凭证"
              class="image cursor scale"
            />
          </viewer>
        </span>
        <span class="item-content flex flex-item-align" v-else>-</span>
      </div>

      <div
        class="basic-info-item"
        v-if="echoData.is_opc_pay !== '1' && (type === 'CLI_ADDITION' || type === 'CLI_OPENING')"
      >
        <span class="item-label">诊所支付金额</span>
        <span class="item-content">￥{{ echoData.cli_price }}</span>
      </div>

      <div class="basic-info-item" v-else>
        <span class="item-label">直营运营中心支付金额</span>
        <span class="item-content">￥{{ echoData.com_price }}</span>
      </div>
      <div
        class="basic-info-item"
        v-if="echoData.is_opc_pay !== '1' && (type === 'CLI_ADDITION' || type === 'CLI_OPENING')"
      >
        <span class="item-label flex flex-item-center">诊所支付方式</span>
        <span
          class="item-content"
          v-if="echoData.pay_info && echoData.pay_info.clinic && echoData.pay_info.clinic.desc_list.length"
        >
          <span v-for="(item, ind) in echoData.pay_info.clinic.desc_list" :key="ind">{{ item + '&nbsp;' }}</span>
          <br />
          <viewer
            v-if="echoData.pay_info.clinic.imgs.length"
            :images="echoData.pay_info.clinic.imgs"
            class="flex flex-item-align"
            style="margin: 10px 10px 10px 30px"
          >
            <img
              style="width: 45px; height: auto; margin-right: 10px"
              v-for="(item, index) in echoData.pay_info.clinic.imgs"
              :key="index"
              :src="item | imageStyle"
              alt="支付凭证"
              class="image cursor scale"
            />
          </viewer>
        </span>
        <span class="item-content flex flex-item-align" v-else>-</span>
      </div>

      <div class="basic-info-item" v-else>
        <span class="item-label flex flex-item-center">直营运营中心支付方式</span>
        <span
          class="item-content"
          v-if="echoData.pay_info && echoData.pay_info.company && echoData.pay_info.company.desc_list.length"
        >
          <span v-for="(item, ind) in echoData.pay_info.company.desc_list" :key="ind">{{ item + '&nbsp;' }}</span>
          <br />
          <viewer
            v-if="echoData.pay_info.company.imgs.length"
            :images="echoData.pay_info.company.imgs"
            class="flex flex-item-align"
            style="margin: 10px 10px 10px 30px"
          >
            <img
              style="width: 45px; height: auto; margin-right: 10px"
              v-for="(item, index) in echoData.pay_info.company.imgs"
              :key="index"
              :src="item"
              alt="支付凭证"
              class="image cursor scale"
            />
          </viewer>
        </span>
        <span class="item-content flex flex-item-align" v-else>-</span>
      </div>
      <div class="basic-info-item">
        <span class="item-label">签收状态</span>
        <span class="item-content">{{ echoData.sign_status_desc || '-' }}</span>
      </div>
      <div class="basic-info-item">
        <span class="item-label">备注</span>
        <span class="item-content" style="word-break: break-all">{{ echoData.remark || '-' }}</span>
      </div>
      <div class="basic-info-item">
        <span class="item-label">附件</span>
        <span class="item-content">
          <div v-if="!echoData?.attach_images?.length">-</div>
          <Picture
            style="margin-top: 10px"
            v-else
            isQueryDetail
            v-model="echoData.attach_images"
            :limit="9"
            :format="['jpg', 'jpeg', 'png', 'pdf', 'ofd']"
            accept=".pdf,.ofd,image/jpg,image/jpeg,image/png"
          />
        </span>
      </div>
    </div>
    <div class="goods-list">
      <!-- <h3 class="f-title">
        商品清单
      </h3> -->
      <div class="table-wrapper">
        <div class="panel-nav flex flex-item-between custom-sticky" style="top: 56px">
          <div ref="tabList">
            <a
              v-for="item in tabList"
              :key="item.type"
              :class="{ active: tableTab === item.type }"
              class="nav"
              @click.prevent.capture="tabChange(item.type)"
            >
              {{ item.name }}
            </a>
          </div>
          <div>
            <Button @click="confirmPass" type="primary" v-if="echoData.can_sign === '1' && tableTab === 'express'"
              >全部确认收货
            </Button>
            <Button class="ml10" v-if="Number(orderInfo.can_refund_apply)" @click="applyAfterSale">申请售后</Button>
          </div>
        </div>
        <div v-show="tableTab === 'goodsList'">
          <!-- 诊所 -->
          <Table
            v-if="showGoodsList"
            :columns="tableColumns"
            :data="goods_items"
            :summary-method="handleSummary"
            border
            show-summary
            class="sticky-table-head"
          >
            <template slot-scope="{ row, index }" slot="img">
              <viewer :images="[row.img]" v-if="row.img" class="flex flex-item-center">
                <img style="width: 80px; height: auto" :src="row.img | imageStyle" class="image" />
              </viewer>
              <span v-else>-</span>
            </template>
            <template slot-scope="{ row, index }" slot="specs">
              <div v-if="row.specs && row.specs.length">
                <div v-for="item in row.specs" :key="item">
                  <span>{{ item.name + ' * ' + item.num }}、</span>
                </div>
              </div>
              <span v-else>-</span>
            </template>
            <template slot-scope="{ row, index }" slot="single_price">
              <div v-if="echoData.is_opc_pay === '1'">{{ row.single_com_price }}</div>
              <div v-else>{{ row.single_cli_price }}</div>
            </template>
            <template slot-scope="{ row, index }" slot="total_price">
              <div v-if="echoData.is_opc_pay === '1'">{{ row.com_price }}</div>
              <div v-else>{{ row.cli_price }}</div>
            </template>
          </Table>

          <!-- 直营运营中心 -->
          <Table
            v-if="type === 'OPC_OWN'"
            :columns="purchase_columns_company"
            :data="goods_items"
            :summary-method="handleSummary"
            border
            show-summary
            class="sticky-table-head"
          >
            <template slot-scope="{ row, index }" slot="img">
              <viewer :images="[row.img]" v-if="row.img" class="flex flex-item-center">
                <img style="width: 80px; height: auto" :src="row.img | imageStyle" class="image" />
              </viewer>
              <span v-else>-</span>
            </template>
            <template slot-scope="{ row, index }" slot="specs">
              <div v-if="row.specs && row.specs.length">
                <div v-for="item in row.specs" :key="item">
                  <span>{{ item.name + ' * ' + item.num }}、</span>
                </div>
              </div>
              <span v-else>-</span>
            </template>
          </Table>

          <!-- 直营运营中心对个人销售 -->
          <Table
            v-if="type === 'OPC_PERSONAL' || type === 'OPC_OUTSIDE'"
            :columns="purchase_columns_person"
            :data="goods_items"
            :summary-method="handleSummary"
            border
            show-summary
            class="sticky-table-head"
          >
            <template slot-scope="{ row, index }" slot="img">
              <viewer :images="[row.img]" v-if="row.img" class="flex flex-item-center">
                <img style="width: 80px; height: auto" :src="row.img | imageStyle" class="image" />
              </viewer>
              <span v-else>-</span>
            </template>
            <template slot-scope="{ row, index }" slot="specs">
              <div v-if="row.specs && row.specs.length">
                <div v-for="item in row.specs" :key="item">
                  <span>{{ item.name + ' * ' + item.num }}、</span>
                </div>
              </div>
              <span v-else>-</span>
            </template>
          </Table>
          <Table
            v-if="type === 'P_MEETING'"
            :columns="purchase_columns_meeting"
            :data="goods_items"
            :summary-method="handleSummary"
            border
            show-summary
            class="sticky-table-head"
          >
            <template slot-scope="{ row, index }" slot="img">
              <viewer :images="[row.img]" v-if="row.img" class="flex flex-item-center">
                <img style="width: 80px; height: auto" :src="row.img | imageStyle" class="image" />
              </viewer>
              <span v-else>-</span>
            </template>
            <template slot-scope="{ row, index }" slot="specs">
              <div v-if="row.specs && row.specs.length">
                <div v-for="item in row.specs" :key="item">
                  <span>{{ item.name + ' * ' + item.num }}、</span>
                </div>
              </div>
              <span v-else>-</span>
            </template>
          </Table>
        </div>
        <!-- 会议活动 -->
        <div v-show="tableTab === 'express'">
          <Table
            :columns="expressColumns"
            :data="express_info"
            :loading="tableLoading"
            border
            :span-method="handleSpan"
            class="sticky-table-head"
          >
            <!-- <template slot="express_desc" slot-scope="{ row }"> -->
            <!--   <div class="expressBox"> -->
            <!--     <div style="text-align: left; flex: 1"> -->
            <!--       <div v-for="(item, index) in row.express_desc" :key="index"> -->
            <!--         {{ item.express_name }} {{ item.express_no }} -->
            <!--       </div> -->
            <!--     </div> -->
            <!--     <a style="width: 70px" @click="showExpress(row)"> -->
            <!--       <svg-icon iconClass="express" class="helpIcon cursor"></svg-icon> -->
            <!--       查看物流</a -->
            <!--     > -->
            <!--   </div> -->
            <!-- </template> -->
            <!-- <template slot="ship_time" slot-scope="{ row }"> -->
            <!--   {{ row.ship_time | date_format }} -->
            <!-- </template> -->

            <template slot="express_no" slot-scope="{ row }">
              <div>
                <div>
                  {{ row.express_name || '-' }}
                  {{ row.express_no }}
                </div>
              </div>
            </template>
            <template slot-scope="{ row }" slot="action">
              <a v-if="row.normal_express === '1'" @click="showExpress(row)" style="margin-right: 10px">查看物流</a>
              <span v-else>-</span>
              <!-- <a @click="confirmExpress(row)" v-if="echoData.can_sign === '1' && row.is_sign !== '1'">确认收货</a> -->
              <Button
                type="primary"
                size="small"
                @click="confirmExpress(row)"
                v-if="echoData.can_sign === '1' && row.can_sign === '1'"
                >确认收货
              </Button>
            </template>
          </Table>
        </div>
        <!--     售后明细-->
        <div v-show="tableTab === 'afterSale'">
          <Table :columns="refundApplyColumns" :data="refundApplyData" border class="sticky-table-head">
            <!--            退款金额-->
            <template slot-scope="{ row }" slot="refund_money">
              <!--							{{echoData.type !== 'CLI_OPENING'}}-->
              {{
                echoData.type !== 'CLI_OPENING'
                  ? `¥ ${row.refund_money}`
                  : echoData.is_opc_pay === '1'
                  ? `¥ ${row.com_refund_money}`
                  : `¥ ${row.refund_money}`
              }}
            </template>
            <!--            直营运营中心退款金额-->
            <template slot-scope="{ row }" slot="com_refund_money"> ¥ {{ row.com_refund_money }}</template>
            <!--            上海树家退款金额-->
            <template slot-scope="{ row }" slot="sj_refund_money"> ¥ {{ row.sj_refund_money }}</template>

            <template slot-scope="{ row, index }" slot="goods">
              <div v-for="(item, goods_key) in row.list" :key="goods_key">
                {{ item.goods_name }}*{{ item.num }} <span v-show="goods_key + 1 < row.list.length">,</span>
              </div>
            </template>

            <template slot-scope="{ row }" slot="refund_reason">
              <span>{{ row.refund_reason || '-' }}</span>
            </template>

            <template slot-scope="{ row }" slot="refund_desc">
              <span>{{ row.refund_desc || '-' }}</span>
            </template>

            <template slot-scope="{ row, index }" slot="create_time">
              <span>{{ row.create_time | date_format }}</span>
            </template>

            <template slot-scope="{ row }" slot="action">
              <KLink
                :to="{ path: '/purchase/after-sale/offline-detail', query: { apply_code: row.apply_code } }"
                target="_blank"
                >详情
              </KLink>
            </template>
          </Table>
        </div>

        <!-- 开票信息 -->
        <Table
          v-show="tableTab == 'invoice'"
          :columns="invoiceColumns"
          :data="invoiceList"
          :loading="tableLoading"
          border
        >
          <!-- 申请主体 -->
          <template slot="applicant" slot-scope="{ row }">
            <div>{{ row.applicant.applicant || '-' }}</div>
          </template>
          <!-- 金额 -->
          <template slot="amount" slot-scope="{ row }">
            <div>{{ row.amount ? `￥${row.amount}` : '-' }}</div>
          </template>
          <!-- 发票内容 -->
          <template slot="content" slot-scope="{ row }">
            <span v-if="row.drawer_type === '3'">
              {{ row.content }}
            </span>
            <span v-else>
              <a @click="checkGoodsDetail(row)">{{ row.content }}</a>
            </span>
          </template>
          <!-- 发票编号 -->
          <template slot="invoice_no" slot-scope="{ row }">
            <div>{{ row.invoice_no || '-' }}</div>
          </template>
          <!-- 发票预览 -->
          <template slot="preview" slot-scope="{ row }">
            <a
              v-if="(row.nuonuo && row.nuonuo.c_paper_pdf_url?.length) || (row.nuonuo && row.nuonuo.c_url?.length)"
              @click="previewInvoice(row)"
              >点击预览</a
            >

            <span v-else>-</span>
          </template>
        </Table>
      </div>
    </div>
    <div class="fixed-bottom-wrapper">
      <back-button></back-button>
      <!--			<Button  type="primary" style="margin:0 20px;" v-if="echoData.status==='REJECTED'" @click="editPurchaseOrder">修改</Button>-->
      <!--			<Button  type="primary" style="margin:0 20px;" @click="editPurchaseOrder">修改采购单</Button>-->
    </div>

    <purchase-modal
      :showModal.sync="editModalVisible"
      :orderInfo="orderInfo"
      :goods_items="local_goods_items"
      :pack_order_code="echoData.pack_order_code"
    ></purchase-modal>
    <k-logistics-progress
      v-model="logisticsVisible"
      :is-logistics-detail="false"
      :express_detail="progress_express_detail"
    ></k-logistics-progress>
    <apply-after-sale-modal
      v-model="applyAfterSaleVisible"
      :detail="orderInfo"
      :order-type="orderType"
      @applySuccess="applySuccess"
    ></apply-after-sale-modal>
    <tips-modal
      v-model="tipVisible"
      @onOk="onOk"
      :showCancel="false"
      confirm-text="好的"
      :contentText="contentText"
    ></tips-modal>

    <!-- 商品明细 -->
    <GoodsModal :goods-visible.sync="goodsVisible" :goodsDetails="goodsDetails"></GoodsModal>
    <invoice-preview v-model="invoicePreviewVisible" :invoice-urls="previewInvoiceUrls"></invoice-preview>
  </div>
</template>

<script>
import S from 'utils/util';
import io from 'utils/request'; // Http request
import search from '@/mixins/search';
import purchaseModal from './components/purchase-modal';
import renderHeader from '@/mixins/renderHeader';
import kLogisticsProgress from '@/components/k-logistics-progress/k-logistics-progress';
import ApplyAfterSaleModal from './components/apply-after-sale-modal';
import tipsModal from '@/components/confirmModal/TipsModal';
import { $operator } from '@/utils/operation';
import { date_format } from 'utils/filters';
import Picture from '@/components/upload/picture';
import InvoicePreview from '@/components/InvoicePreview/index.vue';

export default {
  name: 'detail',
  mixins: [search, renderHeader],
  components: {
    purchaseModal,
    kLogisticsProgress,
    ApplyAfterSaleModal,
    tipsModal,
    Picture,
    InvoicePreview,
    GoodsModal: () => import('@/view/finance/invoice/components/GoodsDetail')
  },
  data() {
    return {
      type: '', // 诊所采购： 'CLI_ADDITION'/'CLI_OPENING' ,直营运营中心采购自用 :'OPC_OWN' ,直营运营中心对个人销售(含秒杀):  'OPC_PERSONAL'
      goods_status: '',
      selectedAddress: {},
      echoData: {
        status: '',
        code: '',
        type_text: '',
        pur_main_name: '', // 采购主体
        logistics_status_text: '',
        clinic_name: '',
        create_time: '',
        can_sign: '',
        pack_order_code: '',
        cli_price: '',
        com_price: '',
        personal_price: '',
        service_price: '',
        remark: '',
        address: '',
        mobile: '',
        consignee: '',
        reject_reason: '',
        pay_info: {},
        attach_images: []
      },
      tableColumns: [
        { title: '序号', type: 'index', key: 'index', align: 'center', width: 80 },
        { title: '商品编号', key: 'spu_code', align: 'center' },
        { title: '商品', key: 'name', align: 'center' },
        { title: '介绍图', slot: 'img', key: 'img', align: 'center' },
        { title: '类型', key: 'type_text', align: 'center' },
        { title: '采购单价', slot: 'single_price', align: 'center' },
        { title: '数量', key: 'num', align: 'center' },
        { title: '采购规格', slot: 'specs', key: 'specs', align: 'center', minWidth: 100 },
        { title: '采购合计(元)', slot: 'total_price', align: 'center' },
        { title: '已发货数量', key: 'shipped_num', align: 'center' },
        { title: '未发货数量', key: 'can_ship_num', align: 'center' }
      ],

      purchase_columns_company: [
        { title: '序号', type: 'index', key: 'index', align: 'center', width: 80 },
        { title: '商品编号', key: 'spu_code', align: 'center', width: 75 },
        { title: '商品', key: 'name', align: 'center' },
        { title: '介绍图', slot: 'img', align: 'center', width: 120 },
        { title: '类型', key: 'type_text', align: 'center' },
        { title: '采购规格', slot: 'specs', key: 'specs', align: 'center', minWidth: 100 },
        { title: '直营运营中心进货单价(元)', key: 'single_com_price', align: 'center' },
        { title: '数量', key: 'num', align: 'center' },
        { title: '直营运营中心进货合计(元)', key: 'com_price', align: 'center' }
      ],

      purchase_columns_person: [
        { title: '序号', type: 'index', key: 'index', align: 'center', width: 80 },
        { title: '商品编号', key: 'spu_code', align: 'center', width: 75 },
        { title: '商品', key: 'name', align: 'center' },
        { title: '介绍图', slot: 'img', align: 'center', width: 120 },
        { title: '采购规格', slot: 'specs', key: 'specs', align: 'center', minWidth: 100 },
        { title: '类型', key: 'type_text', align: 'center' },
        {
          title: '个人采购单价',
          comOutTitle: '外部公司采购指导单价',
          key: 'single_personal_price',
          align: 'center',
          width: 100
        },
        { title: '直营运营中心进货单价', key: 'single_com_price', align: 'center', width: 120 },
        { title: '数量', key: 'num', align: 'center' },
        {
          title: '个人采购合计(元)',
          comOutTitle: '外部公司采购总价',
          key: 'personal_price',
          align: 'center'
        },
        { title: '直营运营中心进货合计(元)', key: 'com_price', align: 'center' },
        { title: '已发货数量', key: 'shipped_num', align: 'center' },
        { title: '未发货数量', key: 'can_ship_num', align: 'center' }
      ],
      purchase_columns_meeting: [
        { title: '序号', type: 'index', key: 'index', align: 'center', width: 80 },
        { title: '商品编号', key: 'spu_code', align: 'center', width: 75 },
        { title: '商品', key: 'name', align: 'center' },
        { title: '介绍图', slot: 'img', align: 'center', width: 120 },
        { title: '采购规格', slot: 'specs', key: 'specs', align: 'center', minWidth: 100 },
        { title: '类型', key: 'type_text', align: 'center' },
        // { title: '直营运营中心进货指导单价', key: 'guide_single_com_price', align: 'center', },
        { title: '直营运营中心进货单价', key: 'single_com_price', align: 'center', width: 120 },
        { title: '数量', key: 'num', align: 'center' },
        { title: '直营运营中心进货合计(元)', key: 'com_price', align: 'center' },
        { title: '已发货数量', key: 'shipped_num', align: 'center' },
        { title: '未发货数量', key: 'can_ship_num', align: 'center' }
      ],

      editModalVisible: false,
      model1: '',
      pur_code: '',
      express_list: [],
      goods_items_copy: [],
      goods_items: [],
      orderInfo: {},
      local_goods_items: [],
      tableTab: 'goodsList',
      expressColumns: [
        {
          title: '发货单号',
          key: 'ship_code',
          align: 'center',
          renderHeader: (h, params) => this._renderHeader(h, params, '此处发货单号指流转到ERP销售订单的平台单号')
        },
        {
          title: '商品',
          align: 'center',
          render: (h, { row }) => h('span', {}, row.goods_text || '-')
        },
        {
          title: '物流单号',
          align: 'center',
          slot: 'express_no'
          // render: (h, { row }) => h('span', {}, row.express_no || '-')
        },
        {
          title: '操作',
          slot: 'action',
          align: 'center',
          width: 200
        },
        {
          title: '签收状态',
          align: 'center',
          render: (h, { row }) => h('span', {}, row.is_sign === '1' ? '已签收' : '未签收' || '-')
        },
        {
          title: '签收时间',
          align: 'center',
          render: (h, { row }) => h('span', {}, date_format(row.sign_time) || '-')
          // render: (h, { row }) => h('span', {}, row.express_no || '-')
        },
        {
          title: '签收方式',
          key: 'code',
          align: 'center',
          renderHeader: (h, params) =>
            h(
              'div',
              {
                style: {
                  display: 'flex',
                  alignItem: 'center'
                }
              },
              [
                h(
                  'Tooltip',
                  {
                    props: {
                      theme: 'dark',
                      placement: 'top'
                    }
                  },
                  [
                    h('Icon', {
                      props: {
                        type: 'md-help-circle'
                      },
                      style: {
                        cursor: 'pointer',
                        fontSize: '16px',
                        marginRight: '4px'
                      }
                    }),
                    h(
                      'div',
                      {
                        slot: 'content',
                        class: 'text-inner',
                        style: {
                          maxWidth: '500px'
                        }
                      },
                      [
                        h('div', {}, '物流签收：物流快递单签收。'),
                        h('div', {}, '手动签收：点击"确认收货"按钮，完成签收。'),
                        h('div', {}, '入库签收：手动完成入库操作，系统自动签收。'),
                        h('div', {}, '超时签收：商品出库后7天，系统自动签收。'),
                        h('div', {}, '无需签收：当商品的发货方式为自提或无需发货时，无需签收。')
                      ]
                    )
                  ]
                ),
                params.column.title
              ]
            ),
          render: (h, { row }) => h('span', {}, row.sign_type_desc || '-')
        },
        {
          title: '物流回传/录入时间',
          key: 'code',
          align: 'center',
          render: (h, { row }) => h('span', {}, date_format(row.ship_time) || '-')
        }
      ],
      express_info: [],
      logisticsVisible: false,
      progress_express_detail: [],
      promotion: {},
      applyAfterSaleVisible: false,
      tipVisible: false,
      contentText: '',
      tabList: [
        { type: 'goodsList', name: '商品清单' },
        { type: 'express', name: '包裹物流' },
        { type: 'invoice', name: '开票信息' }
      ],
      refundApplyColumns: [],
      refundApplyCommonColumns: [
        { title: '售后单号', key: 'apply_code', align: 'center' },
        { title: '售后类型', key: 'refund_type_text', align: 'center' },
        { title: '退款商品', slot: 'goods', align: 'center' },
        {
          title: '换货情况',
          key: 'change_state_desc',
          align: 'center',
          minWidth: 100,
          renderHeader: (h, params) => this._renderHeader(h, params, '系统暂不支持换货功能，这里仅起到标记作用。'),
          isExchange: true
        },
        { title: '售后原因', slot: 'refund_reason', align: 'center' },
        { title: '售后说明', slot: 'refund_desc', align: 'center' },
        { title: '状态', key: 'status_text', align: 'center' },
        { title: '申请时间', slot: 'create_time', align: 'center' },
        { title: '操作', slot: 'action', align: 'center' }
      ],
      refundTypeColumns: [
        {
          title: '退款金额',
          slot: 'refund_money',
          key: 'refund_money',
          align: 'center',
          type: 'all',
          renderHeader: (h, params) => {
            return h(
              'span',
              {},
              `${
                this.echoData.type !== 'CLI_OPENING'
                  ? '退款金额'
                  : this.echoData.is_opc_pay === '1'
                  ? '直营运营中心退款金额'
                  : '诊所退款金额'
              }`
            );
          }
        },
        {
          title: '直营运营中心退款',
          slot: 'com_refund_money',
          key: 'com_refund_money',
          align: 'center',
          type: ['OPC_PERSONAL', 'OPC_OUTSIDE']
        },
        { title: '上海树家退款金额', slot: 'sj_refund_money', key: 'sj_refund_money', align: 'center', type: 'all' }
      ],
      refundApplyData: [],
      // orderDetail: {},
      orderType: '',

      // 开票信息
      invoiceColumns: [
        { title: '序号', type: 'index', key: 'index', align: 'center' },
        { title: '申请主体', slot: 'applicant', align: 'center' },
        { title: '开票方', key: 'drawer', align: 'center' },
        { title: '申请时间', key: 'create_time', align: 'center' },
        { title: '开票时间', key: 'finished_at', align: 'center' },
        { title: '状态', key: 'status_text', align: 'center' },
        { title: '金额', slot: 'amount', align: 'center' },
        { title: '发票内容', slot: 'content', align: 'center' },
        { title: '发票编号', slot: 'invoice_no', align: 'center' },
        { title: '发票预览', slot: 'preview', align: 'center' }
      ],
      invoiceList: [],
      goodsVisible: false,
      goodsDetails: [], // 详情明细
      previewInvoiceUrls: [],
      invoicePreviewVisible: false
    };
  },
  computed: {
    getCodeLabel() {
      const ent_type = this.echoData?.ent_type || '';
      // 省公司/个人客户显示主体ID
      const id_type_menu = ['COM', 'PERSON_C'];
      // 诊所/养疗馆/外部公司显示主体编号
      const code_type_menu = ['CLI', 'OUTSIDE'];
      if (id_type_menu.includes(ent_type)) {
        return '主体ID';
      }
      if (code_type_menu.includes(ent_type)) {
        return '主体编号';
      }
      return '';
    },
    showExchange() {
      let orderTypes = ['RXJ_ADDITION', 'CLI_ADDITION', 'OPC_OWN', 'OPC_PERSONAL'];
      return orderTypes.includes(this.orderType);
    },
    showGoodsList() {
      const type = this.type;
      return type === 'CLI_ADDITION' || type === 'CLI_OPENING' || type === 'RXJ_ADDITION';
    }
  },
  watch: {},
  created() {
    const { pur_code } = this.$route.query;
    if (pur_code) {
      this.pur_code = pur_code;
      this.getDetail();
      this.refundApplyColumns = this.refundApplyCommonColumns;
      this.getInvoicePurchasedetail();
    }
  },
  mounted() {
    if (this.$route.query.openType === 'receipt') {
      this.tabChange('express');
    }
  },
  methods: {
    previewInvoice(row) {
      // c_paper_pdf_url由nuonuo返回，不确定类型
      let flag =
        (Array.isArray(row.nuonuo?.c_paper_pdf_url) && row.nuonuo.c_paper_pdf_url?.length > 0) ||
        (typeof row.nuonuo?.c_paper_pdf_url === 'string' && row.nuonuo.c_paper_pdf_url !== '');

      const urls = flag ? row.nuonuo.c_paper_pdf_url : row.nuonuo.c_url;

      // 是否是单张图片，单张就直接打开
      let singleFlag = (Array.isArray(urls) && urls.length === 1) || (typeof urls === 'string' && urls !== '');
      if (singleFlag) {
        const a = document.createElement('a');
        a.href = Array.isArray(urls) ? urls[0] : urls;
        a.target = '_blank';
        a.download = '';
        a.style.display = 'none';
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        return;
      }
      this.previewInvoiceUrls = urls;
      this.invoicePreviewVisible = true;
    },
    /**
     * @description:  开票相关
     */
    checkGoodsDetail(row) {
      this.goodsDetails = row.goods;
      this.goodsVisible = true;
    },
    // * api 采购订单开票详情
    getInvoicePurchasedetail() {
      let params = {
        order_code: this.$route.query.pur_code
      };
      this.$api.getInvoicePurchasedetail(params).then(
        res => {
          this.invoiceList = res.list;
        },
        rej => {
          this.$Message.error(rej.errmsg);
        }
      );
    },
    getDetail() {
      this.$api.getPurchaseInfo({ pur_code: this.pur_code }).then(({ info }) => {
        console.log('-> info', info);
        this.orderInfo = info;
        this.type = info.type;
        if (info.type === 'RXJ_ADDITION') {
          this.tableColumns[5].title = '采购单价';
          this.tableColumns[9].title = '采购合计(元)';
        }
        this.echoData = info;
        this.echoData.mobile = info.consignee_info.mobile;
        this.echoData.consignee = info.consignee_info.consignee;
        this.orderType = info.type;
        this.goods_items = info.items.map(item => {
          item.can_ship_num = Number(item.can_ship_num) <= 0 ? 0 : Number(item.can_ship_num);
          return item;
        });
        this.express_list = info.express_list;
        this.goods_items_copy = this._.cloneDeep(info.items);
        if (this.type !== 'P_MEETING') {
          this.echoData.address = `${info.consignee_info.prov.name} ${info.consignee_info.city.name} ${
            info.consignee_info.county.name || ''
          }  ${info.consignee_info.detail}`;
        }
        if (info.type === 'OPC_OUTSIDE') {
          this.purchase_columns_person.forEach(item => {
            item.comOutTitle && (item.title = item.comOutTitle);
          });
        }
        if (info.pack_order_code) {
          this.getPackageInfo(info.pack_order_code);
        }
        if (this.type === 'P_MEETING') {
          this.promotion = info.promotion;
        }
        this.express_info = info.express_info;
        if (this.$route.query.openType === 'receipt') {
          this.setTabListToTop(this.$refs.tabList);
        }
        if (!this.showExchange) {
          this.refundApplyColumns = this.refundApplyCommonColumns.filter(item => !item.isExchange);
        }
        this.getOrderRefundApplyList();
      });
    },
    setTabListToTop(ele) {
      this.$nextTick(() => {
        function getElementTop(element) {
          let actualTop = element.offsetTop;
          let current = element.offsetParent;

          while (current !== null) {
            actualTop += current.offsetTop;
            current = current.offsetParent;
          }

          return actualTop;
        }

        const elementTop = getElementTop(ele);
        window.scrollTo({
          top: elementTop - 56 - 5
        });
      });
    },
    getPackageInfo(order_code) {
      this.$api.getPackageInfo({ order_code }).then(res => {
        console.log('-> res', res);
        this.local_goods_items = res.items;
      });
    },
    editPurchaseOrder() {
      this.editModalVisible = true;
    },
    handleSummary({ columns, data }) {
      const sums = {};

      columns.forEach((column, index) => {
        const key = column.key;
        if (index === 0) {
          console.log('index', index, key);
          sums[key] = {
            key,
            value: '合计'
          };
          return;
        }

        let whiteList = [];
        let type = this.type;
        let whiteTypeList = ['CLI_ADDITION', 'CLI_OPENING'];
        if (whiteTypeList.includes(type)) {
          whiteList = [9, 10, 11, 12];
        } else if (type === 'OPC_OWN') {
          whiteList = [7, 8];
        } else if (type === 'OPC_PERSONAL' || type === 'OPC_OUTSIDE') {
          whiteList = [10, 11, 12];
        } else if (type === 'P_MEETING') {
          whiteList = [8, 9, 10, 11];
        }

        if (whiteList.indexOf(index) > -1) {
          const values = data.map(item => Number(item[key]));
          if (!values.every(value => isNaN(value))) {
            const v = values.reduce((prev, curr) => {
              const value = Number(curr);
              if (!isNaN(value)) {
                return $operator.add(prev, curr);
              } else {
                return prev;
              }
            }, 0);
            sums[key] = {
              key,
              value: S.mathAdd(v, 0)
            };
          } else {
            sums[key] = {
              key,
              value: '-'
            };
          }
        } else {
          sums[key] = {
            key,
            value: ''
          };
        }
      });
      return sums;
    },
    tabChange(tab) {
      this.tableTab = tab;
    },

    showExpress(row) {
      console.log('row', row);
      this.progress_express_detail = [row];
      this.logisticsVisible = true;
    },
    getOrderRefundApplyList() {
      let params = { order_code: this.$route.query.pur_code };
      // let params = {order_code: 'PCO230316164230766561'}
      console.log('=>(detail.vue:241) params', params);
      this.$api
        .getOrderRefundApplyList(params)
        .then(res => {
          this.refundApplyData = res.list;
          if (this.refundApplyData.length > 0) {
            this.tabList = [
              { type: 'goodsList', name: '商品清单' },
              { type: 'express', name: '包裹物流' },
              { type: 'invoice', name: '开票信息' },
              { type: 'afterSale', name: '售后明细' }
            ];
            this.typeCountComputed();
          }

          console.log('=>(detail.vue:242) res', res);
        })
        .catch(err => this.$Message.error(err.errmsg));
    },
    applyAfterSale() {
      if (this.orderInfo.can_refund_apply_num === '0') {
        this.tipVisible = true;
        this.contentText = '订单内商品已全部退款，无法重复发起售后';
        return;
      }
      this.applyAfterSaleVisible = true;
    },
    applySuccess() {
      this.applyAfterSaleVisible = false;
      this.contentText = '售后申请提交成功，请等待审核';
      this.tipVisible = true;
      this.getOrderRefundApplyList();
    },
    onOk() {
      this.tipVisible = false;
    },
    // 线下订单根据不同的类型，展示不同退款金额
    typeCountComputed() {
      let filterTableColumns = this.refundTypeColumns.filter(item => {
        if (item.type == 'all' || (Array.isArray(item.type) && item.type.includes(this.type))) {
          return true;
        }
      });
      this.refundApplyColumns.splice(2, 0, ...filterTableColumns);
    },
    confirmExpress(item) {
      console.log(item);
      let { ship_code, pack_index, deliver_index } = item;
      console.log({
        ship_code,
        pack_index,
        deliver_index
      });
      this.$Modal.confirm({
        title: '是否确认收货？',
        loading: true,
        onOk: () => {
          io.post('/pms_opc/joinin.purchase.signDeliver', {
            ship_code,
            pack_index,
            deliver_index
          }).then(
            res => {
              this.$Message.success('确认收货成功');
              this.$Modal.remove();
              this.getDetail();
            },
            err => {
              this.$Message.error(err.errmsg);
              this.$Modal.remove();
            }
          );
        },
        onCancel: () => {
          // this.$Message.info('Clicked cancel');
        }
      });
    },
    confirmPass() {
      this.$Modal.confirm({
        title: '是否确认全部收货？',
        loading: true,
        onOk: () => {
          io.post('/pms_opc/joinin.purchase.signWhole', {
            pur_code: this.pur_code
          }).then(
            res => {
              this.$Message.success('全部确认收货成功');
              this.$Modal.remove();
              this.getDetail();
            },
            err => {
              this.$Message.error(err.errmsg);
              this.$Modal.remove();
            }
          );
        },
        onCancel: () => {
          // this.$Message.info('Clicked cancel');
        }
      });
    },
    handleSpan({ row, column, rowIndex, columnIndex }) {
      // 合并单元格起始位置
      let firstBiginColsIndex = 0;
      // 合并单元格数量
      let firstColsNum = 1;
      // 合并单元格起始位置
      let secondBiginColsIndex = 0;
      // 合并单元格数量
      let secondColsNum = 1;

      // 一级单元格合并
      // [0,1,2]代表从第一行开始合并三行
      // [2,3,4,5]代表从第三行开始合并四行
      let includeFirstIndex = [];
      this.express_info.forEach((item, index) => {
        if (item.ship_unique === row.ship_unique) {
          includeFirstIndex.push(index);
        }
      });
      if (includeFirstIndex.includes(rowIndex)) {
        firstBiginColsIndex = includeFirstIndex[0];
        firstColsNum = includeFirstIndex.length;
      }
      // 二级单元格合并
      let includeSecondIndex = [];
      this.express_info.forEach((item, index) => {
        if (item.goods_unique === row.goods_unique) {
          includeSecondIndex.push(index);
        }
      });
      if (includeSecondIndex.includes(rowIndex)) {
        secondBiginColsIndex = includeSecondIndex[0];
        secondColsNum = includeSecondIndex.length;
      }

      //---------------------------------------------------------------------这部分代码不用动-
      //合并一级单元格
      if (firstColsNum + firstBiginColsIndex > this.express_info.length) {
        console.error(new Error('合并列数超出表格列数'));
        return;
      }

      if (columnIndex === 0 && rowIndex === firstBiginColsIndex) {
        return [firstColsNum, 1];
      } else {
        if (columnIndex === 0 && rowIndex > firstBiginColsIndex && rowIndex < firstColsNum + firstBiginColsIndex) {
          return [0, 0];
        }
      }

      //合并二级单元格
      if (secondColsNum + secondBiginColsIndex > this.express_info.length) {
        console.error(new Error('合并列数超出表格列数'));
        return;
      }

      if (columnIndex === 1 && rowIndex === secondBiginColsIndex) {
        return [secondColsNum, 1];
      } else {
        if (columnIndex === 1 && rowIndex > secondBiginColsIndex && rowIndex < secondColsNum + secondBiginColsIndex) {
          return [0, 0];
        }
      }
    }
  }
};
</script>

<style lang="less" scoped>
.detail-wrapper {
  .basic-info {
    border: 1px solid #b7b7b7;

    .basic-info-item {
      display: flex;
      line-height: 22px;
      border-bottom: 1px solid #b7b7b7;

      &:last-of-type {
        border-bottom: none;
      }

      text-align: center;

      .item-label {
        width: 180px;
        border-right: 1px solid #b7b7b7;
        background: #e6e6e6;
        display: flex;
        justify-content: center;
        align-items: center;
      }

      .item-content {
        flex: 1;
        text-align: left;
        text-indent: 30px;
      }
    }
  }

  .goods-list {
    padding-top: 20px;
  }

  .logistics-information {
    margin-bottom: 30px;

    .logistics-info-box {
      padding: 12px 12px 6px;
      border: 1px solid #e7e7e7;
      width: 260px;
      margin: 12px;
      background: rgba(225, 225, 225, 0.3);

      .title {
        font-size: 15px;
        font-weight: 600;
        line-height: 22px;
      }

      .info-item {
        margin-bottom: 20px;
      }

      .foot-tip {
        line-height: 20px;
      }
    }
  }
}

.table-wrapper {
  padding: 10px 0 20px;
  position: relative;

  ::v-deep .ivu-table-wrapper {
    overflow: unset;
  }
}

.sticky-table-head {
  ::v-deep .ivu-table {
    overflow: unset;

    .ivu-table-header {
      position: sticky;
      top: 94px;
      z-index: 3;
    }
  }
}

.cursor {
  cursor: pointer;
}

.scale {
  &:hover {
    transform: scale(1.2);
  }
}

.expressBox {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 12px;

  .search {
    cursor: pointer;
  }
}
</style>

<style lang="less">
.ivu-tooltip-inner {
  max-width: 400px;
}
</style>
