<template>
  <div class="f_order-wrapper">
    <Form inline @submit.native.prevent @keyup.enter.native="onSearch">
      <Row>
        <FormItem>
          <Input v-model="queryFormData.goods_name" placeholder="请输入商品名称" clearable />
        </FormItem>
        <FormItem>
          <Input v-model="queryFormData.code" placeholder="单号" clearable />
        </FormItem>
        <FormItem>
          <DatePicker
            v-model="timeRange"
            :options="disabledTime"
            class="time-range"
            clearable
            format="yyyy-MM-dd"
            placeholder="创建时间"
            type="daterange"
            @on-change="times => handleTimeChange(times)"
          ></DatePicker>
        </FormItem>
        <FormItem>
          <surning-search
            :showType="true"
            :isClearable="true"
            :showOutside="true"
            ref="nursing-search"
            :typeList="typeList"
            v-model="queryFormData.ent_code"
            @getType="type => (queryFormData.ent_type = type)"
          ></surning-search>
        </FormItem>
        <FormItem>
          <license-search
            ref="license-search"
            v-model="queryFormData.ent_code_organ"
            :type="queryFormData.ent_type_organ"
            :isClearable="true"
            :company_id="queryFormData.company_id"
            :showOutside="true"
            :showOpc="true"
            @getType="type => (queryFormData.ent_type_organ = type)"
          ></license-search>
        </FormItem>
      </Row>

      <Row>
        <FormItem>
          <clinic-type-search
            v-model="queryFormData.clinic_type"
            :clinic_direct_type="queryFormData.clinic_direct_type"
            @directChange="directChange"
          ></clinic-type-search>
        </FormItem>
        <FormItem>
          <Select v-model="queryFormData.type" clearable placeholder="请选择订单类型">
            <Option v-for="item in typeDesc" :value="item.id" :key="item.id">{{ item.desc }}</Option>
          </Select>
        </FormItem>
        <FormItem>
          <Select v-model="queryFormData.status" placeholder="状态" clearable>
            <Option v-for="item in statusDesc" :key="item.id" :value="item.id">{{ item.desc }}</Option>
          </Select>
        </FormItem>
        <FormItem>
          <Select v-model="queryFormData.sign_status" placeholder="请选择签收状态" clearable>
            <Option v-for="item in signStatusDesc" :key="item.id" :value="item.id">{{ item.desc }}</Option>
          </Select>
        </FormItem>
        <FormItem>
          <Button class="mr10" type="primary" @click="onSearch">筛选</Button>
          <Button class="mr10" type="default" @click="onResetSearch">重置</Button>
          <!--          <Button :loading="exportLoading" type="default" @click="exportExcel">导出</Button>-->
          <Dropdown trigger="click" @on-click="exportExcel">
            <Button :loading="exportLoading">
              导出
              <Icon type="ios-arrow-down" />
            </Button>
            <DropdownMenu slot="list">
              <DropdownItem name="order">导出订单明细</DropdownItem>
              <DropdownItem name="goods">导出商品明细</DropdownItem>
            </DropdownMenu>
          </Dropdown>
        </FormItem>
      </Row>
    </Form>

    <div class="table-wrapper">
      <div class="panel-nav flex flex-item-between">
        <div>
          <a :class="{ active: !queryFormData.status }" class="nav" @click.prevent.capture="onStatusChange('')">
            全部
          </a>
          <a
            v-for="item in statusDesc"
            :key="item.id"
            :class="{ active: $route.query.status == item.id }"
            class="nav"
            @click.prevent.capture="onStatusChange(item.id)"
          >
            {{ item.desc }}
            <!--            <Tag :color="getTagColor(item.id)">{{ status_count[item.id] }}</Tag>-->
          </a>
        </div>
        <div>
          <Button type="primary" @click="createOrder">创建采购单</Button>
        </div>
      </div>
      <Table :columns="tableCols" :data="list" :loading="tableLoading" :height="$store.state.app.clientHeight - 331">
        <template slot="status_text" slot-scope="{ row }">
          <span :style="statusColor(row)">{{ row.status_text }}</span>
        </template>

        <!-- 诊所/个人采购金额 -->
        <template slot-scope="{ row }" slot="pur_main_price">
          <span v-if="row.pur_main_price">￥{{ row.pur_main_price }}</span>
          <span v-else>-</span>
        </template>
        <template slot="ent_type_text" slot-scope="{ row }">
          <div style="text-align: left">
            <div>主体类型：{{ row.ent_type_text }}</div>
            <div v-if="row.ent_type === 'PERSON_C'">
              <span>会员名称：</span>{{ row.vip_type_desc?.join('；') || '-' }}
            </div>
            <div v-else>
              诊所类型：
              <span v-if="row.clinic_direct_type_text && row.clinic_type_text">
                {{ row.clinic_direct_type_text }} / {{ row.clinic_type_text }}
              </span>
              <span v-else>-</span>
            </div>
          </div>
        </template>
        <!-- 省公司出厂金额 -->
        <template slot-scope="{ row }" slot="com_price">
          <span v-if="row.com_price">￥{{ row.com_price }}</span>
          <span v-else>-</span>
        </template>
        <!--        <template slot="cli_type" slot-scope="{ row }">-->
        <!--          <div v-if="row.clinic_direct_type_text && row.clinic_type_text">-->
        <!--            {{ row.clinic_direct_type_text }} / {{ row.clinic_type_text }}-->
        <!--          </div>-->
        <!--          <div v-else>-</div>-->
        <!--        </template>-->
        <template slot="organization_name" slot-scope="{ row }">
          {{ row.organization_name || '-' }}
        </template>
        <template slot="action" slot-scope="{ row }">
          <a @click="toDetail(row.code)">详情</a>
          <!--					<a @click="toDetail(row.code,true)" style="margin-left: 10px;" v-if="row.status==='REJECTED'">修改</a>-->
          <a v-if="row.can_sign === '1'" style="margin-left: 10px" @click="toDetail(row.code, 'receipt')">确认收货</a>
          <a @click="editOrder(row.code)" style="margin-left: 10px" v-if="row.status === 'REJECTED'">修改</a>
        </template>
      </Table>
      <div class="block_20"></div>
      <KPage
        :current="+queryFormData.page"
        :page-size="+queryFormData.pageSize"
        :total="total"
        style="text-align: center"
        @on-change="handleCurrentChange"
        @on-page-size-change="handleSizeChange"
      />
    </div>
  </div>
</template>

<script>
import S from 'utils/util';
import search from '@/mixins/search';
import surningSearch from '@/components/surning-search/surning-search';
import licenseSearch from '@/components/license-search/license-search';
import ClinicTypeSearch from '../../../components/remote-search/clinic-type-search.vue';

const init_query_form_data = {
  page: 1,
  pageSize: 20,
  code: '',
  status: '',
  clinic_id: '',
  sign_status: '',
  ent_code: '',
  ent_type: '',
  goods_name: '', // 商品名称
  main: '', // 采购主体
  type: '', // 类型
  ent_code_organ: '',
  ent_type_organ: '',
  clinic_type: '',
  clinic_direct_type: '',
  st: '',
  et: '',
  r: ''
};
export default {
  name: 'list',
  components: {
    ClinicTypeSearch,
    surningSearch,
    licenseSearch
  },
  mixins: [search],
  data() {
    return {
      apiName: 'getPurchaseList',
      queryFormData: { ...init_query_form_data },
      exportLoading: false, // 导出
      timeRange: [],
      disabledTime: {
        disabledDate(date) {
          return date && date.valueOf() > Date.now();
        }
      },
      isRsjCompany: 0, // 0 不是总公司, =1 是总公司
      clinic_list: [],
      statusDesc: [],
      signStatusDesc: [],
      typeDesc: [],
      tableCols: [
        { title: '采购单号', key: 'code', align: 'center', width: 100 },
        { title: '采购主体', key: 'pur_main_name', align: 'center', width: 120 },
        { title: '主体类型', slot: 'ent_type_text', align: 'center', width: 200 },
        // { title: '诊所类型', slot: 'cli_type', align: 'center', width: 120 },
        { title: '营业执照名称', slot: 'organization_name', align: 'center', width: 120 },
        {
          title: '进货商品',
          key: 'goods_name',
          align: 'center',
          width: 220,
          render: function (h, params) {
            return h(
              'Tooltip',
              {
                props: { placement: 'bottom-start', transfer: true }
              },
              [
                h(
                  'span',
                  {
                    style: {
                      textOverflow: 'ellipsis',
                      whiteSpace: 'normal',
                      overflow: 'hidden',
                      display: '-webkit-box',
                      webkitBoxOrient: 'vertical',
                      webkitLineClamp: 2 + ''
                    }
                  },
                  params.row.goods_name
                ),
                h(
                  'span',
                  { slot: 'content', style: { whiteSpace: 'normal', wordBreak: 'break-all' } },
                  params.row.goods_name
                )
              ]
            );
          }
        },
        { title: '订单类型', key: 'type_text', align: 'center', width: 120 },
        // { title: '诊所', key: 'clinic_name', align: 'center' },
        { title: '采购金额', slot: 'pur_main_price', align: 'center', width: 120 },
        { title: '直营运营中心进货金额', slot: 'com_price', align: 'center', width: 150 },
        { title: '采购总数', key: 'total_num', align: 'center', width: 120 },
        // { title: '采购金额(元)', key: 'total_money', align: 'center' },
        // {title: '类型', key: 'addition_text', align: 'center'},
        { title: '状态', key: 'status_text', slot: 'status_text', align: 'center', width: 120 },
        { title: '创建时间', key: 'create_time', align: 'center', width: 140 },
        {
          title: '签收状态',
          align: 'center',
          render: (h, { row }) => h('span', {}, row.sign_status_desc || '-'),
          width: 120
        },
        { title: '操作', slot: 'action', align: 'center', fixed: 'right', width: 80 }
      ],
      typeList: [
        { label: '诊所', value: 'CLI' },
        { label: '直营运营中心', value: 'OPC' },
        { label: '外部公司', value: 'OUTSIDE' },
        { label: '个人客户', value: 'PERSON_C' }
      ],
      status_count: [],
      directTypeDesc: [],
      cliTypeDesc: []
    };
  },
  computed: {
    statusColor(row) {
      return row => {
        switch (row.status) {
          case 'WAIT_AUDIT':
            return {
              color: '#f90'
            };
          case 'WAIT_RECHECK':
            return {
              color: '#f90'
            };
          case 'REJECTED':
            return {
              color: '#ed4014'
            };
          case 'WAIT_SHIP':
            return {
              color: '#2db7f5'
            };
          case 'PART_SHIP':
            return {
              color: '#2db7f5'
            };
          case 'HAS_SHIP':
            return {
              color: '#19be6b'
            };
        }
      };
    },
    getTagColor(type) {
      return type => {
        switch (type) {
          // case 'W_OP_AUDIT': // 待供应商审核
          case 'W_AUDIT': // 待平台审核
          case 'W_RECHECK': // 待买家退货
          case 'WAIT_PAY': // 待商家收货
            return 'warning';
          case 'W_SHIP': // 待发货
            return 'primary';
          case 'REJECTED': // 待发货
            return 'error';
          case 'SHIPPED': // 已发货
          case 'FINISHED': // 已完成
            return 'success';
          default: // 已取消
            return 'default';
        }
      };
    }
  },
  watch: {},
  created() {
    this.queryFormData = S.merge(this.queryFormData, this.$route.query);
    this.submitQueryForm(true);
    this.getOptionsList();
    // 获取诊所类型枚举
    this.getClinicOptions();
  },
  mounted() {},
  methods: {
    // 导出文件
    exportExcel(name) {
      const downloadMethod = name === 'goods' ? 'getPurchaseGoodsUrl' : 'getPurchaseOrderUrl';
      this.exportLoading = true;
      let params = {
        ...this.queryFormData
      };
      this.$api[downloadMethod](params).then(
        res => {
          this.action = res.url;
          this.download(res.url);
          this.exportLoading = false;
        },
        err => {
          this.$Message.error(err.errmsg);
          this.exportLoading = false;
        }
      );
    },
    // 通过a标签下载
    download(url) {
      var downloadLink = document.createElement('a');
      downloadLink.setAttribute('href', url);
      downloadLink.setAttribute('target', '_blank');
      downloadLink.setAttribute('style', 'display:none');
      document.body.appendChild(downloadLink);
      downloadLink.click();
      document.body.removeChild(downloadLink);
    },
    editOrder(code) {
      console.log('-> code', code);
      this.$router.push({
        path: '/purchase/order/create',
        query: {
          pur_code: code
        }
      });
    },
    // 创建诊所采购单
    createOrder() {
      this.$router.push({
        path: '/purchase/order/create',
        query: {
          isRsjCompany: this.isRsjCompany
        }
      });
    },
    toDetail(pur_code, openType) {
      let query = {
        pur_code,
        openType
      };
      this.$router.push({
        path: '/purchase/order/detail',
        query
      });
    },
    onStatusChange(status) {
      console.log('-> status', status);
      this.queryFormData.page = 1;
      this.queryFormData.status = status;
      this.submitQueryForm();
    },
    onResetSearch() {
      this.queryFormData = { ...init_query_form_data };
      this.timeRange = [];
      this.submitQueryForm();
      this.$refs['nursing-search'].clear();
      this.$refs['license-search'].clear();
    },
    getOptionsList() {
      this.$api.getPurchaseOptions().then(res => {
        this.clinic_list = res.clinic_list;
        this.statusDesc = S.descToArrHandle(res.listStatusDesc);
        this.signStatusDesc = S.descToArrHandle(res.signStatusDesc);

        // 类型数据
        this.typeDesc = S.descToArrHandle(res.typeDesc);

        this.isRsjCompany = res.isRsjCompany;
      });
    },
    handlerListData(data) {
      this.status_count = data.status_count;
    },
    getClinicOptions() {
      this.$api.getClinicOptions().then(res => {
        this.directTypeDesc = S.descToArrHandle(res.directTypeDesc);
        this.cliTypeDesc = S.descToArrHandle(res.typeDesc);
      });
    },
    directChange(val) {
      this.$set(this.queryFormData, 'clinic_direct_type', val);
    }
  },
  beforeRouteUpdate(to, from, next) {
    // S.log(to.params['nav-stack-key-dir'], 'nav-stack-key-dir')
    this.queryFormData = S.merge(init_query_form_data, to.query);
    this.getTimeRange();
    this.loadList();
    next();
  }
};
</script>

<style lang="less" scoped></style>
