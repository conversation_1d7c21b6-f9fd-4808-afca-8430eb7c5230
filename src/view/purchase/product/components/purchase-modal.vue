<template>
  <div>
    <Modal
      :mask-closable="false"
      :value="showModal"
      title="新增诊所采购单"
      width="800px"
      class-name="standard-modal custom-body-modal"
      @on-visible-change="closeModal"
      @on-ok="submitForm('purchaseModal')"
    >
      <div slot="footer" class="modal-foot">
        <Button @click="closeModal(false)">取消</Button>
        <Button type="primary" @click="submitForm('purchaseModal')">确定</Button>
      </div>
      <div class="purchase-modal-wrapper">
        <div class="modal-header flex flex-item-between flex-item-align custom-sticky header" style="top: 0px">
          <span>发货明细：</span>
          <Form @submit.native.prevent>
            <FormItem style="margin-bottom: 0">
              <Select
                filterable
                v-model="select_good"
                placeholder="请选择商品"
                style="width: 200px"
                @on-select="selectGoods"
                clearable
                ref="goodsSelect"
              >
                <Option v-for="(item, index) in goods_list" :key="index" :value="item.spu_code">{{ item.name }}</Option>
              </Select>
            </FormItem>
          </Form>
        </div>
        <div class="table-wrapper">
          <Table
            :columns="tableColumns_order"
            :data="select_goods_list"
            :summary-method="handleSummary"
            border
            show-summary
            class="sticky-table-head"
          >
            <template slot="unit" slot-scope="{ row, index }">
              <InputNumber
                v-model="select_goods_list[index].unit"
                :min="0"
                :precision="0"
                placeholder="数量"
                @on-change="e => changeNum(e, index)"
                :disabled="!!row.spu_list.length || (is_non_direct_up === '1' && row.group_id === 'door_material')"
                @on-blur="changeBuyNum(row, index)"
              />
            </template>
            <template slot="spu_list" slot-scope="{ row, index }">
              <div class="flex flex-item-center">
                <div class="flex flex-c">
                  <div v-for="(item, index) in row.checked_spu_list" :key="index" class="flex">
                    {{ item.name + ' * ' + item.unit }}、
                  </div>
                </div>
                <a v-if="row.spu_list.length" @click="setBatch(row, index)">设置规格</a>
                <span v-else>-</span>
              </div>
            </template>
            <template slot="action" slot-scope="{ row, index }">
              <a style="color: red" @click="deleteGood(row, index)">删除</a>
            </template>
          </Table>
        </div>
        <Form ref="purchaseModal" :label-width="100" :model="formData" :rules="ruleValidate" label-colon>
          <div>
            <Row>
              <Col span="24">
                <FormItem label="诊所" prop="generic_name" v-if="orderInfo.ent_type == 'CLI'">
                  <Input v-model="formData.clinic_name" disabled />
                </FormItem>
              </Col>
              <Col span="24">
                <FormItem label="收件人姓名" prop="consignee_info.consignee">
                  <Input v-model="formData.consignee_info.consignee" placeholder="收件人姓名"></Input>
                </FormItem>
              </Col>
              <Col span="24">
                <FormItem label="收件人手机号" prop="consignee_info.mobile">
                  <Input v-model="formData.consignee_info.mobile" placeholder="收件人手机号"></Input>
                </FormItem>
              </Col>
              <Col span="24">
                <FormItem label="收件地址" required>
                  <div class="addWrap custom-addWrap">
                    <div class="addressBox" style="width: 100%">
                      <!--										<v-region v-model="selectedAddress" @values="regionChange"></v-region>-->
                      <el-cascader
                        v-model="selectedAddress"
                        :options="options"
                        clearable
                        placeholder="请选择收件地址"
                        size="small"
                        popper-class="address-com"
                        style="width: 100%"
                        @change="regionChange"
                      >
                      </el-cascader>
                    </div>
                    <div class="addressInput" style="width: 100%">
                      <Input v-model="formData.consignee_info.detail" placeholder="详细地址"></Input>
                    </div>
                  </div>
                </FormItem>
              </Col>
              <Col span="24">
                <FormItem label="备注" prop="phonetic_code">
                  <Input v-model="formData.remark" autosize type="textarea" />
                </FormItem>
              </Col>
            </Row>
          </div>
        </Form>
      </div>
    </Modal>
    <Modal :mask-closable="false" v-model="batchVisible" title="设置商品采购规格" width="680px" class="spu-modal">
      <div slot="footer" class="modal-foot">
        <div style="" class="flex flex-item-align flex-item-between">
          <div>
            商品待采购件数 <span style="color: red"> {{ editRow.wait_purchase_num }} </span>件，已设置
            <span style="color: green">{{ setUnit }} </span>件
          </div>
          <div>
            <Button @click="cancelSet">取消</Button>
            <Button type="primary" @click="setSpu">确定</Button>
          </div>
        </div>
      </div>
      <div>
        <div class="flex flex-item-align spu-box">
          <div class="spu-items flex" v-for="(item, idx) in set_spu_list" :key="item.spu">
            <span style="min-width: 130px; text-align: left"> {{ item.name }}</span>
            <InputNumber
              v-model="item.unit"
              placeholder="数量"
              controls-outside
              :ref="item.spu"
              @on-change="changeSpuUnit(item, idx)"
              :precision="0"
              :min="0"
              :max="item.can_num && Number(item.can_num) + 1"
              size="small"
              style="width: 80px; margin-left: 20px"
            >
            </InputNumber>
          </div>
        </div>
      </div>
    </Modal>
  </div>
</template>

<script>
import S from 'utils/util';
import { CodeToText, regionData } from '@/utils/chinaMap';
import { $operator } from '@/utils/operation';

export default {
  name: 'purchase-modal',
  props: {
    showModal: {
      type: Boolean,
      default: false
    },
    orderInfo: {
      type: Object,
      default: {}
    },
    // 诊所类型为非直营（升级）的诊所: 是：'1'， 否：'0'
    is_non_direct_up: {
      type: String,
      default: ''
    }
  },
  data() {
    const init_form_data = {
      pack_order_code: this.$route.query.order_code,
      clinic_id: '',
      consignee_info: {
        city: {
          code: '',
          name: ''
        },
        county: {
          code: '',
          name: ''
        },
        prov: {
          code: '',
          name: ''
        },
        mobile: '',
        consignee: '',
        detail: ''
      },
      remark: '',
      item_list: [],
      spec_map: {}
    };
    const validateMobile = (rule, value, callback) => {
      if (value === '') {
        callback(new Error('请输入正确的手机号码'));
      } else {
        const reg = /^1[3456789]\d{9}$/;
        if (!reg.test(value)) {
          callback(new Error('请输入正确的手机号码'));
        }
        callback();
      }
    };
    return {
      batchVisible: false,
      options: regionData,
      select_good: '',
      selectedAddress: [],
      formData: {
        ...init_form_data
      },
      tableColumns_order: [
        { title: '商品编号', key: 'spu_code', align: 'center' },
        { title: '商品', key: 'name', align: 'center' },
        { title: '商品类型', key: 'type_text', align: 'center' },
        { title: '待采购数', key: 'wait_purchase_num', align: 'center' },
        { title: '采购数量', key: 'unit', slot: 'unit', align: 'center', width: 100 },
        { title: '采购规格', key: 'spu_list', slot: 'spu_list', align: 'center', minWidth: 120 },
        { title: '操作', slot: 'action', align: 'center' }
      ],
      ruleValidate: {
        'consignee_info.consignee': [{ required: true, message: '请填写收件人人名', trigger: 'blur' }],
        'consignee_info.mobile': [{ required: true, trigger: 'blur', validator: validateMobile }]
      },
      select_goods_list: [],
      goods_list: [],
      currentInfo: {},
      clinic_list: [],
      editIndex: 0,
      set_spu_list: [], //设置的spu列表
      editRow: {}
    };
  },
  computed: {
    setUnit() {
      return this.set_spu_list.reduce((target, cur, arr) => {
        return target + Number(cur.unit);
      }, 0);
    }
  },
  watch: {
    orderInfo: {
      handler(val) {
        console.log('-> val', val);
        console.log(S.isEmptyObject(val));
        if (val.items && val.items.length) {
          this.formData.consignee_info = val.consignee_info;
          this.currentInfo = val;
          console.log('-> currentInfo', this.currentInfo);
          this.goods_list = val.items
            .filter(item => Number(item.wait_purchase_num) > 0)
            .map(item => {
              return {
                ...item,
                wait_purchase_num: Number(item.wait_purchase_num),
                // unit: Number(item.num) - Number(item.has_purchase_num)
                unit: null,
                checked_spu_list: []
              };
            });
          if (val.is_confirm_new === '1') {
            this.goods_list = this.goods_list.filter(item => item.type !== 'SERVICE');
          }
          this.formData.clinic_name = val.clinic_info.name;
          this.formData.clinic_id = val.clinic_info.id;

          this.formData.remark = val.remark;
          console.log('-> goods_list', this.goods_list);
          if (val.consignee_info.county.code) {
            this.selectedAddress = [
              val.consignee_info.prov.code,
              val.consignee_info.city.code,
              val.consignee_info.county.code
            ];
          } else {
            this.selectedAddress = [val.consignee_info.prov.code, val.consignee_info.city.code];
          }
        }
      }
    }
  },
  created() {},
  mounted() {},
  methods: {
    //取消设置
    cancelSet() {
      this.set_spu_list = [];
      this.batchVisible = false;
    },
    //设置商品采购规格
    setSpu() {
      let specs_obj = {};
      const temList = this.set_spu_list.filter(item => item.unit);
      console.log('-> temList', temList);
      const amount = temList.reduce((tar, cur) => {
        specs_obj[cur.spu] = cur.unit;
        return tar + cur.unit;
      }, 0);
      console.log('-> amount', amount, this.select_goods_list[this.editIndex], specs_obj);
      if (amount > this.select_goods_list[this.editIndex].wait_purchase_num) {
        this.$Message.error(`采购数量不能大于待采购数量`);
        return;
      }
      this.select_goods_list[this.editIndex].checked_spu_list = temList;
      this.select_goods_list[this.editIndex].specs = specs_obj;
      this.select_goods_list[this.editIndex].unit = this.setUnit;
      this.batchVisible = false;
    },
    changeSpuUnit(item, index) {
      if (item.unit > Number(item.can_num)) {
        this.$Message.warning(`最大可采${item.max_num}件，剩余${item.can_num}件可采`);
        this.$nextTick(() => {
          this.set_spu_list[index].unit = Number(item.can_num);
        });
      }
    },
    setBatch(row, index) {
      console.log('-> row,index', row, index);
      this.editIndex = index;
      this.editRow = row;
      this.batchVisible = true;
      this.set_spu_list = row.spu_list.map(item => {
        console.log('-> item', item);
        if (row.checked_spu_list.length) {
          row.checked_spu_list.map(sub => {
            console.log('-> sub', sub);
            if (sub.spu === item.spu) {
              item.unit = sub.unit;
            }
          });
        }
        return { ...item, unit: item.unit || 0 };
      });
    },
    submitForm(name) {
      this.$refs[name].validate(valid => {
        if (valid) {
          console.log(this.select_goods_list);
          if (!this.select_goods_list.length) {
            this.$Message.error('请选择要发货的商品');
            return;
          }
          let isOk = true;
          const item_list = this.select_goods_list.map(item => {
            console.log(item);
            if (!item.unit || Number(item.unit) <= 0) {
              this.$Message.error(`商品${item.name}数量不能为0`);
              isOk = false;
            }
            if (item.unit > item.num - item.has_purchase_num) {
              this.$Message.error(`商品${item.name}数量不能大于未建单数`);
              isOk = false;
            }
            if (item)
              return {
                spu_code: item.spu_code,
                num: item.unit
              };
          });

          if (
            !this.formData.consignee_info.prov.name ||
            !this.formData.consignee_info.city.name ||
            !this.formData.consignee_info.detail
          ) {
            this.$Message.error('请完善收件地址');
            return;
          }
          if (!isOk) return;
          const temObj = this.select_goods_list.filter(item => item.checked_spu_list.length);
          let spec_map = {};
          temObj.map(item => {
            console.log('-> item', item);
            spec_map[item.spu_code] = item.specs;
          });
          this.formData.item_list = item_list;
          this.formData.spec_map = spec_map;
          console.log('formData', this.formData);
          this.$api.editPurchaseOrder(this.formData).then(
            res => {
              console.log(res);
              this.$Message.success('新建采购单成功');
              this.$parent.getDetail();
              this.closeModal(false);
            },
            err => this.$Message.error(err.errmsg)
          );
        } else {
          this.$Message.error('请完善表单信息');
        }
      });
    },
    changeNum(e, index) {
      console.log('-> e', e);
      this.select_goods_list[index].unit = e;
    },
    changeBuyNum(row, index) {
      console.log('-> row', row);
      if (row.buy_info?.type === 'MIN' && row.unit && row.unit < Number(row.buy_info?.num)) {
        this.changeNum(Number(row.buy_info?.num), index);
      } else if (row.buy_info?.type === 'TIMES') {
        // 起购倍数
        console.log('-> 起购倍数');
        if (row.unit && row.unit < Number(row.buy_info.num)) {
          // 小于起购倍数
          this.changeNum(Number(row.buy_info.num), index);
        } else if (row.unit && row.unit > Number(row.buy_info.num)) {
          // 大于起购倍数但不为整数倍
          let times = Math.round($operator.divide(row.unit, Number(row.buy_info.num), 3));
          let unitNum = $operator.multiply(times, Number(row.buy_info.num));
          this.changeNum(unitNum, index);
        }
      }
    },
    deleteGood(row, index) {
      if (this.is_non_direct_up === '1' && row.group_id === 'door_material') {
        let allDirectGoodLength = this.select_goods_list.filter(item => item.group_id === 'door_material').length;
        console.log('=>(purchase-modal.vue:432) allDirectGoodLength', allDirectGoodLength);
        // 场景：一起采购的商品中的个别商品退回后，再采购，不再提示
        if (allDirectGoodLength > 1) {
          // 默认场景：删除提示5s
          this.$Message.info({
            content: `${this.allDirectGood()}必须一起采购`,
            duration: 5
          });
        }
        this.select_goods_list = this.select_goods_list.filter(item => item.group_id !== 'door_material');
      } else {
        // 默认删除逻辑
        this.select_goods_list.splice(index, 1);
      }
    },
    handleSummary({ columns, data }) {
      console.log('-> data', data);
      const sums = {};
      columns.forEach((column, index) => {
        const key = column.key;
        if (index === 0) {
          sums[key] = {
            key,
            value: '合计'
          };
          return;
        }
        if (index === 1 || index === 2 || index === 3 || index === 5) {
          sums[key] = {
            key,
            value: ''
          };
          return;
        }
        const values = data.map(item => Number(item[key]));
        if (!values.every(value => isNaN(value))) {
          const v = values.reduce((prev, curr) => {
            const value = Number(curr);
            if (!isNaN(value)) {
              return prev + curr;
            } else {
              return prev;
            }
          }, 0);
          sums[key] = {
            key,
            value: v
          };
        } else {
          sums[key] = {
            key,
            value: ''
          };
        }
      });

      return sums;
    },
    //地区选择
    regionChange(address) {
      console.log('-> address', address);
      if (address.length) {
        console.log(CodeToText[address[0]]);
        const province = {
          name: CodeToText[address[0]],
          code: address[0]
        };
        const city = {
          name: CodeToText[address[1]],
          code: address[1]
        };
        const area = {
          name: CodeToText[address[2]],
          code: address[2]
        };
        console.log(province, city, area);
        this.formData.consignee_info.prov = province;
        this.formData.consignee_info.city = city;
        this.formData.consignee_info.county = area;
      } else {
        this.formData.consignee_info.prov = { name: '', code: '' };
        this.formData.consignee_info.city = { name: '', code: '' };
        this.formData.consignee_info.county = { name: '', code: '' };
      }
    },
    selectGoods(val) {
      let isExit = this.select_goods_list.findIndex(item => item.spu_code === val.value);
      if (isExit > -1) {
        this.$Message.error('请勿重复添加');
        return;
      }
      let group_id = this.goods_list.find(item => item.spu_code === val.value).group_id;
      if (this.is_non_direct_up === '1' && group_id === 'door_material') {
        this.handleSelectAllDirectUpGood(val);
      } else {
        // 默认单选商品逻辑
        this.handleSelectGood(val);
      }
      this.$refs.goodsSelect.clearSingleSelect();
    },
    handleSelectGood(val) {
      const addItem = this.goods_list.filter(item => item.spu_code === val.value);
      const copy_item = this._.cloneDeep(addItem);
      console.log('-> addItem', addItem);
      this.select_goods_list = this.select_goods_list.concat(copy_item);
    },
    handleSelectAllDirectUpGood(val) {
      const addItem = this.goods_list.filter(item => item.group_id === 'door_material');
      addItem.forEach(item => {
        item.unit = item.wait_purchase_num;
      });
      const copy_item = this._.cloneDeep(addItem);
      console.log('-> addItem', addItem);
      this.select_goods_list = this.select_goods_list.concat(copy_item);
      // 场景：一起采购的商品中的个别商品退回后，再采购，不再提示
      if (addItem.length > 1) {
        // 默认场景：选择商品提示5s
        this.$Message.info({
          content: `${this.allDirectGood()}必须一起采购`,
          duration: 5
        });
      }
    },
    // 诊所类型为非直营（升级）的诊所，部分商品必须一起采购
    allDirectGood() {
      return this.goods_list
        .filter(item => item.group_id === 'door_material')
        .map(item => {
          return item.name;
        })
        .join(',');
    },
    //关闭弹窗
    closeModal(show) {
      console.log('-> show', show);
      this.select_goods_list = [];
      this.select_good = '';
      if (!show) {
        this.$emit('update:showModal', show);
      }
    }
  }
};
</script>

<style lang="less" scoped>
.header {
  padding-top: 30px;
  padding-bottom: 10px;
  z-index: 5;
}
.spu-box {
  flex-wrap: wrap;

  .spu-items {
    align-items: center;
    width: 50%;
    margin: 10px 0;
  }
}
.table-wrapper {
  padding: 0px 0 30px;
  position: relative;
  ::v-deep .ivu-table-wrapper {
    overflow: unset;
  }
}
::v-deep .sticky-table-head {
  .ivu-table {
    overflow: unset !important;
    .ivu-table-header {
      position: sticky;
      top: 72px;
      z-index: 1;
    }
  }
}
::v-deep .custom-body-modal {
  .ivu-modal {
    .ivu-modal-body {
      padding: 0 30px 0 !important;
    }
  }
}
.custom-addWrap {
  flex-direction: column;

  .addressInput {
    margin-top: 20px;
  }
}
</style>
<style lang="less">
.addWrap {
  display: flex;
  align-items: center;

  .addressBox {
    div.rg-select div.rg-select__el div.rg-select__content {
      padding: 0 30px 0px 15px !important;
      font-size: 12px;
    }
  }

  .addressInput {
    flex: 1;
  }
}

.spu-modal {
  .ivu-modal {
    .ivu-modal-body {
      min-height: 400px;
      max-height: 500px;
      overflow-y: auto;
    }
  }

  .ivu-input-number-input {
    text-align: center;
  }
}
</style>
