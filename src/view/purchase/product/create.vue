<template>
  <div>
    <Form ref="productRef" :label-width="180" :model="formData" :rules="ruleValidate" label-colon>
      <div class="block-header">基本信息</div>
      <div>
        <Row>
          <Col span="14">
            <FormItem label="采购主体类型" prop="type">
              <Radio-group v-model="formData.type" @on-change="typeChange">
                <Radio
                  :label="type_item.label"
                  :disabled="!!$route.query.order_code"
                  v-for="(type_item, type_index) in typeList"
                  :key="type_item + type_index"
                >
                  {{ type_item.value }}
                </Radio>
              </Radio-group>
            </FormItem>
          </Col>

          <Col span="14">
            <FormItem v-if="formData.type === 'CLI'" label="诊所" :prop="!order_code ? 'clinic_id' : ''">
              <Select
                v-model="formData.clinic_id"
                v-if="!order_code"
                filterable
                placeholder="请选择诊所"
                @on-select="selectClinic"
              >
                <Option v-for="(item, key) in clinic_list" :key="item.id" :value="item.id">{{ item.name }}</Option>
              </Select>
              <Input v-else :value="clinic_name" disabled />
            </FormItem>
          </Col>
          <Col span="14">
            <FormItem label="营业执照名称" required>
              {{ clinicInfo.organization_name || '-' }}
            </FormItem>
          </Col>
          <Col span="14">
            <FormItem label="诊所类型" required>
              {{ clinicInfo.clinic_type_desc || '-' }}
            </FormItem>
          </Col>
          <Col span="14">
            <FormItem label="付款方类型" prop="opc_payer_type">
              <Radio-group v-model="formData.opc_payer_type" @on-change="changeOpcPayerType">
                <Radio
                  :label="item.id"
                  :disabled="!!$route.query.order_code"
                  v-for="(item, item_index) in opcPayerTypeDesc"
                  :key="item.id"
                >
                  {{ item.desc }}
                </Radio>
              </Radio-group>
            </FormItem>
          </Col>
          <Col span="14">
            <FormItem v-if="formData.opc_payer_type === 'CLI'" label="诊所预付款余额支付" prop="cli_price">
              <InputNumber
                v-model="formData.cli_price"
                :precision="2"
                :active-change="false"
                :min="0"
                :max="100000000"
                style="width: 100%"
                placeholder="请输入诊所预付款余额支付金额"
                @on-change="changeAdvance"
              />
            </FormItem>
          </Col>
          <Col :span="10" v-if="formData.opc_payer_type === 'CLI' && !!formData.clinic_id">
            <div class="balance-box">
              预付款余额：
              <k-link
                v-if="showRechargeButton('/finance/balance/detail')"
                :to="{
                  path: '/finance/balance/detail',
                  query: {
                    id: clinicInfo.wallet_id
                  }
                }"
                target="_blank"
                class="balance"
                >{{ clinicInfo.balance || 0 }}
              </k-link>

              <span v-else>{{ clinicInfo.balance || 0 }}</span>
              元
              <Button
                class="ml-4"
                size="small"
                type="primary"
                v-if="showRechargeButton('/finance/recharge/edit')"
                :to="{
                  path: '/finance/recharge/edit',
                  query: {
                    payer_type: 'CLI',
                    payer_id: clinicInfo.wallet_ent_id
                  }
                }"
                target="_blank"
                >去充值
              </Button>
            </div>
          </Col>
          <Col span="14">
            <FormItem
              :label-width="190"
              label="直营运营中心预付款余额支付"
              prop="com_price"
              v-if="formData.opc_payer_type === 'OPC'"
            >
              <InputNumber
                v-model="formData.com_price"
                :precision="2"
                :active-change="false"
                :min="0"
                :max="100000000"
                style="width: 100%"
                placeholder="请输入直营运营中心预付款余额"
                @on-change="changeAdvance"
              />
            </FormItem>
          </Col>
          <Col :span="10" v-if="formData.opc_payer_type === 'OPC'">
            <div class="balance-box">
              预付款余额：
              <k-link
                v-if="showRechargeButton('/finance/balance/detail')"
                :to="{
                  path: '/finance/balance/detail',
                  query: {
                    id: opcenterInfo.wallet_id
                  }
                }"
                target="_blank"
                class="balance"
                >{{ opcenterInfo.balance || 0 }}
              </k-link>
              <span v-else>{{ opcenterInfo.balance || 0 }} </span>
              元
              <Button
                class="ml-4"
                size="small"
                type="primary"
                v-if="showRechargeButton('/finance/recharge/edit')"
                :to="{
                  path: '/finance/recharge/edit',
                  query: {
                    payer_type: 'OPC',
                    payer_id: opcenterInfo.wallet_ent_id
                  }
                }"
                target="_blank"
                >去充值
              </Button>
            </div>
          </Col>
          <Col span="14">
            <FormItem label="收件人姓名" prop="consignee_info.consignee">
              <Input v-model="formData.consignee_info.consignee" placeholder="收件人姓名"></Input>
            </FormItem>
          </Col>
          <Col span="14">
            <FormItem label="收件人手机号" prop="consignee_info.mobile">
              <Input v-model="formData.consignee_info.mobile" placeholder="收件人手机号"></Input>
            </FormItem>
          </Col>
          <Col span="14">
            <FormItem label="收件地址" required>
              <div class="addWrap">
                <div class="addressBox" style="width: 55%">
                  <!--									<v-region v-model="selectedAddress" @values="regionChange"></v-region>-->
                  <el-cascader
                    v-model="selectedAddress"
                    :options="options"
                    clearable
                    popper-class="address-com"
                    placeholder="请选择收件地址"
                    size="small"
                    style="width: 100%"
                    @change="regionChange"
                  >
                  </el-cascader>
                </div>
                <div class="addressInput ml10">
                  <Input v-model="formData.consignee_info.detail" placeholder="详细地址"></Input>
                </div>
              </div>
            </FormItem>
          </Col>
          <Col span="14">
            <FormItem label="备注">
              <Input v-model="formData.remark" autosize type="textarea" />
            </FormItem>
          </Col>
        </Row>
      </div>
    </Form>
    <div class="product-list" style="position: relative">
      <div class="custom-sticky custom-block-header-wrapper" style="top: 56px">
        <div class="block-header" style="margin: 0px">商品清单</div>
      </div>
      <div class="table-wrapper">
        <Table
          class="sticky-table-head"
          :columns="formData.opc_payer_type === 'OPC' ? tableColumns_opc : tableColumns_cli"
          :data="goods_items"
          :loading="tableLoading"
          :summary-method="handleSummary"
          border
          show-summary
        >
          <template slot-scope="{ row, index }" slot="img">
            <viewer :images="[row.img]" v-if="row.img" class="flex flex-item-center">
              <img style="width: 80px; height: auto" :src="row.img | imageStyle('B.w300')" class="image" />
            </viewer>
            <span v-else>-</span>
          </template>
        </Table>
      </div>
    </div>
    <div class="fixed-bottom-wrapper">
      <Button @click="$router.back()">返回</Button>
      <Button style="margin-left: 20px" type="primary" @click="submitForm('productRef')" :loading="submitLoading"
        >提交</Button
      >
    </div>

    <div class="block_45"></div>
  </div>
</template>

<script>
import { CodeToText, regionData } from '@/utils/chinaMap';
import S from 'utils/util';

const init_form_data = {
  type: 'CLI', // 主体采购类型,
  clinic_id: '',
  remark: '',
  consignee_info: {
    city: { code: '', name: '' },
    prov: { code: '', name: '' },
    county: { code: '', name: '' },
    detail: '',
    mobile: '',
    consignee: ''
  },
  opc_payer_type: '',
  cli_price: null,
  com_price: null
};
export default {
  name: 'create',
  data() {
    const validateMobile = (rule, value, callback) => {
      if (value === '') {
        callback(new Error('请输入正确的手机号码'));
      } else {
        const reg = /^1[3456789]\d{9}$/;
        if (!reg.test(value)) {
          callback(new Error('请输入正确的手机号码'));
        }
        callback();
      }
    };
    const validateJoininFee = (rule, value, callback) => {
      if (!value) {
        if (this.formData.opc_payer_type === 'CLI') {
          callback(new Error('请输入诊所预付款余额支付金额'));
        } else if (this.formData.opc_payer_type === 'OPC') {
          callback(new Error('请输入直营运营中心预付款余额'));
        }
      } else {
        callback();
      }
    };
    return {
      options: regionData,
      typeList: [{ value: '诊所', label: 'CLI' }], // 主体采购类型枚举
      formData: {
        ...init_form_data
      },
      tableLoading: false,
      selectedAddress: [],
      prodTypes: {},
      tableCols: [
        { title: '单号', key: 'code', align: 'center' },
        { title: '付款方', key: 'payer_name', align: 'center' },
        { title: '收款方', key: 'payee_name', align: 'center' },
        { title: '类型', key: 'biz_type_text', align: 'center' },
        { title: '收支类型', key: 'type_text', align: 'center' },
        { title: '支付方式', key: 'pay_type_test', align: 'center' },
        { title: '金额(元)', key: 'amount', align: 'center' }
      ],
      tableColumns_cli: [
        { title: '序号', type: 'index', align: 'center' },
        { title: '商品编号', key: 'spu_code', align: 'center' },
        { title: '商品', key: 'name', align: 'center' },
        { title: '介绍图', slot: 'img', key: 'img', align: 'center' },
        { title: '类型', key: 'type_text', align: 'center' },
        { title: '诊所进货价', key: 'single_cli_price', align: 'center' },
        // { title: '出厂价', key: 'single_com_price', align: 'center' },
        { title: '数量', key: 'num', align: 'center' },
        { title: '进货总价(元)', key: 'cli_price', align: 'center' }
        // { title: '出厂总价(元)', key: 'com_price', align: 'center' }
      ],
      tableColumns_opc: [
        { title: '序号', type: 'index', align: 'center' },
        { title: '商品编号', key: 'spu_code', align: 'center' },
        { title: '商品', key: 'name', align: 'center' },
        { title: '介绍图', slot: 'img', key: 'img', align: 'center' },
        { title: '类型', key: 'type_text', align: 'center' },
        { title: '诊所进货价', key: 'single_com_price', align: 'center' },
        // { title: '出厂价', key: 'single_com_price', align: 'center' },
        { title: '数量', key: 'num', align: 'center' },
        { title: '进货总价(元)', key: 'com_price', align: 'center' }
        // { title: '出厂总价(元)', key: 'com_price', align: 'center' }
      ],
      ruleValidate: {
        'consignee_info.consignee': [{ required: true, message: '请填写收件人姓名', trigger: 'change' }],
        'consignee_info.mobile': [{ required: true, trigger: 'change', validator: validateMobile }],
        clinic_id: [{ required: true, trigger: 'change', message: '请选择诊所' }],
        opc_payer_type: [{ required: true, trigger: 'change', message: '请选择付款方类型' }],
        type: [{ required: true, trigger: ['blur', 'change'], message: '请选择采购主体类型' }],
        cli_price: [{ required: true, trigger: 'change', type: 'number', message: '请输入诊所预付款余额支付金额' }],
        com_price: [{ required: true, trigger: 'change', type: 'number', message: '请输入直营运营中心预付款余额' }]
      },
      account_details: [],
      clinic_list: [],
      goods_items: [],
      order_code: '',
      clinic_name: '',

      submitLoading: false, // 提交的loading
      opcPayerTypeDesc: [],
      opcenterInfo: { balance: null },
      clinicInfo: { balance: null }
    };
  },
  computed: {
    showRechargeButton() {
      return path => {
        return S.rootPageCheck(path);
      };
    }
  },
  watch: {},
  created() {
    const { order_code } = this.$route.query;
    if (order_code) {
      this.order_code = order_code;
      this.getPcgInfo({ order_code });
    } else {
      this.getCreateInfo();
    }
    this.getPackageOptions();
  },
  mounted() {},
  methods: {
    // 采购主体类型切换
    typeChange(val) {
      // 当前确定的方案是，当诊所和养疗馆切换时，所有的数据清除
      this.goods_items = [];
      this.account_details = [];
      this.formData.clinic_id = '';
      this.selectedAddress = [];
      this.formData.consignee_info = {
        city: { code: '', name: '' },
        prov: { code: '', name: '' },
        county: { code: '', name: '' },
        detail: '',
        mobile: '',
        consignee: ''
      };
      this.$refs['productRef'].resetFields();
      this.formData.type = val;
    },
    getPcgInfo(order_code) {
      this.$api.getPackageInfo(order_code).then(res => {
        this.clinic_list = [res.clinic_info];
        this.formData.type = res.ent_type;
        this.clinic_name = res.clinic_info.name;
        this.account_details = res.account_details;
        this.formData.clinic_id = res.clinic_info.id;
        this.formData.remark = res.remark;
        this.formData.opc_payer_type = res.opc_payer_type;
        this.formData.cli_price = Number(res.cli_price);
        this.formData.com_price = Number(res.com_price);
        this.clinicInfo = res.clinic_info;
        this.clinicInfo.organization_name = res.organization_name;
        this.clinicInfo.clinic_type_desc = res.clinic_type_desc;
        this.goods_items = res.items;
        this.opcenterInfo = res.opc_info || {};
        console.log(res.consignee_info.city);
        if (res.consignee_info.county.code) {
          this.selectedAddress = [
            res.consignee_info.prov.code,
            res.consignee_info.city.code,
            res.consignee_info.county.code
          ];
        } else {
          this.selectedAddress = [res.consignee_info.prov.code, res.consignee_info.city.code];
        }
        console.log('-> this.selectedAddress', this.selectedAddress);
        this.formData.consignee_info = res.consignee_info;
      });
    },
    getPackageOptions() {
      this.$api
        .getPackageOptions()
        .then(res => {
          console.log('=>(detail.vue:242) res', res);
          this.opcPayerTypeDesc = S.descToArrHandle(res.opcPayerTypeDesc).filter(item => item.id !== 'OPC');
        })
        .catch(err => this.$Message.error(err.errmsg));
    },
    selectClinic({ value }) {
      console.log('=>(create.vue:285) value', value);
      this.clinicInfo = this.clinic_list.find(item => item.id === value);
      this.getCreateInfo(value);
    },
    //地区选择
    regionChange(address) {
      console.log('-> address', address);
      if (address.length) {
        console.log(CodeToText[address[0]]);
        const province = {
          name: CodeToText[address[0]],
          code: address[0]
        };
        const city = {
          name: CodeToText[address[1]],
          code: address[1]
        };
        const area = {
          name: CodeToText[address[2]],
          code: address[2]
        };
        console.log(province, city, area);
        this.formData.consignee_info.prov = province;
        this.formData.consignee_info.city = city;
        this.formData.consignee_info.county = area;
      } else {
        this.formData.consignee_info.prov = { name: '', code: '' };
        this.formData.consignee_info.city = { name: '', code: '' };
        this.formData.consignee_info.county = { name: '', code: '' };
      }
    },
    handleSummary({ columns, data }) {
      console.log('-> data', data);
      const sums = {};
      columns.forEach((column, index) => {
        const key = column.key;
        if (index === 0) {
          sums[key] = {
            key,
            value: '合计'
          };
          return;
        }
        if (index === 1 || index === 3 || index === 4 || index === 5 || index === 6) {
          sums[key] = {
            key,
            value: ''
          };
          return;
        }
        const values = data.map(item => Number(item[key]));
        if (!values.every(value => isNaN(value))) {
          const v = values.reduce((prev, curr) => {
            const value = Number(curr);
            if (!isNaN(value)) {
              return prev + curr;
            } else {
              return prev;
            }
          }, 0);
          if (key == 'num') {
            sums[key] = {
              key,
              value: v
            };
          } else {
            sums[key] = {
              key,
              value: v.toFixed(2)
            };
          }
        } else {
          sums[key] = {
            key,
            value: ''
          };
        }
      });
      return sums;
    },
    getCreateInfo(clinic_id) {
      console.log('-> clinic_id', clinic_id);
      let params = {};
      if (clinic_id) {
        params.clinic_id = clinic_id;
      }

      this.$api.getCreatInfo(params).then(res => {
        console.log('-> res', res);

        if (params.clinic_id) {
          this.account_details = res.account_details;
          this.goods_items = res.items;
          this.formData.consignee_info = res.consignee_info;

          if (res.consignee_info.county.code) {
            this.selectedAddress = [
              res.consignee_info.prov.code,
              res.consignee_info.city.code,
              res.consignee_info.county.code
            ];
          } else {
            this.selectedAddress = [res.consignee_info.prov.code, res.consignee_info.city.code];
          }
        } else {
          this.clinic_list = res.clinic_list;
          this.goods_items = res.items;
          this.opcenterInfo = res.opcenter;
        }

        if (this.formData.cli_price > 0 || this.formData.com_price > 0) {
          this.getCreatePackages();
        }
      });
    },
    submitForm(name) {
      this.$refs[name].validate(valid => {
        if (valid) {
          // TODO 返回地址信息不全 待后端修复后打开
          // if (!this.formData.consignee_info.prov.code || !this.formData.consignee_info.city.code || !this.formData.consignee_info.county.code || !this.formData.consignee_info.detail) {
          // 	this.$Message.error('请完善收件地址')
          // 	return
          // }
          if (
            !this.formData.consignee_info.prov.name ||
            !this.formData.consignee_info.city.name ||
            !this.formData.consignee_info.detail
          ) {
            this.$Message.error('请完善收件地址');
            return;
          }
          if (this.order_code) {
            const params = this.formData;
            params.order_code = this.order_code;

            this.submitLoading = true;
            this.$api
              .editPackage(params)
              .then(
                res => {
                  console.log(res);
                  this.$Message.success('修改开业产品包成功');
                  this.formData = {
                    ...init_form_data
                  };
                  this.$router.replace('/purchase/product/list');
                },
                err => this.$Message.error(err.errmsg)
              )
              .finally(() => {
                this.submitLoading = false;
              });
          } else {
            this.submitLoading = true;
            this.$api
              .createPackage(this.formData)
              .then(
                res => {
                  this.$Message.success('创建开业产品包成功');
                  this.formData = {
                    ...init_form_data
                  };
                  this.$router.replace('/purchase/product/list');
                },
                err => this.$Message.error(err.errmsg)
              )
              .finally(() => {
                this.submitLoading = false;
              });
          }
        } else {
          this.$Message.error('请完善表单信息');
        }
      });
    },
    // 更改预付款金额，拉取商品列表
    changeAdvance(val) {
      if (val) {
        this.getCreatePackages(val);
      }
    },
    getCreatePackages() {
      const params = {
        cli_price: this.formData.cli_price || '',
        com_price: this.formData.com_price || '',
        clinic_id: this.formData.clinic_id
      };
      this.$api.getPackageItems(params).then(res => {
        console.log('%c=>(create.vue:337) res', 'font-size: 18px;color: #FF7043 ;', res);
        this.goods_items = res.items;
      });
    },
    changeOpcPayerType(val) {
      console.log('=>(create.vue:584) val', val);
      this.formData.cli_price = null;
      this.formData.com_price = null;
      this.goods_items = [];
    }
  }
};
</script>

<style lang="less" scoped>
.record-table {
  :deep(.ivu-table-header .ivu-table-column-center) {
    line-height: normal;
  }

  :deep(.ivu-table-column-center) {
    height: 32px;
  }
}

.custom-block-header-wrapper {
  position: sticky;
  padding-bottom: 20px;
  background: #fff;
}

.table-wrapper {
  position: relative;

  ::v-deep .ivu-table-wrapper {
    overflow: unset;
  }
}

.sticky-table-head {
  ::v-deep .ivu-table {
    overflow: unset;

    .ivu-table-header {
      position: sticky;
      top: 113px;
      z-index: 3;
    }
  }
}
</style>
<style lang="less">
.addWrap {
  display: flex;
  align-items: center;

  .addressBox {
    // /deep/ .rg-select__el{
    //   /deep/ .rg-select__content {
    //     padding: 0 30px 0px 15px !important;
    //   }
    // }
    div.rg-select div.rg-select__el div.rg-select__content {
      padding: 0 30px 0px 15px !important;
    }
  }

  .addressInput {
    flex: 1;
  }
}
.balance-box {
  line-height: 32px;
  margin-left: 12px;

  .balance {
    color: #1157e5;
  }
}
</style>
