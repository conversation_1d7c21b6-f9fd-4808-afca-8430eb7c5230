<template>
  <div class="detail-wrapper">
    <h3 class="f-title">基础信息</h3>
    <div class="basic-info">
      <!--			<div class="basic-info-item" style="justify-content: center;background: #cccccc;">-->
      <!--				采购单信息-->
      <!--			</div>-->
      <div class="basic-info-item">
        <span class="item-label">编号</span>
        <span class="item-content">{{ order_code }}</span>
      </div>
      <div class="basic-info-item">
        <span class="item-label">状态</span>
        <span class="item-content">
          {{ status_text }}
          <span style="color: red" v-if="status === 'REJECTED'">( 驳回原因：{{ reject_reason || '-' }} )</span>
        </span>
      </div>
      <!--			<div class="basic-info-item">-->
      <!--				<span class="item-label">发货状态</span>-->
      <!--				<span class="item-content">{{ status }}</span>-->
      <!--			</div>-->
      <div class="basic-info-item">
        <span class="item-label">采购主体</span>
        <span class="item-content" v-if="ent_type == 'CLI'">{{ clinic_info.name }}</span>
      </div>
      <div class="basic-info-item">
        <span class="item-label">营业执照名称</span>
        <span class="item-content">{{ orderInfo.organization_name || '-' }}</span>
      </div>
      <div class="basic-info-item">
        <span class="item-label">主体类型</span>
        <span class="item-content">{{ ent_type_text }}</span>
      </div>
      <div class="basic-info-item" v-if="ent_type === 'CLI'">
        <span class="item-label">诊所类型</span>
        <span class="item-content">
          <span v-if="clinic_direct_type_text && clinic_type_text">
            {{ clinic_direct_type_text }} / {{ clinic_type_text }}
          </span>
          <span v-else>-</span>
        </span>
      </div>
      <div class="basic-info-item">
        <span class="item-label">收件人姓名</span>
        <span class="item-content">{{ consignee_info.consignee }}</span>
      </div>
      <div class="basic-info-item">
        <span class="item-label">收件人手机号</span>
        <span class="item-content">{{ consignee_info.mobile }}</span>
      </div>
      <div class="basic-info-item">
        <span class="item-label">收货地址</span>
        <span class="item-content">{{ address }}</span>
      </div>
      <div class="basic-info-item">
        <span class="item-label">下单时间</span>
        <span class="item-content">{{ create_time }}</span>
      </div>
      <!--      <div class="basic-info-item">-->
      <!--        <span class="item-label">预付款收支明细</span>-->
      <!--        <span class="item-content">-->
      <!--          &lt;!&ndash; &ndash;&gt;-->
      <!--          <span v-for="(code, index) in account_detail_codes" :key="index">-->
      <!--            <KLink-->
      <!--              :to="{-->
      <!--                path: code.slice(0, 1) === 'I' ? '/finance/earnings/edit' : '/finance/expenses/edit',-->
      <!--                query: { code, type: code.slice(0, 1) === 'I' ? 'IN' : 'OUT' }-->
      <!--              }"-->
      <!--              target="_blank"-->
      <!--              style="margin-right: 20px"-->
      <!--              >{{ code }}</KLink-->
      <!--            >-->
      <!--            <span v-if="index === 0" style="margin-right: 20px">/</span>-->
      <!--          </span>-->
      <!--        </span>-->
      <!--      </div>-->
      <!--			<div class="basic-info-item">-->
      <!--				<span class="item-label">付款单号</span>-->
      <!--				<span class="item-content">-</span>-->
      <!--			</div>-->

      <div class="basic-info-item">
        <span class="item-label">{{ is_opc_pay ? '直营运营中心' : '诊所' }}支付金额</span>
        <span class="item-content">￥{{ echo_cli_price }}</span>
      </div>
      <div class="basic-info-item">
        <span class="item-label">备注</span>
        <span class="item-content">{{ remark || '-' }}</span>
      </div>
    </div>
    <div class="goods-list">
      <h3 class="f-title custom-sticky" style="top: 56px; padding-top: 10px">商品清单</h3>
      <div class="table-wrapper">
        <div class="panel-nav flex flex-item-between custom-sticky" style="top: 94px">
          <div>
            <a :class="{ active: !goods_status }" class="nav" @click.prevent.capture="onStatusChange('')"> 全部 </a>
            <a
              :class="{ active: goods_status == 'hasBuildOrder' }"
              class="nav"
              @click.prevent.capture="onStatusChange('hasBuildOrder')"
            >
              已采购
            </a>
            <a
              :class="{ active: goods_status == 'hastBuildOrder' }"
              class="nav"
              @click.prevent.capture="onStatusChange('hastBuildOrder')"
            >
              待采购
            </a>
            <a
              :class="{ active: goods_status == 'hasRefund' }"
              class="nav"
              @click.prevent.capture="onStatusChange('hasRefund')"
            >
              已退款
            </a>
          </div>
          <div class="creat-button" v-if="status !== ''">
            <Button
              v-if="status !== 'FINISHED' && status !== 'OP_AUDIT' && status !== 'WAIT_AUDIT' && status !== 'REJECTED'"
              type="default"
              @click="refundModalVisible = true"
              class="mr10"
              >申请退款
            </Button>
            <Button
              v-if="status !== 'FINISHED' && status !== 'OP_AUDIT' && status !== 'WAIT_AUDIT' && status !== 'REJECTED'"
              type="primary"
              @click="createModalVisible = true"
              >创建采购单
            </Button>
          </div>
        </div>
        <Table
          :columns="is_opc_pay ? tableColumns_op : tableColumns_cli"
          :data="goods_items"
          :summary-method="handleSummary"
          border
          show-summary
          class="sticky-table-head"
        >
          <template slot-scope="{ row, index }" slot="img">
            <viewer :images="[row.img]" v-if="row.img" class="flex flex-item-center">
              <img style="width: 80px; height: auto" :src="row.img | imageStyle" class="image" />
            </viewer>
            <span v-else>-</span>
          </template>

          <template slot-scope="{ row }" slot="cli_price">
            <span v-if="!goods_status"> {{ row.cli_price }}</span>
            <span v-else-if="goods_status === 'hasBuildOrder'">{{ row.has_purchase_price.cli }}</span>
            <span v-else-if="goods_status === 'hasRefund'">{{ row.refund_pass_price.cli }}</span>
            <span v-else>{{ row.wait_purchase_price.cli }}</span>
          </template>
          <template slot-scope="{ row }" slot="com_price">
            <span v-if="!goods_status"> {{ row.com_price }}</span>
            <span v-else-if="goods_status === 'hasBuildOrder'">{{ row.has_purchase_price.com }}</span>
            <span v-else-if="goods_status === 'hasRefund'">{{ row.refund_pass_price.com }}</span>
            <span v-else>{{ row.wait_purchase_price.com }}</span>
          </template>
          <template slot-scope="{ row }" slot="num">
            {{ getNum(row) }}
          </template>
        </Table>
      </div>
    </div>

    <div class="operator-box">
      <h3 class="f-title">操作记录</h3>
      <Table :columns="operatorColumns" :data="operatorRecordData" border>
        <template slot="create_time" slot-scope="{ row }">
          {{ row.create_time | date_format }}
        </template>
        <template slot="source" slot-scope="{ row }">
          {{ row.source || '-' }}
        </template>
        <template slot="operator_name" slot-scope="{ row }">
          <span v-if="row.operator_name"
            >{{ row.operator_name }}<span v-if="row.operator_role_name">({{ row.operator_role_name }})</span></span
          >
          <span v-else>-</span>
        </template>
        <template slot="remark" slot-scope="{ row }">
          {{ row.remark }}
        </template>
      </Table>
    </div>
    <div class="fixed-bottom-wrapper">
      <back-button></back-button>
      <Button v-if="isShowExamine" style="margin: 0 20px" type="error" @click="refuse">审核驳回 </Button>
      <Button v-if="isShowExamine" type="primary" @click="pass">审核通过 </Button>
    </div>
    <!-- 申请退款弹窗 -->
    <refund-modal :orderInfo="refund_info" :goodsList="goodsList" :showModal.sync="refundModalVisible"></refund-modal>
    <!-- 创建采购单  -->
    <purchase-modal
      :orderInfo="orderInfo"
      :is_non_direct_up="is_non_direct_up"
      :showModal.sync="createModalVisible"
    ></purchase-modal>
    <!-- 驳回弹窗 -->
    <Modal v-model="refuseModalVisible" :mask-closable="false" title="审核驳回">
      <div>
        <p>请输入驳回原因</p>
        <Input
          v-model="refuseText"
          class="mt6 refuse-input"
          style="width: 100%"
          placeholder="请输入驳回原因"
          type="textarea"
        />
      </div>

      <div slot="footer">
        <Button @click="refuseCancel">取消</Button>
        <Button type="primary" :loading="refuseLoading" @click="refuseOk">确定</Button>
      </div>
    </Modal>
    <ConfirmModal :confirmVisible.sync="confirmVisible" @ok="confirmFn"></ConfirmModal>
  </div>
</template>

<script>
import purchaseModal from './components/purchase-modal';
import refundModal from '../components/refund-modal';
import ConfirmModal from '_c/confirmModal/confirmModal';

export default {
  name: 'detail',
  mixins: [],
  components: { purchaseModal, refundModal, ConfirmModal },
  data() {
    return {
      tableColumns_cli: [
        { title: '序号', type: 'index', align: 'center' },
        { title: '商品编号', key: 'spu_code', align: 'center' },
        { title: '商品', key: 'name', align: 'center' },
        { title: '介绍图', slot: 'img', key: 'img', align: 'center' },
        { title: '类型', key: 'type_text', align: 'center' },
        { title: '诊所进货价', key: 'single_cli_price', align: 'center' },
        { title: '数量', slot: 'num', key: 'num', align: 'center' },
        { title: '进货总价(元)', slot: 'cli_price', key: 'cli_price', align: 'center' }
      ],
      tableColumns_op: [
        { title: '序号', type: 'index', align: 'center' },
        { title: '商品编号', key: 'spu_code', align: 'center' },
        { title: '商品', key: 'name', align: 'center' },
        { title: '介绍图', slot: 'img', key: 'img', align: 'center' },
        { title: '类型', key: 'type_text', align: 'center' },
        { title: '诊所进货价', key: 'single_com_price', align: 'center' },
        { title: '数量', slot: 'num', key: 'num', align: 'center' },
        { title: '进货总价(元)', slot: 'com_price', key: 'com_price', align: 'center' }
      ],
      orderInfo: {},
      goodsList: [], // 未发货商品
      refund_info: {}, // 申请退款数据
      createModalVisible: false,
      refundModalVisible: false, // 申请退款弹窗
      detailType: '',
      order_code: '',
      clinic_info: {}, //诊所信息
      address: '', //地址信息
      remark: '',
      status_text: '',
      status: '',
      reject_reason: '', // 驳回原因
      consignee_info: {},
      goods_status: '',
      goods_items: [],
      goods_items_copy: [],
      echo_cli_price: 0,
      echo_com_price: 0,
      cli_price: 0,
      com_price: 0,
      sumNum: 0,
      create_time: '',
      ent_type_text: '', // 主体类型
      ent_type: '',
      clinic_direct_type_text: '',
      clinic_type_text: '',
      account_detail_codes: '',
      is_non_direct_up: '',
      cp_id: '',
      is_cp_pay: '',
      // 操作记录
      operatorColumns: [
        { title: '时间', slot: 'create_time', align: 'center' },
        { title: '操作主体', slot: 'source', align: 'center' },
        { title: '操作人', slot: 'operator_name', align: 'center' },
        { title: '操作记录', slot: 'remark', align: 'center' }
      ],
      operatorRecordData: [],

      refuseModalVisible: false, // 审核弹窗是否显示的flag
      refuseText: '', // 驳回绑定的字段
      confirmVisible: false,
      refuseLoading: false,
      is_opc_pay: false // 是否由直营中心支付
    };
  },
  computed: {
    isShowExamine() {
      // 特殊数据的审核流程由省公司自己审核
      let { allow_com_audit_fin, status } = this.orderInfo;
      if (allow_com_audit_fin == '1' && status == 'WAIT_AUDIT') {
        return true;
      } else {
        return false;
      }
    },
    getNum(row) {
      return row => {
        if (this.goods_status == '') {
          return row.num;
        } else if (this.goods_status === 'hasBuildOrder') {
          return row.has_purchase_num;
        } else if (this.goods_status === 'hastBuildOrder') {
          // return Number(row.num) - row.has_purchase_num
          return Number(row.wait_purchase_num);
        } else if (this.goods_status === 'hasRefund') {
          return Number(row.refund_pass_num);
        }
      };
    }
  },
  watch: {
    refuseModalVisible(val) {
      if (!val) {
        this.refuseText = '';
      }
    }
  },
  created() {
    const { order_code, type } = this.$route.query;
    this.order_code = order_code;
    this.detailType = type;
    this.getDetail();
  },
  mounted() {},
  methods: {
    // 返回
    back() {
      this.$router.back();
    },
    refuse() {
      this.refuseModalVisible = true;
    },
    // 驳回弹窗事件
    refuseCancel() {
      this.refuseModalVisible = false;
    },
    // 审核驳回弹窗确定
    refuseOk() {
      let { refuseText } = this;
      let { order_code } = this;
      if (!refuseText) {
        this.$Message.error('请输入驳回原因');
        return;
      }
      this.setPackageStatus(order_code, 'REJECT', refuseText);
    },
    // 审核通过
    pass() {
      this.confirmVisible = true;
    },
    confirmFn() {
      let { order_code } = this;
      this.setPackageStatus(order_code, 'PASS');
    },

    // * api 更改状态
    setPackageStatus(code, act, reason = '') {
      let params = {
        order_code: code,
        act: act,
        reason: reason
      };
      if (act === 'REJECT') {
        this.refuseLoading = true;
      }
      this.$api
        .setPackageStatus(params)
        .then(
          res => {
            if (act === 'REJECT') {
              this.refuseModalVisible = false;
              this.$Message.success(`驳回成功`);
              this.back();
            }
            if (act === 'PASS') {
              this.$Message.success(`审核通过`);
              this.confirmVisible = false;
              this.back();
            }
          },
          rej => {
            this.$Message.error(rej.errmsg);
          }
        )
        .finally(() => {
          this.refuseLoading = false;
        });
    },

    getPackageOperationlog(id) {
      let params = {
        id,
        page: 1,
        pageSize: 200
      };
      this.$api
        .getPackageOperationlog(params)
        .then(res => {
          this.operatorRecordData = res.list;
        })
        .catch(err => this.$Message.error(err.errmsg));
    },
    showRefuseModal() {
      this.refuseModalVisible = true;
    },
    handleSummary({ columns, data }) {
      console.log('-> data', data);
      const sums = {};
      columns.forEach((column, index) => {
        const key = column.key;
        if (index === 0) {
          sums[key] = {
            key,
            value: '合计'
          };
          return;
        }
        if (index === 1 || index === 2 || index === 3 || index === 6 || index === 4 || index === 5) {
          sums[key] = {
            key,
            value: ''
          };
          return;
        }
        if (key === 'cli_price') {
          sums[key] = {
            key,
            value: this.cli_price
          };
          return;
        }
        if (key === 'com_price') {
          sums[key] = {
            key,
            value: this.com_price
          };
          return;
        }
        if (key === 'num') {
          sums[key] = {
            key,
            value: this.sumNum
          };
          return;
        }
        const values = data.map(item => Number(item[key]));
        if (!values.every(value => isNaN(value))) {
          const v = values.reduce((prev, curr) => {
            const value = Number(curr);
            if (!isNaN(value)) {
              return prev + curr;
            } else {
              return prev;
            }
          }, 0);
          if (key == 'num') {
            sums[key] = {
              key,
              value: v
            };
          }
        } else {
          sums[key] = {
            key,
            value: ''
          };
        }
      });
      return sums;
    },
    onStatusChange(status) {
      console.log('-> status', status);
      this.goods_status = status;
      let cli_price = 0;
      let com_price = 0;
      let sumNum = 0;

      if (!status) {
        this.goods_items = this.goods_items_copy;
        this.goods_items.map(item => {
          cli_price += Number(item.cli_price);
          com_price += Number(item.com_price);
          sumNum += Number(item.num);
          this.cli_price = cli_price.toFixed(2);
          this.com_price = com_price.toFixed(2);
          this.sumNum = sumNum;
        });
      }
      if (status === 'hasBuildOrder') {
        this.goods_items = this.goods_items_copy.filter(item => Number(item.has_purchase_num) > 0);
        this.goods_items.map(item => {
          cli_price += Number(item.has_purchase_price.cli);
          com_price += Number(item.has_purchase_price.com);
          sumNum += Number(item.has_purchase_num);
          this.cli_price = cli_price.toFixed(2);
          this.com_price = com_price.toFixed(2);
          this.sumNum = sumNum;
        });
      } else if (status === 'hastBuildOrder') {
        this.goods_items = this.goods_items_copy.filter(item => Number(item.wait_purchase_num) > 0);
        this.goods_items.map(item => {
          cli_price += Number(item.wait_purchase_price.cli);
          com_price += Number(item.wait_purchase_price.com);
          sumNum += Number(item.wait_purchase_num);
          this.cli_price = cli_price.toFixed(2);
          this.com_price = com_price.toFixed(2);
          this.sumNum = sumNum;
        });
      } else if (status === 'hasRefund') {
        // 已退款
        this.goods_items = this.goods_items_copy.filter(item => Number(item.refund_pass_num) > 0);
        console.log('this.goods_items', this.goods_items);
        this.goods_items.map(item => {
          cli_price += Number(item.refund_pass_price.cli);
          com_price += Number(item.refund_pass_price.com);
          sumNum += Number(item.refund_pass_num);
          this.cli_price = cli_price.toFixed(2);
          this.com_price = com_price.toFixed(2);
          this.sumNum = sumNum;
        });
      }
    },
    // 处理申请退款得数据
    handleRefundInfo(res) {
      let refund_obj = {
        clinic_name: res.clinic_info.name || '',
        cli_price: '',
        com_price: '',
        items: [],
        refund_info: '',
        remark: '',
        ent_type: res.ent_type,
        ent_type_text: res.ent_type_text,
        is_opc_pay: res.is_opc_pay === '1'
      };
      return refund_obj || {};
    },
    getDetail() {
      this.$api.getPackageInfo({ order_code: this.order_code }).then(res => {
        console.log(res);
        this.getPackageOperationlog(res.id);
        for (const spu in res.spu_spec_map) {
          console.log('-> spu', spu);
          res.items.map(item => {
            if (item.spu_code === spu) {
              console.log(item);
              item.spu_list = res.spu_spec_map[spu];
            }
          });
        }
        res.items.map(item => {
          if (!item.spu_list) {
            item.spu_list = [];
          }
        });
        this.$router.replace({ query: { ...this.$route.query, pack_order_id: res.id } });
        this.orderInfo = res;
        this.clinic_info = res.clinic_info;
        this.ent_type_text = res.ent_type_text;
        this.ent_type = res.ent_type;
        this.account_details = res.account_details;
        this.consignee_info = res.consignee_info;
        this.remark = res.remark;
        this.reject_reason = res.reject_reason;
        this.status_text = res.status_text;
        this.status = res.status;
        this.create_time = res.create_time;
        this.cli_price = res.cli_price;
        this.echo_cli_price = res.is_opc_pay === '1' ? res.com_price : res.cli_price;
        this.com_price = res.com_price;
        this.echo_com_price = res.com_price;
        this.goods_items = res.items;
        this.refund_info = this.handleRefundInfo(res);
        this.goodsList = res.items;
        this.cp_id = res.cp_id;
        this.is_cp_pay = res.is_cp_pay === '1';
        this.is_opc_pay = res.is_opc_pay === '1';
        this.goods_items_copy = this._.cloneDeep(res.items);
        this.goods_items_copy.map(item => {
          this.sumNum += Number(item.num);
        });
        this.address = `${res.consignee_info.prov.name}  ${res.consignee_info.city.name}   ${res.consignee_info.county.name}  ${res.consignee_info.detail}`;

        // goods_items拿取新的数据后，要根据当前tab的选中，做一下数据过滤
        this.onStatusChange(this.goods_status);
        this.account_detail_codes = res.account_detail_codes;
        this.is_non_direct_up = res.is_non_direct_up;
        this.clinic_direct_type_text = res.clinic_direct_type_text;
        this.clinic_type_text = res.clinic_type_text;
        console.log('=>(detail.vue:457) this.is_non_direct_up', this.is_non_direct_up);
      });
    }
  }
};
</script>

<style lang="less" scoped>
.detail-wrapper {
  margin-bottom: 30px;

  .basic-info {
    border: 1px solid #b7b7b7;

    .basic-info-item {
      display: flex;
      line-height: 22px;
      border-bottom: 1px solid #b7b7b7;

      &:last-of-type {
        border-bottom: none;
      }

      text-align: center;

      .item-label {
        width: 180px;
        border-right: 1px solid #b7b7b7;
        background: #e6e6e6;
      }

      .item-content {
        flex: 1;
        text-align: left;
        text-indent: 30px;
      }
    }
  }

  .goods-list {
    padding-top: 10px;
  }

  .operator-box {
    padding-top: 20px;
  }

  .footer {
    text-align: center;
    padding: 20px;
  }
}

.table-wrapper {
  position: relative;

  ::v-deep .ivu-table-wrapper {
    overflow: unset;
  }
}

.sticky-table-head {
  ::v-deep .ivu-table {
    overflow: unset;

    .ivu-table-header {
      position: sticky;
      top: 134px;
      z-index: 3;
    }
  }
}
.refuse-input {
  width: 100%;
  max-width: 100%;

  :deep(.ivu-input) {
    height: 100px;
  }
}
</style>
