<template>
  <div class="f_order-wrapper">
    <Form inline @submit.native.prevent @keyup.enter.native="onSearch">
      <Row>
        <FormItem>
          <Input v-model="queryFormData.order_code" placeholder="采购单号" clearable />
        </FormItem>
        <FormItem>
          <surning-search
            ref="nursing-search"
            :showType="true"
            :extra-params="{
              fill_opc: 1
            }"
            v-model="queryFormData.ent_code"
            :type="queryFormData.ent_type"
            @getType="changeEntType"
            :isClearable="true"
            :company_id="queryFormData.company_id"
          ></surning-search>
        </FormItem>
        <FormItem>
          <license-search
            ref="license-search"
            v-model="queryFormData.ent_code_organ"
            :type="queryFormData.ent_type_organ"
            :isClearable="true"
            :company_id="queryFormData.company_id"
            @getType="type => (queryFormData.ent_type_organ = type)"
          ></license-search>
        </FormItem>
        <FormItem>
          <Select v-model="queryFormData.status" clearable placeholder="请选择状态">
            <Option v-for="item in statusDesc" :value="item.id" :key="item.id">{{ item.desc }}</Option>
          </Select>
        </FormItem>
        <FormItem>
          <DatePicker
            type="daterange"
            clearable
            :options="disabledTime"
            format="yyyy-MM-dd"
            placeholder="采购时间"
            v-model="timeRange"
            @on-change="times => handleTimeChange(times)"
            class="time-range"
          ></DatePicker>
        </FormItem>
      </Row>

      <Row>
        <FormItem>
          <clinic-type-search
            v-model="queryFormData.clinic_type"
            :clinic_direct_type="queryFormData.clinic_direct_type"
            @directChange="directChange"
          ></clinic-type-search>
        </FormItem>
        <FormItem style="text-align: left">
          <Button type="primary" class="mr10" @click="onSearch">筛选</Button>
          <Button type="default" class="mr10" @click="onResetSearch">重置</Button>
          <Button type="default" @click="exportExcel" :loading="exportLoading">导出</Button>
        </FormItem>
        <FormItem style="text-align: right">
          <Button type="primary" class="mr10" @click="createProductPackage">创建开业产品包</Button>
        </FormItem>
      </Row>
    </Form>
    <div class="table-wrapper">
      <div class="panel-nav flex flex-item-between">
        <div>
          <a :class="{ active: !queryFormData.status }" class="nav" @click.prevent.capture="onStatusChange('')">
            全部
          </a>
          <a
            v-for="(status_item, status_index) in statusDesc"
            :key="status_index"
            :class="{ active: $route.query.status == status_item.id }"
            class="nav"
            @click.prevent.capture="onStatusChange(status_item.id)"
          >
            {{ status_item.desc }}
          </a>
        </div>
      </div>
      <Table :loading="tableLoading" :columns="tableCols" :data="list" :height="$store.state.app.clientHeight - 345">
        <template slot="cli_type" slot-scope="{ row }">
          <div v-if="row.clinic_direct_type_text && row.clinic_type_text">
            {{ row.clinic_direct_type_text }} / {{ row.clinic_type_text }}
          </div>
          <div v-else>-</div>
        </template>
        <template slot="organization_name" slot-scope="{ row }">
          {{ row.organization_name || '-' }}
        </template>
        <template slot-scope="{ row }" slot="status_text">
          <span :style="statusColor(row)">{{ row.status_text }}</span>
        </template>
        <template slot="ent_type_text" slot-scope="{ row }">
          <div style="text-align: left">
            <div>主体类型：{{ row.ent_type_text }}</div>
            <div>
              <span>诊所类型：</span
              ><span v-if="row.clinic_direct_type_text && row.clinic_type_text"
                >{{ row.clinic_direct_type_text }} / {{ row.clinic_type_text }} </span
              ><span v-else>-</span>
            </div>
          </div>
        </template>
        <template slot-scope="{ row }" slot="action">
          <a
            @click="toDetail(row.order_code, 'addOrder')"
            class="mr10"
            v-if="
              row.status != 'FINISHED' &&
              row.status !== 'OP_AUDIT' &&
              row.status !== 'WAIT_AUDIT' &&
              row.status !== 'REJECTED'
            "
            >建单</a
          >
          <a
            class="mr10"
            @click="editOrder(row.order_code)"
            v-if="row.status !== 'OP_AUDIT' && row.status !== 'WAIT_AUDIT' && row.status != 'FINISHED'"
            >{{ row.status == 'REJECTED' ? '修改' : '编辑' }}</a
          >

          <a
            class="mr10"
            v-if="row.allow_com_audit_fin === '1' && row.status == 'WAIT_AUDIT'"
            @click="toDetail(row.order_code)"
            >审核</a
          >
          <a @click="toDetail(row.order_code, 'checkDetail')">详情</a>
        </template>
      </Table>
      <div class="block_20"></div>
      <KPage
        :total="total"
        :page-size="+queryFormData.pageSize"
        :current="+queryFormData.page"
        @on-change="handleCurrentChange"
        @on-page-size-change="handleSizeChange"
        style="text-align: center"
      />
    </div>
  </div>
</template>

<script>
import S from 'utils/util';
import search from '@/mixins/search';
import surningSearch from '@/components/surning-search/surning-search';
import licenseSearch from '@/components/license-search/license-search';
import clinicTypeSearch from '@/components/remote-search/clinic-type-search.vue';

const init_query_form_data = {
  page: 1,
  pageSize: 20,
  id: '',
  name: '',
  ent_type: '',
  order_code: '',
  goods_type: '',
  ent_code: '',
  clinic_type: '',
  clinic_direct_type: 'DIGITIZE',
  r: ''
};
export default {
  name: 'list',
  mixins: [search],
  components: {
    surningSearch,
    licenseSearch,
    clinicTypeSearch
  },
  data() {
    return {
      apiName: 'getOrderList',
      queryFormData: { ...init_query_form_data },
      timeRange: [],
      disabledTime: {
        disabledDate(date) {
          return date && date.valueOf() > Date.now();
        }
      },
      statusDesc: [],
      tableCols: [
        { title: '编号', key: 'order_code', align: 'center', minWidth: 100 },
        { title: '采购主体', key: 'ent_name', align: 'center', width: 120 },
        // { title: '诊所类型', slot: 'cli_type', align: 'center', width: 140 },
        { title: '营业执照名称', slot: 'organization_name', align: 'center', width: 120 },
        { title: '主体类型', slot: 'ent_type_text', align: 'center', width: 180 },
        { title: '商品总数', key: 'total_num', align: 'center', width: 80 },
        { title: '已采购数', key: 'has_purchase_num', align: 'center', width: 80 },
        { title: '待采购数', key: 'no_purchase_num', align: 'center', width: 80 },
        { title: '已发货数', key: 'has_shipped_num', align: 'center', width: 80 },
        { title: '已采购待发货数', key: 'wait_ship_num', align: 'center', width: 120 },
        { title: '已退款', key: 'refund_pass_num', align: 'center', width: 80 },
        { title: '状态', key: 'status_text', slot: 'status_text', align: 'center', width: 120 },
        { title: '创建时间', key: 'create_time', align: 'center', width: 150 },
        { title: '操作', slot: 'action', align: 'center', width: 120, fixed: 'right' }
      ],
      exportLoading: false
    };
  },
  computed: {
    statusColor(row) {
      return row => {
        switch (row.status) {
          case 'INIT':
            return {
              color: '#2db7f5'
            };
          case 'OP_AUDIT':
          case 'WAIT_AUDIT':
          case 'UNDERWAY':
            return {
              color: '#f90'
            };
          case 'FINISHED':
            return {
              color: '#19be6b'
            };
        }
      };
    }
  },
  watch: {},
  created() {
    this.queryFormData = S.merge(this.queryFormData, this.$route.query);
    this.submitQueryForm(true);
    this.getOptionsList();
  },
  mounted() {},
  methods: {
    changeEntType(type) {
      this.queryFormData.ent_type = type;
    },
    editOrder(order_code) {
      console.log(order_code);
      this.$router.push(`/purchase/product/create?order_code=${order_code}`);
    },
    //建单
    toDetail(order_code, type) {
      this.$router.push({
        path: '/purchase/product/detail',
        query: {
          order_code,
          type
        }
      });
    },
    onStatusChange(status) {
      this.queryFormData.page = 1;
      this.queryFormData.status = status;
      this.submitQueryForm();
    },
    onResetSearch() {
      this.queryFormData = { ...init_query_form_data };
      this.timeRange = [];
      this.submitQueryForm();
      this.$refs['nursing-search'].clear();
      this.$refs['license-search'].clear();
    },
    getOptionsList() {
      this.$api.getPackageOptions().then(res => {
        console.log('-> res', res);
        this.statusDesc = S.descToArrHandle(res.statusDesc);
      });
    },
    createProductPackage() {
      this.$router.push('/purchase/product/create');
    },
    exportExcel() {
      this.exportLoading = true;
      let params = {
        ...this.queryFormData
      };
      this.$api.getPduducList(params).then(
        res => {
          this.action = res.url;
          this.download(res.url);
          this.exportLoading = false;
        },
        err => {
          this.$Message.error(err.errmsg);
          this.exportLoading = false;
        }
      );
    },
    download(url) {
      const downloadLink = document.createElement('a');
      downloadLink.setAttribute('href', url);
      downloadLink.setAttribute('target', '_blank');
      downloadLink.setAttribute('style', 'display:none');
      document.body.appendChild(downloadLink);
      downloadLink.click();
      document.body.removeChild(downloadLink);
    },
    directChange(val) {
      this.$set(this.queryFormData, 'clinic_direct_type', val);
    }
  },
  beforeRouteUpdate(to, from, next) {
    // S.log(to.params['nav-stack-key-dir'], 'nav-stack-key-dir')
    this.queryFormData = S.merge(init_query_form_data, to.query);
    this.getTimeRange();
    this.loadList();
    next();
  }
};
</script>

<style scoped lang="less">
.mr10 {
  margin-right: 10px;
}
</style>
