<template>
<div class="r-detail-wrapper">
	<div class="r-detail-box">
		<div class="detail-item">
			<p class="detail-item-label">退款单号：</p>
			<p class="detail-item-content">{{ info.code || '-' }}</p>
		</div>

		<!-- <div class="detail-item">
			<p class="detail-item-label">诊所：</p>
			<p class="detail-item-content">{{ info.clinic_name || '-' }}</p>
		</div> -->
		<div class="detail-item">
			<p class="detail-item-label">采购主体：</p>
			<p class="detail-item-content">{{ info.clinic_name || '-' }}</p>
		</div>

		<div class="detail-item" v-if="info.ent_type == 'CLI'">
			<p class="detail-item-label">诊所类型：</p>
			<p class="detail-item-content">
          <span v-if="info.clinic_direct_type_text">{{
							info.clinic_direct_type_text
						}} / {{ info.clinic_type_text }}</span>
				<span v-else>-</span>
			</p>
		</div>

		<div class="detail-item">
			<p class="detail-item-label">营业执照名称：</p>
			<p class="detail-item-content">{{ info.organization_name || '-' }}</p>
		</div>
		<div class="detail-item">
			<p class="detail-item-label">主体类型：</p>
			<p class="detail-item-content">{{ info.ent_type_text || '-' }}</p>
		</div>

		<div class="detail-item">
			<p class="detail-item-label">状态：</p>
			<p class="detail-item-content" v-if="info.status !== 'REJECT'">{{ info.status_text || '-' }}</p>
			<p class="detail-item-content" v-else>{{ info.status_text || '-' }}
				<span v-if="info.reject_info.msg" style="color: red">{{
						`（  驳回原因：${info.reject_info.msg}  ）`
					}}</span></p>
		</div>

		<div class="detail-item">
			<p class="detail-item-label">退款商品明细：</p>
			<p class="detail-item-content" style="width: 85%">
				<Table :columns="getTableCol" :data="info.items" border show-summary :summary-method="handleSummary">
				</Table>
			</p>
		</div>

		<!-- 诊所的退款金额和养疗馆的退款金额均采用info.cli_price， 如果是养疗馆的退款金额，后端会做处理， -->
		<!-- 当前写两种，方便为了后面拆字段预留空间 -->
		<div class="detail-item" v-if="info.is_opc_pay === '0'">
			<p class="detail-item-label">诊所退款金额：</p>
			<p class="detail-item-content"><span v-if="info.cli_price">￥</span>{{ info.cli_price || '-' }}</p>
		</div>

		<div class="detail-item" v-if="info.is_opc_pay === '1'">
			<p class="detail-item-label">直营运营中心退款金额：</p>
			<p class="detail-item-content"><span v-if="info.cli_price">￥</span>{{ info.com_price || '-' }}</p>
		</div>

		<div class="detail-item">
			<p class="detail-item-label">退款原因：</p>
			<p class="detail-item-content">{{ info.refund_info || '-' }}</p>
		</div>

		<div class="detail-item">
			<p class="detail-item-label">备注：</p>
			<p class="detail-item-content">{{ info.remark || '-' }}</p>
		</div>
	</div>
	<p style="height: 10px"></p>
	<div class="fixed-bottom-wrapper">
		<back-button></back-button>
		<Button v-if="info.status == 'REJECT'" type="primary" style="margin:0 20px;" @click="refundModalVisible = true">
			修改退款申请
		</Button>
	</div>
	<!-- 申请退款弹窗 -->
	<refund-modal title="修改退款申请" :goodsList="goodsList" :orderInfo="refund_info"
								:showModal.sync="refundModalVisible"></refund-modal>
</div>
</template>

<script>
import refundModal from '../components/refund-modal'
import S from 'utils/util'

export default {
	name: 'detail',
	components: {refundModal},
	mixins: [],
	props: {},
	data() {
		return {
			refundInfo: [], // 退款商品list
			refundModalVisible: false, // 修改退款申请弹窗的flag
			tableCols: [],
			// 退款商品明细cloumns
			tableCols_cli: [
				{title: '商品编号', key: 'spu_code', align: 'center'},
				{title: '商品', key: 'name', align: 'center'},
				{title: '类型', key: 'type_text', align: 'center'},
				{title: '诊所进货单价', key: 'single_cli_price', align: 'center'},
				{title: '退款数量', key: 'num', align: 'center'},
				{title: '诊所退款金额', key: 'cli_price', align: 'center'},
			],

			tableCols_op: [
				{title: '商品编号', key: 'spu_code', align: 'center'},
				{title: '商品', key: 'name', align: 'center'},
				{title: '类型', key: 'type_text', align: 'center'},
				{title: '诊所进货单价', key: 'single_com_price', align: 'center'},
				{title: '退款数量', key: 'num', align: 'center'},
				{title: '直营运营中心退款金额', key: 'com_price', align: 'center'},
			],
			info: {},
			refund_info: {},
			type: false,
			goodsList: []
		}
	},
	computed: {
		getTableCol(){
			return this.info.is_opc_pay === '1' ? this.tableCols_op : this.tableCols_cli
		}
	},
	watch: {},
	created() {
	},
	mounted() {
		this.getRefundInfo()
	},
	methods: {
		back() {
			this.$router.push('/purchase/refund/list')
		},
		// 修改采购单按钮事件
		editRefund() {

		},
		// 合计
		handleSummary({columns, data}) {
			const sums = {}
			columns.forEach((column, index) => {
				const key = column.key
				if (index === 0) {
					sums[key] = {
						key,
						value: '合计',
					}
					return
				}
				let whiteList = [5, 6, 7]
				if (whiteList.indexOf(index) > -1) {
					const values = data.map((item) => Number(item[key]))
					if (!values.every((value) => isNaN(value))) {
						const v = values.reduce((prev, curr) => {
							const value = Number(curr)
							if (!isNaN(value)) {
								return prev + curr
							} else {
								return prev
							}
						}, 0)
						sums[key] = {
							key,
							value: S.mathAdd(v, 0),
						}
					} else {
						sums[key] = {
							key,
							value: '-',
						}
					}
				} else {
					sums[key] = {
						key,
						value: '',
					}
				}
			})
			return sums
		},
		// 获取退款商品数据
		getRefundGoodsItem() {
			let resultArr = []
			this.goodsList.filter(item => {
				this.info.items.forEach(spu_item => {
					if (item.spu_code === spu_item.spu_code) {
						resultArr.push({
							...item,
							wait_num: spu_item.num
						})
					}
				})
			})
			this.refund_info = {...this.info, items: resultArr,is_opc_pay: this.info.is_opc_pay === '1'}
			console.log('refund_info', this.refund_info)
		},
		// api-获取退款单详情
		getRefundInfo() {
			const {id} = this.$route.query
			this.$api.getRefundInfo({id}).then(res => {
				this.info = res.info
				this.getGoodsItem(res.info.pack_order_id)
			}, rej => this.$Message.error(rej.errmsg))
		},
		// api-获取商品数据
		getGoodsItem(id) {
			this.$api.getPackageInfo({id}).then(res => {
				this.goodsList = res.items
				// if (res.info.status == 'REJECT') {
				this.getRefundGoodsItem()
				// }
			})
		}
	},
	filters: {},
}
</script>
<style lang="less" scoped>
.r-detail-wrapper {
	.r-detail-box {
		margin-top: 20px;

		.detail-item {
			display: flex;
			color: #333;
			margin-bottom: 8px;

			.detail-item-label {
				width: 140px;
				min-width: 140px;
				text-align: right;
			}

			.detail-item-content {
				width: 100%;
			}
		}
	}
}
</style>