<template>
  <div class="r-list-wrapper">
    <Form inline @submit.native.prevent @keyup.enter.native="onSearch">
      <Row>
        <Col>
          <FormItem>
            <Input v-model="queryFormData.code" placeholder="请输入退款单号" clearable></Input>
          </FormItem>
        </Col>
        <Col>
          <FormItem>
            <surning-search
              ref="nursing-search"
              v-model="queryFormData.ent_code"
              :default_params="{ fill_person_c: '', fill_opc: '' }"
              @getType="type => (queryFormData.ent_type = type)"
              placeholder="输入退款主体搜索"
            ></surning-search>
          </FormItem>
        </Col>
        <Col>
          <FormItem>
            <DatePicker
              type="daterange"
              clearable
              format="yyyy-MM-dd"
              placeholder="请选择提交时间"
              v-model="timeRange"
              @on-change="times => handleTimeChange(times)"
              class="time-range"
            ></DatePicker>
          </FormItem>
        </Col>
        <Col>
          <FormItem style="text-align: left">
            <Button class="mr10" type="primary" @click="onSearch">筛选</Button>
            <Button type="default" class="mr-10" @click="onResetSearch">重置</Button>
            <Button type="default" @click="exportRefundOrder">导出</Button>
          </FormItem>
        </Col>
      </Row>

      <!-- <FormItem label="诊所:">
			<clinic-search ref="clinic-search" v-model="queryFormData.clinic_id" :isClearable="true"></clinic-search>
		</FormItem> -->
    </Form>
    <div class="table-wrapper">
      <div class="panel-nav">
        <a :class="{ active: !queryFormData.status }" class="nav" @click.prevent.capture="onStatusChange('')"> 全部 </a>
        <a
          :class="{ active: $route.query.status == 'WAIT_AUDIT' }"
          class="nav"
          @click.prevent.capture="onStatusChange('WAIT_AUDIT')"
        >
          待审核
        </a>
        <a
          :class="{ active: $route.query.status == 'PASS' }"
          class="nav"
          @click.prevent.capture="onStatusChange('PASS')"
        >
          已通过
        </a>
        <a
          :class="{ active: $route.query.status == 'REJECT' }"
          class="nav"
          @click.prevent.capture="onStatusChange('REJECT')"
        >
          已驳回
        </a>
      </div>
      <Table :columns="tableCols" :data="list" :loading="tableLoading" :height="$store.state.app.clientHeight - 275">
        <!--		状态		-->
        <template slot="status" slot-scope="{ row }">
          <span :style="statusColor(row)">{{ row.status_text }}</span>
        </template>
        <!--		退款主体		-->
        <template slot="clinic_name" slot-scope="{ row }">
          <div>
            <template v-if="row.ent_type == 'CLI'">
              <div class="flex">
                <span class="slot-l1">诊所名称：</span><span class="slot-v">{{ row.clinic_name || '-' }}</span>
              </div>
              <div class="flex">
                <span class="slot-l1">诊所类型：</span>
                <span>
                  <span class="slot-v" v-if="row.clinic_direct_type_text"
                    >{{ row.clinic_direct_type_text }} / {{ row.clinic_type_text }}</span
                  >
                  <span v-else>-</span>
                </span>
              </div>
            </template>

            <div class="flex" v-if="row.ent_type == 'RXJ'">
              <span class="slot-l1">养疗馆名称：</span><span class="slot-v">{{ row.rxj_name || '-' }}</span>
            </div>
          </div>
        </template>

        <!-- 所属省公司 -->
        <template slot="cli_price" slot-scope="{ row }">
          <div class="flex flex-c">{{ row.is_opc_pay === '0' ? row.cli_price : '-' }}</div>
        </template>
        <!-- 所属省公司 -->
        <template slot="opc_price" slot-scope="{ row }">
          <div class="flex flex-c">{{ row.is_opc_pay === '1' ? row.com_price : '-' }}</div>
        </template>
        <template slot="organization_name" slot-scope="{ row }">
          {{ row.organization_name || '-' }}
        </template>
        <!--时间-->
        <template slot="create_time" slot-scope="{ row }">
          <div class="flex flex-c">
            <div class="flex slot-text">
              <span class="slot-l">创建时间：</span><span class="slot-v">{{ row.create_time | date_format }}</span>
            </div>
            <div class="flex slot-text">
              <span class="slot-l">审核人：</span><span class="slot-v">{{ row.audit_info.operator || '-' }}</span>
            </div>
            <div class="flex slot-text">
              <span class="slot-l">审核时间：</span><span class="slot-v">{{ row.audit_info.time | date_format }}</span>
            </div>
          </div>
        </template>

        <template slot="action" slot-scope="{ row }">
          <a @click="toDetail(row.id)">详情</a>
          <a @click="toDetail(row.id)" style="margin-left: 10px" v-if="row.status === 'REJECT'">修改</a>
        </template>
      </Table>
      <div class="block_20"></div>
      <KPage
        :current="+queryFormData.page"
        :page-size="+queryFormData.pageSize"
        :total="total"
        style="text-align: center"
        @on-change="handleCurrentChange"
        @on-page-size-change="handleSizeChange"
      />
    </div>
  </div>
</template>

<script>
import S from 'utils/util';
import search from '@/mixins/search';
// import clinicSearch from '@/components/clinic-search/clinic-search'
import surningSearch from '@/components/surning-search/surning-search';
import downloadExcel from '@/mixins/downloadExcel';

const init_query_form_data = {
  page: 1,
  pageSize: 20,
  clinic_id: '', // 诊所id
  status: '',
  ent_code: '', // 退款主体
  ent_type: '', // 退款主体的类型
  code: '',
  cp_id: '', // 合作伙伴id
  st: '',
  et: '',
  r: ''
};
export default {
  name: 'list',
  components: {
    // clinicSearch,
    surningSearch
  },
  mixins: [search, downloadExcel],
  props: {},
  data() {
    return {
      apiName: 'getRefundList',
      queryFormData: { ...init_query_form_data },
      clinic_list: [], // 诊所下拉列表数据
      statusDesc: [], // 状态下拉列表数据
      timeRange: [], // 退款时间
      // 退款单表格columns
      tableCols: [
        { title: '退款单号', key: 'code', align: 'center', width: 100 },
        { title: '退款主体', slot: 'clinic_name', align: 'center', width: 180 },
        { title: '营业执照名称', slot: 'organization_name', align: 'center', width: 120 },
        { title: '主体类型', key: 'ent_type_text', align: 'center', width: 70 },
        { title: '退款商品', key: 'goods_text', align: 'center', width: 220 },
        { title: '数量', key: 'num', align: 'center', minWidth: 70 },
        { title: '诊所退款金额', slot: 'cli_price', align: 'center', minWidth: 100 },
        { title: '直营运营中心退款金额', slot: 'opc_price', align: 'center', minWidth: 100 },
        { title: '状态', key: 'status_text', slot: 'status', align: 'center', minWidth: 100 },
        { title: '创建/审核时间', slot: 'create_time', align: 'center', minWidth: 225 },
        { title: '操作', slot: 'action', align: 'center', width: 120, fixed: 'right' }
      ],
      list: []
    };
  },
  computed: {
    statusColor(row) {
      return row => {
        switch (row.status) {
          case 'PASS':
            return {
              color: '#4FBA6E'
            };
          case 'WAIT_AUDIT':
            return {
              color: '#F79C16'
            };
          case 'REJECT':
            return {
              color: '#E14D17'
            };
        }
      };
    }
  },
  watch: {},
  created() {
    this.queryFormData = S.merge(this.queryFormData, this.$route.query);
    this.submitQueryForm(true);
  },
  mounted() {},
  methods: {
    // 重置
    onResetSearch() {
      this.queryFormData = { ...init_query_form_data };
      this.timeRange = [];
      this.submitQueryForm();
      this.$refs['nursing-search'].clear();
    },
    // tab上方的切换事件
    onStatusChange(status) {
      this.queryFormData.page = 1;
      this.queryFormData.status = status;
      this.submitQueryForm();
    },
    // 详情
    toDetail(id) {
      this.$router.push({
        path: '/purchase/refund/detail',
        query: { id }
      });
    },
    exportRefundOrder() {
      this.$api.getRefundExportList(this.queryFormData).then(res => {
        this.download(res.url, '开业退款单列表.xls');
      });
    }
  },
  beforeRouteUpdate(to, from, next) {
    this.queryFormData = S.merge(init_query_form_data, to.query);
    this.getTimeRange();
    this.loadList();
    next();
  },
  filters: {}
};
</script>
<style lang="less" scoped>
.slot-l1 {
  text-align: right;
  min-width: 75px;
}

.slot-l {
  text-align: right;
  min-width: 95px;
}

.slot-l2 {
  text-align: right;
  min-width: 60px;
}

.slot-v {
}
</style>
