<template>
  <div>
    <div v-if="revenueInfo.length > 0">
      <div v-for="(item, revenue_index) in revenueInfo" :key="revenue_index">
        <!--  套餐商品展示    -->
        <div class="tc-box" v-if="item.isTcGoods">
          <!--  套餐信息      -->
          <div class="flex flex-item-align mb10">
            <div class="flex flex-item-align flex-1">
              <img :src="item.tc_info?.image_src | imageStyle('B.w300')" class="goods-img" />
              <div class="goods-info">
                <div class="goods-type">{{ item.tc_info?.goods_type_desc }}</div>
                <div>{{ item.tc_info?.name }}</div>
              </div>
            </div>
            <div class="flex-1">数量：{{ item.tc_info?.quantity }}</div>
            <div class="flex-1">单价：<span v-text-format.number="item.tc_info?.price"></span></div>
            <div class="flex-1">优惠权益：<span v-text-format.number="item.tc_info?.discount_fee"></span></div>
            <div class="flex-1">实际收款：<span v-text-format.number="item.tc_info?.payment_fee"></span></div>
          </div>
          <!--  套餐子项信息      -->
          <div class="flex flex-item-align">
            <div class="flex-1"></div>
            <div class="flex flex-item-align flex-1">
              <img :src="item.image_src | imageStyle('B.w300')" class="goods-img" />
              <div class="goods-info">
                <div class="goods-type">{{ item.divide_type === '1' ? '虚拟商品' : '实物商品' }}</div>
                <div>{{ item.name }}</div>
              </div>
            </div>
            <div class="flex-1">数量：{{ item.quantity }}</div>
            <div class="flex-1" v-if="item.goods_type === '15' || item.goods_type === '30'">
              卡券剩余：{{ item.surplus_qty }}
            </div>
            <div class="flex-1">单价：<span v-text-format.number="item.price"></span></div>
            <div class="flex-1"></div>
          </div>
        </div>
        <!--  非套餐商品展示    -->
        <div class="content-box" v-else>
          <div class="flex flex-item-align flex-1">
            <img :src="item.image_src | imageStyle('B.w300')" class="goods-img" />
            <div class="goods-info">
              <div class="goods-type">{{ item.divide_type === '1' ? '虚拟商品' : '实物商品' }}</div>
              <div>{{ item.name }}</div>
            </div>
          </div>
          <div class="flex-1">数量：{{ item.quantity }}</div>
          <div class="flex-1" v-if="item.goods_type === '15' || item.goods_type === '30'">
            卡券剩余：{{ item.surplus_qty }}
          </div>
          <div class="flex-1">单价：<span v-text-format.number="item.price"></span></div>
          <div class="flex-1">优惠权益：<span v-text-format.number="item.discount_fee"></span></div>
          <div class="flex-1">实际收款：<span v-text-format.number="item.payment_fee"></span></div>
        </div>
        <!--      divide_type 类型:1=服务分账,2=销售分账 -->
        <Table
          v-if="item.divide_type === '1'"
          :loading="tableLoading"
          :columns="serviceTableCols"
          :data="item.divide?.physios"
          :span-method="func => handleServiceSpan(func, item.divide?.physios)"
          class="mb20"
          border
          disabled-hover
        >
          <!--  分账金额      -->
          <template v-slot:divide_money="{ row }"> <span v-text-format.money="row.divide_money"></span>元 </template>
          <template v-slot:name="{ row, index }">
            <div v-if="row.type === '1'">{{ row.name }}</div>
            <div v-else>{{ row.name }}</div>
          </template>
          <template v-slot:ratio="{ row }"> {{ row.ratio_text }}</template>
          <template v-slot:level_desc="{ row }">
            {{ row.level_desc ? `P${row.level_desc}` : '-' }}
          </template>
          <template v-slot:money="{ row }"> <span v-text-format.money="row.money"></span>元 </template>
          <template v-slot:use_time="{ row }">
            {{ row.use_time | date_format }}
          </template>
        </Table>
        <Table
          v-if="item.divide_type === '2'"
          :loading="tableLoading"
          :columns="saleTableCols"
          :data="item.divide?.sales"
          class="mb20"
          :span-method="func => handleSpan(func, item.list?.length)"
          border
          disabled-hover
        >
          <template v-slot:divide_money="{ row }"> <span v-text-format.money="row.divide_money"></span>元 </template>
          <template v-slot:number="{ row }">
            <div>共{{ row.sales_nums }}人</div>
          </template>
          <template v-slot:name="{ row, index }">
            <div>{{ row.name }}</div>
          </template>
          <template v-slot:rank="{ row }">
            <div>{{ row.rank_desc }}</div>
            <div>{{ row.ratio }}%</div>
          </template>
          <template v-slot:money="{ row }"> <span v-text-format.money="row.money"></span>元 </template>
        </Table>
      </div>
      <p class="block-header" @click="test++">修改记录</p>
      <changeRecord :list="recordList"></changeRecord>
    </div>
    <div v-else class="empty" style="height: 70vh">暂无数据</div>

    <div class="fixed-bottom-wrapper">
      <back-button class="mr10"></back-button>
      <!--      <Button type="primary" @click="saveAccount">保存</Button>-->
    </div>
    <!--    <sale-number-modal-->
    <!--      :visible.sync="numberVisible"-->
    <!--      @ok="changeSaleNumber"-->
    <!--      :currentNumber="currentNumber"-->
    <!--      :salesRules="salesRules"-->
    <!--    ></sale-number-modal>-->
    <!--    <select-staff-modal v-model="staffVisible" :divide_type="divideType" @success="changeStaff"></select-staff-modal>-->
  </div>
</template>

<script>
import S from 'utils/util';
import changeRecord from './components/changeRecord/index.vue';
import renderHeader from '@/mixins/renderHeader';
// import saleNumberModal from './components/account/saleNumberModal.vue';
import { $operator } from '@/utils/operation';
// import selectStaffModal from './components/account/selectStaffModal.vue';
import { cloneDeep } from 'lodash';

export default {
  name: 'account',
  components: { changeRecord },
  mixins: [renderHeader],
  props: {},
  data() {
    return {
      tableLoading: false,
      serviceTableCols: [
        { title: '名称', key: 'service_name', width: 100 },
        { title: '分账金额', slot: 'divide_money', width: 100 },
        { title: '角色', key: 'type_desc', width: 100 },
        { title: '人员', slot: 'name', width: 100 },
        { title: '等级', slot: 'level_desc', width: 100 },
        { title: '比例', slot: 'ratio', width: 100 },
        { title: '业绩提成', slot: 'money', width: 100 },
        { title: '核销时间', slot: 'use_time', width: 120 }
      ],
      saleTableCols: [
        { title: '名称', key: 'service_name', width: 100 },
        { title: '分账金额', slot: 'divide_money', width: 100 },
        { title: '参与销售人数', slot: 'number', width: 100 },
        { title: '人员', slot: 'name', width: 100 },
        {
          title: '顺序/比例',
          slot: 'rank',
          width: 100,
          renderHeader: (h, params) =>
            this._renderHeader(
              h,
              params,
              '根据多人业绩提成设置\n' +
                '如果多人销售场景下设置了1个人，销售人员最多只能选择1个人；则按照设置的顺序和比例分佣；\n' +
                '如果多人销售场景下设置了3个人，销售人员最多只能选择3个人；则按照设置的顺序和比例分佣；\n' +
                '多人销售场景最多可设置5个人',
              '300',
              'rank-class'
            )
        },
        {
          title: '业绩提成',
          slot: 'money',
          width: 100,
          renderHeader: (h, params) =>
            this._renderHeader(
              h,
              params,
              '销售业绩最多按分账金额5%计算；\n' +
                '单人销售业绩 = 分账金额 x 5% x 100%\n' +
                '多人销售业绩 = 分账金额 x 5% x 个人分账比例\n',
              '300',
              'rank-class'
            )
        }
      ],
      list: [{}, {}, {}],
      revenueInfo: [],
      recordList: [],
      test: 0,
      numberVisible: false,
      revenueIndex: 0,
      staffIndex: 0,
      currentNumber: '1', // 当前人数
      sales_divide_ratio: 0, // 销售分成比例
      staffVisible: false,
      divideType: '',
      physioRules: {},
      salesRules: {},
      emptySaleInfo: {
        id: '',
        money: 0,
        name: '请选择销售人员',
        rank: '1',
        rank_desc: '第一人',
        ratio: '100',
        type_text: '现金',
        sales_nums: '1'
      }
    };
  },
  computed: {},
  watch: {},
  created() {
    this.getOrderRevenueDetail();
    // this.getOrderRevenuePickPlan();
  },
  mounted() {},
  methods: {
    getOrderRevenueDetail() {
      let params = {
        order_id: this.$route.query.order_id
      };
      console.log('=>(detail.vue:241) params', params);
      this.$api
        .getOrderRevenueDetail(params)
        .then(res => {
          this.sales_divide_ratio = $operator.divide(res.sales_divide_ratio, 100);
          res.attrs.forEach(item => {
            item.isTcGoods = !S.isEmptyObject(item.tc_info);
          });
          this.revenueInfo = res.attrs;
          this.recordList = res.operation_logs;
        })
        .catch(err => this.$Message.error(err.errmsg));
    },
    handleSpan({ row, column, rowIndex, columnIndex }, length) {
      const totalRows = 5; // 假设总共有 5 行，这里可以根据实际情况调整

      if (columnIndex === 0 || columnIndex === 1 || columnIndex === 2) {
        // 第一列的合并逻辑
        if (rowIndex === 0) {
          return {
            rowspan: totalRows, // 合并的行数
            colspan: 1 // 不合并列
          };
        } else {
          return {
            rowspan: 0, // 其他行不再创建额外单元格
            colspan: 0
          };
        }
      }
      // 处理其他列的逻辑（如果有的话）
    },
    handleServiceSpan({ row, column, rowIndex, columnIndex }, data) {
      if (columnIndex === 0 || columnIndex === 1) {
        // 处理第一列 name
        let rowspan = 1;
        for (let i = rowIndex + 1; i < data.length; i++) {
          if (data[i].card_no === data[rowIndex].card_no) {
            rowspan++;
          } else {
            break;
          }
        }
        if (rowspan > 1) {
          return {
            rowspan: rowspan,
            colspan: 1
          };
        } else if (rowIndex > 0 && data[rowIndex - 1].card_no === data[rowIndex].card_no) {
          // 属于被合并的单元格，移除
          return {
            rowspan: 0,
            colspan: 0
          };
        } else {
          // 单独的单元格，保留默认样式
          return {
            rowspan: 1,
            colspan: 1
          };
        }
      }
    },
    // todo 无保存修改逻辑，最新代码请查看诊所此处逻辑
    // 打开销售人数编辑弹框
    selectSaleNum(item, revenue_index, row) {
      console.log('=>(account.vue:202) item', item);
      this.revenueIndex = revenue_index;
      this.numberVisible = true;
      console.log('=>(account.vue:245) row', row);
      this.currentNumber = row.sales_nums;
    },
    // 修改参与销售人数
    changeSaleNumber(val) {
      // 当前商品的支付金额
      let payment_fee = this.revenueInfo[this.revenueIndex].payment_fee;
      // 销售总分成金额
      let saleTotalDivideFee = $operator.multiply(payment_fee, this.sales_divide_ratio);
      let originSales = cloneDeep(this.revenueInfo[this.revenueIndex].divide.sales);
      let length = originSales.length;
      const rankWords = [
        '第一人',
        '第二人',
        '第三人',
        '第四人',
        '第五人',
        '第六人',
        '第七人',
        '第八人',
        '第九人',
        '第十人'
      ];

      if (Number(val) !== length) {
        // 小于原销售人数,截取到对应人数的数据
        if (Number(val) < length) {
          let newSaleArray = originSales.slice(0, Number(val));
          let matchSaleRules = this.salesRules[newSaleArray.length];
          console.log('=>(account.vue:254) matchSaleRules', matchSaleRules);
          newSaleArray.forEach((item, index) => {
            // 比例要重新获取
            item.ratio = matchSaleRules[index];
            console.log('=>(account.vue:259) item.ratio', item.ratio);
            // money 要重新计算
            item.money = $operator.multiply(saleTotalDivideFee, $operator.divide(matchSaleRules[index], 100));
            item.sales_nums = val;
            item.rank = String(index + 1);
            item.rank_desc = rankWords[index];
          });
          this.revenueInfo[this.revenueIndex].divide.sales = cloneDeep(newSaleArray);
        }
        // 大于原销售人数
        if (Number(val) > length) {
          let emptyArray = [];
          for (let i = 0; i < Number(val) - length; i++) {
            emptyArray.push(this.emptySaleInfo);
          }
          let newSaleArray = originSales.concat(emptyArray);
          console.log('=>(account.vue:294) newSaleArray', newSaleArray);
          let matchSaleRules = this.salesRules[newSaleArray.length];

          newSaleArray = newSaleArray.map((item, index) => {
            return {
              ...item,
              ratio: matchSaleRules[index],
              // money 要重新计算
              money: $operator.multiply(saleTotalDivideFee, $operator.divide(matchSaleRules[index], 100)),
              sales_nums: val,
              rank: String(index + 1),
              rank_desc: rankWords[index]
            };
            // 比例要重新获取
          });
          console.log('=>(account.vue:299) newSaleArray', newSaleArray);
          this.revenueInfo[this.revenueIndex].divide.sales = cloneDeep(newSaleArray);
        }
      }
    },
    getOrderRevenuePickPlan() {
      let params = {
        order_id: this.$route.query.order_id
      };
      console.log('=>(detail.vue:241) params', params);
      this.$api
        .getOrderRevenuePickPlan(params)
        .then(res => {
          console.log('=>(detail.vue:242) res', res);
          this.physioRules = res.rules?.physio;
          this.salesRules = res.rules?.sales;
        })
        .catch(err => this.$Message.error(err.errmsg));
    },
    selectStaff(row, divide_type, revenue_index, index) {
      console.log('=>(account.vue:246) row, divide_type', row, divide_type);
      this.staffVisible = true;
      this.divideType = divide_type;
      this.revenueIndex = revenue_index;
      this.staffIndex = index;
    },
    changeStaff(staff_info) {
      console.log('=>(account.vue:336) staff_info', staff_info);
      // this.revenueInfo[this.revenueIndex].divide.physios
      // 修改服务人员
      if (this.divideType === '1') {
        // 当前商品的支付金额
        let payment_fee = this.revenueInfo[this.revenueIndex].payment_fee;
        // 初始服务数据
        let originalPhysios = this.revenueInfo[this.revenueIndex].divide.physios;
        // 判断下一条是否为该理疗师的关联导师
        let isRelateMentor = originalPhysios[this.staffIndex + 1]?.type === '2';
        if (isRelateMentor) {
          // 如果为true，则删除该条数据
          originalPhysios.splice(this.staffIndex + 1, 1);
        }
        // todo 导师要不要删除
        let newPhysiosArray = originalPhysios.map((item, index) => {
          if (index === this.staffIndex) {
            return {
              ...item,
              id: staff_info.id,
              name: staff_info.name,
              level_desc: staff_info.level,
              ratio: this.physioRules[Number(staff_info.level)],
              money: $operator.multiply(payment_fee, $operator.divide(this.physioRules[Number(staff_info.level)], 100))
            };
          } else {
            return item;
          }
        });
        if (!S.isEmptyObject(staff_info.parent)) {
          const mentorInfo = {
            type: '2',
            type_desc: '导师',
            use_time: originalPhysios[this.staffIndex].use_time,
            id: staff_info.parent.id,
            name: staff_info.parent.name,
            level_desc: '',
            ratio: staff_info.parent.ratio,
            money: $operator.multiply(payment_fee, $operator.divide(staff_info.parent.ratio, 100))
          };
          newPhysiosArray.splice(this.staffIndex + 1, 0, mentorInfo);
        }
        console.log('=>(account.vue:384) newPhysiosArray', newPhysiosArray);
        this.revenueInfo[this.revenueIndex].divide.physios = cloneDeep(newPhysiosArray);
      }
      // 修改销售人员
      if (this.divideType === '2') {
        this.revenueInfo[this.revenueIndex].divide.sales = this.revenueInfo[this.revenueIndex].divide.sales.map(
          (item, index) => {
            if (index === this.staffIndex) {
              return {
                ...item,
                id: staff_info.id,
                name: staff_info.name,
                type: staff_info.type
              };
            } else {
              return item;
            }
          }
        );
      }

      console.log('=>(account.vue:341) this.revenueInfo[this.revenueIndex]', this.revenueInfo[this.revenueIndex]);
    },
    saveAccount() {
      let flag = this.revenueInfo.every(item => {
        // 服务
        if (item.divide_type === '1') {
          // todo 目前没有检查项
          return true;
        } else if (item.divide_type === '2') {
          return item.divide.sales.every(sale_item => {
            console.log('=>(account.vue:413) sale_item', sale_item);
            if (!sale_item.id) {
              this.$Message.error(`请选择${item.name}的销售人员`);
              return false;
            } else {
              return true;
            }
          });
        }
      });
      if (!flag) {
        return;
      }
      this.updateOrderRevenue();
    },
    updateOrderRevenue() {
      let params = {
        order_id: this.$route.query.order_id,
        attrs: this.handleAttrs()
      };
      console.log('=>(detail.vue:241) params', params);
      this.$api
        .updateOrderRevenue(params)
        .then(res => {
          console.log('=>(detail.vue:242) res', res);
          this.$Message.success('核算业绩修改成功');
          this.$router.push('/trade/order/list');
        })
        .catch(err => this.$Message.error(err.errmsg));
    },
    handleAttrs() {
      let attrs = {};
      this.revenueInfo.forEach(item => {
        attrs[item.order_attr_id] = {};
        if (item.divide_type === '1') {
          attrs[item.order_attr_id].physio_ids = item.divide.physios
            .filter(physios_item => physios_item.type !== '2')
            .map(tech_item => tech_item.id);
        } else if (item.divide_type === '2') {
          attrs[item.order_attr_id].sales_ids = item.divide.sales.map(sale_item => sale_item.id);
        }
      });
      console.log('=>(account.vue:451) attrs', attrs);
      return attrs;
    }
  }
};
</script>
<style scoped lang="less">
.tc-box {
  background-color: #f2f2f2;
  padding: 10px;
}
.content-box {
  display: flex;
  align-items: center;
  background-color: #f2f2f2;
  padding: 10px;
}

.goods-img {
  width: 50px;
  height: 50px;
  margin-right: 10px;
  border-radius: 4px;
}
.goods-info {
  text-align: center;
  .goods-type {
    //padding: 0 10px;
    width: 70px;
    //max-width: 100px;
    //padding: 0 5px;
    color: #75b8de;
    border: 1px solid #75b8de;
    border-radius: 4px;
    margin-bottom: 3px;
    box-sizing: border-box;
  }
}
</style>
<style lang="less">
.rank-class {
  .ivu-tooltip-inner {
    white-space: pre-wrap;
  }
}
</style>
