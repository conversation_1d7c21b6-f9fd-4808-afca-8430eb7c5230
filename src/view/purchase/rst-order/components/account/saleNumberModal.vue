<template>
  <Modal
    ref="editActivityModal"
    :value="visible"
    title="修改销售人数"
    :mask-closable="false"
    width="320px"
    @on-cancel="cancel"
    @on-visible-change="changeVisible"
  >
    <div>
      <div class="mb10">请选择销售人数</div>
      <Select v-model="number" placeholder="请选择销售人数">
        <Option v-for="item in numberDesc" :key="item.id" :value="item.id">{{ item.desc }}</Option>
      </Select>
    </div>
    <div slot="footer">
      <Button @click="cancel">取消</Button>
      <Button type="primary" @click="submitForm" :loading="submitLoading">确定</Button>
    </div>
  </Modal>
</template>

<script>
import S from 'utils/util';
const initFormData = {
  code: '', //销售单编号；如果不填写，创建时系统会自动生成
};

export default {
  name: 'saleNumberModal',
  mixins: [],
  components: {},
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    id: {
      type: String,
      default: '',
    },
    currentNumber: {
      type: String,
      default: '',
    },
  },

  data() {
    return {
      formData: { ...initFormData },
      submitLoading: false,
      number: '1',
      numberDesc: [
        { id: '1', desc: '共1人' },
        { id: '2', desc: '共2人' },
        { id: '3', desc: '共3人' },
        { id: '4', desc: '共4人' },
        { id: '5', desc: '共5人' },
      ],
    };
  },

  computed: {},

  watch: {},

  created() {},

  mounted() {},

  methods: {
    changeVisible(val) {
      if (val) {
        this.number = this.currentNumber;
      } else {
        this.formData = { ...initFormData }; // 重置数据
      }
    },
    cancel() {
      this.$emit('update:visible', false);
    },
    submitForm() {
      this.$emit('ok', this.number);
      this.cancel();
    },
  },

  destroyed() {},
};
</script>

<style scoped lang="less"></style>
