<template>
  <Modal
    :value="value"
    width="400px"
    :title="divide_type === '1' ? '选择理疗师' : '选择销售人员'"
    :footer-hide="false"
    :mask-closable="false"
    :lock-scroll="true"
    @on-visible-change="changeVisible"
  >
    <div class="content flex flex-item-align">
      <!--      remote-->
      <!--      :remote-method="getOrderRevenueMembers"-->
      <!--      @visible-change="visibleChange"-->
      <!--      @clear="getOrderRevenueMembers('')"-->
      <el-select
        v-model="staff_id"
        class="flex-1"
        popper-class="relateAdvisorSelect"
        size="small"
        filterable
        :placeholder="divide_type === '1' ? '请选择理疗师' : '请选择销售人员'"
        :loading="searchLoading"
        @change="selectSup"
        clearable
      >
        <!--        <Row class="option-item row-header">-->
        <!--          <Col :span="6">头像</Col>-->
        <!--          <Col :span="12">姓名</Col>-->
        <!--          <Col :span="6">手机号</Col>-->
        <!--        </Row>-->

        <el-option
          class="option-item"
          v-for="(item, index) in staffList"
          :key="index"
          :label="item.name"
          :value="item.id"
        >
          {{ item.name }}({{ item.mobile }})
          <!--          <Row>-->
          <!--            <Col :span="6">-->
          <!--              <img-->
          <!--                :src="item.avatar || 'http://img-sn-i01s-cdn.rsjxx.com/rsjxx/2023/1220/151459_81411.png'"-->
          <!--                alt=""-->
          <!--                width="30"-->
          <!--                height="30"-->
          <!--                class="avatar"-->
          <!--              />-->
          <!--            </Col>-->
          <!--            <Col :span="12" class="ecs" style="display: inline-block">{{ item.name }}</Col>-->
          <!--            <Col :span="6">{{ item.mobile }}</Col>-->
          <!--          </Row>-->
        </el-option>
      </el-select>
    </div>

    <div slot="footer">
      <Button @click="closeModal">取消</Button>
      <Button :loading="confirmLoading" type="primary" @click="confirm">确认</Button>
    </div>
  </Modal>
</template>

<script>
export default {
  name: 'relateAdvisor',
  mixins: [],

  components: {},
  props: {
    value: {
      type: Boolean,
      default: false
    },

    divide_type: {
      type: String,
      default: '1'
    }
  },

  data() {
    return {
      staffList: [],
      confirmLoading: false,
      searchLoading: false,
      staff_id: '',
      currentItem: {}
    };
  },

  computed: {},

  watch: {},

  created() {},

  mounted() {},

  destroyed() {},

  methods: {
    visibleChange(val) {
      if (!val && this.value) {
        this.getOrderRevenueMembers(this.currentItem?.name);
      }
    },
    selectSup(val) {
      this.staff_id = val;
      this.currentItem = this.staffList.find(item => item.id === val);
    },
    changeVisible(visible) {
      if (visible) {
        this.getOrderRevenueMembers('');
      } else {
        this.closeModal();
      }
    },
    getOrderRevenueMembers(keyword) {
      // console.log('=>(relateAdvisor.vue:105) keyword', keyword);
      this.searchLoading = true;
      let params = {
        // keyword,
        type: this.divide_type
      };
      this.$api
        .getOrderRevenueMembers(params)
        .then(res => {
          this.staffList = res;
        })
        .finally(() => {
          this.searchLoading = false;
        });
    },
    clearData() {
      this.staff_id = '';
      this.currentItem = {};
      this.staffList = [];
    },

    closeModal() {
      this.clearData();
      this.$emit('input', false);
    },

    confirm() {
      if (!this.staff_id) {
        this.$Message.error(`请选择${this.divide_type === '1' ? '理疗师' : '销售人员'}`);
        return;
      }
      let staffInfo = this.staffList.find(item => item.id === this.staff_id);
      this.$emit('success', staffInfo);
      this.closeModal();
    }
  }
};
</script>

<style scoped lang="less">
.content {
  padding: 20px 20px 40px;
}

.option-item {
  //text-align: center;
  //padding: 4px 0;
  //height: auto;

  .avatar {
    border-radius: 50%;
  }
}

.row-header {
  position: sticky;
  top: 0px;
  z-index: 2;
  padding: 8px 0;
  background-color: #f5f5f5;
  font-weight: bold;
}
</style>

<style lang="less">
.relateAdvisorSelect {
  .el-select-dropdown__list {
    padding: 0px;
  }
}
</style>
