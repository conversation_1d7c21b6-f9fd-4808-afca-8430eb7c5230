<template>
  <Modal
    ref="compareModal"
    :value="visible"
    title="修改日志记录"
    :mask-closable="false"
    width="1000px"
    @on-cancel="cancel"
    @on-visible-change="changeVisible"
    class-name="compareModal"
    footer-hide
  >
    <div class="custom-modal-content" :style="{ maxHeight: $store.state.app.clientHeight - 400 + 'px' }">
      <div>
        <div class="block-header">更改前</div>
        <div class="compare-item">
          <div v-for="(item, revenue_index) in compareInfo.before" :key="revenue_index">
            <!--  套餐商品展示    -->
            <div class="tc-box" v-if="item.tc_info?.name">
              <!--  套餐信息      -->
              <div class="flex flex-item-align mb10">
                <div class="flex flex-item-align flex-1">
                  <img :src="item.tc_info?.image_src | imageStyle('B.w300')" class="goods-img" />
                  <div class="goods-info">
                    <div class="goods-type">{{ item.tc_info?.goods_type_desc }}</div>
                    <div>{{ item.tc_info?.name }}</div>
                  </div>
                </div>
                <div class="flex-1">数量：{{ item.tc_info?.quantity }}</div>
                <div class="flex-1">单价：<span v-text-format.number="item.tc_info?.price"></span></div>
                <div class="flex-1">优惠权益：<span v-text-format.number="item.tc_info?.discount_fee"></span></div>
                <div class="flex-1">实际收款：<span v-text-format.number="item.tc_info?.payment_fee"></span></div>
              </div>
              <!--  套餐子项信息      -->
              <div class="flex flex-item-align">
                <div class="flex-1"></div>
                <div class="flex flex-item-align flex-1">
                  <img :src="item.image_src | imageStyle('B.w300')" class="goods-img" />
                  <div class="goods-info">
                    <div class="goods-type">{{ item.divide_type === '1' ? '虚拟商品' : '实物商品' }}</div>
                    <div>{{ item.name }}</div>
                  </div>
                </div>
                <div class="flex-1">数量：{{ item.quantity }}</div>
                <div class="flex-1" v-if="item.goods_type === '15' || item.goods_type === '30'">
                  卡券剩余：{{ item.surplus_qty }}
                </div>
                <div class="flex-1">单价：<span v-text-format.number="item.price"></span></div>
                <div class="flex-1"></div>
              </div>
            </div>
            <!--  非套餐商品展示    -->
            <div class="content-box" v-else>
              <div class="flex flex-item-align flex-1">
                <img :src="item.image_src | imageStyle('B.w300')" class="goods-img" />
                <div class="goods-info">
                  <div class="goods-type">{{ item.divide_type === '1' ? '虚拟商品' : '实物商品' }}</div>
                  <div>{{ item.name }}</div>
                </div>
              </div>
              <div class="flex-1">数量：{{ item.quantity }}</div>
              <div class="flex-1" v-if="item.goods_type === '15' || item.goods_type === '30'">
                卡券剩余：{{ item.surplus_qty }}
              </div>
              <div class="flex-1">单价：<span v-text-format.number="item.price"></span></div>
              <div class="flex-1">优惠权益：<span v-text-format.number="item.discount_fee"></span></div>
              <div class="flex-1">实际收款：<span v-text-format.number="item.payment_fee"></span></div>
            </div>
            <!--      divide_type 类型:1=服务分账,2=销售分账 -->
            <Table
              v-if="item.divide_type === '1'"
              :columns="serviceTableCols"
              :data="item.divide?.physios"
              :span-method="func => handleServiceSpan(func, item.divide?.physios)"
              class="mb20"
              border
              disabled-hover
            >
              <!--  分账金额      -->
              <template v-slot:divide_money="{ row }">
                <span v-text-format.money="row.divide_money"></span>元
              </template>
              <template v-slot:name="{ row, index }">
                <div v-if="row.type === '1'">{{ row.name }}</div>
                <div v-else>{{ row.name }}</div>
              </template>
              <template v-slot:ratio="{ row }"> {{ row.ratio_text }}</template>
              <template v-slot:level_desc="{ row }">
                {{ row.level_desc ? `P${row.level_desc}` : '-' }}
              </template>
              <template v-slot:money="{ row }"> <span v-text-format.money="row.money"></span>元 </template>
              <template v-slot:use_time="{ row }">
                {{ row.use_time | date_format }}
              </template>
            </Table>
            <Table
              v-if="item.divide_type === '2'"
              :columns="saleTableCols"
              :data="item.divide?.sales"
              class="mb20"
              :span-method="func => handleSpan(func, item.list?.length)"
              border
              disabled-hover
            >
              <!--  分账金额      -->
              <template v-slot:divide_money="{ row }">
                <span v-text-format.money="row.divide_money"></span>元
              </template>
              <template v-slot:number="{ row }">
                <div>共{{ row.sales_nums }}人</div>
              </template>
              <template v-slot:name="{ row, index }">
                <div>{{ row.name }}</div>
              </template>
              <template v-slot:rank="{ row }">
                <div>{{ row.rank_desc }}</div>
                <div>{{ row.ratio }}%</div>
              </template>
              <template v-slot:money="{ row }"> <span v-text-format.money="row.money"></span>元 </template>
            </Table>
          </div>
        </div>
        <div class="block-header">更改后</div>
        <div class="compare-item">
          <div v-for="(item, revenue_index) in compareInfo.after" :key="revenue_index">
            <!--  套餐商品展示    -->
            <div class="tc-box" v-if="item.tc_info?.name">
              <!--  套餐信息      -->
              <div class="flex flex-item-align mb10">
                <div class="flex flex-item-align flex-1">
                  <img :src="item.tc_info?.image_src | imageStyle('B.w300')" class="goods-img" />
                  <div class="goods-info">
                    <div class="goods-type">{{ item.tc_info?.goods_type_desc }}</div>
                    <div>{{ item.tc_info?.name }}</div>
                  </div>
                </div>
                <div class="flex-1">数量：{{ item.tc_info?.quantity }}</div>
                <div class="flex-1">单价：<span v-text-format.number="item.tc_info?.price"></span></div>
                <div class="flex-1">优惠权益：<span v-text-format.number="item.tc_info?.discount_fee"></span></div>
                <div class="flex-1">实际收款：<span v-text-format.number="item.tc_info?.payment_fee"></span></div>
              </div>
              <!--  套餐子项信息      -->
              <div class="flex flex-item-align">
                <div class="flex-1"></div>
                <div class="flex flex-item-align flex-1">
                  <img :src="item.image_src | imageStyle('B.w300')" class="goods-img" />
                  <div class="goods-info">
                    <div class="goods-type">{{ item.divide_type === '1' ? '虚拟商品' : '实物商品' }}</div>
                    <div>{{ item.name }}</div>
                  </div>
                </div>
                <div class="flex-1">数量：{{ item.quantity }}</div>
                <div class="flex-1" v-if="item.goods_type === '15' || item.goods_type === '30'">
                  卡券剩余：{{ item.surplus_qty }}
                </div>
                <div class="flex-1">单价：<span v-text-format.number="item.price"></span></div>
                <div class="flex-1"></div>
              </div>
            </div>
            <!--  非套餐商品展示    -->
            <div class="content-box" v-else>
              <div class="flex flex-item-align flex-1">
                <img :src="item.image_src | imageStyle('B.w300')" class="goods-img" />
                <div class="goods-info">
                  <div class="goods-type">{{ item.divide_type === '1' ? '虚拟商品' : '实物商品' }}</div>
                  <div>{{ item.name }}</div>
                </div>
              </div>
              <div class="flex-1">数量：{{ item.quantity }}</div>
              <div class="flex-1" v-if="item.goods_type === '15' || item.goods_type === '30'">
                卡券剩余：{{ item.surplus_qty }}
              </div>
              <div class="flex-1">单价：<span v-text-format.number="item.price"></span></div>
              <div class="flex-1">优惠权益：<span v-text-format.number="item.discount_fee"></span></div>
              <div class="flex-1">实际收款：<span v-text-format.number="item.payment_fee"></span></div>
            </div>
            <!--      divide_type 类型:1=服务分账,2=销售分账 -->
            <Table
              v-if="item.divide_type === '1'"
              :columns="serviceTableCols"
              :data="item.divide?.physios"
              :span-method="func => handleServiceSpan(func, item.divide?.physios)"
              class="mb20"
              border
              disabled-hover
            >
              <!--  分账金额      -->
              <template v-slot:divide_money="{ row }">
                <span v-text-format.money="row.divide_money"></span>元
              </template>
              <template v-slot:name="{ row, index }">
                <div v-if="row.type === '1'">{{ row.name }}</div>
                <div v-else>{{ row.name }}</div>
              </template>
              <template v-slot:ratio="{ row }"> {{ row.ratio_text }} </template>
              <template v-slot:level_desc="{ row }">
                {{ row.level_desc ? `P${row.level_desc}` : '-' }}
              </template>
              <template v-slot:money="{ row }"> <span v-text-format.money="row.money"></span>元 </template>
              <template v-slot:use_time="{ row }">
                {{ row.use_time | date_format }}
              </template>
            </Table>
            <Table
              v-if="item.divide_type === '2'"
              :columns="saleTableCols"
              :data="item.divide?.sales"
              class="mb20"
              :span-method="func => handleSpan(func, item.list?.length)"
              border
              disabled-hover
            >
              <!--  分账金额      -->
              <template v-slot:divide_money="{ row }">
                <span v-text-format.money="row.divide_money"></span>元
              </template>
              <template v-slot:number="{ row }">
                <div>共{{ row.sales_nums }}人</div>
              </template>
              <template v-slot:name="{ row, index }">
                <div>{{ row.name }}</div>
              </template>
              <template v-slot:rank="{ row }">
                <div>{{ row.rank_desc }}</div>
                <div>{{ row.ratio }}%</div>
              </template>
              <template v-slot:money="{ row }"> <span v-text-format.money="row.money"></span>元 </template>
            </Table>
          </div>
        </div>
      </div>
    </div>
    <div slot="footer">
      <Button @click="cancel">取消</Button>
      <Button type="primary" @click="submitForm" :loading="submitLoading">确定</Button>
    </div>
  </Modal>
</template>

<script>
import S from 'utils/util';
import renderHeader from '@/mixins/renderHeader';
const initFormData = {
  code: '' //销售单编号；如果不填写，创建时系统会自动生成
};

export default {
  name: 'editActivityModal',
  mixins: [renderHeader],
  components: {},
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    id: {
      type: String,
      default: ''
    },
    compareInfo: {
      type: Object,
      default: {
        before: {},
        after: {}
      }
    }
  },

  data() {
    return {
      formData: { ...initFormData },
      submitLoading: false,
      serviceTableCols: [
        { title: '名称', key: 'service_name', width: 100 },
        { title: '分账金额', slot: 'divide_money', width: 100 },
        { title: '角色', key: 'type_desc', width: 100 },
        { title: '人员', slot: 'name', width: 100 },
        { title: '等级', slot: 'level_desc', width: 100 },
        { title: '比例', slot: 'ratio', width: 100 },
        { title: '业绩提成', slot: 'money', width: 100 },
        { title: '核销时间', slot: 'use_time', width: 100 }
      ],
      saleTableCols: [
        { title: '名称', key: 'service_name', width: 100 },
        { title: '分账金额', slot: 'divide_money', width: 100 },
        { title: '参与销售人数', slot: 'number', width: 100 },
        { title: '人员', slot: 'name', width: 100 },
        {
          title: '顺序/比例',
          slot: 'rank',
          width: 100,
          renderHeader: (h, params) =>
            this._renderHeader(
              h,
              params,
              '根据多人业绩提成设置\n' +
                '如果多人销售场景下设置了1个人，销售人员最多只能选择1个人；则按照设置的顺序和比例分佣；\n' +
                '如果多人销售场景下设置了3个人，销售人员最多只能选择3个人；则按照设置的顺序和比例分佣；\n' +
                '多人销售场景最多可设置5个人',
              '300',
              'rank-class'
            )
        },
        {
          title: '业绩提成',
          slot: 'money',
          width: 100,
          renderHeader: (h, params) =>
            this._renderHeader(
              h,
              params,
              '销售业绩最多按分账金额5%计算；\n' +
                '单人销售业绩 = 分账金额 x 5% x 100%\n' +
                '多人销售业绩 = 分账金额 x 5% x 个人分账比例\n',
              '300',
              'rank-class'
            )
        }
      ]
    };
  },

  computed: {},

  watch: {},

  created() {},

  mounted() {},

  methods: {
    changeVisible(val) {
      if (val) {
      } else {
        this.formData = { ...initFormData }; // 重置数据
        // this.$refs['modal'].$el.querySelector('.ivu-modal-body').scrollTop = 0; // 重置滚动条高度
      }
    },
    cancel() {
      this.$emit('update:visible', false);
    },
    submitForm() {},
    handleSpan({ row, column, rowIndex, columnIndex }, length) {
      const totalRows = 5; // 假设总共有 5 行，这里可以根据实际情况调整

      if (columnIndex === 0 || columnIndex === 1 || columnIndex === 2) {
        // 第一列的合并逻辑
        if (rowIndex === 0) {
          return {
            rowspan: totalRows, // 合并的行数
            colspan: 1 // 不合并列
          };
        } else {
          return {
            rowspan: 0, // 其他行不再创建额外单元格
            colspan: 0
          };
        }
      }
      // 处理其他列的逻辑（如果有的话）
    },
    handleServiceSpan({ row, column, rowIndex, columnIndex }, data) {
      if (columnIndex === 0 || columnIndex === 1) {
        // 处理第一列 name
        let rowspan = 1;
        for (let i = rowIndex + 1; i < data.length; i++) {
          if (data[i].card_no === data[rowIndex].card_no) {
            rowspan++;
          } else {
            break;
          }
        }
        if (rowspan > 1) {
          return {
            rowspan: rowspan,
            colspan: 1
          };
        } else if (rowIndex > 0 && data[rowIndex - 1].card_no === data[rowIndex].card_no) {
          // 属于被合并的单元格，移除
          return {
            rowspan: 0,
            colspan: 0
          };
        } else {
          // 单独的单元格，保留默认样式
          return {
            rowspan: 1,
            colspan: 1
          };
        }
      }
    }
  },

  destroyed() {}
};
</script>

<style scoped lang="less">
.block-header {
  background-color: #efefef;
  padding: 10px;
  margin: 0px 0px 20px 0;
  position: relative;
}
.compare-item {
  flex: 1;
  &:first-child {
    margin-right: 40px;
  }
  .sub-title {
    font-size: 14px;
    font-weight: 700;
    margin-top: 5px;
    margin-bottom: 5px;
  }
}
</style>
<style lang="less">
.compareModal {
  .custom-modal-content {
    overflow-y: auto;
  }
  .ivu-modal-body {
    padding: 20px 10px 20px 30px;
  }
}
</style>
<style scoped lang="less">
.tc-box {
  background-color: #f2f2f2;
  padding: 10px;
}
.content-box {
  display: flex;
  align-items: center;
  background-color: #f2f2f2;
  padding: 10px;
}

.goods-img {
  width: 50px;
  height: 50px;
  margin-right: 10px;
  border-radius: 4px;
}
.goods-info {
  text-align: center;
  .goods-type {
    //padding: 0 10px;
    width: 70px;
    color: #75b8de;
    border: 1px solid #75b8de;
    border-radius: 4px;
    margin-bottom: 3px;
    box-sizing: border-box;
  }
}
</style>
<style lang="less">
.rank-class {
  .ivu-tooltip-inner {
    white-space: pre-wrap;
  }
}
</style>
