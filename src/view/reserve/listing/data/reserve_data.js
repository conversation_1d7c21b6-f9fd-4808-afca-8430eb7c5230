// 待到店
export const wait_columns = [
  { title: '到店人', slot: 'name', align: 'center', minWidth: 200 },
  { title: '预约服务', slot: 'services_desc', align: 'center', minWidth: 120 },
  { title: '理疗师', slot: 'physio_desc', align: 'center', minWidth: 120 },
  { title: '预约到店时间', slot: 'reserve_time', align: 'center', minWidth: 120, sortable: 'custom' },
  { title: '顾客备注', slot: 'user_remark', align: 'center', minWidth: 100 },
  { title: '门店备注', slot: 'remark', align: 'center', minWidth: 100 },
  { title: '预约门店', slot: 'clinic_name', align: 'center', minWidth: 100 },
  { title: '订单来源', key: 'source_desc', align: 'center', minWidth: 100 },
  { title: '上次服务时间', slot: 'last_svc_time', align: 'center', minWidth: 160, sortable: 'custom' },
  { title: '下单时间', slot: 'create_time', align: 'center', minWidth: 160, sortable: 'custom' }
];

// 已到店
export const arrive_columns = [
  { title: '到店人', slot: 'name', align: 'center', minWidth: 200 },
  { title: '预约服务', slot: 'services_desc', align: 'center', minWidth: 120 },
  { title: '理疗师', slot: 'physio_desc', align: 'center', minWidth: 120 },
  { title: '预约到店时间', slot: 'reserve_time', align: 'center', minWidth: 120, sortable: 'custom' },
  { title: '实际到店时间', slot: 'arrival_time', align: 'center', minWidth: 120, sortable: 'custom' },
  { title: '顾客备注', slot: 'user_remark', align: 'center', minWidth: 100 },
  { title: '门店备注', slot: 'remark', align: 'center', minWidth: 100 },
  { title: '预约门店', slot: 'clinic_name', align: 'center', minWidth: 100 },
  { title: '订单来源', key: 'source_desc', align: 'center', minWidth: 100 },
  { title: '上次服务时间', slot: 'last_svc_time', align: 'center', minWidth: 160, sortable: 'custom' },
  { title: '下单时间', slot: 'create_time', align: 'center', minWidth: 160, sortable: 'custom' }
];

// 已完成
export const finished_columns = [
  { title: '到店人', slot: 'name', align: 'center', minWidth: 200 },
  { title: '预约服务', slot: 'services_desc', align: 'center', minWidth: 120 },
  { title: '理疗师', slot: 'physio_desc', align: 'center', minWidth: 120 },
  { title: '预约到店时间', slot: 'reserve_time', align: 'center', minWidth: 120, sortable: 'custom' },
  { title: '实际到店时间', slot: 'arrival_time', align: 'center', minWidth: 120, sortable: 'custom' },
  { title: '顾客备注', slot: 'user_remark', align: 'center', minWidth: 100 },
  { title: '门店备注', slot: 'remark', align: 'center', minWidth: 100 },
  { title: '预约门店', slot: 'clinic_name', align: 'center', minWidth: 100 },
  { title: '订单来源', key: 'source_desc', align: 'center', minWidth: 100 },
  { title: '上次服务时间', slot: 'last_svc_time', align: 'center', minWidth: 160, sortable: 'custom' },
  { title: '下单时间', slot: 'create_time', align: 'center', minWidth: 160, sortable: 'custom' },
  { title: '评价', slot: 'score', align: 'center', minWidth: 160 },
  { title: '操作', slot: 'operator', align: 'center', minWidth: 100 }
];

// 已取消
export const cancel_columns = [
  { title: '到店人', slot: 'name', align: 'center', minWidth: 200 },
  { title: '预约服务', slot: 'services_desc', align: 'center', minWidth: 120 },
  { title: '理疗师', slot: 'physio_desc', align: 'center', minWidth: 120 },
  { title: '预约到店时间', slot: 'reserve_time', align: 'center', minWidth: 120, sortable: 'custom' },
  { title: '顾客备注', slot: 'user_remark', align: 'center', minWidth: 100 },
  { title: '门店备注', slot: 'remark', align: 'center', minWidth: 100 },
  { title: '预约门店', slot: 'clinic_name', align: 'center', minWidth: 100 },
  { title: '订单来源', key: 'source_desc', align: 'center', minWidth: 100 },
  { title: '上次服务时间', slot: 'last_svc_time', align: 'center', minWidth: 160, sortable: 'custom' },
  { title: '下单时间', slot: 'create_time', align: 'center', minWidth: 160, sortable: 'custom' },
  { title: '取消时间', slot: 'cancel_at', align: 'center', minWidth: 160 },
  { title: '操作', slot: 'operator', align: 'center', minWidth: 100 }
];

// 待确认
export const channel_columns = [
  { title: '到店人', slot: 'name', minWidth: 200 },
  { title: '预约单号', slot: 'code', minWidth: 200 },
  { title: '预约状态', slot: 'status_desc', minWidth: 100 },
  { title: '预约服务', slot: 'services_desc', minWidth: 120 },
  { title: '服务人', slot: 'physio_desc', minWidth: 120 },
  { title: '预约到店时间', slot: 'reserve_time', minWidth: 120, sortable: 'custom' },
  { title: '实际到店时间', slot: 'arrival_time', minWidth: 120, sortable: 'custom' },
  { title: '开单时间', slot: 'order_at', minWidth: 120 },
  { title: '顾客备注', slot: 'user_remark', minWidth: 100 },
  { title: '门店备注', slot: 'remark', minWidth: 100 },
  { title: '预约单来源', key: 'source_desc', minWidth: 110 },
  { title: '上次服务时间', slot: 'last_svc_time', minWidth: 160, sortable: 'custom' },
  { title: '下单时间', slot: 'create_time', minWidth: 160, sortable: 'custom' },
  { title: '支付状态', slot: 'is_payed_desc', minWidth: 80, fixed: 'right' },
  { title: '操作', slot: 'operator', minWidth: 200, fixed: 'right' }
];
