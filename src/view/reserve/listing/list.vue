<template>
  <div>
    <Form inline :label-width="0" @submit.native.prevent @keyup.enter.native="onSearch">
      <FormItem label="">
        <Input v-model="queryFormData.kw" placeholder="到店人/到店人手机号" clearable />
      </FormItem>

      <FormItem label="">
        <Input v-model="queryFormData.physio_name" placeholder="理疗师" clearable />
      </FormItem>

      <FormItem label="">
        <DatePicker
          type="daterange"
          placeholder="下单时间"
          :value="timeRange"
          clearable
          @on-change="times => handleTimeChange(times)"
        ></DatePicker>
      </FormItem>

      <FormItem label="">
        <DatePicker
          type="daterange"
          placeholder="预约时间"
          :value="arriveTimeRange"
          clearable
          @on-change="times => handleTimeChange(times, 'arrival_st', 'arrival_et')"
        ></DatePicker>
      </FormItem>

      <FormItem>
        <Button type="primary" class="mr10" @click="onSearch">筛选</Button>
        <Button type="default" @click="onResetSearch">重置</Button>
      </FormItem>
    </Form>
    <div class="panel-nav" v-if="Object.keys(statusDesc).length">
      <a
        v-for="(item, key) in statusDesc"
        :key="key"
        class="nav"
        :class="{ active: $route.query.list_status == key }"
        @click.prevent.capture="onStatusChange(key)"
      >
        {{ item.desc }}
        <Tag :color="statusColor(key)">{{ list_count[key] }}</Tag>
      </a>
    </div>
    <Table
      :loading="tableLoading"
      :columns="tableCols"
      :data="list"
      @on-sort-change="sortChanged"
      :height="$store.state.app.clientHeight - 280"
    >
      <!-- 到店人 -->
      <template slot-scope="{ row }" slot="name">
        <div>
          <div class="lv-box">
            <div class="label">姓名：</div>
            <div class="value">{{ row.reserve_user?.name || '-' }}</div>
          </div>
          <div class="lv-box">
            <div class="label">手机号：</div>
            <div class="value">{{ row.reserve_user?.mobile || '-' }}</div>
          </div>
        </div>
      </template>

      <!-- 预约服务 -->
      <template slot-scope="{ row }" slot="services_desc">
        <div>{{ getServices(row.services) }}</div>
      </template>

      <!-- 理疗师 -->
      <template slot-scope="{ row }" slot="physio_desc">
        <div>{{ getPhysical(row.services) }}</div>
      </template>

      <!-- 预约到店时间 -->
      <template slot-scope="{ row }" slot="reserve_time">
        <div :style="{ color: row.is_delay === '1' ? 'red' : '', 'text-align': 'center' }">
          <div>{{ row.reserve_ms }}</div>
          <div>{{ row.reserve_day_text }}</div>
          <div v-if="row.is_delay === '1'">{{ row.delay_text }}</div>
        </div>
      </template>

      <!-- 实际到店时间 -->
      <template slot-scope="{ row }" slot="arrival_time">
        <div v-if="row.is_arrival === '1'">
          <div>{{ row.arrival_ms }}</div>
          <div>{{ row.arrival_day_text }}</div>
        </div>
        <div v-else>-</div>
      </template>

      <!-- 开单时间 -->
      <template slot-scope="{ row }" slot="order_at">
        <div>{{ row.order_at || '-' }}</div>
      </template>

      <!-- 顾客备注 -->
      <template slot-scope="{ row }" slot="user_remark">
        <Tooltip :content="row.user_remark" :disabled="row.user_remark?.length < 10">
          <div class="ecs ecs-2">{{ row.user_remark || '-' }}</div>
        </Tooltip>
      </template>

      <!-- 门店备注 -->
      <template slot-scope="{ row }" slot="remark">
        <Tooltip :content="row.remark" :disabled="row.remark?.length < 10">
          <div class="ecs ecs-2">{{ row.remark || '-' }}</div>
        </Tooltip>
      </template>

      <!-- 预约门店 -->
      <template slot-scope="{ row }" slot="clinic_name">
        <div>{{ row.clinic_name || '-' }}</div>
      </template>

      <!-- 上次服务时间 -->
      <template slot-scope="{ row }" slot="last_svc_time">
        <div>{{ row.last_svc_at || '-' }}</div>
      </template>

      <template slot-scope="{ row }" slot="score">
        <div class="lv-box">
          <div class="label label90">技术专业度：</div>
          <div class="value">{{ row.technology_grade || '-' }}</div>
        </div>

        <div class="lv-box">
          <div class="label label90">服务态度：</div>
          <div class="value">{{ row.service_grade || '-' }}</div>
        </div>

        <div class="lv-box">
          <div class="label label90">整体体验：</div>
          <div class="value">{{ row.overall_grade || '-' }}</div>
        </div>

        <div class="lv-box">
          <div class="label label90">环境与舒适度：</div>
          <div class="value">{{ row.comfort_grade || '-' }}</div>
        </div>
      </template>

      <!-- 下单时间 -->
      <template slot-scope="{ row }" slot="create_time">
        <div>{{ row.create_at || '-' }}</div>
      </template>

      <!-- 取消时间 -->
      <template slot-scope="{ row }" slot="cancel_at">
        <div>{{ row.cancel_at || '-' }}</div>
        <div>{{ row.cancel_reason }}</div>
      </template>

      <template slot-scope="{ row, index }" slot="operator">
        <OperationFolding :maxVisibleActions="3" :actions="getActions(row, index)" :row="row"> </OperationFolding>
      </template>
    </Table>

    <div class="block_20"></div>

    <KPage
      :current.sync="+queryFormData.page"
      :page-size.sync="+queryFormData.pageSize"
      :total="total"
      style="text-align: center"
      @on-change="handleCurrentChange"
      @on-page-size-change="handleSizeChange"
    />
  </div>
</template>

<script>
import S from 'utils/util'; // Some commonly used tools
import search from '@/mixins/search'; // Runtime information
import { wait_columns, arrive_columns, finished_columns, cancel_columns, channel_columns } from './data/reserve_data';

let init_query_form_data = {
  page: 1,
  pageSize: 20,
  kw: '',
  physio_name: '',
  st: '',
  et: '',
  arrival_st: '',
  arrival_et: '',
  list_status: '1',
  sort_type: '',
  sort_method: '',
  r: ''
};

export default {
  name: 'list',
  mixins: [search],
  components: {},
  data() {
    return {
      queryFormData: { ...init_query_form_data },
      apiName: 'getReserveReserveList',
      tableCols: [],
      tableLoading: false,
      list: [],
      total: 0,
      list_count: {},
      services: [],
      addReserveVisible: false,
      statusDesc: {},
      currentRow: {}
    };
  },

  computed: {
    getActions() {
      return (row, index) => {
        return [
          // 查看订单
          {
            label: '查看订单',
            handler: this.seeOrder,
            tagType: 'a',
            params: row,
            isHidden: !(row.status === '9999' || row.status === '7000') || !row.order_id || row.order_id === '0'
          }
        ];
      };
    },
    getServices() {
      return list => {
        let name = [];
        list?.forEach(item => {
          name.push(item.goods_service.name);
        });
        return name.join('、') || '未选择服务';
      };
    },
    getPhysical() {
      return list => {
        let name = [];
        list?.forEach(item => {
          name.push(item.physio.name);
        });
        return name.join('、') || '-';
      };
    },
    statusColor() {
      return status => {
        switch (status) {
          case '2': // 已到店
            return '#ffad33';
          case '3': // 已完成
            return '#19be6b';
          case '1': // 待到店
            return '#ed4014';
          case '4': // 已取消
            return 'default';
        }
      };
    }
  },

  created() {
    this.getReserveReserveOptions();
    this.queryFormData = S.merge(this.queryFormData, this.$route.query);
    this.submitQueryForm(true);
  },

  methods: {
    // 查看订单
    seeOrder(row) {
      this.$router.push({
        path: '/purchase/rst-order/detail',
        query: {
          orderid: row.order_id,
          orderType: 'shop_order'
        }
      });
    },

    sortChanged({ column: { slot }, order }) {
      if (order === 'normal') {
        order = '';
      }

      if (slot) {
        this.queryFormData.sort_type = slot;
        this.queryFormData.sort_method = order;
        this.submitQueryForm();
      }
    },
    handlerListData(data) {
      this.list_count = data.list_status_count;
    },

    onStatusChange: function (status) {
      this.queryFormData.page = 1;
      this.queryFormData.list_status = status;
      this.submitQueryForm();
    },

    onSearch: function () {
      this.queryFormData.page = 1;
      this.submitQueryForm();
    },
    onResetSearch: function () {
      this.queryFormData = { ...init_query_form_data };
      this.timeRange = [];
      this.submitQueryForm();
    },

    onPageChange: function (page, pageSize) {
      this.queryFormData.page = page;
      this.queryFormData.pageSize = pageSize;
      this.submitQueryForm();
    },

    getReserveReserveOptions() {
      this.$api.getReserveReserveOptions().then(res => {
        this.statusDesc = res.list_status_desc;
      });
    }
  },

  watch: {
    'queryFormData.list_status': {
      handler(val) {
        const map = {
          1: wait_columns,
          2: arrive_columns,
          3: finished_columns,
          4: cancel_columns,
          5: channel_columns
        };
        this.tableCols = map[val];
      },
      immediate: true
    }
  },

  beforeRouteUpdate: function (to, from, next) {
    this.queryFormData = S.merge(init_query_form_data, to.query);
    this.getTimeRange();
    this.getTimeRange('arrival_st', 'arrival_et', 'arriveTimeRange');
    this.loadList();
    next();
  }
};
</script>

<style lang="less" scoped>
.status-tip {
  color: #999;
  font-size: 12px;
}
.mr10 {
  margin-right: 10px;
}
</style>

<style lang="less" scoped>
.lv-box {
  display: flex;

  .label {
    width: 60px;
    min-width: 60px;
    text-align: right;
  }

  .label90 {
    width: 90px;
    min-width: 90px;
  }

  .value {
    text-align: left;
  }
}
</style>
