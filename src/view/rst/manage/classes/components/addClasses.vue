<template>
  <Modal
    ref="customModal"
    :value="value"
    width="650px"
    :title="row.id ? '编辑班次' : '添加班次'"
    :footer-hide="false"
    :mask-closable="false"
    :lock-scroll="true"
    @on-visible-change="changeVisible"
  >
    <div class="content">
      <div class="form-box">
        <Form :label-width="100" ref="queryFormData" :label-colon="true" :model="queryFormData">
          <FormItem label="班次名称" style="width: 451px">
            <Input
              v-model="queryFormData.name"
              placeholder="请输入班次名称"
              :disabled="!isRepresent"
              show-word-limit
              maxlength="10"
            />
          </FormItem>
          <FormItem label="班次时间">
            <div class="flex">
              <Select
                v-model="queryFormData.st"
                placeholder="请选择开始时间"
                :disabled="!isRepresent"
                style="width: 160px"
                @on-change="bcSTTimeChange"
              >
                <Option v-for="item in time_list" :value="item" :key="item">{{ item }}</Option>
              </Select>
              <div class="ml10 mr10">一</div>
              <Select
                v-model="queryFormData.et"
                placeholder="请选择结束时间"
                :disabled="!isRepresent"
                style="width: 160px"
                @on-change="bcETTimeChange"
              >
                <Option
                  v-for="item in time_list"
                  :value="item"
                  :key="item"
                  v-show="compareTimes(queryFormData.st, item)"
                  >{{ item }}
                </Option>
              </Select>

              <Button
                v-if="isRepresent"
                type="default"
                class="ml20"
                @click="addResetTime"
                :disabled="queryFormData.sleep_range.length >= 3"
                >添加休息时段
              </Button>
            </div>
          </FormItem>

          <FormItem label="休息时段" v-for="(item, index) in queryFormData.sleep_range" :key="index">
            <div class="flex">
              <Select
                v-model="queryFormData.sleep_range[index].st"
                placeholder="请选择开始时间"
                :disabled="!isRepresent"
                style="width: 160px"
                @on-change="rsSTTimeChange"
              >
                <Option v-for="item in time_list" :value="item" :key="item" v-show="calcResetSTTime(index, item)"
                  >{{ item }}
                </Option>
              </Select>
              <div class="ml10 mr10">一</div>
              <Select
                v-model="queryFormData.sleep_range[index].et"
                placeholder="请选择结束时间"
                :disabled="!isRepresent"
                style="width: 160px"
                @on-change="rsETTimeChange"
              >
                <Option v-for="item in time_list" :value="item" :key="item" v-show="calcResetETTime(index, item)"
                  >{{ item }}
                </Option>
              </Select>

              <Button
                v-if="isRepresent"
                type="default"
                class="ml20"
                style="width: 104px"
                @click="deleteResetTime(index)"
                >删除</Button
              >
            </div>
          </FormItem>
        </Form>
      </div>
    </div>
    <div slot="footer">
      <Button @click="closeModal">{{ isRepresent ? '取消' : '关闭' }}</Button>
      <Button v-if="isRepresent" :loading="confirmLoading" type="primary" @click="confirm">确定</Button>
    </div>
  </Modal>
</template>

<script>
export default {
  name: 'addClasses',
  mixins: [],

  components: {},
  props: {
    value: {
      type: Boolean,
      default: false
    },
    row: {
      type: Object,
      default: () => {}
    },
    clinic_id: {
      type: String,
      default: ''
    },
    isRepresent: {
      type: Boolean,
      default: false
    }
  },

  data() {
    return {
      confirmLoading: false,
      queryFormData: {
        name: '',
        st: '10:00',
        et: '22:00',
        sleep_range: []
      },
      time_list: []
    };
  },

  computed: {
    // 计算休息时段可以显示的开始时间
    calcResetSTTime() {
      return (index, item) => {
        if (this.compareTimes(this.queryFormData.st, item, '<=') && this.compareTimes(item, this.queryFormData.et)) {
          return true;
        }
      };
    },
    // 计算休息时段可以显示的结束时间
    calcResetETTime() {
      return (index, item) => {
        let current_st = this.queryFormData.sleep_range[index].st;
        // 结束时间小于班次的开始时间和结束时间
        if (this.compareTimes(this.queryFormData.st, item) && this.compareTimes(item, this.queryFormData.et, '<=')) {
          if (current_st && this.compareTimes(item, current_st, '<=')) {
            return false;
          } else {
            return true;
          }
        }
      };
    }
  },

  watch: {},

  created() {
    this.time_list = this.generateTimeSlots();
  },

  mounted() {},

  destroyed() {},

  methods: {
    bcSTTimeChange(val) {
      this.initResetTime();
      if (!this.compareTimes(val, this.queryFormData.et)) {
        this.$Message.error('班次的开始时间必须小于结束时间');
      }
    },
    bcETTimeChange() {
      this.initResetTime();
    },
    initResetTime() {
      this.queryFormData.sleep_range = [];
    },
    // 休息时段的开始时间变化
    rsSTTimeChange() {
      this.validateRestPeriods();
    },
    // 休息时段的结束时间变化
    rsETTimeChange() {
      this.validateRestPeriods();
    },
    // 校验数据是否填写
    validData() {
      if (!this.queryFormData.name) {
        this.$Message.error('班次名称不可为空');
        return false;
      }

      if (!this.queryFormData.st || !this.queryFormData.et) {
        this.$Message.error('班次时间不可为空');
        return false;
      }

      for (let item of this.queryFormData.sleep_range) {
        if (!item.st || !item.et) {
          this.$Message.error('休息时间段填写不完整');
          return false;
        }
      }
      return true;
    },
    // 校验休息时段是否有效
    validateRestPeriods() {
      let list = this.queryFormData.sleep_range;

      // 校验每个休息时段的开始时间小于结束时间
      for (let i = 0; i < list.length; i++) {
        const rest = list[i];
        if (this.compareTimes(rest.et, rest.st, '<=')) {
          this.$Message.error('休息开始时间必须小于结束时间');
          return false;
        }

        // 校验休息时段不重叠
        for (let j = i + 1; j < list.length; j++) {
          const otherRest = list[j];
          if (otherRest.st && rest.st && otherRest.et && rest.et) {
            if (
              (this.compareTimes(otherRest.st, rest.st, '<=') && this.compareTimes(rest.st, otherRest.et)) ||
              (this.compareTimes(otherRest.st, rest.et) && this.compareTimes(rest.et, otherRest.et, '<=')) ||
              (this.compareTimes(rest.st, otherRest.st, '<=') && this.compareTimes(otherRest.st, rest.et)) ||
              (this.compareTimes(rest.st, otherRest.et) && this.compareTimes(otherRest.et, rest.et, '<='))
            ) {
              this.$Message.error('休息时段不能重叠');
              return false;
            }
          }
        }
      }

      return true;
    },
    addResetTime() {
      this.queryFormData.sleep_range.push({ st: this.queryFormData.st, et: '' });
    },
    deleteResetTime(index) {
      this.$delete(this.queryFormData.sleep_range, index);
    },
    // 以分钟进行隔断
    generateTimeSlots(minute = 10) {
      const timeSlots = [];
      let currentTime = new Date(0); // 获取一个从 1970 年 1 月 1 日 00:00:00 UTC 开始的时间

      for (let i = 0; i < (24 * 60) / minute; i++) {
        let hours = currentTime.getUTCHours().toString().padStart(2, '0'); // 小时，补充两位
        let minutes = currentTime.getUTCMinutes().toString().padStart(2, '0'); // 分钟，补充两位
        timeSlots.push(`${hours}:${minutes}`);

        currentTime.setUTCMinutes(currentTime.getUTCMinutes() + minute); // 每次加5分钟
      }
      return timeSlots;
    },

    // 比较两个时间段的大小 time1 < time 2
    compareTimes(time1, time2, symbol) {
      // 将时间字符串转化为 Date 对象，日期部分可以使用当天的日期
      const today = new Date().toISOString().split('T')[0]; // 获取今天的日期（不含时间）

      const time1Date = new Date(`${today}T${time1}`); // 将时间拼接到今天的日期
      const time2Date = new Date(`${today}T${time2}`);
      if (symbol === '<=') {
        return time1Date <= time2Date;
      } else {
        return time1Date < time2Date;
      }
    },

    changeVisible(visible) {
      if (visible) {
        if (this.row.id) {
          this.getR2RangeShow();
        }
      } else {
        this.closeModal();
      }
    },

    clearData() {
      this.queryFormData = {
        name: '',
        st: '10:00',
        et: '22:00',
        sleep_range: []
      };
    },

    closeModal() {
      this.clearData();
      this.$emit('input', false);
    },

    confirm() {
      if (this.validateRestPeriods() && this.validData()) {
        this.getR2RangeCheckclash();
      }
    },

    getR2RangeStore() {
      let params = {
        ...this.queryFormData,
        clinic_id: this.clinic_id
      };
      this.$api.getR2RangeStore(params).then(res => {
        this.$emit('success');
        this.closeModal();
      });
    },

    confirmAgain(message) {
      this.$Modal.confirm({
        title: '提示',
        content: `<p style="word-break: break-all">${message}</p>`,
        onOk: () => {
          this.row.id ? this.getR2RangeUpdate() : this.getR2RangeStore();
        }
      });
    },

    // 班次冲突检查
    getR2RangeCheckclash() {
      this.confirmLoading = true;
      let params = {
        ...this.queryFormData,
        id: this.row.id,
        clinic_id: this.clinic_id
      };
      this.$api
        .getR2RangeCheckclash(params)
        .then(res => {
          this.confirmLoading = false;
          if (res.status === 'warn') {
            this.confirmAgain(res.message);
          } else {
            this.row.id ? this.getR2RangeUpdate() : this.getR2RangeStore();
          }
        })
        .catch(err => this.$Message.error(err.errmsg))
        .finally(() => (this.confirmLoading = false));
    },

    getR2RangeUpdate() {
      let params = {
        ...this.queryFormData,
        id: this.row.id,
        clinic_id: this.clinic_id
      };
      this.$api
        .getR2RangeUpdate(params)
        .then(res => {
          this.$emit('success');
          this.closeModal();
        })
        .catch(err => this.$Message.error(err.errmsg));
    },

    getR2RangeShow() {
      let params = {
        id: this.row.id
      };
      this.$api.getR2RangeShow(params).then(res => {
        this.queryFormData.name = res.name;
        this.queryFormData.st = res.st;
        this.queryFormData.et = res.et;
        this.queryFormData.sleep_range = res.sleep_range;
      });
    }
  }
};
</script>

<style scoped lang="less">
::v-deep .ivu-modal-body {
  max-height: 300px;
  min-height: 300px;
  overflow-y: auto;
}

.content {
  .form-box {
  }
}

.ml20 {
  margin-left: 20px;
}
</style>
