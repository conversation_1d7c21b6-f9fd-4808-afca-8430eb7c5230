<template>
  <div class="flex" style="height: 100%">
    <clinic-list
      style="min-width: 246px; max-width: 246px; margin-right: 15px"
      :clinic_id="queryFormData.clinic_id"
      @changeClinic="changeClinic"
      @getSettings="getSettings"
    ></clinic-list>
    <div class="global-list-box flex-1" v-if="queryFormData.clinic_id">
      <div style="font-size: 16px; font-weight: 500; margin-bottom: 10px">{{ clinic_name }}</div>
      <!-- 功能区域 -->
      <div v-if="isRepresent" class="global-list-function" style="padding-top: 0" ref="functionRefs">
        <Button type="primary" @click="addClasses">添加班次</Button>
      </div>

      <!-- 列表区域 -->
      <div class="global-list-table">
        <!--		设置max-width: 99%是为了处理缩放浏览器导致表格超出页面		-->
        <Table
          class="global-table-style"
          :loading="tableLoading"
          :columns="tableCols"
          :data="list"
          :height="$store.state.app.clientHeight - _getOtherHeight"
          style="max-width: 99%; max-height: 80vh"
        >
          <template slot-scope="{ row }" slot="use_nums">
            <Poptip trigger="hover" :disabled="row.physios?.length == 0">
              <div slot="content" class="staff-wrapper">
                <div v-for="(item, index) in row.physios" :key="index" class="staff-box">
                  <span>{{ item.name }}</span>
                  <span class="tag">{{ item.type == 'DOCTOR' ? '医生' : getPhysioName }}</span>
                </div>
              </div>
              <a>{{ row.use_nums }}</a>
            </Poptip>
          </template>

          <template slot-scope="{ row }" slot="range_time_text">
            <span>{{ row.range_time_text || '-' }}</span>
          </template>

          <template slot-scope="{ row }" slot="action">
            <div v-if="isRepresent">
              <a @click="edit(row)" class="mr10">编辑</a>

              <Poptip v-if="Number(row.use_nums) === 0" confirm title="确认删除?" @on-ok="delReserveRange(row.id)">
                <a href="javascript:;">删除</a>
              </Poptip>

              <Tooltip v-else :content="'当前班次有人使用无法删除'" placement="top">
                <span style="color: #999">删除</span>
              </Tooltip>
            </div>
            <div v-else>
              <a @click="edit(row)" class="mr10">查看</a>
            </div>
          </template>
        </Table>
      </div>

      <!-- 分页区域 -->
      <!--    <div class="global-list-page" ref="pageRefs">-->
      <!--      <KPage-->
      <!--        :total="total"-->
      <!--        :page-size.sync="queryFormData.pageSize"-->
      <!--        :current.sync="queryFormData.page"-->
      <!--        @on-change="onPageChange"-->
      <!--      />-->
      <!--    </div>-->

      <!-- 添加班次 -->
      <add-classes
        v-model="addClassesVisible"
        :row="currentRow"
        :clinic_id="queryFormData.clinic_id"
        :isRepresent="isRepresent"
        @success="onSearch"
      ></add-classes>
    </div>
    <div v-else class="empty flex-1" style="flex-direction: column">
      <img src="https://img-sn-i01s-cdn.rsjxx.com/image/2025/0403/102559_48232.png" alt="" width="260" height="260" />
      <div style="font-size: 14px">当前暂无关联诊所，无法进行相关操作</div>
    </div>
  </div>
</template>

<script>
import S from '@/utils/util'; // Some commonly used tools
import listCalc from '@/mixins/listCalc';
import search from '@/mixins/search';
import addClasses from './components/addClasses.vue';
import { getPhysioName } from '@/utils/runtime';
import clinicList from '../components/clinicList.vue';

let init_query_form_data = {
  // page: 1,
  // pageSize: 20,
  clinic_id: '',
  r: ''
};

export default {
  name: 'list',
  components: { addClasses, clinicList },
  mixins: [search, listCalc],

  data() {
    return {
      reserve_height: 60,
      queryFormData: { ...init_query_form_data },
      apiName: 'getR2RangeList',
      tableCols: [
        { title: '班次名称', key: 'name' },
        { title: '班次时间', slot: 'range_time_text' },
        { title: '使用人数', slot: 'use_nums' },
        { title: '操作', slot: 'action' }
      ],
      tableLoading: false,
      total: 0,
      list: [],
      addClassesVisible: false,
      currentRow: {},
      clinic_name: '',
      isRepresent: false // 是否开启代理
    };
  },
  computed: {
    getPhysioName() {
      return getPhysioName();
    }
  },
  watch: {},
  created() {
    // 页面初始化时，需优先获取子组件中的接口返回诊所的诊所列表，并获取默认诊所。故初始化逻辑写入changeClinic方法中
    // this.queryFormData = S.merge(this.queryFormData, this.$route.query);
    // this.submitQueryForm(true);
  },

  methods: {
    edit(row) {
      this.currentRow = row;
      this.addClassesVisible = true;
    },
    addClasses() {
      this.currentRow = {};
      this.addClassesVisible = true;
    },
    onSearch: function () {
      this.queryFormData.page = 1;
      this.submitQueryForm();
    },
    onResetSearch: function () {
      this.queryFormData = { ...init_query_form_data };
      this.timeRange = [];
      this.submitQueryForm();
    },
    submitQueryForm(replace) {
      this.queryFormData.r = S.random(6); // 只有在参数发生变化时才会触发前置守卫；所以添加随机数，保证url参数一定有修改
      let searchObj = {};
      for (const searchKey in this.queryFormData) {
        if (this.queryFormData[searchKey] || this.queryFormData[searchKey] === 0) {
          searchObj[searchKey] = this.queryFormData[searchKey];
        }
      }
      if (replace) {
        this.$router.replace({ query: searchObj });
      } else {
        this.$router.push({ query: searchObj });
      }
      this.loadList();
    },

    changeClinic(item, isReload) {
      if (isReload) {
        this.queryFormData = S.merge(this.queryFormData, this.$route.query);

        console.log('=>(list.vue:171) item', item);
        if (!item) {
          this.queryFormData.clinic_id = '';
          this.$router.replace({ query: this.queryFormData });
          return;
        }
        this.queryFormData.clinic_id = item.clinic_id;
        this.clinic_name = item.clinic_name;
        this.submitQueryForm(true);

        console.log('=>(list.vue:176) 123');
      } else {
        this.queryFormData.clinic_id = item.clinic_id;
        this.clinic_name = item.clinic_name;

        console.log('=>(list.vue:174) ？？？');
        this.submitQueryForm();
      }
    },

    getSettings(val) {
      this.isRepresent = val === '1';
    },

    delReserveRange(id) {
      let params = {
        id
      };
      console.log('=>(detail.vue:241) params', params);
      this.$api
        .delReserveRange(params)
        .then(res => {
          console.log('=>(detail.vue:242) res', res);
          this.$Message.success('删除成功');
          this.loadList();
        })
        .catch(err => this.$Message.error(err.errmsg));
    }
  }
};
</script>

<style lang="less" scoped>
.staff-wrapper {
  width: 300px;
  padding: 10px 10px 0px;
  display: flex;
  flex-wrap: wrap;
  max-height: 400px;
  overflow-y: auto;
  .staff-box {
    background: #ece6ff;
    border-radius: 4px;
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    //margin-right: 2%;
    padding: 4px;
    margin-bottom: 10px;
    //&:nth-child(2n) {
    //  margin-right: 0px;
    //}
  }
}
.tag {
  margin-left: 10px;
  color: #155bd4;
}
</style>
