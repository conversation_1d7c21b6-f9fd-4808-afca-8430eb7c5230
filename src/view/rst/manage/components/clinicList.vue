<template>
  <div class="clinic-box" :style="{ height: $store.state.app.clientHeight - 168 + 'px',minHeight: '300px' }">
    <Form @submit.native.prevent @keyup.enter.native="getRangeClinicList(false)">
      <FormItem style="margin-bottom: 0">
        <Input v-model="keyword" placeholder="搜索门店名称" class="mb10" autocomplete="off">
          <Icon type="ios-search" slot="suffix" @click="getRangeClinicList(false)" />
        </Input>
      </FormItem>
    </Form>

    <div class="clinic-list hidden-scroll">
      <div
        class="clinic-item"
        :class="{ active: item.clinic_id === clinic_id }"
        v-for="(item, index) in clinicList"
        :key="item.clinic_id"
        @click="changeClinic(item, false)"
      >
        <div>{{ item.clinic_name }}</div>
        <div class="no-schedule" v-if="item.is_month_exists_schedule === '0'">(暂无排班)</div>
      </div>
      <div v-show="clinicList.length === 0" class="empty" style="min-height: 50vh">当前暂无诊所</div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'changeClinic.vue',

  components: {},
  props: {
    boxHeight: {
      type: Number,
      default: null
    },
    clinic_id: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      currentClinic: 0,
      clinicList: [],
      keyword: '' // 搜索关键字
    };
  },
  computed: {},
  watch: {},
  created() {
    // 由于列表模式下，需控制操作列的展示与否，故需优先获取代理配置
    this.getReserveRangeSetting();
  },
  mounted() {},
  methods: {
    changeClinic(item, isReload) {
      this.$emit('changeClinic', item, isReload);
    },
    getRangeClinicList(isReload = false) {
      let params = {
        clinic_name: this.keyword
      };
      this.$api
        .getRangeClinicList(params)
        .then(res => {
          this.clinicList = res.list;
          if (isReload) {
            if (!this.$route.query.clinic_id) {
              this.changeClinic(res.list[0], isReload);
            } else {
              let currentClinic = res.list.find(item => item.clinic_id === this.$route.query.clinic_id);
              // 兼容切换合伙人时携带历史诊所id场景
              if (!currentClinic && res.list.length > 0) {
                currentClinic = res.list[0];
              }
              this.changeClinic(currentClinic, isReload);
            }
          }
        })
        .catch(err => this.$Message.error(err.errmsg));
    },

    getReserveRangeSetting() {
      let params = {};
      console.log('=>(detail.vue:241) params', params);
      this.$api
        .getReserveRangeSetting(params)
        .then(res => {
          console.log('=>(detail.vue:242) res', res);
          this.$emit('getSettings', res.is_reserve_union_range);
          this.getRangeClinicList(true);
        })
        .catch(err => this.$Message.error(err.errmsg));
    }
  }
};
</script>
<style lang="less" scoped>
.clinic-box {
  //max-height: 80vh;
  border: 1px solid #ebedf0;
  padding: 16px;
  border-radius: 4px;
  overflow: hidden;
  .clinic-list {
    //max-height: 70vh;
    padding-top: 10px;
    height: 93%;
    overflow-y: auto;
    .clinic-item {
      //padding: 10px 0;
      padding-bottom: 10px;
      font-weight: 400;
      font-size: 14px;
      border-bottom: 1px solid #f1f1f1;
      cursor: pointer;
      margin-bottom: 15px;
      &:hover {
        color: #155bd4;
      }

      .no-schedule {
        font-size: 12px;
        color: #999;
      }
    }
    .active {
      color: #155bd4;
    }
  }
}
::v-deep .ivu-input {
  border-radius: 4px;
}
</style>
