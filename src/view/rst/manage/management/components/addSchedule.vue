<template>
  <Modal
    ref="customModal"
    :value="value"
    width="800px"
    :title="title"
    :footer-hide="false"
    :mask-closable="false"
    :lock-scroll="true"
    @on-visible-change="changeVisible"
  >
    <div class="content">
      <div class="pb-day-box">
        <div class="label">排班日期：</div>
        <div class="pb-day-text" v-if="current_date">{{ current_date }}</div>
        <Tooltip v-else max-width="500" :content="checked_date.join('、')" :disabled="checked_date.length < 4">
          <div class="pb-day-text ecs" :class="{ cursor: checked_date.length > 3 }">{{ checked_date.join('、') }}</div>
        </Tooltip>
      </div>
      <!--  班次排班 -->
      <div v-if="list.length">
        <div class="bc-box" v-for="(item, index) in list" :key="index">
          <div class="bc-item">
            <div class="bc-item-header">
              <div class="desc">{{ item.range_name }}</div>
              <div class="day-text">{{ item.range_time_text }}</div>
            </div>

            <div class="add-staff-box">
              <div class="staff-common staff-add" @click="addStaff(index)" v-if="isRepresent">
                <Icon type="md-add" class="add-icon" />
                <span class="ml4">添加员工</span>
              </div>

              <div class="staff-common staff-item" v-for="(p_item, p_index) in item.physios" :key="p_index">
                <Icon v-if="isRepresent" class="delete" type="ios-close-circle" @click="deleteStaff(index, p_index)" />
                <Tooltip
                  :content="p_item?.name"
                  :disabled="p_item?.name?.length < 6"
                  :class="{ cursor: p_item?.name?.length > 5 }"
                >
                  <div class="staff-name ecs">{{ p_item?.name }}</div>
                </Tooltip>

                <div class="staff-position ecs">{{ p_item?.type === 'DOCTOR' ? '医生' : getPhysioName }}</div>
              </div>

              <div v-if="!isRepresent && item.physios?.length === 0" style="padding-left: 16px; color: #999">
                暂无数据
              </div>
            </div>
          </div>
        </div>
      </div>

      <!--  暂无班次 -->
      <div class="bc-empty" v-else>
        <div class="empty-text">
          {{ isRepresent ? '暂无班次，请前往添加' : '暂无操作权限，请在相关后台完成排班设置' }}
        </div>
        <Button type="primary" @click="toClasses" v-if="isRepresent">前往班次管理</Button>
      </div>
    </div>
    <div slot="footer">
      <Button @click="closeModal">{{ isRepresent ? '取消' : '关闭' }}</Button>
      <Button v-if="isRepresent" :loading="confirmLoading" type="primary" @click="confirm">确定</Button>
    </div>

    <choose-staff
      v-model="addStaffVisible"
      :hasCheckedStaff="hasCheckedStaff"
      :allCheckedStaff="allCheckedStaff"
      :date_list="date_list"
      :range_id="list[current_index]?.range_id"
      :clinic_id="clinic_id"
      @change="staffChange"
    ></choose-staff>
  </Modal>
</template>

<script>
import { getPhysioName } from '@/utils/runtime';
import chooseStaff from './chooseStaff.vue';
import cloneDeep from 'lodash.clonedeep';
export default {
  name: 'addSchedule',
  mixins: [],

  components: { chooseStaff },
  props: {
    value: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      default: '添加排班员工'
    },
    checked_date: {
      type: Array,
      default: () => []
    },
    current_date: {
      type: String,
      default: ''
    },
    ranges: {
      type: Array,
      default: () => []
    },
    current_ranges: {
      type: Array,
      default: () => []
    },
    clinic_id: {
      type: String,
      default: ''
    },
    isRepresent: {
      type: Boolean,
      default: false
    }
  },

  data() {
    return {
      confirmLoading: false,
      addStaffVisible: false,
      list: [],
      current_index: 0
    };
  },

  computed: {
    date_list() {
      return this.current_date ? [this.current_date] : this.checked_date;
    },
    getPhysioName() {
      return getPhysioName();
    },
    allCheckedStaff() {
      let list = [];
      this.list.forEach(item => {
        list = list.concat(item.physios);
      });
      return list || [];
    },
    // 如果是单个时间，需要回显当天的排班的理疗师
    isSingleDate() {
      if (this.current_date || this.checked_date.length === 1) {
        return true;
      } else {
        return false;
      }
    },
    // 已选中的员工
    hasCheckedStaff() {
      return this.list[this.current_index]?.physios || [];
    }
  },

  watch: {},

  created() {},

  mounted() {},

  destroyed() {},

  methods: {
    init() {
      if (this.isSingleDate && this.current_ranges.length) {
        this.list = cloneDeep(this.current_ranges);
      } else {
        this.list = cloneDeep(this.ranges);
      }
    },
    toClasses() {
      this.$router.push({
        path: '/rst/manage/list',
        query: {
          componentsName: 'classes',
          clinic_id: this.clinic_id
        }
      });
      this.closeModal();
    },
    deleteStaff(index, p_index) {
      this.list[index]?.physios?.splice(p_index, 1);
    },
    staffChange(val) {
      let current_item = this.list[this.current_index].physios;
      this.$set(this.list[this.current_index], 'physios', [...current_item, ...val]);
    },
    addStaff(index) {
      this.current_index = index;
      this.addStaffVisible = true;
    },

    changeVisible(visible) {
      if (visible) {
        this.init();
      } else {
        this.closeModal();
      }
    },

    clearData() {
      this.list = [];
      this.current_index = 0;
    },

    closeModal() {
      this.clearData();
      this.$emit('close');
      this.$emit('input', false);
    },

    confirm() {
      this.getR2ScheduleBatchsave();
    },

    // 设置排班
    getR2ScheduleBatchsave() {
      this.confirmLoading = true;
      let params = {
        date_list: this.current_date ? [this.current_date] : this.checked_date,
        schedules: this.handlerSchedules(),
        clinic_id: this.clinic_id
      };
      this.$api
        .getR2ScheduleBatchsave(params)
        .then(res => {
          this.$emit('success');
          this.closeModal();
        })
        .catch(err => this.$Message.error(err.errmsg))
        .finally(() => (this.confirmLoading = false));
    },

    handlerSchedules() {
      let list = [];
      this.list.forEach(item => {
        if (item?.physios?.length) {
          list.push({
            range_id: item.range_id,
            physio_ids: item?.physios?.map(p_item => p_item.id)
          });
        }
      });
      return list || [];
    }
  }
};
</script>

<style scoped lang="less">
::v-deep .ivu-modal-body {
  max-height: 500px;
  min-height: 500px;
  padding: 0px;
  overflow-y: auto;
}

.content {
  width: 100%;
  padding: 0 16px 16px;

  .pb-day-box {
    display: flex;
    align-items: center;
    font-size: 14px;
    position: sticky;
    top: 0px;
    background: #fff;
    height: 40px;
    z-index: 2;

    .label {
      color: #ccc;
      min-width: fit-content;
    }

    .pb-day-text {
      margin-left: 4px;
      margin-top: 4px;
      font-size: 16px;
    }
  }

  .bc-box {
    margin-top: 10px;

    .bc-item {
      margin-bottom: 10px;

      .bc-item-header {
        background: #f7f8fa;
        color: #323233;
        padding: 16px;
        display: flex;
        align-items: center;

        .day-text {
          opacity: 0.5;
          margin-left: 8px;
        }
      }

      .add-staff-box {
        margin-top: 20px;
        display: flex;
        flex-wrap: wrap;

        .staff-common {
          position: relative;
          border-radius: 4px;
          font-size: 14px;
          padding: 8px;
          box-sizing: border-box;
          margin-right: 10px;
          margin-bottom: 10px;
          width: 99px;
          height: 60px;

          &:nth-child(7n) {
            margin-right: 0px;
          }
        }

        .staff-add {
          border: 1px dashed rgba(50, 50, 51, 0.15);
          cursor: pointer;
          display: flex;
          justify-content: center;
          align-items: center;

          .add-icon {
            font-size: 20px;
          }

          &:hover {
            border-color: #155bd4;
            color: #155bd4;
          }
        }

        .staff-item {
          background: #f5f5f5;

          .delete {
            position: absolute;
            right: -6px;
            top: -6px;
            border-radius: 50%;
            color: #b9a1a1;
            font-size: 22px;
            cursor: pointer;

            &:hover {
              transform: scale(1.1);
            }
          }

          .staff-position {
            opacity: 0.5;
          }
        }
      }
    }
  }

  .bc-empty {
    margin-top: 200px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;

    .empty-text {
      font-size: 14px;
      color: #333;
      margin-bottom: 16px;
    }
  }
}
.cursor {
  cursor: pointer;
}
</style>
