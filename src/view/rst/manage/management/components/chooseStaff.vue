<template>
  <Modal
    ref="customModal"
    :value="value"
    width="800px"
    :title="title"
    :footer-hide="false"
    :mask-closable="false"
    :lock-scroll="true"
    @on-visible-change="changeVisible"
  >
    <div class="content">
      <div class="staff-collection" v-if="staff_list.length || doctor_list.length">
        <Checkbox :indeterminate="indeterminate" v-model="checkAll" @on-change="onAllChange">员工集合</Checkbox>
      </div>
      <div class="staff-content">
        <div v-if="staff_list.length || doctor_list.length">
          <div class="title">{{ getPhysioName }}子集</div>
          <CheckboxGroup v-model="checkedStaffGroup" v-if="staff_list.length">
            <Checkbox :label="item.id" v-for="(item, index) in staff_list" :key="index">
              <div class="staff-name">{{ item.name }}</div>
              <div class="staff-position">{{ getPhysioName }}</div>
            </Checkbox>
          </CheckboxGroup>
          <div v-else style="margin-bottom: 10px">暂无可添加排班员工</div>
          <div class="title" v-if="is_rst">医生子集</div>
          <CheckboxGroup v-model="checkedDoctorGroup" v-if="doctor_list.length && is_rst">
            <Checkbox :label="item.id" v-for="(item, index) in doctor_list" :key="index">
              <div class="staff-name">{{ item.name }}</div>
              <div class="staff-position">医生</div>
            </Checkbox>
          </CheckboxGroup>
          <div v-if="!doctor_list.length && is_rst" style="margin-bottom: 10px">暂无可添加排班员工</div>
        </div>
        <div class="empty" v-else>暂无可添加排班员工</div>
      </div>
    </div>
    <div slot="footer">
      <Button @click="closeModal">取消</Button>
      <Button :loading="confirmLoading" type="primary" @click="confirm">确定</Button>
    </div>
  </Modal>
</template>

<script>
import { getPhysioName, isRstClinic } from '@/utils/runtime';
export default {
  name: 'chooseStaff',
  mixins: [],

  components: {},
  props: {
    value: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      default: '添加排班员工'
    },
    // 单个日期已选中的员工
    hasCheckedStaff: {
      type: Array,
      default: () => {}
    },
    // 所有排班选中的员工
    allCheckedStaff: {
      type: Array,
      default: () => []
    },
    // 排班的班次
    date_list: {
      type: Array,
      default: () => []
    },
    // 班次id
    range_id: {
      type: String,
      default: ''
    },
    clinic_id: {
      type: String,
      default: ''
    }
  },

  data() {
    return {
      confirmLoading: false,
      checkAll: false,
      checkedStaffGroup: [], // 选中的理疗师
      checkedDoctorGroup: [], // 选中的医生
      staff_list: [], // 理疗师合集
      doctor_list: [] // 医生合集
    };
  },

  computed: {
    getPhysioName() {
      return getPhysioName();
    },
    is_rst() {
      return isRstClinic();
    },
    indeterminate() {
      let staff_length = this.staff_list.length;
      let staff_checked_length = this.checkedStaffGroup.length;
      let doctor_length = this.doctor_list.length;
      let doctor_checked_length = this.checkedDoctorGroup.length;
      if (
        staff_checked_length + doctor_checked_length > 0 &&
        staff_checked_length + doctor_checked_length < staff_length + doctor_length
      ) {
        return true;
      } else {
        return false;
      }
    }
  },

  watch: {
    checkedStaffGroup() {
      this.changeAllStatus();
    },
    checkedDoctorGroup() {
      this.changeAllStatus();
    }
  },

  created() {},

  mounted() {},

  destroyed() {},

  methods: {
    changeAllStatus() {
      if (
        this.checkedStaffGroup.length === this.staff_list.length &&
        this.checkedDoctorGroup.length === this.doctor_list.length
      ) {
        this.checkAll = true;
      } else {
        this.checkAll = false;
      }
    },
    onAllChange(val) {
      if (val) {
        this.checkedStaffGroup = this.staff_list.map(item => item.id);
        this.checkedDoctorGroup = this.doctor_list.map(item => item.id);
      } else {
        this.checkedStaffGroup = [];
        this.checkedDoctorGroup = [];
      }
    },

    changeVisible(visible) {
      if (visible) {
        // todo 流程优先 后续回滚榕树堂与普通诊所区分
        if (isRstClinic()) {
          this.getPhysicalTMemberlist();
        } else {
          this.getPhysicalTList();
        }
      } else {
        this.closeModal();
      }
    },

    clearData() {
      this.checkedStaffGroup = [];
      this.checkedDoctorGroup = [];
      this.checkAll = false;
      this.confirmLoading = false;
    },

    closeModal() {
      this.clearData();
      this.$emit('input', false);
    },

    confirm() {
      let list = this.handlerGroup();
      if (list.length === 0) {
        this.$Message.error('至少选择一个排班员工');
        return;
      }
      this.getR2ScheduleCheckclash();
    },

    handleGroupIds() {
      let ids = [];
      let source_list = this.handlerGroup();
      source_list.forEach(item => {
        ids.push(item.id);
      });
      return ids;
    },

    confirmAgain(message) {
      this.$Modal.confirm({
        title: '提示',
        content: `<p style="word-break: break-all">${message}</p>`,
        onOk: () => {
          this.$emit('change', this.handlerGroup());
          this.closeModal();
        }
      });
    },

    // 医生排班冲突检查
    getR2ScheduleCheckclash() {
      this.confirmLoading = true;
      let params = {
        range_id: this.range_id,
        date_list: this.date_list || [],
        physio_ids: this.handleGroupIds()
      };
      this.$api
        .getR2ScheduleCheckclash(params)
        .then(res => {
          this.confirmLoading = false;
          if (res.status === 'warn') {
            this.confirmAgain(res.message);
          } else {
            this.$emit('change', this.handlerGroup());
            this.closeModal();
          }
        })
        .finally(() => (this.confirmLoading = false));
    },

    handlerGroup() {
      let list = [];
      this.staff_list.map(item => {
        if (this.checkedStaffGroup.includes(item.id)) {
          list.push(item);
        }
      });
      this.doctor_list.map(item => {
        if (this.checkedDoctorGroup.includes(item.id)) {
          list.push(item);
        }
      });

      console.log('=>(chooseStaff.vue:246) list', list);
      return list;
    },

    // 获取理疗师
    getPhysicalTList() {
      let params = {
        page: 1,
        pageSize: 1000,
        status: 'ON'
      };
      this.$api.getPhysicalTList(params).then(res => {
        this.staff_list = [];
        res.list.forEach(item => {
          if (!this.allCheckedStaff.some(a_item => a_item.id === item.id)) {
            this.staff_list.push(item);
          }
        });
        this.echoHasCheckedStaff();
      });
    },

    // 获取员工（理疗师+医生）
    getPhysicalTMemberlist() {
      let params = {
        page: 1,
        pageSize: 1000,
        status: 'ON',
        clinic_id: this.clinic_id
      };
      this.$api.getPhysicalTMemberlist(params).then(res => {
        this.staff_list = [];
        this.doctor_list = [];
        res.physio_list?.forEach(item => {
          if (!this.allCheckedStaff.some(a_item => a_item.id === item.id)) {
            this.staff_list.push(item);
          }
        });
        res.doctor_list?.forEach(item => {
          if (!this.allCheckedStaff.some(a_item => a_item.id === item.id)) {
            this.doctor_list.push(item);
          }
        });
        this.echoHasCheckedStaff();
      });
    },

    echoHasCheckedStaff() {
      this.hasCheckedStaff?.forEach(item => {
        this.staff_list?.forEach(c_item => {
          if (item.id === c_item.id) {
            this.checkedStaffGroup.push(item.id);
          }
        });
      });

      this.hasCheckedStaff?.forEach(item => {
        this.doctor_list?.forEach(c_item => {
          if (item.id === c_item.id) {
            this.checkedDoctorGroup.push(item.id);
          }
        });
      });
    }
  }
};
</script>

<style scoped lang="less">
::v-deep .ivu-modal-body {
  max-height: 500px;
  min-height: 500px;
  overflow-y: auto;
}

::v-deep .ivu-checkbox-group {
  display: flex;
  flex-direction: column;

  .ivu-checkbox-group-item {
    margin-bottom: 10px;
    display: flex;
    align-items: center;
    color: #323233;
    font-size: 14px;

    .staff-name {
      margin-left: 15px;
    }

    .staff-position {
      margin-left: 20px;
      opacity: 0.5;
    }
  }
}

.content {
  margin: 16px;
  border: 1px solid #dcdee0;
  height: 460px;

  .staff-collection {
    padding: 10px 20px;
    border-bottom: 1px solid #dcdee0;
  }

  .staff-content {
    margin: 10px 0px;
    padding: 10px 30px;
    height: 400px;
    overflow-y: auto;
  }
}
.empty {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  color: #333;
}

.title {
  margin-bottom: 10px;
  font-weight: bold;
  font-size: 14px;
}
</style>
