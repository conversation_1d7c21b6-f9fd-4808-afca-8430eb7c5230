<template>
  <Modal
    ref="listBatchSchedule"
    :value="visible"
    title="批量排班"
    :mask-closable="false"
    width="400px"
    @on-cancel="cancel"
    @on-visible-change="changeVisible"
  >
    <div class="batch-schedule-content">
      <div class="select-range">
        <span class="label">选择班次：</span>
        <Select v-model="selectedRange" style="width: 200px" placeholder="请选择班次" clearable>
          <Option v-for="range in rangeList" :key="range.id" :value="range.id">{{ range.name }}</Option>
        </Select>
      </div>
    </div>
    <div slot="footer">
      <Button @click="cancel">取消</Button>
      <Button type="primary" @click="submitForm" :loading="submitLoading">确定</Button>
    </div>
  </Modal>
</template>

<script>
import S from 'utils/util';
const initFormData = {
  code: '' //销售单编号；如果不填写，创建时系统会自动生成
};

export default {
  name: 'listBatchSchedule',
  mixins: [],
  components: {},
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    selectedStaff: {
      type: Object,
      default: () => {}
    },
    dateRange: {
      type: Array,
      default: () => []
    },
    clinic_id: {
      type: String,
      default: ''
    },
    startDate: {
      type: String,
      default: ''
    },
    endDate: {
      type: String,
      default: ''
    }
  },

  data() {
    return {
      formData: { ...initFormData },
      submitLoading: false,
      rangeList: [],
      selectedRange: null
    };
  },

  computed: {},

  watch: {},

  created() {},

  mounted() {},

  methods: {
    // 获取班次列表
    getRangeList() {
      let params = {
        clinic_id: this.clinic_id
      };
      this.$api.getR2RangeList(params).then(res => {
        this.rangeList = res.list || [];
      });
    },

    changeVisible(val) {
      if (val) {
        this.getRangeList();
      }
      if (!val) {
        this.selectedRange = null;
      }
    },

    cancel() {
      this.$emit('update:visible', false);
    },

    submitForm() {
      if (!this.selectedRange) {
        this.$Message.error('请选择班次');
        return;
      }

      this.submitLoading = true;
      // 构建提交数据
      const params = {
        date_list: this.getDateList(),
        schedules: [{ range_id: this.selectedRange, physio_ids: Object.keys(this.selectedStaff) }],
        clinic_id: this.clinic_id,
        is_clean_physio: 1
      };

      this.$api
        .getR2ScheduleBatchsave(params)
        .then(res => {
          this.$emit('on-success');
          this.submitLoading = false;
          this.cancel();
        })
        .catch(err => this.$Message.error(err.errmsg))
        .finally(() => (this.submitLoading = false));
    },

    // 获取日期列表
    getDateList() {
      const startDate = this.$moment(this.startDate);
      const endDate = this.$moment(this.endDate);
      const dateList = [];

      let currentDate = startDate.clone();
      while (currentDate.isSameOrBefore(endDate)) {
        dateList.push(currentDate.format('YYYY-MM-DD'));
        currentDate.add(1, 'day');
      }

      console.log('=>(listBatchSchedule.vue:128) dateList', dateList);
      return dateList;
    }
  },

  destroyed() {}
};
</script>

<style scoped lang="less">
.batch-schedule-content {
  padding: 20px;

  .select-range {
    display: flex;
    align-items: center;

    .label {
      width: 70px;
      color: #666;
    }
  }
}
</style>
