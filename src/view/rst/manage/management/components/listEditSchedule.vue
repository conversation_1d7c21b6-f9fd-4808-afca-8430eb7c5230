<template>
  <Modal
    ref="listEditSchedule"
    :value="visible"
    title="编辑排班"
    :mask-closable="false"
    width="500px"
    @on-cancel="cancel"
    @on-visible-change="changeVisible"
  >
    <div class="schedule-edit-content">
      <div class="staff-info">
        <span class="label">员工姓名：</span>
        <span>{{ staff?.name }}</span>
      </div>
      <div class="schedule-list">
        <div v-for="(item, index) in scheduleList" :key="index" class="schedule-item">
          <span class="date">{{ item.date_text }}</span>
          <Select v-model="item.range_id" style="width: 200px" clearable>
            <Option v-for="range in rangeList" :key="range.id" :value="range.id">{{ range.name }}</Option>
          </Select>
        </div>
      </div>
    </div>
    <div slot="footer">
      <Button @click="cancel">取消</Button>
      <Button type="primary" @click="submitForm" :loading="submitLoading">确认</Button>
    </div>
  </Modal>
</template>

<script>
import S from 'utils/util';
const initFormData = {
  code: '' //销售单编号；如果不填写，创建时系统会自动生成
};

export default {
  name: 'listEditSchedule',
  mixins: [],
  components: {},
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    id: {
      type: String,
      default: ''
    },
    staff: {
      type: Object,
      default: () => {}
    },
    clinic_id: {
      type: String,
      default: ''
    },
    pageStartDate: {
      type: String,
      default: ''
    },
    startDate: {
      type: String,
      default: ''
    },
    endDate: {
      type: String,
      default: ''
    }
  },

  data() {
    return {
      formData: { ...initFormData },
      submitLoading: false,
      rangeList: [],
      scheduleList: []
    };
  },

  computed: {},

  watch: {},

  created() {},

  mounted() {},

  methods: {
    // 获取班次列表
    getRangeList() {
      let params = {
        clinic_id: this.clinic_id
      };
      this.$api.getR2RangeList(params).then(res => {
        this.rangeList = res.list || [];
      });
    },

    // 初始化排班列表
    initScheduleList() {
      const startDate = this.$moment(this.startDate);
      const endDate = this.$moment(this.endDate);
      const scheduleList = [];

      let currentDate = startDate.clone();
      let currentIndex = 0;
      // 处理页面起始日期在今日之前的场景
      if (this.pageStartDate !== this.startDate) {
        const days = this.$moment(this.startDate).diff(this.$moment(this.pageStartDate), 'days');
        currentIndex = currentIndex + days;
      }
      while (currentDate.isSameOrBefore(endDate)) {
        const weekDay = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'][currentDate.day()];
        scheduleList.push({
          date: currentDate.format('YYYY-MM-DD'),
          date_text: `${currentDate.format('MM-DD')} ${weekDay}`,
          range_id: this.staff.list[currentIndex].range_id === '0' ? '' : this.staff.list[currentIndex].range_id
        });
        currentDate.add(1, 'day');
        currentIndex++;
      }

      console.log('=>(listEditSchedule.vue:111) this.scheduleList', scheduleList);
      this.scheduleList = scheduleList;
    },

    changeVisible(val) {
      if (val) {
        this.getRangeList();
        this.initScheduleList();
      } else {
        this.formData = { ...initFormData };
        this.scheduleList = [];
      }
    },
    cancel() {
      this.$emit('update:visible', false);
    },
    submitForm() {
      this.submitLoading = true;
      // 这里添加提交逻辑
      const params = {
        physio_id: this.staff.physio_id,
        schedules: this.scheduleList.map(item => ({
          date: item.date,
          range_id: item.range_id
        })),
        clinic_id: this.clinic_id
      };

      this.$api.saveMemberSchedule(params).then(res => {
        this.$Message.success('编辑排班成功');
        this.submitLoading = false;
        this.cancel();
        this.$emit('refresh');
      });
    }
  },

  destroyed() {}
};
</script>

<style scoped lang="less">
.schedule-edit-content {
  padding: 0 20px;

  .staff-info {
    margin-bottom: 20px;

    .label {
      color: #666;
    }
  }

  .schedule-list {
    max-height: 350px;
    overflow-y: auto;

    .schedule-item {
      display: flex;
      align-items: center;
      margin-bottom: 15px;

      .date {
        width: 120px;
        color: #333;
      }
    }
  }
}
</style>
