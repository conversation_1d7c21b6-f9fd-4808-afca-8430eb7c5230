<template>
  <Modal
    ref="customModal"
    :value="value"
    width="500px"
    :title="title"
    :footer-hide="false"
    :closable="false"
    :mask-closable="true"
    :lock-scroll="true"
    @on-visible-change="changeVisible"
  >
    <div class="content">
      <div class="title">
        <span>{{ row.name }}</span>
        <span style="margin-left: 10px">{{ row.date }}</span>
      </div>
      <div class="tip">可批量修改当天可提供服务，只针对小程序下单用户不会被显示</div>
      <CheckboxGroup class="group-box" v-model="groupList">
        <div v-for="(item, index) in service_list" :key="index" class="checkbox-box">
          <Checkbox :label="item.id" :disabled="!isRepresent">{{ item.service_name }}</Checkbox>
        </div>
      </CheckboxGroup>
    </div>
    <div slot="footer">
      <Button @click="closeModal">{{ isRepresent ? '取消' : '关闭' }}</Button>
      <Button v-if="isRepresent" :loading="confirmLoading" type="primary" @click="confirm">确定</Button>
    </div>
  </Modal>
</template>

<script>
export default {
  name: 'updatePhysioModal',
  mixins: [],

  components: {},
  props: {
    value: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      default: '可批量修改当天可提供服务'
    },
    row: {
      type: Object,
      default: () => {}
    },
    isRepresent: {
      type: Boolean,
      default: false
    },
    clinic_id: {
      type: String,
      default: ''
    }
  },

  data() {
    return {
      confirmLoading: false,
      groupList: [],
      service_list: []
    };
  },

  computed: {},

  watch: {},

  created() {},

  mounted() {},

  destroyed() {},

  methods: {
    changeVisible(visible) {
      if (visible) {
        this.getR2SPhysioServiceInfo();
      } else {
        this.closeModal();
      }
    },

    clearData() {
      this.groupList = [];
      this.service_list = [];
    },

    closeModal() {
      this.clearData();
      this.$emit('input', false);
    },

    /**
     * @description: 确定事件
     * */
    confirm() {
      this.getR2SPhysioeExcludeservice();
    },

    getR2SPhysioServiceInfo() {
      let params = {
        range_id: this.row.range_id,
        physio_id: this.row.physio_id,
        date: this.row.date,
        clinic_id: this.clinic_id
      };
      this.$api
        .getR2SPhysioServiceInfo(params)
        .then(res => {
          this.service_list = res.service_list || [];
          this.handleServiceEcho();
        })
        .catch(err => this.$Message.error(err.errmsg));
    },

    handleServiceEcho() {
      this.service_list.forEach(item => {
        if (item.checked == '1') {
          this.groupList.push(item.id);
        }
      });
    },

    getUnCheckedList() {
      let list = [];
      this.service_list.forEach(item => {
        let index = this.groupList.findIndex(group_item => group_item == item.id);
        if (index == -1) {
          list.push(item.id);
        }
      });
      return list;
    },

    getR2SPhysioeExcludeservice() {
      this.confirmLoading = true;
      let params = {
        range_id: this.row.range_id,
        physio_id: this.row.physio_id,
        date: this.row.date,
        exclude_ids: this.getUnCheckedList(),
        clinic_id: this.clinic_id
      };
      this.$api
        .getR2SPhysioeExcludeservice(params)
        .then(res => {
          this.$Message.success('设置成功');
          this.$emit('success');
          this.closeModal();
        })
        .catch(err => this.$Message.error(err.errmsg))
        .finally(() => (this.confirmLoading = false));
    }
  }
};
</script>

<style scoped lang="less">
.content {
  .title {
    display: flex;
    width: 100%;
    height: 20px;
    line-height: 20px;
    font-size: 14px;
    color: #17233d;
    font-weight: 500;
  }
  .tip {
    margin-top: 10px;
    margin-bottom: 20px;
    background: #f2edff;
    border-radius: 4px;
    border: 1px solid #b39af7;
    padding: 10px 15px;
    display: flex;
    align-items: center;
  }
  .group-box {
    margin-top: 10px;
  }
}
::v-deep .ivu-modal-body {
  max-height: 500px;
  min-height: 250px;
  overflow-y: auto;
}
::v-deep .ivu-modal-header {
  display: none;
}
::v-deep .ivu-checkbox-group {
  display: flex;
  flex-wrap: wrap;
  .ivu-checkbox-group-item {
    width: fit-content;
    margin-bottom: 6px;
    display: flex;
    align-items: flex-start;
    .ivu-checkbox {
      margin-right: 4px;
      margin-top: 1px;
    }
  }
}
.checkbox-box {
  width: 33%;
}
</style>
