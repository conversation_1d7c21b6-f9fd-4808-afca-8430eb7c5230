export default {
  computed: {
    format_currentDate() {
      return this.$moment(this.new_currentDate).format('YYYY年MM月');
    },
    // 当前年份
    currentYear() {
      const date = this.$moment(this.new_currentDate, 'YYYY-MM');
      return date.year();
    },
    // 当前月份（0-11）
    currentMonth() {
      const date = this.$moment(this.new_currentDate, 'YYYY-MM');
      return date.month();
    },
    // 当月天数
    daysInMonth() {
      return new Date(this.currentYear, this.currentMonth + 1, 0).getDate();
    },
    // 当月第一天是星期几 返回值范围为 0 到 6，分别对应星期日到星期六。
    firstDayOfMonth() {
      return new Date(this.currentYear, this.currentMonth, 1).getDay();
    },
    // 上月的天数
    daysInPrevMonth() {
      return new Date(this.currentYear, this.currentMonth, 0).getDate();
    },
  },
  watch: {
    isChecked(val) {
      if (val) {
        this.checkedAll();
      } else {
        this.unCheckedAll();
      }
    },
  },
  methods: {
    checkedAll() {
      this.calendar_list.forEach(chunk_itme => {
        chunk_itme.isChecked = true;
        chunk_itme.list.forEach(item => {
          if (!this.compareTimes(item.full_date, this.today_date) && item.current) {
            item.isChecked = true;
          }
        });
      });
      this.calendar_title.forEach(item => (item.isChecked = true));
    },
    unCheckedAll() {
      this.isChecked = false;
      this.calendar_list.forEach(chunk_itme => {
        chunk_itme.isChecked = false;
        chunk_itme.list.forEach(item => (item.isChecked = false));
      });
      this.calendar_title.forEach(item => (item.isChecked = false));
    },
    // 对表头和全部勾选清空
    initHeaderCheckedStatus() {
      this.calendar_title.forEach(item => (item.isChecked = false));
      this.isChecked = false;
    },
    // 补零
    padZero(num, length = 2) {
      return String(num).padStart(length, '0');
    },
    // 列选中切换
    colChecked(val, header_index) {
      this.calendar_list.forEach((item, index) => {
        item.list.forEach((chunk_item, chunk_index) => {
          if (
            header_index === chunk_index &&
            !this.compareTimes(chunk_item.full_date, this.today_date) &&
            chunk_item.current
          ) {
            this.$set(this.calendar_list[index].list[chunk_index], 'isChecked', val);
          }
        });
      });
    },
    // 行选中切换
    rowChecked(val, row_index) {
      this.calendar_list.forEach((item, index) => {
        if (row_index === index) {
          item.list.forEach((chunk_item, chunk_index) => {
            if (!this.compareTimes(chunk_item.full_date, this.today_date) && chunk_item.current) {
              this.$set(this.calendar_list[index].list[chunk_index], 'isChecked', val);
            }
          });
        }
      });
    },
    // 计算日历数组
    calc_calendar_list() {
      const days = [];
      const prevMonthDays = this.daysInPrevMonth; // 上月总天数
      let nextMonthDays = 42 - (this.firstDayOfMonth + this.daysInMonth); // 下月天数
      // 上个月的尾部日期
      for (let i = this.firstDayOfMonth - 1; i >= 0; i--) {
        days.push({
          full_date: this.$moment(`${this.new_currentDate}-${prevMonthDays - i}`)
            .subtract(1, 'months')
            .format('YYYY-MM-DD'),
          date: this.padZero(prevMonthDays - i),
          current: false, // 非当前月
        });
      }

      // 当前月日期
      for (let i = 1; i <= this.daysInMonth; i++) {
        days.push({
          full_date: this.$moment(`${this.new_currentDate}-${i}`).format('YYYY-MM-DD'),
          date: this.padZero(i),
          current: true, // 当前月
          isChecked: false,
        });
      }
      if (nextMonthDays >= 7) {
        nextMonthDays = nextMonthDays - 7;
      }
      // 下个月的开头日期
      for (let i = 1; i <= nextMonthDays; i++) {
        days.push({
          full_date: this.$moment(`${this.new_currentDate}-${nextMonthDays}`).add(1, 'months').format('YYYY-MM-DD'),
          date: this.padZero(i),
          current: false, // 非当前月
          isChecked: false,
        });
      }
      let list = this.chunkArr(days, 7) || [];
      this.calendar_list = list;
    },
    // 获取当前日期
    getCurrentDate() {
      this.new_currentDate = this.$moment().format('yyyy-MM');
    },
    // 更新日期的选中状态
    dayCheckedChange(val, index, chunk_index) {
      this.$set(this.calendar_list[index].list[chunk_index], 'isChecked', val);
    },
    // 在当前的基础上，对月份进行加减
    updateMonth(index) {
      if (index > 0) {
        this.new_currentDate = this.$moment(this.new_currentDate).add(1, 'months').format('yyyy-MM');
      } else {
        this.new_currentDate = this.$moment(this.new_currentDate).subtract(1, 'months').format('yyyy-MM');
      }
      this.initHeaderCheckedStatus();
      this.calc_calendar_list();
      this.submitQueryForm();
    },
    // 将传入的日期与当前日期做比较，返回大于或者小于
    dateComparison(time) {
      const current_date = this.$moment(`${this.new_currentDate}-01`);
      const target_date = this.$moment(`${time}`);
      return target_date.isBefore(current_date);
    },
    // 判断日期是否已过期
    dateIsExpire(time) {
      // 定义目标日期和当前日期
      const targetDate = this.$moment(time);
      const currentDate = this.$moment().format('YYYY-MM-DD'); // 获取当前日期
      return targetDate.isBefore(currentDate);
    },
    // 切割
    chunkArr(arr, size) {
      //判断如果不是数组(就没有length)，或者size没有传值，size小于1，就返回空数组
      if (!arr.length || !size || size < 1) return [];
      let [start, end, result] = [null, null, []];
      for (let i = 0; i < Math.ceil(arr.length / size); i++) {
        start = i * size;
        end = start + size;
        result.push({
          isChecked: false,
          list: arr.slice(start, end),
        });
      }
      return result;
    },
    // 比较两个时间段的大小 time1 < time 2
    compareTimes(time1, time2, symbol) {
      const time1Date = new Date(time1);
      const time2Date = new Date(time2);
      if (symbol === '<=') {
        return time1Date <= time2Date;
      } else {
        return time1Date < time2Date;
      }
    },
  },
};
