<template>
  <Modal
    ref="compareModal"
    :value="visible"
    title="修改日志记录"
    :mask-closable="false"
    width="900px"
    @on-cancel="cancel"
    @on-visible-change="changeVisible"
    class-name="compareModal"
    footer-hide
  >
    <div class="custom-modal-content" :style="{ maxHeight: $store.state.app.clientHeight - 400 + 'px' }">
      <div class="flex flex-item-between">
        <div class="compare-item">
          <p class="block-header">变更前</p>
          <div class="sub-title">基本信息</div>
          <div>导师姓名：{{ compareInfo.before?.name }}</div>
          <div>导师手机号：{{ compareInfo.before?.mobile }}</div>
          <div>导师身份证号：{{ compareInfo.before?.idcard }}</div>
          <div>
            头像：<img
              :src="
                compareInfo.before?.avatar
                  | imageStyle('B.w300', 'http://img-sn-i01s-cdn.rsjxx.com/rsjxx/2023/1220/151459_81411.png')
              "
              alt=""
              style="width: 30px; height: 30px; border-radius: 50%"
            />
          </div>
          <div class="sub-title">佣金抽成</div>
          <div>佣金比例：{{ compareInfo.before?.scale }}%</div>
        </div>
        <div class="compare-item">
          <p class="block-header">变更前</p>
          <div class="sub-title">基本信息</div>
          <div>导师姓名：{{ compareInfo.after?.name }}</div>
          <div>导师手机号：{{ compareInfo.after?.mobile }}</div>
          <div>导师身份证号：{{ compareInfo.after?.idcard }}</div>
          <div>
            头像：<img
              :src="
                compareInfo.after?.avatar
                  | imageStyle('B.w300', 'http://img-sn-i01s-cdn.rsjxx.com/rsjxx/2023/1220/151459_81411.png')
              "
              alt=""
              style="width: 30px; height: 30px; border-radius: 50%"
            />
          </div>
          <div class="sub-title">佣金抽成</div>
          <div>佣金比例：{{ compareInfo.after?.scale }}%</div>
        </div>
      </div>
    </div>
    <div slot="footer">
      <Button @click="cancel">取消</Button>
      <Button type="primary" @click="submitForm" :loading="submitLoading">确定</Button>
    </div>
  </Modal>
</template>

<script>
import S from 'utils/util';
const initFormData = {
  code: '' //销售单编号；如果不填写，创建时系统会自动生成
};

export default {
  name: 'editActivityModal',
  mixins: [],
  components: {},
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    id: {
      type: String,
      default: ''
    },
    compareInfo: {
      type: Object,
      default: {
        before: {},
        after: {}
      }
    }
  },

  data() {
    return {
      formData: { ...initFormData },
      submitLoading: false
    };
  },

  computed: {},

  watch: {},

  created() {},

  mounted() {},

  methods: {
    changeVisible(val) {
      if (val) {
      } else {
        this.formData = { ...initFormData }; // 重置数据
        // this.$refs['modal'].$el.querySelector('.ivu-modal-body').scrollTop = 0; // 重置滚动条高度
      }
    },
    cancel() {
      this.$emit('update:visible', false);
    },
    submitForm() {}
  },

  destroyed() {}
};
</script>

<style scoped lang="less">
.block-header {
  background-color: #efefef;
  padding: 10px;
  margin: 0px 0px 20px 0;
  position: relative;
}
.compare-item {
  flex: 1;
  &:first-child {
    margin-right: 40px;
  }
  .sub-title {
    font-size: 14px;
    font-weight: 700;
    margin-top: 5px;
    margin-bottom: 5px;
  }
}
</style>
<style lang="less">
.compareModal {
  .custom-modal-content {
    overflow-y: auto;
    padding-right: 20px;
  }
  .ivu-modal-body {
    padding: 20px 10px 20px 30px;
  }
}
</style>
