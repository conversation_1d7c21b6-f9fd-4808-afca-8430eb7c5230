<template>
  <div class="stored-wrapper">
    <Table
      :loading="tableLoading"
      :columns="tableCols"
      :data="list"
      :height="$store.state.app.clientHeight - 420 < 500 ? 500 : $store.state.app.clientHeight - 420"
    >
      <template slot-scope="{ row, index }" slot="time">
        {{ row.create_time | date_format }}
      </template>
      <template slot-scope="{ row, index }" slot="operator">
        {{ row.operator || '-' }}
      </template>
      <template slot-scope="{ row, index }" slot="operator_desc">
        {{ row.remark || '-' }}
        <a class="ml10" v-if="row.data?.before" @click="showCompare(row)">查看更改明细</a>
      </template>
    </Table>
    <p style="height: 10px"></p>
    <compare-modal :visible.sync="compareVisible" :compare-info="compareInfo"></compare-modal>
  </div>
</template>

<script>
import compareModal from './components/compareModal.vue';
let init_query_form_data = {
  page: 1,
  pageSize: 20,
};
export default {
  name: 'serviceList',
  components: { compareModal },
  mixins: [],
  props: {},
  data() {
    return {
      tableLoading: false, // 表格获取的数据的loading
      tableCols: [
        { title: '时间', slot: 'time', align: 'center' },
        { title: '操作人', slot: 'operator', align: 'center' },
        { title: '操作记录', slot: 'operator_desc', align: 'center' },
      ],
      total: 0, // 总条数
      list: [],
      queryFormData: { ...init_query_form_data },
      compareVisible: false,
      compareInfo: {},
    };
  },
  computed: {},
  watch: {},
  created() {},
  mounted() {
    this.getRstOperationLog();
  },
  methods: {
    onPageChange(page, pageSize) {
      this.queryFormData.page = page;
      this.queryFormData.pageSize = pageSize;
      this.getRstOperationLog();
    },

    getRstOperationLog() {
      this.tableLoading = true;
      let params = {
        b_id: this.$route.query.id,
        b_type: 20,
      };

      this.$api
        .getRstOperationLog(params)
        .then(res => {
          this.list = res.records;
          // this.total = res.total;
        })
        .finally(() => (this.tableLoading = false));
    },
    showCompare(row) {
      this.compareVisible = true;
      this.compareInfo = row.data;
    },
  },
  filters: {},
};
</script>
<style lang="less" scoped>
::v-deep .ivu-table thead th {
  background-color: #f2f2f2 !important;
}

::v-deep .ivu-page {
  text-align: right !important;
}

.ml10 {
  margin-left: 10px;
}

.avatar {
  display: inline-block;
  width: 30px;
  height: 30px;
  min-width: 30px;
  border-radius: 4px;
}
</style>
