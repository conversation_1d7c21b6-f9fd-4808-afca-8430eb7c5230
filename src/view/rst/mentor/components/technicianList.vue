<template>
  <div class="stored-wrapper">
    <Table
      :loading="tableLoading"
      :columns="tableCols"
      :data="list"
      :height="$store.state.app.clientHeight - 420 < 500 ? 500 : $store.state.app.clientHeight - 420"
    >
      <template slot-scope="{ row, index }" slot="name">
        <div class="flex">
          <img
            class="avatar"
            :src="
              row.avatar | imageStyle('B.w300', 'https://img-sn-i01s-cdn.rsjxx.com/rsjxx/2024/1211/205237_75643.png')
            "
          />
          <div class="ml10 flex flex-item-align">{{ row.name }}</div>
        </div>
      </template>

      <template slot-scope="{ row, index }" slot="price">
        <div>¥{{ Number(row.price || 0).toFixed(2) }}</div>
      </template>
    </Table>
    <p style="height: 10px"></p>
    <KPage
      :total="Number(total)"
      :page-size.sync="queryFormData.pageSize"
      :current.sync="queryFormData.page"
      @on-change="onPageChange"
      style="text-align: center"
    />
  </div>
</template>

<script>
let init_query_form_data = {
  page: 1,
  pageSize: 20
};
export default {
  name: 'serviceList',
  components: {},
  mixins: [],
  props: {},
  data() {
    return {
      tableLoading: false, // 表格获取的数据的loading
      tableCols: [
        { title: '理疗师姓名', key: 'name', align: 'center' },
        { title: '所属门店', key: 'clinic_name', align: 'center' },
        { title: '所属合伙人', key: 'cp_name', align: 'center' }
      ],
      total: 0, // 总条数
      list: [],
      queryFormData: { ...init_query_form_data }
    };
  },
  computed: {},
  watch: {},
  created() {},
  mounted() {
    this.getMentorPhysioList();
  },
  methods: {
    onPageChange(page, pageSize) {
      this.queryFormData.page = page;
      this.queryFormData.pageSize = pageSize;
      this.getMentorPhysioList();
    },

    getMentorPhysioList() {
      this.tableLoading = true;
      let params = {
        ...this.queryFormData,
        id: this.$route.query.id
      };

      this.$api
        .getMentorPhysioList(params)
        .then(res => {
          this.list = res.list;
          this.total = res.total;
        })
        .finally(() => (this.tableLoading = false));
    }
  },
  filters: {}
};
</script>
<style lang="less" scoped>
::v-deep .ivu-table thead th {
  background-color: #f2f2f2 !important;
}

::v-deep .ivu-page {
  text-align: right !important;
}

.ml10 {
  margin-left: 10px;
}

.avatar {
  display: inline-block;
  width: 30px;
  height: 30px;
  min-width: 30px;
  border-radius: 4px;
}
</style>
