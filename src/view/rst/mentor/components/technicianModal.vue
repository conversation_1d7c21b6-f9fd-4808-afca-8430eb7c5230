<template>
  <Modal
    ref="editActivityModal"
    :value="visible"
    title="关联理疗师"
    :mask-closable="false"
    width="800px"
    footer-hide
    @on-cancel="cancel"
  >
    <div class="goods-content">
      <div class="top">
        <div class="flex flex-item-between mb10">
          <div>
            <Input
              class="cursor-wrapper"
              v-model="queryFormData.name"
              @keyup.enter.native="onSearch"
              icon="ios-search"
              @on-click="onSearch"
              placeholder="请输入理疗师姓名"
              style="width: 180px"
            />
          </div>
        </div>

        <!-- table -->
        <Table :loading="tableLoading" stripe :columns="tableColumns" :data="list" class="mb10" height="424">
          <!--      拆零单位-->
          <template slot-scope="{ row }" slot="split_unit">
            {{ row.split_prod_unit || '-' }}
          </template>
        </Table>

        <KPage
          :total="total"
          class="margin-top16"
          :page-size="queryFormData.pageSize"
          :current="queryFormData.page"
          :page-size-opts="[5, 8, 20]"
          @on-change="handleCurrentChange"
          @on-page-size-change="handleSizeChange"
          style="text-align: center"
        />
      </div>
    </div>
  </Modal>
</template>

<script>
import S from 'utils/util';
let init_query_form_data = {
  page: 1,
  pageSize: 8,
  name: '',
};
export default {
  name: 'chooseGoods',
  components: {},
  mixins: [],
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    mentorId: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      // 分页
      queryFormData: { ...init_query_form_data },
      total: 0,
      tableLoading: false,

      confirmLoading: false, // 确定加载loading

      tableColumns: [
        { title: '理疗师姓名', key: 'name', align: 'center' },
        { title: '所属门店', key: 'clinic_name', align: 'center' },
        { title: '所属合伙人', key: 'cp_name', align: 'center' },
      ],
      list: [],
      checkedIdList: [],
      checkedList: [],
      drugProdTypeDesc: [],
    };
  },
  computed: {},
  watch: {
    visible(val) {
      if (!val) {
        // Modal关闭,做一些你希望的操作
        this.resetPageData();
      } else {
        this.getList();
      }
    },
  },
  created() {
    this.getGoodsOptions();
  },
  mounted() {},
  methods: {
    // 属性
    reload() {
      this.resetPageData();
      this.getList();
    },

    resetPageData() {
      this.queryFormData = { ...init_query_form_data };
      this.checkedIdList = [];
      this.checkedList = [];
    },

    handleSizeChange(val) {
      console.log('-> val', val);
      this.queryFormData.page = 1;
      this.queryFormData.pageSize = val;
      this.getList();
    },

    handleCurrentChange(val) {
      this.queryFormData.page = val;
      this.getList();
    },

    // 勾选事件
    checkboxEvent(row) {
      let index = this.checkedIdList.indexOf(row.id);

      console.log('index', index, row);
      if (row.checked) {
        if (index > -1) return;
        this.checkedIdList.push(row.id);
        this.checkedList.push(row);
      } else {
        if (index < 0) return;
        this.checkedIdList.splice(index, 1);
        this.checkedList.splice(index, 1);
      }
    },

    // 已勾选的数据回显
    echoChecked() {
      this.checkedList.forEach(checked_item => {
        this.list.forEach((item, index) => {
          if (checked_item.id === item.id) {
            this.list[index].checked = true;
          }
        });
      });
    },

    closeDia() {
      this.$emit('update:visible', false);
    },
    cancel() {
      this.closeDia();
    },
    confirm() {
      this.closeDia();
    },

    onSearch() {
      this.queryFormData.page = 1;
      this.getList();
    },

    handleList(list) {
      list.map(item => {
        item.checked = false;
        item.disabled = false;
      });
      return list;
    },

    getList() {
      this.tableLoading = true;
      let params = { ...this.queryFormData, id: this.mentorId };
      this.$api
        .getMentorPhysioList(params)
        .then(data => {
          this.resetScroll();
          this.list = this.handleList(data.list);
          this.total = data.total;

          this.tableLoading = false;
        })
        .catch(error => {
          this.$Message.error(error.errmsg);
        })
        .finally(() => {
          this.tableLoading = false;
        });
    },

    //诊所选项列表
    getGoodsOptions() {
      this.$api.getGoodsListOptions().then(res => {
        this.drugProdTypeDesc = S.descToArrHandle(res.drugProdTypeDesc);
      });
    },

    resetScroll() {
      this.$nextTick(() => {
        let overflowY = this.$el.getElementsByClassName('ivu-table-body')[0];
        if (!overflowY) {
          return;
        }
        overflowY.scrollTop = 0;
      });
    },
  },
  filters: {},
};
</script>

<style scoped lang="less"></style>
