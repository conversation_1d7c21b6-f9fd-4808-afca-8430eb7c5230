<template>
  <div class="detail-wrapper">
    <div class="info-box">
      <div class="info-box-header">
        <div class="header-left">
          <div class="avatar-box">
            <img
              class="avatar"
              :src="
                detailInfo.avatar
                  | imageStyle('B.w300', 'http://img-sn-i01s-cdn.rsjxx.com/rsjxx/2023/1220/151459_81411.png')
              "
            />
          </div>

          <div class="name-box">
            <div class="name-box-top">
              <div class="name">{{ detailInfo.name || '-' }}</div>
            </div>
          </div>
        </div>
        <div class="header-right">
          <svg-icon name="user-edit" class="ml-24 cursor" style="width: 20px; height: 20px" @click="edit"></svg-icon>
        </div>
      </div>

      <div class="info-detail-box">
        <div class="detail-item">
          <div class="item-label">联系电话：</div>
          <div class="item-value">{{ detailInfo.mobile || '-' }}</div>
        </div>
        <div class="detail-item">
          <div class="item-label">身份证号码：</div>
          <div class="item-value">{{ detailInfo.idcard || '-' }}</div>
        </div>
      </div>
    </div>

    <div class="tabs-box">
      <Tabs v-model="currentTabId" @on-click="tabIndexChange">
        <TabPane :label="item.title" name="message" v-for="item in tabList" :key="item.id" :name="item.id"></TabPane>
      </Tabs>
    </div>

    <component :is="currentComponent"></component>

    <div style="height: 35px"></div>
    <div class="fixed-bottom-wrapper">
      <back-button></back-button>
    </div>
  </div>
</template>

<script>
import technicianList from './components/technicianList.vue';
import changeRecord from './components/changeRecord/index.vue';

export default {
  name: 'detail',
  components: { technicianList, changeRecord },
  mixins: [],
  data() {
    return {
      currentTabId: '0',
      tabList: [
        { id: '0', title: '关联理疗师', component: 'technicianList' },
        { id: '1', title: '修改记录', component: 'changeRecord' }
      ], // tab切换list
      detailInfo: {}
    };
  },
  computed: {
    currentComponent() {
      return this.tabList.find(item => item.id === this.currentTabId).component;
    }
  },
  watch: {},
  created() {
    this.getMentorInfo();
  },
  mounted() {},
  methods: {
    edit() {
      this.$router.push({
        path: '/rst/mentor/edit',
        query: {
          id: this.$route.query.id,
          action: 'edit'
        }
      });
    },
    tabIndexChange(id) {
      this.currentTabId = id;
      this.$router.replace({ query: { ...this.$route.query, routeTabId: id } });
    },
    getMentorInfo() {
      let params = {
        id: this.$route.query.id,
        scene: 'info'
      };
      this.$api.getMentorInfo(params).then(res => {
        this.detailInfo = res;
      });
    }
  }
};
</script>

<style lang="less" scoped>
.detail-wrapper {
  .info-box {
    background: #faf9f9;
    padding: 10px 20px;
    border-radius: 4px;

    .info-box-header {
      display: flex;
      align-items: center;
      justify-content: space-between;

      .header-left {
        display: flex;
        align-items: center;

        .avatar-box {
          width: 50px;
          height: 50px;
          min-width: 50px;
          background: 50px;
          border-radius: 50%;

          .avatar {
            width: 100%;
            height: 100%;
            border-radius: 50%;
          }
        }

        .name-box {
          margin-left: 20px;

          .name-box-top {
            display: flex;
            align-items: center;
            font-size: 14px;

            .name {
            }

            .level {
              margin-left: 10px;
            }
          }
        }
      }
    }

    .info-detail-box {
      margin-top: 20px;
      display: flex;
      flex-wrap: wrap;

      .detail-item {
        width: 33%;
        display: flex;
        margin-bottom: 10px;
        white-space: pre-line;
        word-break: break-all;

        .item-label {
          width: 80px;
          min-width: 80px;
          text-align: right;
        }

        .item-value {
          white-space: pre-line;
          word-break: break-all;
        }
      }
    }
  }

  .tabs-box {
  }
}
.level-svg {
  font-size: 20px;
}
</style>
