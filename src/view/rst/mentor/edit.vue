<template>
  <div>
    <Form
      :label-width="130"
      label-colon
      label-position="right"
      :model="formData"
      :rules="formRules"
      ref="form"
      :disabled="
        $route.query.action === 'detail' || $route.query.action === 'audit' || $route.query.action === 'auditDetail'
      "
    >
      <!--      <div class="reject-box" v-if="true">-->
      <!--        <div></div>-->
      <!--      </div>-->
      <Alert type="warning" v-if="formData.audit_status === '3'">
        <div class="mb10">审核时间：{{ formData.audit_time | date_format }}</div>
        <div>审核意见：{{ formData.audit_reason }}</div>
      </Alert>
      <p class="block-header">基本信息</p>
      <FormItem label="导师姓名" prop="name">
        <Input placeholder="请输入导师姓名" v-model="formData.name" maxlength="10" show-word-limit />
      </FormItem>
      <FormItem label="导师手机号" prop="mobile">
        <Input placeholder="请输入导师手机号" v-model="formData.mobile" />
      </FormItem>
      <FormItem label="导师身份证号" prop="idcard">
        <Input placeholder="请输入导师身份证号" v-model="formData.idcard" maxlength="18" />
      </FormItem>
      <FormItem label="头像" prop="avatar">
        <material-picture
          v-model="formData.avatar"
          :limit="1"
          :disabled="
            $route.query.action === 'detail' || $route.query.action === 'audit' || $route.query.action === 'auditDetail'
          "
        ></material-picture>
      </FormItem>
      <p class="block-header">佣金抽成</p>
      <FormItem label="佣金比例" prop="scale">
        <div class="flex flex-item-align">
          <InputNumber
            placeholder="请输入佣金比例"
            v-model="formData.scale"
            style="flex: 1"
            :min="0"
            :max="5"
            :precision="0"
          />
          <div class="unit-box">%</div>
        </div>

        <div class="note">根据关联下属的理疗师完成的服务订单实收金额产生分佣比例</div>
      </FormItem>
    </Form>
    <div class="fixed-bottom-wrapper">
      <back-button class="mr10"></back-button>
      <Button
        type="primary"
        v-if="
          $route.query.action !== 'detail' && $route.query.action !== 'audit' && $route.query.action !== 'auditDetail'
        "
        @click="handleSubmit"
        :loading="submitLoading"
        >保存</Button
      >
      <!--      <audit-refuse v-if="$route.query.action === 'audit'" class="mr10" @ok="handleRefuse"></audit-refuse>-->
      <!--      <audit-pass v-if="$route.query.action === 'audit'" @ok="handlePass"></audit-pass>-->
    </div>
  </div>
</template>

<script>
import MaterialPicture from '@/components/MaterialCenter/MaterialPicture.vue';

const init_form_data = {
  name: '',
  mobile: '',
  avatar: '',
  idcard: '',
  scale: null
};
const validateMobile = (rule, value, callback) => {
  if (value === '') {
    callback(new Error('请输入正确的手机号码'));
  } else {
    const reg = /^1[3456789]\d{9}$/;
    if (!reg.test(value)) {
      callback(new Error('请输入正确的手机号码'));
    }
    callback();
  }
};
const validateMobileCard = (rule, value, callback) => {
  if (value === '' || value === null || value === undefined) {
    callback();
  } else {
    const reg = /^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/;
    if (!reg.test(value)) {
      callback(new Error('请输入正确的身份证号'));
    }
    callback();
  }
};
export default {
  name: 'create',

  components: { MaterialPicture },
  props: {},
  data() {
    return {
      formData: { ...init_form_data },
      formRules: {
        name: [{ required: true, message: '请输入导师姓名', trigger: 'change' }],
        mobile: [{ required: true, validator: validateMobile, trigger: 'change' }],
        idcard: [{ required: true, validator: validateMobileCard, trigger: 'change' }],
        scale: [{ required: true, message: '请输入佣金比例', trigger: 'blur', type: 'number' }]
      },
      submitLoading: false
    };
  },
  computed: {},
  watch: {},
  created() {},
  mounted() {
    if (this.$route.query.id) {
      this.getMentorInfo();
    }
  },
  methods: {
    handleSubmit() {
      this.$refs.form.validate(valid => {
        if (valid) {
          this.editMentor();
        }
      });
    },

    editMentor() {
      this.submitLoading = true;
      let params = {
        id: this.$route.query.id || '',
        ...this.formData
      };
      console.log('=>(detail.vue:241) params', params);
      this.$api
        .editMentor(params)
        .then(res => {
          console.log('=>(detail.vue:242) res', res);
          this.$router.back();
          this.$Message.success(`${this.$route.query.id ? '编辑成功' : '创建成功'}`);
        })
        .catch(err => this.$Message.error(err.errmsg))
        .finally(() => (this.submitLoading = false));
    },
    getMentorInfo() {
      let params = {
        id: this.$route.query.id,
        scene:
          this.$route.query.action === 'reEdit' ||
          this.$route.query.action === 'audit' ||
          this.$route.query.action === 'auditDetail'
            ? 'audit_info'
            : 'info' // 重新提交及审核为audit_info， 详情及编辑为info
      };
      console.log('=>(detail.vue:241) params', params);
      this.$api
        .getMentorInfo(params)
        .then(res => {
          console.log('=>(detail.vue:242) res', res);
          Object.keys(res).forEach(item => {
            if (item === 'scale') {
              this.formData[item] = Number(res[item]);
            } else {
              this.formData[item] = res[item];
            }
          });
        })
        .catch(err => this.$Message.error(err.errmsg));
    },
    handleRefuse(reason) {
      this.auditMentor('3', reason);
    },
    handlePass() {
      this.auditMentor('2');
    },
    auditMentor(action, reason = '') {
      let params = {
        id: this.$route.query.id,
        audit_status: action,
        audit_reason: reason
      };
      this.$api.auditMentor(params).then(res => {
        this.$Message.success(`审核${action === '2' ? '通过' : '驳回'}成功`);
        this.$router.back();
      });
    }
  }
};
</script>
<style scoped lang="less">
::v-deep .ivu-form-item {
  width: 60%;
}
.unit-box {
  width: 60px;
  height: 32px;
  background-color: #efefef;
  text-align: center;
  border: 1px solid #dcdcdc;
  border-left: 0;
  box-sizing: border-box;
  border-radius: 2px;
}
</style>
