<template>
  <div>
    <Form inline @submit.native.prevent @keyup.enter.native="onSearch">
      <Row>
        <FormItem>
          <Input v-model="queryFormData.name" placeholder="请输入导师姓名" clearable />
        </FormItem>
        <FormItem>
          <Select v-model="queryFormData.status" placeholder="请选择状态" clearable>
            <Option v-for="item in statusDesc" :key="item.id" :value="item.id">{{ item.desc }}</Option>
          </Select>
        </FormItem>
        <FormItem style="text-align: left">
          <Button type="primary" class="mr10" @click="onSearch">筛选</Button>
          <Button type="default" @click="onResetSearch" class="mr10">重置</Button>
          <Button type="primary" @click="createMentor">添加导师</Button>
        </FormItem>
      </Row>
    </Form>

    <div>
      <div class="panel-nav">
        <a
          class="nav"
          v-for="item in auditDescList"
          :key="item.id"
          :class="{ active: $route.query.list_status == item.id }"
          @click.prevent.capture="onStatusChange(item.id)"
        >
          {{ item.desc }}
          {{ item.status }}
          <Tag :color="getTagColor(item.id)">{{ list_count[item.id] }}</Tag>
        </a>
      </div>
      <Table :loading="tableLoading" :columns="tableCols" :data="list" :height="$store.state.app.clientHeight - 280">
        <template v-slot:name="{ row }">
          <div class="flex flex-item-align">
            <img
              :src="
                row.avatar | imageStyle('B.w300', 'http://img-sn-i01s-cdn.rsjxx.com/rsjxx/2023/1220/151459_81411.png')
              "
              class="avatar"
            />
            <div style="text-align: left">
              <div>{{ row.name }}</div>
              <div>{{ row.mobile }}</div>
            </div>
          </div>
        </template>
        <template slot-scope="{ row }" slot="scale">
          <div>{{ row.scale }}%</div>
        </template>
        <template slot-scope="{ row }" slot="physio_union_count">
          <a v-if="Number(row.physio_union_count) > 0" @click="showTechnician(row)">{{ row.physio_union_count }}</a>
          <div v-else>0</div>
        </template>

        <template slot-scope="{ row }" slot="create_info">
          <div>创建来源：{{ row.source_text || '-' }}</div>
          <div>创建人：{{ row.creator || '-' }}</div>
          <div>创建时间：{{ row.create_time | date_format }}</div>
        </template>

        <template slot-scope="{ row }" slot="audit_create_time">
          <div v-if="row.audit_type === '2'">{{ row.audit_create_time | date_format }}</div>
          <div v-else>-</div>
        </template>

        <template slot-scope="{ row }" slot="audit_time">
          {{ row.audit_time | date_format }}
        </template>
        <template slot-scope="{ row }" slot="audit_reason">
          {{ row.audit_reason || '-' }}
        </template>

        <template slot-scope="{ row }" slot="desc">
          <Tooltip max-width="300" :content="row.desc" v-if="row.desc">
            <div class="ecs ecs-2">{{ row.desc }}</div>
          </Tooltip>
          <div v-else>-</div>
        </template>

        <template slot-scope="{ row }" slot="status_text">
          <span>{{ row.status_text }}</span>
        </template>

        <!--操作记录-->
        <template slot-scope="{ row }" slot="action_log">
          <div style="text-align: left">
            <p>操作人：{{ row.operator }}</p>
            <p>操作时间：{{ row.operator_time | date_format('YYYY.MM.DD HH:mm') }}</p>
          </div>
        </template>

        <template slot-scope="{ row, index }" slot="action">
          <OperationFolding :actions="getActions(row, index)" :row="row" :max-visible-actions="3"> </OperationFolding>
        </template>
      </Table>
    </div>

    <div class="block_20"></div>
    <KPage
      :current="+queryFormData.page"
      :page-size="+queryFormData.pageSize"
      :total="+total"
      style="text-align: center"
      @on-change="handleCurrentChange"
      @on-page-size-change="handleSizeChange"
    />
    <technician-modal :visible.sync="technicianVisible" :mentor-id="currentId"></technician-modal>
  </div>
</template>

<script>
import search from '@/mixins/search';
import S from '@/utils/util';
import technicianModal from './components/technicianModal.vue';

const init_query_form_data = {
  page: 1,
  pageSize: 20,
  title: '',
  list_status: '1',
  r: ''
};
export default {
  name: 'list',
  components: { technicianModal },
  mixins: [search],
  props: {},
  data() {
    return {
      apiName: 'getMentorList',
      queryFormData: {
        ...init_query_form_data
      },
      // tableCols: [
      //   { title: '导师姓名', slot: 'name', width: 150 },
      //   { title: '佣金比例', slot: 'scale' },
      //   { title: '关联理疗师', slot: 'physio_union_count' },
      //   { title: '类型', key: 'audit_type_text' },
      //   { title: '状态', slot: 'status_text' },
      //   { title: '创建信息', slot: 'create_info', width: 200 },
      //   { title: '更新时间', slot: 'audit_create_time', width: 150 },
      //   { title: '操作', slot: 'action', width: 200 },
      // ],
      statusDesc: [],
      auditDescList: [],
      list_count: {},
      sourceList: [],
      technicianVisible: false,
      currentId: ''
    };
  },
  computed: {
    getActions() {
      return (row, index) => {
        return [
          {
            label: '编辑',
            handler: this.editMentor,
            tagType: 'a',
            params: {
              row,
              action: 'edit'
            },
            isHidden: this.queryFormData.list_status !== '1'
          },
          {
            label: '详情',
            handler: this.toDetail,
            tagType: 'a',
            params: row.id,
            isHidden: this.queryFormData.list_status !== '1'
          },
          {
            label: row.status === 'OFF' ? '启用' : '停用',
            confirmText: row.status === 'OFF' ? '确定启用?' : '停用后，导师所关联的所有理疗师服务将不会再被分佣',
            handler: this.changeMentorStatus,
            params: {
              id: row.id,
              status: row.status === 'OFF' ? 'ON' : 'OFF'
            },
            tagType: 'Poptip',
            isHidden: this.queryFormData.list_status !== '1'
          },
          {
            label: '重新提交',
            handler: this.editMentor,
            tagType: 'a',
            params: {
              row,
              action: 'reEdit'
            },
            isHidden: this.queryFormData.list_status !== '3'
          },
          {
            label: '详情',
            handler: this.editMentor,
            tagType: 'a',
            params: {
              row,
              action: 'auditDetail'
            },
            isHidden: this.queryFormData.list_status !== '2' && this.queryFormData.list_status !== '3'
          }
        ];
      };
    },
    getTagColor(type) {
      return type => {
        switch (type) {
          case '2': // 待审核
            return 'warning';
          case '1': // 已发货
            return 'success';
          case '3': // 待发货
            return 'error';

          default: // 已取消
            return 'default';
        }
      };
    },
    tableCols() {
      if (this.$route.query.list_status === '2') {
        return [
          { title: '导师姓名', slot: 'name' },
          { title: '佣金比例', slot: 'scale' },
          { title: '类型', key: 'audit_type_text' },
          { title: '创建信息', slot: 'create_info', width: 200 },
          { title: '更新时间', slot: 'audit_create_time' },
          { title: '操作', slot: 'action', width: 200 }
        ];
      } else if (this.$route.query.list_status === '3') {
        return [
          { title: '导师姓名', slot: 'name', width: 150 },
          { title: '佣金比例', slot: 'scale' },
          { title: '类型', key: 'audit_type_text' },
          { title: '创建信息', slot: 'create_info', width: 200 },
          { title: '更新时间', slot: 'audit_create_time', width: 150 },
          { title: '审核时间', slot: 'audit_time', width: 150 },
          { title: '审核意见', slot: 'audit_reason' },
          { title: '操作', slot: 'action', width: 150 }
        ];
      } else {
        return [
          { title: '导师姓名', slot: 'name', width: 150 },
          { title: '佣金比例', slot: 'scale' },
          { title: '关联理疗师', slot: 'physio_union_count' },
          { title: '类型', key: 'audit_type_text' },
          { title: '状态', slot: 'status_text' },
          { title: '创建信息', slot: 'create_info', width: 200 },
          { title: '更新时间', slot: 'audit_create_time', width: 150 },
          { title: '操作', slot: 'action', width: 200 }
        ];
      }
    }
  },
  watch: {},
  created() {
    this.queryFormData = S.merge(this.queryFormData, this.$route.query);
    this.submitQueryForm(true);
    this.getMentorOptions();
  },
  mounted() {},
  methods: {
    // 创建活动
    createMentor() {
      this.$router.push({ path: '/rst/mentor/edit' });
    },
    editMentor(item) {
      let { row, action } = item;
      console.log('=>(list.vue:174) item', item);
      this.$router.push({
        path: '/rst/mentor/edit',
        query: {
          id: row.id,
          action
        }
      });
    },

    toDetail(id) {
      this.$router.push({
        path: '/rst/mentor/detail',
        query: {
          id
        }
      });
    },

    changeMentorStatus(item) {
      let params = {
        id: item.id,
        status: item.status
      };
      console.log('=>(detail.vue:241) params', params);
      this.$api
        .changeMentorStatus(params)
        .then(res => {
          this.$Message.success(`${item.status === 'ON' ? '启用成功' : '停用成功'}`);
          this.loadList();
        })
        .catch(err => this.$Message.error(err.errmsg));
    },

    // 重置
    onResetSearch() {
      this.queryFormData = { ...init_query_form_data };
      this.timeRange = [];
      this.submitQueryForm();
    },
    onStatusChange(status) {
      console.log('-> status', status);
      this.queryFormData.page = 1;
      this.queryFormData.list_status = status;
      this.submitQueryForm();
    },
    getMentorOptions() {
      this.$api
        .getMentorOptions()
        .then(res => {
          console.log('=>(detail.vue:242) res', res);
          this.statusDesc = S.descToArrHandle(res.status);
          this.auditDescList = S.descToArrHandle(res.list_status);
          this.sourceList = S.descToArrHandle(res.source);
        })
        .catch(err => this.$Message.error(err.errmsg));
    },
    handlerListData(data) {
      this.list_count = data.list_status_count;
    },
    showTechnician(row) {
      this.technicianVisible = true;
      this.currentId = row.id;
    }
  },
  beforeRouteUpdate(to, from, next) {
    this.queryFormData = S.merge(init_query_form_data, to.query);
    this.getTimeRange();
    this.loadList();
    next();
  },
  filters: {}
};
</script>

<style lang="less" scoped>
.avatar {
  width: 29px;
  height: 29px;
  min-width: 29px;
  min-height: 29px;
  border-radius: 50%;
  margin-right: 10px;
}
</style>
