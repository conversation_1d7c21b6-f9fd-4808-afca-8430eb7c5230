<template>
  <Modal
    ref="compareModal"
    :value="visible"
    title="修改日志记录"
    :mask-closable="false"
    width="900px"
    @on-cancel="cancel"
    @on-visible-change="changeVisible"
    class-name="compareModal"
    footer-hide
  >
    <div class="custom-modal-content">
      <div class="flex flex-item-between">
        <div class="compare-item">
          <p class="block-header">变更前</p>
          <div class="sub-title">基本信息</div>
          <div class="item-box"><span class="label-text">理疗师姓名：</span>{{ compareInfo.before?.name }}</div>
          <div class="item-box"><span class="label-text">理疗师手机号：</span>{{ compareInfo.before?.mobile }}</div>
          <div class="item-box"><span class="label-text">理疗师身份证号：</span>{{ compareInfo.before?.idcard }}</div>
          <div class="item-box">
            <span class="label-text">头像：</span
            ><img
              :src="
                compareInfo.before?.avatar
                  | imageStyle('B.w300', 'http://img-sn-i01s-cdn.rsjxx.com/rsjxx/2023/1220/151459_81411.png')
              "
              alt=""
              style="width: 30px; height: 30px; border-radius: 50%"
            />
          </div>
          <div class="sub-title">理疗师等级</div>
          <div class="item-box"><span class="label-text">理疗师等级：</span>P{{ compareInfo.before?.level }}</div>
          <div class="sub-title">理疗师点</div>
          <div class="item-box"><span class="label-text">技能：</span>{{ compareInfo.before?.base_skill_text }}</div>
        </div>
        <div class="compare-item">
          <p class="block-header">变更后</p>
          <div class="sub-title">基本信息</div>
          <div class="item-box">
            <span class="label-text">理疗师姓名：</span
            ><span :class="{ red: compareData('name') }">{{ compareInfo.after?.name }}</span>
          </div>
          <div class="item-box">
            <span class="label-text">理疗师手机号：</span
            ><span :class="{ red: compareData('mobile') }">{{ compareInfo.after?.mobile }}</span>
          </div>
          <div class="item-box">
            <span class="label-text">理疗师身份证号：</span
            ><span :class="{ red: compareData('idcard') }">{{ compareInfo.after?.idcard }}</span>
          </div>
          <div class="item-box">
            <span class="label-text">头像：</span
            ><img
              :src="
                compareInfo.after?.avatar
                  | imageStyle('B.w300', 'http://img-sn-i01s-cdn.rsjxx.com/rsjxx/2023/1220/151459_81411.png')
              "
              alt=""
              style="width: 30px; height: 30px; border-radius: 50%"
            />
          </div>
          <div class="sub-title">理疗师等级</div>
          <div class="item-box">
            <span class="label-text">理疗师等级：</span
            ><span :class="{ red: compareData('level') }">P{{ compareInfo.after?.level }}</span>
          </div>
          <div class="sub-title">理疗师点</div>
          <div class="item-box">
            <span class="label-text">技能：</span>
            <span
              :class="{ red: isNewSkill(compareInfo.before?.base_skill_text, item) }"
              v-for="(item, index) in handlerToArray(compareInfo.after?.base_skill_text)"
              :key="index"
              >{{ index + 1 < handlerToArray(compareInfo.after?.base_skill_text).length ? `${item},` : item }}</span
            >
          </div>
        </div>
      </div>
    </div>
    <div slot="footer">
      <Button @click="cancel">取消</Button>
      <Button type="primary" @click="submitForm" :loading="submitLoading">确定</Button>
    </div>
  </Modal>
</template>

<script>
import S from 'utils/util';

const initFormData = {
  code: '' //销售单编号；如果不填写，创建时系统会自动生成
};

export default {
  name: 'editActivityModal',
  mixins: [],
  components: {},
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    id: {
      type: String,
      default: ''
    },
    compareInfo: {
      type: Object,
      default: () => ({
        before: {},
        after: {}
      })
    }
  },

  data() {
    return {
      formData: { ...initFormData },
      submitLoading: false
    };
  },

  computed: {
    compareData() {
      return field => {
        return (
          (this.compareInfo?.before && this.compareInfo?.before[field]) !==
          (this.compareInfo?.after && this.compareInfo?.after[field])
        );
      };
    },
    handlerToArray() {
      return text => {
        return text?.split(',') || [];
      };
    },
    isNewSkill() {
      return (source_text, val) => {
        let list = source_text?.split(',') || [];
        return list.findIndex(item => item == val) == -1;
      };
    }
  },

  watch: {},

  created() {},

  mounted() {},

  methods: {
    changeVisible(val) {
      if (val) {
      } else {
        this.formData = { ...initFormData }; // 重置数据
        // this.$refs['modal'].$el.querySelector('.ivu-modal-body').scrollTop = 0; // 重置滚动条高度
      }
    },
    cancel() {
      this.$emit('update:visible', false);
    },
    submitForm() {}
  },

  destroyed() {}
};
</script>

<style scoped lang="less">
::v-deep .ivu-modal-body {
  max-height: 500px;
  min-height: 500px;
  overflow-y: auto;
}
.block-header {
  background-color: #efefef;
  padding: 10px;
  margin: 0px 0px 20px 0;
  position: relative;
}

.compare-item {
  flex: 1;

  &:first-child {
    margin-right: 40px;
  }

  .sub-title {
    font-size: 14px;
    font-weight: 700;
    margin-top: 5px;
    margin-bottom: 5px;
  }
  .item-box {
    line-height: 28px;
  }
}

.label-text {
  display: inline-block;
  width: 110px;
  text-align: right;
  margin-right: 10px;
  color: #666;
}
.red {
  color: red;
}
</style>
<style lang="less">
.compareModal {
  .custom-modal-content {
    overflow-y: auto;
    padding-right: 20px;
  }

  .ivu-modal-body {
    padding: 20px 10px 20px 30px;
  }
}
</style>
