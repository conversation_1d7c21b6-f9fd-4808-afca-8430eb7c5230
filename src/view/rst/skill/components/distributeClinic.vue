<template>
  <Modal
    :value="value"
    width="800px"
    :title="title"
    :footer-hide="false"
    :mask-closable="false"
    :lock-scroll="true"
    @on-visible-change="changeVisible"
  >
    <div class="content" v-if="value">
      <div class="search-box">
        <Input
          v-model="queryFormData.keyword"
          placeholder="请输入搜索门店"
          clearable
          @keyup.enter.native="onSearch"
          @on-clear="onSearch"
          style="width: 180px"
        >
          <Icon type="ios-search" slot="suffix" />
        </Input>
      </div>
      <Table
        :loading="tableLoading"
        :columns="tableCols"
        :data="list"
        :height="370"
        @on-select-all="selectAll"
        @on-select="select"
        @on-select-all-cancel="selectAllCancel"
        @on-select-cancel="selectCancel"
      >
        <template slot-scope="{ row, index }" slot="id">
          <!--          <Radio @on-change="checkChange(row)" v-model="list[index].checked"-->
          <!--            ><span style="margin-left: 10px">{{ row.id }}</span></Radio-->
          <!--          >-->
          {{ row.id }}
        </template>

        <template slot-scope="{ row }" slot="offline_code">
          <div>{{ row.offline_code }}</div>
        </template>

        <template slot-scope="{ row }" slot="address">
          <div v-if="row.prov_name">
            {{ row.prov_name }}{{ row.city_name }}{{ row.county_name }}{{ row.address_other }}
          </div>
          <div v-else>-</div>
        </template>

        <template slot-scope="{ row }" slot="info">
          <div class="lv-box">
            <div class="label">联系人:</div>
            <div class="value">{{ row.leading_person }}</div>
          </div>
          <div class="lv-box">
            <div class="label">手机号:</div>
            <div class="value">{{ row.leading_mobile }}</div>
          </div>
        </template>
      </Table>
      <div class="block_20"></div>
      <KPage
        :total="total"
        :page-size="queryFormData.pageSize"
        :current="queryFormData.page"
        @on-change="handleCurrentChange"
        @on-page-size-change="handleSizeChange"
        style="text-align: center"
      />
    </div>

    <div slot="footer">
      <Button @click="closeModal">取消</Button>
      <Button :loading="confirmLoading" type="primary" @click="confirm">确认</Button>
    </div>
  </Modal>
</template>

<script>
let init_query_from_data = {
  page: 1,
  pageSize: 20,
  keyword: ''
};
export default {
  name: 'distributeClinic',
  mixins: [],

  components: {},
  props: {
    value: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      default: '关联门店'
    },
    row: {
      type: Object,
      default: () => {}
    },
    ids: {
      type: Array,
      default: () => []
    },
    echoClinicList: {
      type: Array,
      default: () => []
    }
  },

  data() {
    return {
      queryFormData: { ...init_query_from_data },
      confirmLoading: false,
      list: [],
      total: 0,
      tableLoading: false,
      tableCols: [
        { type: 'selection', align: 'center', width: 40 },
        { title: '诊所编号', slot: 'id', align: 'left', minWidth: 80 },
        { title: '诊所门店编号', slot: 'offline_code', align: 'center', minWidth: 100 },
        { title: '诊所名称', key: 'name', align: 'center', minWidth: 100 },
        { title: '经营地址', slot: 'address', align: 'center', minWidth: 100 },
        { title: '联系人/手机号', slot: 'info', align: 'center', minWidth: 120 }
      ],
      select_item: {},
      selected_items: {}
    };
  },

  computed: {},

  watch: {},

  created() {},

  mounted() {},

  destroyed() {},

  methods: {
    onSearch: function () {
      this.queryFormData.page = 1;
      this.getList();
    },

    handleEchoSelectItem() {
      if (this.select_item.id) {
        this.list.forEach((item, index) => {
          if (item.id === this.select_item.id) {
            this.$set(this.list, index, { ...item, checked: true });
          }
        });
      }
    },

    handleSizeChange(val) {
      this.queryFormData.page = 1;
      this.queryFormData.pageSize = val;
      this.getList();
    },
    handleCurrentChange(val) {
      this.queryFormData.page = val;
      this.getList();
    },

    onRefresh: function () {
      this.getList();
    },
    checkChange(row) {
      this.select_item = row;
      this.list.forEach((item, index) => {
        if (item.id == row.id) {
          this.$set(this.list, index, { ...item, checked: true });
        } else {
          this.$set(this.list, index, { ...item, checked: false });
        }
      });
    },
    confirm() {
      if (!Object.keys(this.selected_items).length) {
        this.$Message.error('请选择要关联的门店');
        return;
      }
      this.getPhysioDispatch();
    },
    changeVisible(visible) {
      if (visible) {
        this.getList();
      } else {
        this.closeModal();
      }
    },

    clearData() {
      this.select_item = {};
      this.selected_items = {};
      this.total = 0;
      this.list = [];
    },

    closeModal() {
      this.clearData();
      this.$emit('input', false);
    },

    getList() {
      this.tableLoading = true;
      let params = {
        ...this.queryFormData
      };
      this.$api
        .getPhysioSearchCli(params)
        .then(res => {
          res.list.forEach(item => {
            if (this.echoClinicList.includes(item.id)) {
              item._checked = true;
              this.$set(this.selected_items, item.id, item);
            }
          });
          this.list = res.list;
          console.log('=>(distributeClinic.vue:214) this.list', this.list);
          this.total = res.total;
          // this.list.forEach(item => (item.checked = false));
          // this.handleEchoSelectItem();
        })
        .finally(() => (this.tableLoading = false));
    },

    getPhysioDispatch(force = false) {
      this.confirmLoading = true;
      let params = {
        force: force,
        clinic_id: Object.keys(this.selected_items),
        ids: this.ids
      };
      this.$api
        .getPhysioDispatch(params)
        .then(
          res => {
            if (res.status === 'warn') {
              this.handleWarn(res.message);
            } else if (res.status === 'error') {
              this.handleError(res.message);
            } else {
              this.$emit('success');
              this.closeModal();
            }
          },
          err => this.$Message.error(err.errmsg)
        )
        .finally(() => (this.confirmLoading = false));
    },

    handleWarn(message = '') {
      this.$Modal.confirm({
        title: message,
        onOk: () => {
          this.getPhysioDispatch(true);
        }
      });
    },

    handleError(message = '') {
      this.$Modal.warning({
        title: message,
        okText: '取消'
      });
    },

    // 全选
    selectAll(val) {
      val.forEach(item => {
        this.$set(this.selected_items, item.id, item);
      });
    },
    // 选择单个
    select(val, row) {
      this.$set(this.selected_items, row.id, row);
    },
    // 全部取消
    selectAllCancel() {
      this.list &&
        this.list.forEach(item => {
          this.$delete(this.selected_items, item.id);
        });
    },
    // 取消单个
    selectCancel(val, row) {
      this.$delete(this.selected_items, row.id);
    }
  }
};
</script>

<style scoped lang="less">
::v-deep .ivu-modal-body {
  max-height: 500px;
  min-height: 500px;
  overflow-y: auto;
}

.lv-box {
  display: flex;
  align-items: center;

  .label {
    width: 70px;
    min-width: 70px;
    text-align: right;
  }

  .value {
    text-align: left;
  }
}
.search-box {
  display: flex;
  justify-content: flex-end;
  margin-bottom: 10px;
}
</style>
