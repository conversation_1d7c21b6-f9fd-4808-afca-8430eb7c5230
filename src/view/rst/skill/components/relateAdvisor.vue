<template>
  <Modal
    :value="value"
    width="600px"
    :title="title"
    :footer-hide="false"
    :mask-closable="false"
    :lock-scroll="true"
    @on-visible-change="changeVisible"
  >
    <div class="content flex flex-item-align">
      <div>关联导师：</div>
      <el-select
        v-model="selectMentor"
        remote
        :remote-method="searchMentor"
        class="flex-1"
        popper-class="relateAdvisorSelect"
        size="small"
        filterable
        placeholder="请选择导师"
        :loading="searchLoading"
        @change="selectSup"
        @visible-change="visibleChange"
        @clear="searchMentor('')"
        clearable
      >
        <Row class="option-item row-header">
          <Col :span="6">头像</Col>
          <Col :span="12">姓名</Col>
          <Col :span="6">手机号</Col>
        </Row>

        <el-option
          class="option-item"
          v-for="(item, index) in mentorList"
          :key="index"
          :label="item.name"
          :value="item.id"
        >
          <Row>
            <Col :span="6">
              <img
                :src="
                  item.avatar
                    | imageStyle('B.w300', 'https://img-sn-i01s-cdn.rsjxx.com/rsjxx/2023/1220/151459_81411.png')
                "
                alt=""
                width="30"
                height="30"
                class="avatar"
              />
            </Col>
            <Col :span="12" class="ecs" style="display: inline-block">{{ item.name }}</Col>
            <Col :span="6">{{ item.mobile }}</Col>
          </Row>
        </el-option>
      </el-select>
    </div>

    <div slot="footer">
      <Button @click="closeModal">取消</Button>
      <Button :loading="confirmLoading" type="primary" @click="confirm">确认</Button>
    </div>
  </Modal>
</template>

<script>
export default {
  name: 'relateAdvisor',
  mixins: [],

  components: {},
  props: {
    value: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      default: '关联导师'
    },
    ids: {
      type: Array,
      default: () => []
    }
  },

  data() {
    return {
      mentorList: [],
      confirmLoading: false,
      searchLoading: false,
      selectMentor: '',
      currentItem: {}
    };
  },

  computed: {},

  watch: {},

  created() {},

  mounted() {},

  destroyed() {},

  methods: {
    visibleChange(val) {
      if (!val && this.value) {
        this.searchMentor(this.currentItem?.name);
      }
    },
    selectSup(val) {
      this.selectMentor = val;
      this.currentItem = this.mentorList.find(item => item.id === val);
    },
    changeVisible(visible) {
      if (visible) {
        this.searchMentor('');
      } else {
        this.closeModal();
      }
    },
    searchMentor(keyword) {
      console.log('=>(relateAdvisor.vue:105) keyword', keyword);
      this.searchLoading = true;
      let params = {
        status: 'ON',
        keyword
      };
      this.$api
        .getPhysioSearchAdvisor(params)
        .then(res => {
          this.mentorList = res.list;
        })
        .finally(() => {
          this.searchLoading = false;
        });
    },
    clearData() {
      this.selectMentor = '';
      this.currentItem = {};
      this.mentorList = [];
    },

    closeModal() {
      this.clearData();
      this.$emit('input', false);
    },

    confirm() {
      console.log(this.ids);
      // if (!this.selectMentor) {
      //   this.$Message.error('请选择导师');
      //   return;
      // }
      this.confirmLoading = true;
      this.$api.checkRelateAdvisor({ ids: this.ids, advisor_id: this.selectMentor }).then(res => {
        console.log('%c=>(relateAdvisor.vue:130) res', 'color: #ECA233;font-size: 16px;', res);
        if (res.status !== 'SUCCESS') {
          this.$Modal.confirm({
            title: '温馨提示',
            content: res.message,
            onOk: () => {
              this.confirmRelate();
            },
            onCancel: () => {
              this.confirmLoading = false;
            }
          });
        } else {
          this.confirmRelate();
        }
      });
    },
    confirmRelate() {
      this.$api
        .technicianRelateAdvisor({ ids: this.ids, advisor_id: this.selectMentor })
        .then(res => {
          this.$Message.success('关联成功');
          this.$emit('success');
          this.closeModal();
        })
        .finally(() => {
          this.confirmLoading = false;
        });
    }
  }
};
</script>

<style scoped lang="less">
.content {
  padding: 20px 20px 40px;
}

.option-item {
  text-align: center;
  padding: 4px 0;
  height: auto;

  .avatar {
    border-radius: 50%;
  }
}

.row-header {
  position: sticky;
  top: 0px;
  z-index: 2;
  padding: 8px 0;
  background-color: #f5f5f5;
  font-weight: bold;
}
</style>

<style lang="less">
.relateAdvisorSelect {
  .el-select-dropdown__list {
    padding: 0px;
  }
}
</style>
