<template>
  <div class="stored-wrapper">
    <div class="score-box">
      <div class="score-item">技术专业度：{{ detailInfo.technology_grade || '-' }}</div>
      <div class="score-item">服务态度：{{ detailInfo.service_grade || '-' }}</div>
      <div class="score-item">整体体验：{{ detailInfo.overall_grade || '-' }}</div>
      <div class="score-item">环境与舒适度：{{ detailInfo.comfort_grade || '-' }}</div>
    </div>
    <Table
      border
      :loading="tableLoading"
      :columns="tableCols"
      :data="list"
      :height="$store.state.app.clientHeight - 480 < 400 ? 400 : $store.state.app.clientHeight - 480"
    >
      <template slot-scope="{ row, index }" slot="name">
        <div class="flex">
          <img
            class="avatar"
            :src="row.img ? `${row.img}-B.w300` : 'https://img-sn-i01s-cdn.rsjxx.com/rsjxx/2024/1211/205237_75643.png'"
          />
          <div class="ml10 flex flex-item-align">{{ row.name }}</div>
        </div>
      </template>

      <template slot-scope="{ row, index }" slot="need">
        <div v-if="row.need?.length">
          <span v-for="(item, index) in row.need" :key="index" :class="{ 'not-obtained': !isGet(row, item) }">{{
            index < row.need?.length - 1 ? `${item}、` : item
          }}</span>
        </div>
        <div v-else>-</div>
      </template>

      <template slot-scope="{ row, index }" slot="operator">
        <div>{{ row.can === '1' ? '已获取' : '未获取' }}</div>
      </template>
    </Table>
    <p style="height: 10px"></p>
    <KPage
      :total="Number(total)"
      :page-size.sync="queryFormData.pageSize"
      :current.sync="queryFormData.page"
      @on-change="onPageChange"
      style="text-align: center"
    />
  </div>
</template>

<script>
let init_query_form_data = {
  page: 1,
  pageSize: 20
};
export default {
  name: 'serviceList',
  components: {},
  mixins: [],
  props: {
    detailInfo: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      tableLoading: false, // 表格获取的数据的loading
      tableCols: [
        { title: '服务名称', slot: 'name', align: 'center' },
        { title: '分类', key: 'serv_type_text', align: 'center' },
        { title: '所需技能点', slot: 'need', align: 'center' },
        { title: '操作技能', slot: 'operator', align: 'center' }
      ],
      total: 0, // 总条数
      list: [],
      queryFormData: { ...init_query_form_data }
    };
  },
  computed: {
    isGet() {
      return (row, item) => {
        return row?.have?.includes(item);
      };
    }
  },
  watch: {},
  created() {},
  mounted() {
    this.getPhysioServiceList();
  },
  methods: {
    onPageChange(page, pageSize) {
      this.queryFormData.page = page;
      this.queryFormData.pageSize = pageSize;
      this.getPhysioServiceList();
    },

    getPhysioServiceList() {
      this.tableLoading = true;
      let params = {
        ...this.queryFormData,
        id: this.$route.query.id
      };

      this.$api
        .getPhysioServiceList(params)
        .then(res => {
          this.list = res.list;
          this.total = res.total;
        })
        .finally(() => (this.tableLoading = false));
    }
  },
  filters: {}
};
</script>
<style lang="less" scoped>
.score-box {
  background: #f2f2f2;
  border: 1px solid #e8eaec;
  border-bottom: none;
  display: flex;
  justify-content: right;
  padding: 10px;
  .score-item {
    padding-right: 40px;
  }
}

::v-deep .ivu-table thead th {
  background-color: #f2f2f2 !important;
}

::v-deep .ivu-page {
  text-align: right !important;
}

.ml10 {
  margin-left: 10px;
}

.avatar {
  display: inline-block;
  width: 30px;
  height: 30px;
  min-width: 30px;
  border-radius: 4px;
  object-fit: cover;
}
.not-obtained {
  color: #c4bbbb;
}
</style>
