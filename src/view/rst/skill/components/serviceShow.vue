<template>
  <Modal
    ref="customModal"
    :value="value"
    width="800px"
    :title="title"
    :footer-hide="true"
    :mask-closable="false"
    :lock-scroll="true"
    @on-visible-change="changeVisible"
  >
    <div class="content" v-if="value">
      <Table class="global-table-style" :columns="tableCols" :data="list" :height="360">
        <template slot-scope="{ row, index }" slot="name">
          <div class="flex">
            <img
              class="avatar"
              :src="
                row.img | imageStyle('B.w300', 'https://img-sn-i01s-cdn.rsjxx.com/rsjxx/2024/1211/205237_75643.png')
              "
            />
            <div class="ml10 flex flex-item-align">{{ row.name }}</div>
          </div>
        </template>
      </Table>
    </div>
  </Modal>
</template>

<script>
export default {
  name: 'serviceShow',
  mixins: [],

  components: {},
  props: {
    value: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      default: '关联服务'
    },
    row: {
      type: Object,
      default: () => {}
    }
  },

  data() {
    return {
      tableCols: [
        { title: '服务名称', slot: 'name', align: 'left' },
        { title: '分类', key: 'serv_type_text', align: 'center' }
      ],
      list: []
    };
  },

  computed: {},

  watch: {},

  created() {},

  mounted() {},

  destroyed() {},

  methods: {
    changeVisible(visible) {
      if (visible) {
        this.list = this.row?.service_list || [];
      } else {
        this.closeModal();
      }
    },

    clearData() {},

    closeModal() {
      this.clearData();
      this.$emit('input', false);
    }
  }
};
</script>

<style scoped lang="less">
::v-deep .ivu-modal-body {
  max-height: 400px;
  min-height: 400px;
  overflow-y: auto;
}
.ml10 {
  margin-left: 10px;
}

.avatar {
  display: inline-block;
  width: 30px;
  height: 30px;
  min-width: 30px;
  border-radius: 4px;
}
</style>
