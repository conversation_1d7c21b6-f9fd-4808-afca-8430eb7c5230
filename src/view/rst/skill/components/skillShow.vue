<template>
  <Modal
    ref="customModal"
    :value="value"
    width="800px"
    :title="title"
    :footer-hide="true"
    :mask-closable="false"
    :lock-scroll="true"
    @on-visible-change="changeVisible"
  >
    <div class="content" v-if="value">
      <div class="tip-box">
        <Icon type="ios-information-circle" color="#8558fa" size="20" />
        <span class="ml10">不同的服务需要理疗师不同的技能能力去匹配，根据理疗师技能能力去关联理疗师所能提供的服务能力</span>
      </div>
      <div class="skill-box">
        <div class="skill-item" v-for="(item, index) in list" :key="index">
          <div class="skill-title">{{ item.title }}</div>
          <CheckboxGroup v-model="checkedGroup">
            <Checkbox :label="s_item.id" disabled v-for="(s_item, s_index) in item.list" :key="'skill'+s_index">{{ s_item.desc }}</Checkbox>
          </CheckboxGroup>
        </div>
      </div>
    </div>
  </Modal>
</template>

<script>
export default {
  name: 'skillShow',
  mixins: [],

  components: {},
  props: {
    value: {
      type: Boolean,
      default: false,
    },
    title: {
      type: String,
      default: '关联理疗师技能',
    },
    row: {
      type: Object,
      default: () => {},
    },
  },

  data() {
    return {
      list: [],
      checkedGroup: []
    };
  },

  computed: {
  },

  watch: {},

  created() {},

  mounted() {},

  destroyed() {},

  methods: {
    changeVisible(visible) {
      if (visible) {
        this.getPhysioSkillOptions();
      } else {
        this.closeModal();
      }
    },

    clearData() {},

    closeModal() {
      this.clearData();
      this.$emit('input', false);
    },


    handleEchoSkill() {
      this.checkedGroup = []
      let skill_list = this.row?.skill_list
      if ( skill_list?.length ) {
        this.list.forEach(item => {
          item.list.forEach(c_item => {
            if (skill_list.includes(c_item.id) ) {
              this.checkedGroup.push(c_item.id)
            }
          })
        })
      }
    },

    getPhysioSkillOptions() {
      let params = {};
      this.$api
        .getPhysioSkillOptions(params)
        .then(res => {
          this.list = res.skill
          this.handleEchoSkill()
        })
    },
  },
};
</script>

<style scoped lang="less">
::v-deep .ivu-modal-body {
  max-height: 400px;
  min-height: 400px;
  overflow-y: auto;
}

.tip-box {
  margin-bottom: 20px;
  background: #f2edff;
  border-radius: 4px;
  border: 1px solid #b39af7;
  padding: 10px 15px;
  display: flex;
  align-items: center;
}

.skill-box {
  .skill-item {
    margin-bottom: 20px;
    .skill-title {
      font-size: 13px;
      margin-bottom: 10px;
    }
  }
}

::v-deep .ivu-checkbox-group {
  display: flex;
  flex-wrap: wrap;
  margin-left: 20px;
  .ivu-checkbox-group-item {
    margin-bottom: 10px;
    width: 15%;
  }
}
</style>
