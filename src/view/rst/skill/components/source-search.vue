<template>
  <div>
    <Select
      ref="source"
      transfer
      :value="value"
      :clearable="isClearable"
      :loading="searchLoading"
      :remote-method="search"
      filterable
      @on-query-change="queryChange"
      class="filterable-select"
      :placeholder="placeholder"
      @on-clear="clearSub"
      @on-select="selectSup"
    >
      <Option v-for="(option, index) in source_list" :key="option.id" :value="option.id">{{ option.name }}</Option>
    </Select>
  </div>
</template>

<script>
import util from '@/utils/util';
export default {
  name: 'source-search',
  components: {},
  mixins: [],
  props: {
    isClearable: {
      type: Boolean,
      default: true
    },
    value: {
      type: String,
      default: ''
    },
    placeholder: {
      type: String,
      default: '请输入搜索创建来源'
    }
  },
  data() {
    return {
      searchLoading: false,
      source_list: [],
      query: '',
      shouldSearch: true
    };
  },
  computed: {},
  watch: {
    value(val) {
      !val && this.searchMethod('');
    }
  },
  created() {},
  mounted() {
    if (!this.$route.query.source_id) {
      this.searchMethod();
    } else {
      this.shouldSearch = false;
      let list = JSON.parse(sessionStorage.getItem('source_list')) || [];
      this.source_list = list;
    }
  },
  methods: {
    searchMethod: util.debounce(function (query) {
      if (!this.shouldSearch) {
        this.shouldSearch = true;
        return;
      }
      this.searchLoading = true;
      this.$api.getPhysioSourceList({ keyword: query }).then(res => {
        this.searchLoading = false;
        this.source_list = res.list;
        sessionStorage.setItem('source_list', JSON.stringify(this.source_list));
      });
    }, 200),
    search() {},
    clear() {
      this.$refs.source.clearSingleSelect();
    },
    selectSup(val) {
      let current_item = this.source_list.find(item => item.id === val.value);
      this.$emit('input', val.value);
      this.$emit('clickItem', current_item.source);
    },
    queryChange(val) {
      this.searchMethod(val);
    },
    clearSub() {
      this.searchMethod();
      this.$emit('input', '');
      this.$emit('clickItem', '');
    }
  }
};
</script>

<style lang="less" scoped>
.filterable-select {
  ::v-deep .ivu-select-input {
    margin-top: -1px;
  }
}
</style>
