<template>
  <div>
    <Select
      ref="store"
      transfer
      :value="value"
      :clearable="isClearable"
      :loading="searchLoading"
      :remote-method="search"
      filterable
      @on-query-change="queryChange"
      class="filterable-select"
      :placeholder="placeholder"
      @on-clear="clearSub"
      @on-select="selectSup"
    >
      <Option v-for="(option, index) in store_list" :key="option.id" :value="option.id">{{ option.name }}</Option>
    </Select>
  </div>
</template>

<script>
import util from '@/utils/util';
export default {
  name: 'store-search',
  components: {},
  mixins: [],
  props: {
    isClearable: {
      type: Boolean,
      default: true
    },
    value: {
      type: String,
      default: ''
    },
    placeholder: {
      type: String,
      default: '请输入搜索所属门店'
    }
  },
  data() {
    return {
      searchLoading: false,
      store_list: [],
      query: '',
      shouldSearch: true
    };
  },
  computed: {},
  watch: {
    value(val) {
      !val && this.searchMethod('');
    }
  },
  created() {},
  mounted() {
    if (!this.$route.query.clinic_id) {
      this.searchMethod();
    } else {
      this.shouldSearch = false;
      let list = JSON.parse(sessionStorage.getItem('store_list')) || [];
      this.store_list = list;
    }
  },
  methods: {
    searchMethod: util.debounce(function (query) {
      if (!this.shouldSearch) {
        this.shouldSearch = true;
        return;
      }
      this.searchLoading = true;
      this.$api.getPhysioSearchCli({ keyword: query }).then(res => {
        this.searchLoading = false;
        this.store_list = res.list;
        sessionStorage.setItem('store_list', JSON.stringify(this.store_list));
      });
    }, 200),
    search() {},
    clear() {
      this.$refs.store.clearSingleSelect();
    },
    selectSup(val) {
      this.$emit('input', val.value);
      this.$emit('clickItem', val.value);
    },
    queryChange(val) {
      this.searchMethod(val);
    },
    clearSub() {
      this.searchMethod();
      this.$emit('input', '');
      this.$emit('clickItem', '');
    }
  }
};
</script>

<style lang="less" scoped>
.filterable-select {
  :deep(.ivu-select-input) {
    margin-top: -1px;
  }
}
</style>
