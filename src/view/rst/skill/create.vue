<template>
  <div class="detail-box">
    <Alert type="warning" v-if="detailInfo.audit_status === '3'">
      <div class="mb10">审核时间：{{ detailInfo.audit_time | date_format('YYYY-MM-DD HH:mm:ss') }}</div>
      <div>审核意见：{{ detailInfo.audit_reason || '-' }}</div>
    </Alert>
    <Form
      :label-width="100"
      :disabled="isDetail"
      ref="queryFormData"
      :label-colon="true"
      :model="queryFormData"
      :rules="ruleValidate"
    >
      <div class="block-header">基本信息</div>
      <div class="form-box">
        <FormItem label="理疗师姓名" prop="name">
          <Input v-model="queryFormData.name" placeholder="请输入理疗师姓名" show-word-limit maxlength="10" />
        </FormItem>

        <FormItem label="手机号" prop="mobile">
          <Input v-model="queryFormData.mobile" style="width: 100%" placeholder="请输入手机号" maxlength="11"></Input>
        </FormItem>

        <FormItem label="身份证号" prop="idcard">
          <Input v-model="queryFormData.idcard" style="width: 100%" placeholder="请输入身份证号" maxlength="18"></Input>
        </FormItem>

        <FormItem label="头像" prop="avatar">
          <material-picture v-model="queryFormData.avatar" :limit="1"></material-picture>
        </FormItem>

        <FormItem label="个人介绍">
          <Input
            v-model="queryFormData.desc"
            type="textarea"
            :autosize="{ minRows: 6, maxRows: 6 }"
            maxlength="500"
            show-word-limit
            placeholder="请输入个人介绍"
          />
        </FormItem>
      </div>
      <div class="block-header flex flex-item-align">
        <div>理疗师等级</div>
        <div class="tip">理疗师的等级会和业绩提成相关</div>
      </div>
      <div class="form-box flex">
        <FormItem label="理疗师等级" prop="level" style="width: 100%">
          <Select v-model="queryFormData.level" placeholder="请选择理疗师等级" clearable>
            <Option v-for="item in level_list" :value="item.id" :key="item.id">{{ item.desc }}</Option>
          </Select>
        </FormItem>

        <!--        <FormItem label="等级对应比例" v-if="level_list[queryFormData.level]?.scale">-->
        <!--          <div class="level-show">{{ level_list[queryFormData.level]?.scale || 0 }}%</div>-->
        <!--        </FormItem>-->
      </div>

      <div class="form-box flex">
        <FormItem label="薪资结构" prop="salary_type" style="width: 100%">
          <RadioGroup v-model="queryFormData.salary_type">
            <Radio v-for="(item, index) in salary_list" :label="item.id" :key="index">{{ item.desc }}</Radio>
          </RadioGroup>
        </FormItem>
      </div>

      <div class="block-header flex flex-item-align">
        <div>技能点</div>
        <div class="tip">不同的服务需要理疗师不同的技能能力去匹配，根据理疗师技能能力去关联理疗师所能提供的服务能力</div>
      </div>

      <FormItem>
        <div class="skill-box">
          <div class="skill-item" v-for="(item, index) in skill_list" :key="index">
            <div class="flex flex-item-align" style="margin-left: -26px">
              <div class="skill-title">{{ item.title }}</div>
              <Checkbox
                style="margin-left: 20px"
                :indeterminate="indeterminate"
                @on-change="handleCheckAll"
                :value="checkAll"
                >全选</Checkbox
              >
            </div>
            <CheckboxGroup v-model="checkedGroup">
              <Checkbox :label="s_item.id" v-for="(s_item, s_index) in item.list" :key="'skill' + s_index"
                >{{ s_item.desc }}
              </Checkbox>
            </CheckboxGroup>
          </div>
        </div>
      </FormItem>
    </Form>
    <div style="height: 35px"></div>
    <div class="fixed-bottom-wrapper">
      <back-button></back-button>
      <Button class="ml10" v-if="!$route.query.type" type="primary" :loading="confirmLoading" @click="save"
        >确认并提交审核
      </Button>
      <!--      <audit-refuse v-if="isDetail && detailInfo.audit_status === '1'" class="ml10" @ok="handleRefuse"></audit-refuse>-->
      <!--      <audit-pass v-if="isDetail && detailInfo.audit_status === '1'" class="ml10" @ok="handlePass"></audit-pass>-->
    </div>
  </div>
</template>

<script>
import MaterialPicture from '@/components/MaterialCenter/MaterialPicture.vue';
import S from '@/utils/util';

const validateMobileCard = (rule, value, callback) => {
  const reg = /^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/;
  if (!reg.test(value)) {
    callback(new Error('请输入正确的身份证号'));
  } else {
    callback();
  }
};
const validateMobile = (rule, value, callback) => {
  if (value === '') {
    callback(new Error('请输入正确的手机号码'));
  } else {
    const reg = /^1[3456789]\d{9}$/;
    if (!reg.test(value)) {
      callback(new Error('请输入正确的手机号码'));
    }
    callback();
  }
};
export default {
  name: 'create',
  components: {
    MaterialPicture
  },
  mixins: [],
  data() {
    return {
      queryFormData: {
        name: '',
        mobile: '',
        avatar: '',
        idcard: '',
        desc: '',
        level: '',
        salary_type: '1'
      },
      ruleValidate: {
        name: [{ required: true, message: '该填写服务名称', trigger: 'change' }],
        mobile: [{ required: true, trigger: 'blur', validator: validateMobile }],
        idcard: [{ required: true, trigger: 'blur', validator: validateMobileCard }],
        avatar: [{ required: true, message: '请上传头像', trigger: 'change' }],
        level: [{ required: true, message: '该选择理疗师等级', trigger: 'change' }],
        salary_type: [{ required: true, message: '该选择薪资结构', trigger: 'change' }]
      },
      detailInfo: {}, // 详情信息
      confirmLoading: false,
      skill_list: [],
      level_list: [],
      checkedGroup: [],
      indeterminate: false,
      checkAll: false
    };
  },
  computed: {
    isDetail() {
      return this.$route.query.type === 'detail';
    }
  },
  watch: {
    checkedGroup(val) {
      if (val.length && val.length === this.skill_list[0]?.list?.length) {
        this.checkAll = true;
        this.indeterminate = false;
      } else {
        if (val.length !== this.skill_list[0]?.list?.length) {
          this.checkAll = false;
          this.indeterminate = false;
          if (val.length) {
            this.indeterminate = true;
          }
        }
      }
    }
  },
  created() {
    this.getPhysioSkillOptions();
  },
  mounted() {},
  methods: {
    handleCheckAll(val) {
      if (val) {
        this.allCheck();
      } else {
        this.unAllCheck();
      }
    },
    allCheck() {
      let list = this.skill_list[0]?.list || [];
      this.checkedGroup = list.map(item => item.id);
    },
    unAllCheck() {
      this.checkedGroup = [];
    },
    handleRefuse(reason) {
      this.auditPromotion('3', reason);
    },
    handlePass() {
      this.auditPromotion('2');
    },
    auditPromotion(action, reason = '') {
      let params = {
        id: this.$route.query.id,
        audit_status: action,
        audit_reason: reason
      };
      this.$api.getPhysioAudit(params).then(
        res => {
          this.$Message.success(`审核${action === '2' ? '通过' : '驳回'}成功`);
          this.$router.back();
        },
        err => this.$Message.error(err.errmsg)
      );
    },
    getPhysioSkillOptions() {
      let params = {};
      this.$api.getPhysioSkillOptions(params).then(
        res => {
          this.skill_list = res.skill;
          this.level_list = res.level;
          this.salary_list = S.descToArrHandle(res.salary);
          let id = this.$route.query.id;
          if (id) {
            this.getPhysioInfo(id);
          }
        },
        err => this.$Message.error(err.errmsg)
      );
    },
    validData() {
      if (!this.checkedGroup.length) {
        this.$Message.error('请至少选择一项技能点');
        return false;
      }
      return true;
    },
    save() {
      this.$refs.queryFormData.validate(valid => {
        if (valid && this.validData()) {
          this.getPhysioEdit();
        }
      });
    },

    // 获取详情
    getPhysioInfo(id) {
      let params = {
        id,
        scene: this.$route.query.field === 'info' ? 'info' : 'audit_info'
      };
      this.$api.getPhysioInfo(params).then(
        res => {
          this.detailInfo = res;
          this.handlerData(res);
        },
        err => this.$Message.error(err.errmsg)
      );
    },

    handlerData(data) {
      this.queryFormData.name = data.name;
      this.queryFormData.mobile = data.mobile;
      this.queryFormData.idcard = data.idcard;
      this.queryFormData.avatar = data.avatar;
      this.queryFormData.desc = data.desc;
      this.queryFormData.level = data.level;
      this.queryFormData.salary_type = data.salary_type;
      this.checkedGroup = data.skill_list;
      if (this.checkedGroup.length === this.skill_list[0]?.list?.length) {
        this.checkAll = true;
      }
    },

    getPhysioEdit() {
      this.confirmLoading = true;
      let params = {
        id: this.$route.query.id,
        ...this.queryFormData,
        skill_list: this.checkedGroup
      };
      this.$api
        .getPhysioEdit(params)
        .then(
          res => {
            this.$Message.success('提交成功');
            this.$router.push('/rst/skill/list');
          },
          err => this.$Message.error(err.errmsg)
        )
        .finally(() => (this.confirmLoading = false));
    }
  }
};
</script>

<style lang="less" scoped>
.detail-box {
  .form-box {
    margin-left: 50px;
    width: 50%;
  }
}

.tip {
  margin-left: 20px;
  font-size: 12px;
  transform: scale(0.9);
  color: #8d8585;
  display: inline-block;
}

.level-show {
  background: #efdfdf;
  height: 30px;
  width: 100px;
  padding-left: 10px;
}

.skill-box {
  .skill-item {
    .skill-title {
      font-size: 13px;
    }
  }
}

::v-deep .ivu-checkbox-group {
  display: flex;
  flex-wrap: wrap;
  margin-left: 20px;

  .ivu-checkbox-group-item {
    margin-bottom: 10px;
    width: 15%;
  }
}
</style>
