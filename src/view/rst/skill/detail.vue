<template>
  <div class="detail-wrapper">
    <div class="info-box">
      <div class="info-box-header">
        <div class="header-left">
          <div class="avatar-box">
            <img
              class="avatar"
              :src="
                detailInfo.avatar
                  ? `${detailInfo.avatar}-B.w300`
                  : 'http://img-sn-i01s-cdn.rsjxx.com/rsjxx/2023/1220/151459_81411.png'
              "
            />
          </div>

          <div class="name-box">
            <div class="name-box-top">
              <div class="name">{{ detailInfo.name || '-' }}</div>
              <div class="level">
                <span>P{{ detailInfo.level || '0' }}</span>
              </div>
            </div>

            <div class="score-box">
              <div class="score">{{ Number(detailInfo.score || 0)?.toFixed(1) }}分</div>
              <div class="order-num">预约单：{{ detailInfo.order_finished_total || '0' }}</div>
            </div>
          </div>
        </div>
      </div>

      <div class="info-detail-box">
        <div class="detail-item">
          <div class="item-label">所属合伙人：</div>
          <div class="item-value">{{ detailInfo.cp_name || '-' }}</div>
        </div>

        <div class="detail-item">
          <div class="item-label">联系电话：</div>
          <div class="item-value">{{ detailInfo.mobile || '-' }}</div>
        </div>

        <div class="detail-item">
          <div class="item-label">所属导师：</div>
          <div class="item-value">{{ detailInfo.advisor_name || '-' }}</div>
        </div>

        <div class="detail-item">
          <div class="item-label">薪资结构：</div>
          <div class="item-value">{{ detailInfo.salary_type_text || '-' }}</div>
        </div>

        <div class="detail-item">
          <div class="item-label">所属门店：</div>
          <div class="item-value">{{ detailInfo.clinic_name || '-' }}</div>
        </div>

        <div class="detail-item">
          <div class="item-label">身份证号码：</div>
          <div class="item-value">{{ detailInfo.idcard || '-' }}</div>
        </div>

        <div class="detail-item">
          <div class="item-label">介绍：</div>
          <div class="item-value">
            <Tooltip
              placement="bottom"
              max-width="500"
              :disabled="detailInfo?.desc?.length < 21"
              :content="detailInfo.desc"
            >
              <span class="ecs ecs-3" :class="{ cursor: detailInfo?.desc?.length > 20 }">{{
                detailInfo.desc || '-'
              }}</span>
            </Tooltip>
          </div>
        </div>
      </div>
    </div>

    <div class="tabs-box">
      <Tabs v-model="currentTabId" @on-click="tabIndexChange">
        <TabPane :label="item.title" name="message" v-for="item in tabList" :key="item.id" :name="item.id"></TabPane>
      </Tabs>
    </div>

    <component :is="currentComponent" :detailInfo="detailInfo"></component>

    <div style="height: 35px"></div>
    <div class="fixed-bottom-wrapper">
      <back-button></back-button>
    </div>
  </div>
</template>

<script>
import serviceList from './components/serviceList.vue';
import recordList from './components/changeRecord';

export default {
  name: 'detail',
  components: { serviceList, recordList },
  mixins: [],
  data() {
    return {
      currentTabId: '0',
      tabList: [
        { id: '0', title: '关联服务', component: 'serviceList' },
        { id: '1', title: '修改记录', component: 'recordList' }
      ],
      detailInfo: {}
    };
  },
  computed: {
    currentComponent() {
      return this.tabList.find(item => item.id === this.currentTabId).component;
    }
  },
  watch: {},
  created() {
    let id = this.$route.query.id;
    if (id) {
      this.getPhysioInfo(id);
    }
  },
  mounted() {},
  methods: {
    tabIndexChange(id) {
      this.currentTabId = id;
      this.$router.replace({ query: { ...this.$route.query, routeTabId: id } });
    },
    // 获取详情
    getPhysioInfo(id) {
      let params = {
        id
      };
      this.$api.getPhysioInfo(params).then(
        res => {
          this.detailInfo = res;
        },
        err => this.$Message.error(err.errmsg)
      );
    }
  }
};
</script>

<style lang="less" scoped>
.detail-wrapper {
  .info-box {
    background: #faf9f9;
    padding: 10px 20px;
    border-radius: 4px;

    .info-box-header {
      display: flex;
      align-items: center;
      justify-content: space-between;

      .header-left {
        display: flex;
        align-items: center;

        .avatar-box {
          width: 50px;
          height: 50px;
          min-width: 50px;
          background: 50px;
          border-radius: 50%;

          .avatar {
            width: 100%;
            height: 100%;
            border-radius: 50%;
            object-fit: cover;
          }
        }

        .name-box {
          margin-left: 20px;

          .name-box-top {
            display: flex;
            align-items: center;
            font-size: 14px;

            .level {
              margin-left: 12px;
            }
          }

          .score-box {
            display: flex;
            align-items: center;
            margin-top: 4px;
            color: #ccc;

            .score {
            }

            .order-num {
              margin-left: 10px;
            }
          }
        }
      }
    }

    .info-detail-box {
      margin-top: 20px;
      display: flex;
      flex-wrap: wrap;

      .detail-item {
        width: 33%;
        display: flex;
        margin-bottom: 10px;
        white-space: pre-line;
        word-break: break-all;

        .item-label {
          width: 80px;
          min-width: 80px;
          text-align: right;
        }

        .item-value {
          white-space: pre-line;
          word-break: break-all;
        }
      }
    }
  }

  .tabs-box {
  }
}
</style>
