<template>
  <div>
    <div class="flex mb-10">
      <Button type="primary" @click="addTechnician">创建理疗师</Button>
    </div>
    <Form inline @submit.native.prevent @keyup.enter.native="onSearch">
      <Row>
        <FormItem>
          <Input v-model="queryFormData.name" placeholder="理疗师姓名" clearable />
        </FormItem>

        <FormItem>
          <store-search ref="store" v-model="queryFormData.clinic_id" :isClearable="true"></store-search>
        </FormItem>

        <FormItem>
          <Input v-model="queryFormData.advisor_name" placeholder="所属导师" clearable />
        </FormItem>

        <FormItem>
          <source-search
            ref="source"
            v-model="queryFormData.source_id"
            @clickItem="name => (queryFormData.source = name)"
            :isClearable="true"
          ></source-search>
        </FormItem>

        <FormItem>
          <Select v-model="queryFormData.status" placeholder="状态">
            <Option v-for="item in status_list" :value="item.id" :key="item.id">{{ item.desc }}</Option>
          </Select>
        </FormItem>

        <FormItem style="text-align: left">
          <Button type="primary" class="mr10" @click="onSearch">筛选</Button>
          <Button type="default" @click="onResetSearch" class="mr10">重置</Button>
        </FormItem>
      </Row>
    </Form>

    <div>
      <div class="panel-nav">
        <a
          class="nav"
          v-for="item in list_status"
          :key="item.id"
          :class="{ active: $route.query.list_status == item.id }"
          @click.prevent.capture="onStatusChange(item.id)"
        >
          {{ item.desc }}
          <Tag :color="getTagColor(item.id)">{{ listCount[item.id] }}</Tag>
        </a>
      </div>
      <Table
        :loading="tableLoading"
        @on-select-all="selectAll"
        @on-select="select"
        @on-select-all-cancel="selectAllCancel"
        @on-select-cancel="selectCancel"
        :columns="tableCols"
        :data="list"
        :height="$store.state.app.clientHeight - 360"
      >
        <template slot-scope="{ row }" slot="technician_info">
          <div class="lv-box" style="align-items: center">
            <div class="label">头像：</div>
            <div class="value">
              <img
                class="avatar"
                :src="
                  row.avatar
                    ? `${row.avatar}-B.w300`
                    : 'http://img-sn-i01s-cdn.rsjxx.com/rsjxx/2023/1220/151459_81411.png'
                "
              />
            </div>
          </div>

          <div class="lv-box">
            <div class="label">姓名：</div>
            <div class="value">{{ row.name || '-' }}</div>
          </div>

          <div class="lv-box">
            <div class="label">手机号：</div>
            <div class="value">{{ row.mobile || '-' }}</div>
          </div>

          <div class="lv-box">
            <div class="label">等级：</div>
            <div class="value">P{{ row.level }}</div>
          </div>

          <div class="lv-box">
            <div class="label">总评分：</div>
            <div class="value">{{ row.score || '-' }}</div>
          </div>
        </template>

        <!--综合评分-->
        <template slot-scope="{ row }" slot="score">
          <div class="lv-box">
            <div class="label label90">技术专业度：</div>
            <div class="value">{{ row.technology_grade || '-' }}</div>
          </div>

          <div class="lv-box">
            <div class="label label90">服务态度：</div>
            <div class="value">{{ row.service_grade || '-' }}</div>
          </div>

          <div class="lv-box">
            <div class="label label90">整体体验：</div>
            <div class="value">{{ row.overall_grade || '-' }}</div>
          </div>

          <div class="lv-box">
            <div class="label label90">环境与舒适度：</div>
            <div class="value">{{ row.comfort_grade || '-' }}</div>
          </div>
        </template>

        <!--状态-->
        <template slot-scope="{ row }" slot="status_desc">
          <div>{{ row.status_desc || '-' }}</div>
        </template>

        <!--所属关系-->
        <template slot-scope="{ row }" slot="relation_clinic">
          <div>
            <Tooltip :content="row.clinic_name" max-width="220">
              <div class="ecs ecs-3">{{ row.clinic_name || '-' }}</div>
            </Tooltip>
          </div>
        </template>
        <!--所属关系-->
        <template slot-scope="{ row }" slot="relation_cp">
          {{ row.cp_name || '-' }}
        </template>

        <!--所属导师-->
        <template slot-scope="{ row }" slot="advisor_name">
          <div>{{ row.advisor_name || '-' }}</div>
        </template>

        <!--类型-->
        <template slot-scope="{ row }" slot="audit_type_text">
          <div>{{ row.audit_type_text || '-' }}</div>
        </template>

        <!--可提供服务-->
        <template slot-scope="{ row }" slot="service">
          <a v-if="row.service_list?.length" @click="showService(row)">{{ row.service_list?.length }}</a>
          <span v-else>0</span>
        </template>

        <!--技能-->
        <template slot-scope="{ row }" slot="skill">
          <a v-if="row.skill_list?.length" @click="showSkill(row)">{{ row.skill_list?.length }}</a>
          <span v-else>0</span>
        </template>

        <!--创建信息-->
        <template slot-scope="{ row }" slot="create_info">
          <div class="lv-box">
            <div class="label">创建来源：</div>
            <div class="value">{{ row.source_text || '-' }}</div>
          </div>

          <div class="lv-box">
            <div class="label">创建人：</div>
            <div class="value">{{ row.creator || '-' }}</div>
          </div>

          <div class="lv-box">
            <div class="label">创建时间：</div>
            <div class="value">{{ row.create_time | date_format('YYYY-MM-DD HH:mm:ss') }}</div>
          </div>
        </template>

        <!--提交时间-->
        <template slot-scope="{ row, index }" slot="audit_create_time">
          <div>{{ row.audit_create_time | date_format('YYYY-MM-DD HH:mm:ss') }}</div>
        </template>

        <template slot-scope="{ row, index }" slot="update_time">
          <div v-if="row.audit_type === '1'">-</div>
          <div v-else>{{ row.update_time | date_format('YYYY-MM-DD HH:mm:ss') }}</div>
        </template>

        <template slot-scope="{ row, index }" slot="audit_time">
          <div>{{ row.audit_time | date_format('YYYY-MM-DD HH:mm:ss') }}</div>
        </template>

        <template slot-scope="{ row, index }" slot="audit_reason">
          <Tooltip :content="row.audit_reason" v-if="row.audit_reason" :disabled="row.audit_reason?.length < 6">
            <div class="ecs cursor">{{ row.audit_reason || '-' }}</div>
          </Tooltip>
          <div v-else>-</div>
        </template>

        <template slot-scope="{ row, index }" slot="action">
          <OperationFolding :maxVisibleActions="3" :actions="getActions(row, index)" :row="row"></OperationFolding>
        </template>
      </Table>
    </div>

    <div class="block_20"></div>

    <div class="btn-group" v-show="queryFormData.list_status === '1' || queryFormData.list_status === '2'">
      <div class="group-label">批量操作:</div>
      <Button type="primary" @click="batchAdd('clinic')">分发门店</Button>
      <Button type="primary" style="margin-left: 10px" @click="batchAdd('advisor')">关联导师</Button>
    </div>
    <KPage
      :current="+queryFormData.page"
      :page-size="+queryFormData.pageSize"
      :total="+total"
      style="text-align: center"
      @on-change="handleCurrentChange"
      @on-page-size-change="handleSizeChange"
    />

    <!--关联服务-->
    <service-show v-model="serviceVisible" :row="currentRow"></service-show>
    <!--技能-->
    <skill-show v-model="skillVisible" :row="currentRow" @success="loadList"></skill-show>
    <!--分发合伙人-->
    <distribute-clinic
      v-model="distributeVisible"
      :ids="getIds"
      @success="distributeSuccess"
      :echoClinicList="echoClinicList"
    ></distribute-clinic>
    <!--关联导师-->
    <relate-advisor v-model="relateAdvisorVisbile" :ids="getIds" @success="distributeSuccess"></relate-advisor>
  </div>
</template>

<script>
import storeSearch from './components/store-search.vue';
import sourceSearch from './components/source-search.vue';
import search from '@/mixins/search';
import S from '@/utils/util';
import serviceShow from './components/serviceShow.vue';
import skillShow from './components/skillShow.vue';
import distributeClinic from './components/distributeClinic.vue';
import RelateAdvisor from './components/relateAdvisor.vue';
import { has_distribute, wait_distribute, wait_examine, has_reject } from './data';
import renderHeader from '@/mixins/renderHeader';

const init_query_form_data = {
  page: 1,
  pageSize: 20,
  name: '',
  clinic_id: '',
  opc_id: '',
  advisor_name: '',
  source_id: '',
  source: '',
  list_status: '1',
  status: '',
  r: ''
};
export default {
  name: 'list',
  components: { RelateAdvisor, storeSearch, sourceSearch, serviceShow, skillShow, distributeClinic },
  mixins: [search, renderHeader],
  props: {},
  data() {
    return {
      apiName: 'getPhysioList',
      queryFormData: {
        ...init_query_form_data
      },
      tableCols: [],
      statusDesc: [],

      currentRow: {},
      serviceVisible: false,
      skillVisible: false,
      selected_items: {},
      list_status: [],
      distributeVisible: false,
      relateAdvisorVisbile: false,
      ids: [],
      listCount: {},
      echoClinicList: [],
      status_list: []
    };
  },
  computed: {
    getIds() {
      return Object.keys(this.selected_items);
    },
    getTagColor() {
      return type => {
        switch (type) {
          case '1':
            return 'success';
          case '2':
            return 'warning';
          case '3':
            return 'warning';
          case '4':
            return 'error';
          default: // 已取消
            return 'default';
        }
      };
    },
    getActions() {
      return (row, index) => {
        return [
          {
            label: row.status == 'ON' ? '停用' : '启用',
            handler: this.updateStatus,
            confirmText: row.status == 'ON' ? '停用后将在所有诊所的排班中移除' : '启用后需要重新进行排班',
            tagType: 'Poptip',
            params: row,
            isHidden: !(this.queryFormData.list_status == '1' || this.queryFormData.list_status == '2')
          },
          {
            label: '编辑',
            handler: this.edit,
            tagType: 'a',
            params: row,
            isHidden: !(
              row.source === 'CP' &&
              row.is_audit === '1' &&
              (this.queryFormData.list_status === '1' || this.queryFormData.list_status === '2')
            )
          },
          {
            label: '重新提交',
            handler: this.submitAgain,
            tagType: 'a',
            params: row,
            isHidden: !(row.audit_status === '3' && this.queryFormData.list_status === '4')
          },
          {
            label: '详情',
            handler: this.toDetail,
            tagType: 'a',
            params: row
          }
        ];
      };
    }
  },
  watch: {
    'queryFormData.list_status': {
      handler(val) {
        const map = {
          1: has_distribute,
          2: wait_distribute,
          3: wait_examine,
          4: has_reject
        };
        this.tableCols = map[val];
        this.tableCols?.forEach((item, index) => {
          if (item.slot === 'service') {
            item.renderHeader = (h, params) =>
              this._renderHeader(h, params, '根据理疗师的技能点去匹配该理疗师可以提供的服务', 400);
          }
        });
      },
      immediate: true
    },
    list() {
      this.echoSelectedItems();
    }
  },
  created() {
    this.getPhysioOptions();
    this.queryFormData = S.merge(this.queryFormData, this.$route.query);
    this.submitQueryForm(true);
  },
  mounted() {},
  methods: {
    handlerListData(data) {
      this.list.forEach((item, index) => {
        if (item.status === 'OFF') {
          this.$set(this.list, index, { ...item, _disabled: true });
        }
      });
    },
    updateStatus(row) {
      this.selected_items = {};
      let params = {
        status: row.status == 'ON' ? 'OFF' : 'ON',
        id: row.id
      };
      this.$api.getPhysioStatus(params).then(
        res => {
          let text = row.status == 'ON' ? '停用成功' : '启用成功';
          this.$Message.success(text);
          this.submitQueryForm(true);
        },
        err => this.$Message.error(err.errmsg)
      );
    },

    distributeSuccess() {
      this.selected_items = [];
      this.onSearch();
    },
    getBatchIds() {
      let list = [];
      for (const key in this.selected_items) {
        list.push(key);
      }
      this.ids = [];
    },
    handleList(res) {
      this.listCount = res.list_status_count;
    },
    batchAdd(val) {
      console.log('=>(list.vue:370) this.selected_items', this.selected_items);
      if (!Object.keys(this.selected_items).length) {
        this.$Message.error('请先选择理疗师');
        return;
      }
      if (!this.areDispatchClinicIdsEqual(this.selected_items)) {
        this.$Message.error('您选择的理疗师所属门店不一致，请确保所有理疗师均属于相同门店后再操作。');
        return;
      }

      this.currentRow = {};
      this.getBatchIds();
      this.echoClinicList = Object.values(this.selected_items)[0].dispatch_clinic_ids;
      console.log('=>(list.vue:388) this.echoClinicList', this.echoClinicList);
      if (val === 'clinic') {
        this.distributeVisible = true;
      } else if (val === 'advisor') {
        this.relateAdvisorVisbile = true;
      }
    },

    areDispatchClinicIdsEqual(obj) {
      const sets = Object.values(obj).map(item => new Set(item.dispatch_clinic_ids));
      console.log('=>(list.vue:390) sets', sets);

      return sets.every((set, _, arr) => {
        if (set.size !== arr[0].size) return false; // 长度不同，直接返回 false
        for (let val of set) {
          if (!arr[0].has(val)) return false; // 检查每个值是否都在基准 Set 中
        }
        return true;
      });
    },
    // 根据选中的数据进行回显
    echoSelectedItems() {
      this.list.forEach((item, index) => {
        if (item.id in this.selected_items) {
          this.list[index]._checked = true;
        }
      });
    },
    // 全选
    selectAll(val) {
      val.forEach(item => {
        this.$set(this.selected_items, item.id, item);
      });
      this.validDistribute();
    },
    // 选择单个
    select(val, row) {
      this.$set(this.selected_items, row.id, row);
      this.validDistribute();
    },
    // 全部取消
    selectAllCancel() {
      this.list &&
        this.list.forEach(item => {
          this.$delete(this.selected_items, item.id);
        });
    },
    // 取消单个
    selectCancel(val, row) {
      this.$delete(this.selected_items, row.id);
    },
    addTechnician() {
      this.$router.push('/rst/skill/create');
    },
    edit(row) {
      this.$router.push({
        path: '/rst/skill/create',
        query: {
          id: row.id,
          field: 'info'
        }
      });
    },
    submitAgain(row) {
      this.$router.push({
        path: '/rst/skill/create',
        query: {
          id: row.id
        }
      });
    },
    toDetail(row) {
      if (this.$route.query.list_status === '3' || this.$route.query.list_status === '4') {
        this.$router.push({
          path: '/rst/skill/create',
          query: {
            id: row.id,
            type: 'detail'
          }
        });
      } else {
        this.$router.push({
          path: '/rst/skill/detail',
          query: {
            id: row.id
          }
        });
      }
    },
    showService(row) {
      this.currentRow = row;
      this.serviceVisible = true;
    },
    showSkill(row) {
      this.currentRow = row;
      this.skillVisible = true;
    },
    // 状态改变调用接口
    onStatusChange(list_status) {
      this.selected_items = {};
      this.queryFormData.page = 1;
      this.queryFormData.list_status = list_status;
      this.submitQueryForm();
    },

    // 重置
    onResetSearch() {
      this.queryFormData = { ...init_query_form_data };
      this.timeRange = [];
      this.$refs.store?.clear();
      this.$refs.source?.clear();
      this.submitQueryForm();
    },

    getPhysioOptions() {
      let params = {};
      this.$api.getPhysioOptions(params).then(res => {
        this.list_status = S.descToArrHandle(res.list_status);
        this.status_list = S.descToArrHandle(res.status);
      });
    },

    // 提示不可分发
    validDistribute() {
      let list = [];
      for (let key in this.selected_items) {
        if (this.selected_items[key].service_list?.length < 1) {
          list.push(this.selected_items[key]);
          this.$delete(this.selected_items, key);
          let index = this.list.findIndex(item => item.id === key);
          setTimeout(() => {
            this.$set(this.list, index, { ...this.list[index], checked: false });
          }, 0);
        }
      }
      if (list.length) {
        let name_list = list.map(item => `${item.name}理疗师`);
        this.$Message.error(`【${name_list.join('、')}】的技能点暂无可提供的服务，至少保证有1个可提供服务才能分发`);
        return false;
      }
      return true;
    }
  },
  beforeRouteUpdate(to, from, next) {
    this.queryFormData = S.merge(init_query_form_data, to.query);
    this.getTimeRange();
    this.loadList();
    next();
  },
  filters: {}
};
</script>

<style lang="less" scoped>
.ml8 {
  margin-left: 8px;
}

.lv-box {
  display: flex;
  align-items: center;

  .label {
    width: 60px;
    min-width: 60px;
    text-align: right;
  }

  .label90 {
    width: 90px;
    min-width: 90px;
  }

  .value {
    text-align: left;
  }
}

.avatar {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  object-fit: cover;
}
.btn-group {
  .group-label {
    margin-right: 10px;
  }
  display: flex;
  align-items: center;
}
</style>
