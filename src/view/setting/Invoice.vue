<template>
  <div class="Invoice-wrapper">
    <div class="block-header"><span>开票信息</span></div>
    <div style="width: 60%">
      <Form
        :label-width="170"
        label-colon
        :model="formData"
        ref="invoiceForm"
        :rules="formDataRules"
        :disabled="formData.status === 'EXAMINE' || formData.status === 'PLAT_EXAMINE'"
      >
        <FormItem label="公司名称" prop="organization_name">
          <Input v-model="formData.organization_name" placeholder="请输入公司名称" />
        </FormItem>
        <FormItem label="公司税号" prop="organization_code">
          <Input
            style="width: 100%"
            :maxlength="18"
            :disabled="dis_edit_code === '1'"
            v-model="formData.organization_code"
            placeholder="请输入18位税号"
            show-word-limit
          ></Input>
        </FormItem>
        <FormItem label="邮箱" prop="invoice.email">
          <Input style="width: 100%" v-model="formData.invoice.email" placeholder="请输入邮箱"></Input>
        </FormItem>

        <FormItem label="注册地址">
          <div class="flex">
            <div class="address flex-1">
              <el-cascader
                :disabled="formData.status === 'EXAMINE' || formData.status === 'PLAT_EXAMINE'"
                v-model="selectedAddress"
                :options="options"
                clearable
                placeholder="请选择注册地址"
                size="small"
                popper-class="address-com"
                style="width: 100%"
                @change="regionChange"
              >
              </el-cascader>
            </div>
            <div class="flex-1 ml10">
              <Input v-model.trim="formData.invoice.reg_address.detail" placeholder="详细地址"></Input>
            </div>
          </div>
        </FormItem>
        <FormItem label="注册电话">
          <Input style="width: 100%" v-model="formData.invoice.reg_mobile" placeholder="请输入注册电话"></Input>
        </FormItem>
        <FormItem label="开户银行">
          <Input style="width: 100%" v-model="formData.invoice.bank" placeholder="请输入开户银行"></Input>
        </FormItem>
        <FormItem label="银行账号">
          <Input style="width: 100%" v-model="formData.invoice.bank_account" placeholder="请输入银行账号"></Input>
        </FormItem>
        <FormItem label="审核状态" v-if="formData.status">
          <div>
            <span>{{ formData.status_desc }}</span>
            <span v-if="formData.status === 'REJECT'" class="red">（驳回原因：{{ formData.reject_reason }}）</span>
          </div>
        </FormItem>
      </Form>
    </div>
    <div class="fixed-bottom-wrapper" v-if="formData.status !== 'EXAMINE' && formData.status !== 'PLAT_EXAMINE'">
      <Button class="btnStyle" type="primary" @click="submit('invoiceForm')">提交 </Button>
    </div>
    <tips-modal
      v-model="tipsVisible"
      @onOk="tipsConfirm"
      confirmText="好的"
      :showCancel="false"
      :contentText="tipsContent"
    >
    </tips-modal>
  </div>
</template>

<script>
import { CodeToText, regionData } from '@/utils/chinaMap';
import TipsModal from '@/components/confirmModal/TipsModal.vue';

export default {
  name: 'Invoice',
  mixins: [],

  components: { TipsModal },

  props: {},

  data() {
    return {
      options: regionData,
      selectedAddress: [],
      formData: {
        organization_code: '', //公司税号
        organization_name: '', //公司名称

        invoice: {
          reg_address: {
            //注册地址
            prov: {
              name: '',
              code: ''
            },
            city: {
              name: '',
              code: ''
            },
            county: {
              name: '',
              code: ''
            },
            detail: ''
          },
          reg_mobile: '', //注册电话
          bank: '', //开户银行
          bank_account: '', //银行账号
          email: '' //邮箱
        }
      },
      formDataRules: {
        organization_name: [{ required: true, message: '请输入公司名称', trigger: 'blur' }],
        organization_code: [
          { required: true, len: 18, message: '请输入18位税号', trigger: 'blur' },
          { type: 'string', pattern: /^[^oO]*$/, message: '税号不能包含字母o', trigger: 'blur' }
        ],
        'invoice.email': [
          { required: true, message: '请输入邮箱', trigger: 'blur' },
          {
            trigger: 'change, blur',
            validator: (rule, value, callback) => {
              if (!this.regEmail(value)) {
                callback(new Error('请输入正确的邮箱'));
              } else {
                callback();
              }
            }
          }
        ]
      }, // 表单校验
      tipsVisible: false,
      tipsContent: '',
      dis_edit_code: ''
    };
  },

  computed: {},

  watch: {},

  created() {
    this.getBaseInfo();
  },

  mounted() {},

  destroyed() {},

  methods: {
    // 邮箱校验
    regEmail(value) {
      return /^\w+((-\w+)|(\.\w+))*\@[A-Za-z0-9]+((\.|-)[A-Za-z0-9]+)*\.[A-Za-z0-9]+$/.test(value);
    },
    getBaseInfo() {
      this.$api.getExamineInvoice().then(res => {
        this.formData.status = res.status;
        this.formData.status_desc = res.status_desc;
        this.formData.reject_reason = res.reject_reason;
        this.formData.invoice.bank = res.invoice.bank;
        this.formData.invoice.bank_account = res.invoice.bank_account;
        this.formData.organization_name = res.organization_name;
        this.formData.organization_code = res.organization_code;
        this.formData.invoice.reg_address = res.invoice.reg_address;
        this.formData.invoice.reg_mobile = res.invoice.reg_mobile;
        this.dis_edit_code = res.dis_edit_code;

        if (res.invoice.reg_address.county.code) {
          this.selectedAddress = [
            res.invoice.reg_address.prov.code,
            res.invoice.reg_address.city.code,
            res.invoice.reg_address.county.code
          ];
        } else {
          this.selectedAddress = [res.invoice.reg_address.prov.code, res.invoice.reg_address.city.code];
        }
        this.formData.invoice.email = res.invoice.email;
      });
    },
    submit(formName) {
      this.$refs[formName].validate(valid => {
        if (valid) {
          this.$api.updateExamineInvoice(this.formData).then(
            res => {
              this.$Message.success('更新开票信息成功');
              // this.getBaseInfo()
              // this.tipsContent = res.msg;
              // this.tipsVisible = true;
            },
            err => this.$Message.error(err.errmsg)
          );
        } else {
          this.$Message.error('请完善表单信息');
        }
      });
    },
    tipsConfirm() {
      this.tipsVisible = false;
      this.getBaseInfo();
    },
    regionChange(address) {
      console.log(address);
      if (address.length) {
        console.log(CodeToText[address[0]]);
        const prov = {
          name: CodeToText[address[0]],
          code: address[0]
        };
        const city = {
          name: CodeToText[address[1]],
          code: address[1]
        };
        const county = {
          name: CodeToText[address[2]],
          code: address[2]
        };
        this.formData.invoice.reg_address.prov = prov;
        this.formData.invoice.reg_address.city = city;
        this.formData.invoice.reg_address.county = county;
      } else {
        this.formData.invoice.reg_address = {
          prov: {
            name: '',
            code: ''
          },
          city: {
            name: '',
            code: ''
          },
          county: {
            name: '',
            code: ''
          },
          detail: ''
        };
      }

      // 手动触发校验
      // this.$refs.formData.validateField('selectedAddress')
    }
  }
};
</script>

<style scoped lang="less">
::v-deep .el-cascader {
  .el-input.is-disabled .el-input__inner {
    background: #f3f3f3;
    color: #aaa;
    border-color: #bcc3d7;
  }
}

.red {
  color: red;
}
</style>
