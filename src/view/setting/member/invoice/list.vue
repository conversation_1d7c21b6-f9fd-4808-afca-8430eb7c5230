<template>
  <div class="Invoice-wrapper">
    <div class="block-header"><span>省公司开票信息</span></div>
    <div style="width: 60%">
      <Form :label-width="170" label-colon :model="formData" ref="invoiceForm" :rules="formDataRules">
        <FormItem label="公司名称" prop="organization_name">
          <Input v-model="formData.organization_name" placeholder="请输入公司名称"/>
        </FormItem>
        <FormItem label="公司税号" prop="organization_code">
          <Input
            style="width: 100%"
            v-model="formData.organization_code"
            placeholder="请输入公司税号"
          ></Input>
        </FormItem>

        <FormItem label="注册地址">
          <div class="flex">
            <div class="address flex-1">
              <el-cascader
                v-model="selectedAddress"
                :options="options"
                clearable
                placeholder="请选择注册地址"
                size="small"
                popper-class="address-com"
                style="width: 100%;"
                @change="regionChange">
              </el-cascader>
            </div>
            <div class="flex-1 ml10">
              <Input v-model.trim="formData.invoice.reg_address.detail" placeholder="详细地址"></Input>
            </div>
          </div>
        </FormItem>
        <FormItem label="注册电话">
          <Input
            style="width: 100%"
            v-model="formData.invoice.reg_mobile"
            placeholder="请输入注册电话"
          ></Input>
        </FormItem>
        <FormItem label="开户银行">
          <Input
            style="width: 100%"
            v-model="formData.invoice.bank"
            placeholder="请输入开户银行"
          ></Input>
        </FormItem>
        <FormItem label="银行账号">
          <Input
            style="width: 100%"
            v-model="formData.invoice.bank_account"
            placeholder="请输入银行账号"
          ></Input>
        </FormItem>
      </Form>
    </div>
    <div class="fixed-bottom-wrapper">
      <Button @click="back" v-if="$route.query.back">返回</Button>
      <dvd />
      <dvd />
      <dvd />
      <dvd />
      <Button class="btnStyle" :loading="submitLoading" type="primary" @click="submit('invoiceForm')">提交</Button>
    </div>

  </div>
</template>

<script>
import { CodeToText, regionData } from '@/utils/chinaMap'
export default {
  name: 'Invoice',
  mixins: [],

  components: {
  },

  props: {},

  data() {
    return {
      options: regionData,
      selectedAddress: [],
      submitLoading: false,
      formData: {
        organization_code: '',//公司税号
        organization_name: '',//公司名称

        invoice: {
          reg_address: {//注册地址
            prov: {
              name: '',
              code: '',
            },
            city: {
              name: '',
              code: '',
            },
            county: {
              name: '',
              code: '',
            },
            detail: '',
          },
          reg_mobile: '',//注册电话
          bank: '',//开户银行
          bank_account: '',//银行账号
        }

      },
      formDataRules: {
        organization_name: [
          { required: true, message: '请输入公司名称', trigger: 'blur' }
        ],
        organization_code: [
          { required: true, message: '请输入公司税号', trigger: 'blur' }
        ],
      }, // 表单校验
    }
  },

  computed: {},

  watch: {},

  created() {
    this.getInvoiceInfo()
  },

  mounted() {
  },

  destroyed() {
  },

  methods: {
    getInvoiceInfo() {
      this.$api.getInvoiceInfo().then( res => {
        this.formData.invoice.bank = res.bank
        this.formData.invoice.bank_account = res.bank_account
        this.formData.organization_name = res.organization_name
        this.formData.organization_code = res.organization_code
        this.formData.invoice.reg_address = res.reg_address
        this.formData.invoice.reg_mobile = res.reg_mobile
        if ( res.reg_address.county && res.reg_address.county.code ) {
          this.selectedAddress = [res.reg_address.prov.code, res.reg_address.city.code, res.reg_address.county.code]
        } else {
          this.selectedAddress = [res.reg_address.prov.code, res.reg_address.city.code]
        }
      } )
    },
    submit(formName) {
      this.$refs[formName].validate(valid=>{
        if(valid){
          this.submitLoading = true
          this.$api.updateInvoiceInfo(this.formData).then(res=>{
            this.$Message.success('更新省公司开票信息成功')
          },err=>this.$Message.error(err.errmsg)).finally( () => this.submitLoading = false )
        }else {
          this.$Message.error('请完善表单信息')
        }
      })
    },
    regionChange( address ) {
      console.log( address )
      if ( address.length ) {
        console.log( CodeToText[address[0]] )
        const prov = {
          name: CodeToText[address[0]],
          code: address[0],
        }
        const city = {
          name: CodeToText[address[1]],
          code: address[1],
        }
        const county = {
          name: CodeToText[address[2]],
          code: address[2],
        }
        this.formData.invoice.reg_address.prov = prov
        this.formData.invoice.reg_address.city = city
        this.formData.invoice.reg_address.county = county
      } else {
        this.formData.invoice.reg_address = {
          prov: {
            name: '',
            code: '',
          },
          city: {
            name: '',
            code: '',
          },
          county: {
            name: '',
            code: '',
          },
          detail: '',
        }
      }

      // 手动触发校验
      // this.$refs.formData.validateField('selectedAddress')
    },
    back () {
      this.$router.push('/finance/invoice/edit')
    }

  },
}
</script>

<style scoped lang="less">

</style>
