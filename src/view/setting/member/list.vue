<template>
  <div>
    <!--    <div class="form-warpper">-->
    <!--      <Form inline @submit.native.prevent @keyup.enter.native="onSearch">-->
    <!--        <Row>-->
    <!--          <FormItem>-->
    <!--            <Select-->
    <!--                v-model="queryFormData.department_id"-->
    <!--                placeholder="所属部门"-->
    <!--                style="width: 200px"-->
    <!--                clearable-->
    <!--            >-->
    <!--              <Option-->
    <!--                  v-for="item in selectableDepartList"-->
    <!--                  :key="item.id"-->
    <!--                  :value="item.id"-->
    <!--              >{{ item.name }}</Option-->
    <!--              >-->
    <!--            </Select>-->
    <!--          </FormItem>-->
    <!--          <Col>-->
    <!--            <FormItem style="text-align: left">-->
    <!--              <Button class="mr10" type="primary" @click="onSearch">筛选</Button>-->
    <!--              <Button class="mr10" type="default" @click="onResetSearch">重置</Button>-->
    <!--              <Button type="primary" @click="onOpenAddModal()">-->
    <!--                <Icon size="16" type="md-add" />-->
    <!--                添加员工-->
    <!--              </Button>-->
    <!--            </FormItem>-->
    <!--          </Col>-->
    <!--        </Row>-->
    <!--      </Form>-->
    <!--    </div>-->

    <Button type="primary" @click="onOpenAddModal()">
      <Icon size="16" type="md-add" />
      添加员工
    </Button>
    <div class="block_10"></div>

    <Table :columns="tableCols" :data="list" :height="$store.state.app.clientHeight - 225" :loading="tableLoading">
      <template slot="name" slot-scope="{ row }">
        {{ row.name }}
      </template>
      <template slot="mobile" slot-scope="{ row }">
        {{ row.mobile }}
      </template>
      <template slot="account" slot-scope="{ row }">
        {{ row.account }}
      </template>
      <template slot="role" slot-scope="{ row }">
        <span v-for="(role, key) in member_roles[row.id]" :key="key">{{ role.name }}</span>
      </template>
      <template slot-scope="{ row }" slot="weAppRole">
        {{ row.weapp_role_name || '-' }}
      </template>
      <template slot-scope="{ row }" slot="department_name">
        {{ row.department_name || '-' }}
      </template>
      <template slot="status" slot-scope="{ row }">
        <span v-if="row.status == 'OFF'" class="text-danger">{{ statusDesc[row.status].desc }}</span>
        <span v-else>{{ statusDesc[row.status].desc }}</span>
      </template>
      <template slot="operate" slot-scope="{ row }">
        <a @click="showChangeModel(row.id)">修改密码</a>
        <Divider type="vertical" />
        <a @click="onOpenAddModal(row.id)">编辑</a>
        <Divider type="vertical" />
        <Poptip v-if="row.status == 'OFF'" confirm title="确定启用？" transfer @on-ok="onStatus(row.id, 'ENABLED')">
          <a>启用</a>
        </Poptip>
        <Poptip v-else confirm title="确定停用？" transfer @on-ok="onStatus(row.id, 'DISABLED')">
          <a>停用</a>
        </Poptip>
      </template>
    </Table>

    <div class="block_20"></div>

    <KPage
      :current="+queryFormData.page"
      :page-size="+queryFormData.pageSize"
      :total="+total"
      style="text-align: center"
      @on-change="onPageChange"
      @on-page-size-change="handleSizeChange"
    />

    <!--     Modal -->
    <Modal
      v-model="addModal"
      :loading="addModalLoading"
      :mask-closable="false"
      :title="addModalTitle"
      :width="450"
      class="ks-add-modal"
      @on-visible-change="roleVisibleChange"
    >
      <div style="position: fixed; top: -200px">
        <input type="text" name="username" />
        <input type="password" name="password" />
      </div>
      <KWidget :labelWidth="80" label="姓名:">
        <Input v-model="addFormData.name" placeholder="请输入姓名" type="text" />
      </KWidget>
      <KWidget :labelWidth="80" label="手机号:">
        <Input v-model="addFormData.mobile" placeholder="请输入手机号" type="text" />
      </KWidget>
      <KWidget :labelWidth="80" label="账号:">
        <Input v-model="addFormData.account" name="new_username" placeholder="请输入账号" type="text" />
      </KWidget>
      <KWidget v-if="!isedit" :labelWidth="80" label="密码:">
        <Input v-model="addFormData.password" placeholder="请输入密码" type="password" />
      </KWidget>
      <KWidget :labelWidth="80" label="角色:">
        <Select v-model="addFormData.role_id" placeholder="请选择角色">
          <Option v-for="(item, key) in roles" :key="key" :value="item.id">{{ item.name }}</Option>
        </Select>
      </KWidget>
      <KWidget label="小程序角色:" :labelWidth="80">
        <Select v-model="addFormData.weapp_role" clearable placeholder="请选择小程序角色">
          <Option v-for="(item, key) in weapp_roles" :value="item.id" :key="key">{{ item.desc }}</Option>
        </Select>
      </KWidget>
      <!--      <KWidget label="所属部门:" :labelWidth="80">-->
      <!--        <template #label>-->
      <!--          <div class="flex" style="align-items: center;">-->
      <!--            <Tooltip :max-width="360" placement="top-start">-->
      <!--              <template #content>-->
      <!--                <p>1. 员工归属哪个部门，那么在数据模块，采购模块，诊所列表就只可以看到哪个部门下的诊所的数据。</p>-->
      <!--                <p>2. 如果员工归属“所有部门”，那么此员工可以在当前的合伙人系统的任意页面看见所有诊所的数据。</p>-->
      <!--              </template>-->
      <!--              <Icon type="md-help-circle" :style="{ cursor: 'pointer', fontSize: '18px', margin: '6px 4px 0 0' }" />-->
      <!--            </Tooltip>-->
      <!--            <div>所属部门:</div>-->
      <!--          </div>-->
      <!--        </template>-->
      <!--        <Select v-model="addFormData.department_id" clearable placeholder="请选择所属部门">-->
      <!--          <Option v-for="item in department_list" :value="item.id" :key="item.id">{{ item.name }}</Option>-->
      <!--        </Select>-->
      <!--      </KWidget>-->
      <template slot="footer">
        <div>
          <Button type="default" @click="addModal = false">取消</Button>
          <Button type="primary" @click="onAdd">确定</Button>
        </div>
      </template>
    </Modal>

    <Modal
      v-model="psdModel"
      :loading="psdModalLoading"
      :mask-closable="false"
      :width="430"
      class="ks-add-modal"
      title="修改密码"
      @on-visible-change="psdVisibleChange"
    >
      <KWidget :labelWidth="70" label="姓名:">
        <div style="line-height: 30px; font-size: 16px">{{ psdInfo.name }}</div>
      </KWidget>
      <KWidget :labelWidth="70" label="新密码:">
        <Input v-model="psdInfo.new_password" type="password" />
      </KWidget>
      <KWidget :labelWidth="70" label="确认密码:">
        <Input v-model="psdInfo.confirm_password" type="password" />
      </KWidget>
      <template slot="footer">
        <div>
          <Button type="default" @click="closepsdModal">取消</Button>
          <Button type="primary" @click="changePassWord">确定</Button>
        </div>
      </template>
    </Modal>
  </div>
</template>

<script>
/* eslint-disable */
import S from '@/utils/util'; // Some commonly used tools
import io from '@/utils/request'; // Http request
/* eslint-disable */
import config from '@/config';

let init_query_form_data = {
  page: 1,
  pageSize: 20,
  name: '',
  department_id: '',
  r: ''
};

let init_add_form_data = {
  id: '',
  name: '',
  mobile: '',
  role_id: '',
  account: '',
  department_id: '',
  password: '',
  weapp_role: ''
};

const ACTIONS = act => {
  S.log(act, 'act');
  return { ENABLED: '启用', DISABLED: '停用' }[act];
};

export default {
  name: 'list',

  data() {
    return {
      queryFormData: { ...init_query_form_data },
      tableCols: [
        { title: '姓名', slot: 'name' },
        { title: '手机号', slot: 'mobile' },
        { title: '账号', slot: 'account' },
        { title: '角色', slot: 'role' },
        { title: '小程序端角色', slot: 'weAppRole' },
        // { title: '所属部门', slot: 'department_name' },
        { title: '状态', slot: 'status', width: 120 },
        { title: '操作', slot: 'operate', width: 150 }
      ],
      tableLoading: false,
      list: [],
      department_list: [],
      old_department_list: [],
      selectableDepartList: [],
      total: 0,
      statusDesc: {},
      roles: null,
      add_roles: {},
      edit_roles: [],
      member_roles: {}, // 用户角色的列表
      addModal: false,
      addModalTitle: '添加成员',
      addModalLoading: true,
      addFormData: { ...init_add_form_data },
      isedit: true,
      psdModel: false,
      psdModalLoading: true,
      psdInfo: {
        id: '',
        name: '',
        new_password: '',
        confirm_password: ''
      },
      weapp_roles: []
    };
  },

  created() {
    this.queryFormData = S.merge(this.queryFormData, this.$route.query);
    this.getMemberOptions();
    // this.getSelectableDepartList();
    this.submitQueryForm(true);
  },

  methods: {
    psdVisibleChange(visible) {
      console.log('-> visible', visible);
      if (!visible) {
        for (const psdInfoKey in this.psdInfo) {
          this.psdInfo[psdInfoKey] = '';
        }
      }
    },
    roleVisibleChange(visible) {
      if (!visible) this.roles = this.add_roles;
      console.log(this.roles);
    },
    onSearch: function () {
      this.queryFormData.page = 1;
      this.submitQueryForm();
    },
    onResetSearch: function () {
      this.queryFormData = { ...init_query_form_data };
      this.submitQueryForm();
    },

    onPageChange: function (page, pageSize) {
      console.log('page: ', page);
      this.queryFormData.page = page;
      this.submitQueryForm();
    },
    handleSizeChange(val) {
      console.log('val: ', val);
      this.queryFormData.pageSize = val;
      console.log('his.queryFormData.page: ', this.queryFormData.page);
      if (this.queryFormData.page != 1) {
        this.queryFormData.page = 1;
        // this.submitQueryForm();
        return
      }
      console.log('2222');
      this.submitQueryForm();
    },
    showChangeModel(id) {
      io.get('pms_opc/member.info', { data: { id } }).then(res => {
        this.psdInfo.id = res.member.id;
        this.psdInfo.name = res.member.name;
        this.psdModel = true;
      });
    },
    changePassWord() {
      if (this.psdInfo.new_password.trim() == '' || this.psdInfo.confirm_password.trim() == '') {
        this.$Message.error('请填写完信息');
        this.psdModalLoading = false;
        return;
      }
      if (this.psdInfo.new_password != this.psdInfo.confirm_password) {
        this.$Message.error('两次密码需填写一致');
        this.psdModalLoading = false;
        return;
      }
      let query = {
        id: this.psdInfo.id,
        new_password: S.encrypt(
          JSON.stringify({
            password: this.psdInfo.new_password,
            expired_time: Date.parse(new Date()) / 1000
          })
        ),
        confirm_password: S.encrypt(
          JSON.stringify({
            password: this.psdInfo.confirm_password,
            expired_time: Date.parse(new Date()) / 1000
          })
        ),
        version: config.cryptoVersion
      };
      this.$api
        .getChgpass(query)
        .then(
          res => {
            this.psdModel = false;
            this.$Message.success('修改密码成功');
            this.psdInfo.new_password = '';
            this.psdInfo.confirm_password = '';
          },
          err => {
            this.$Message.error(err.errmsg);
            return;
          }
        )
        .catch(e => {});
    },
    closepsdModal() {
      this.psdModel = false;
      this.psdInfo.new_password = '';
      this.psdInfo.confirm_password = '';
    },
    getSelectableDepartList() {
      this.$api
        .selectableDepartList({
          source: 'member'
        })
        .then(res => {
          this.selectableDepartList = res?.list || [];
        });
    },
    onOpenAddModal: function (id) {
      if (!id) {
        this.addModalTitle = '添加员工';
        this.isedit = false;
        this.addFormData = { ...init_add_form_data };
        this.department_list = this.old_department_list;
      } else {
        this.addModalTitle = '编辑员工';
        this.isedit = true;
        io.get('pms_opc/member.info', { data: { id } })
          .then(data => {
            this.addFormData.id = data.member.id;
            this.edit_roles = data.all_roles;
            this.roles = data.all_roles;
            this.addFormData.name = data.member.name;
            this.addFormData.mobile = data.member.mobile;
            this.addFormData.role_id = data.member.role_id;
            this.addFormData.department_id = data.member.department_id;
            this.addFormData.account = data.member.account;
            this.addFormData.password = '';
            this.addFormData.weapp_role = data.member.weapp_role;
            this.department_list = data?.department_list || [];
          })
          .catch(error => {
            console.log('-> error', error);
            this.$Message.error(error.errmsg);
          });
      }
      this.addModal = true;
    },

    onAdd: function () {
      let formData = { ...this.addFormData };
      if (!formData.name.trim()) {
        this.$Message.error('请填写员工姓名');
        this.addModalLoading = false;
        return;
      }
      let telreg = this.regRole(this.addFormData.mobile);
      if (!telreg) {
        this.addModalLoading = false;
        this.$Message.error('请输入正确的号码');
        return;
      }
      if (!this.isedit) {
        if (!formData.account.trim()) {
          this.$Message.error('请填写员工账号');
          this.addModalLoading = false;
          return;
        }
        if (!formData.password) {
          this.$Message.error('请填写密码');
          this.addModalLoading = false;
          return;
        }
      }

      if (!formData.role_id) {
        this.$Message.error('请选择角色');
        this.addModalLoading = false;
        return;
      }
      // if (!formData.department_id) {
      //   this.$Message.error('请选择所属部门');
      //   this.addModalLoading = false;
      //   return;
      // }
      let params = {
        ...formData,
        password: S.encrypt(
          JSON.stringify({
            password: this.addFormData.password,
            expired_time: Date.parse(new Date()) / 1000
          })
        ),
        version: config.cryptoVersion
      };
      if (this.isedit) {
        // 如果是编辑，password不要传
        this.$delete(params, 'password');
      }
      console.log(params);
      io.post('pms_opc/member.save', params)
        .then(
          () => {
            this.$Message.success('保存成功');
            this.addModalLoading = false;
            this.addModal = false;
            this.submitQueryForm(true);
          },
          err => {
            this.$Message.error(err.errmsg);
          }
        )
        .catch(error => {
          this.addModalLoading = false;
          this.$Message.error(error.errmsg);
        });
    },

    onStatus: function (id, act) {
      io.post('pms_opc/member.status', {
        id: id,
        act: act
      })
        .then(() => {
          this.$Message.success(ACTIONS(act) + '成功');
          this.submitQueryForm();
        })
        .catch(error => {
          this.$Message.error(error.errmsg);
        });
    },

    getsList: function () {
      this.tableLoading = true;
      io.get('pms_opc/member.list', { data: this.queryFormData })
        .then(data => {
          let list = this.handleList(data.members);
          this.list = list;
          this.total = data.total;
          this.statusDesc = data.statusDesc;
          this.member_roles = data.roles;
          this.roles = data.all_roles;
          this.department_list = data?.department_list || [];
          this.old_department_list = data?.department_list || [];
          this.add_roles = data.all_roles;
          this.tableLoading = false;
        })
        .catch(error => {
          this.$Message.error(error.errmsg);
        });
    },

    handleList: function (list) {
      return list;
    },

    submitQueryForm: function (replace) {
      // 通过修改url参数，触发路由前置守卫(beforeRouteUpdate)，在前置守卫中获取列表数据
      this.queryFormData.r = S.random(6); // 只有在参数发生变化时才会触发前置守卫；所以添加随机数，保证url参数一定有修改
      if (replace) {
        this.$router.replace({ query: this.queryFormData });
      } else {
        this.$router.push({ query: this.queryFormData });
      }
    },
    regRole(tel) {
      let flag;
      let reg = /^1[3456789]\d{9}$/;
      flag = reg.test(tel);
      return flag;
    },

    getMemberOptions() {
      this.$api
        .getMemberOptions()
        .then(res => {
          this.weapp_roles = S.descToArrHandle(res.weappRoleDesc);
        })
        .catch(err => this.$Message.error(err.errmsg));
    }
  },

  beforeRouteUpdate: function (to, from, next) {
    this.queryFormData = S.merge(init_query_form_data, to.query);
    this.getsList();
    next();
  }
};
</script>

<style lang="less"></style>
