<template>
  <div>
    <Tabs type="card" v-model="activeComponentName" @on-click="tabClick">
      <TabPane
        :label="item.tab_name"
        :name="item.componentName"
        v-for="(item, index) in tab_list"
        :key="index"
      ></TabPane>
    </Tabs>

    <component :is="activeComponentName" :ref="activeComponentName"></component>
  </div>
</template>

<script>
export default {
  name: 'list',
  components: {
    setting: () => import('./setting/list.vue')
  },
  mixins: [],
  data() {
    return {
      tab_list: [
        { tab_name: '业绩提成设置', componentName: 'setting' }
        // { tab_name: '理疗师管理', componentName: 'physical' },
        // { tab_name: '排班管理', componentName: 'management' },
        // { tab_name: '班次管理', componentName: 'classes' },
      ],
      activeComponentName: 'setting'
    };
  },
  computed: {},
  watch: {},
  created() {
    this.init();
  },
  mounted() {},
  methods: {
    init() {
      if (this.$route.query.componentName) {
        this.activeComponentName = this.$route.query.componentName;
      } else {
        this.$route.query.componentName = this.tab_list[0].componentName;
      }
    },
    tabClick(val) {
      if (this.$route.query.componentName !== val) {
        this.activeComponentName = val;
        this.$route.query.componentName = val;
      } else {
        this.$refs[this.activeComponentName]?.loadList();
      }
    }
  },

  beforeRouteUpdate: function (to, from, next) {
    if (to.query.componentName) {
      this.activeComponentName = to.query.componentName;
    }
    next();
  }
};
</script>

<style lang="less" scoped></style>
