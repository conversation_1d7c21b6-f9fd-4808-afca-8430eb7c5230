<template>
  <div>
    <Form
      :label-width="80"
      label-colon
      label-position="right"
      :model="formData"
      :rules="formRules"
      ref="form"
      :disabled="$route.query.id && $route.query.action === 'detail'"
    >
      <div>
        <div class="block-header">
          <h2 class="mb10">{{ typeInfo.title }}</h2>
          <div>{{ typeInfo.tip }}</div>
        </div>
        <p class="block-header">基本信息</p>
        <FormItem label="方案名称" prop="name">
          <Input placeholder="请输入方案名称" v-model="formData.name" maxlength="10" show-word-limit />
        </FormItem>

        <p class="block-header">业绩规则</p>
        <div class="sub-title-box" v-if="$route.query.type === '1'">
          <div class="sub-title">所有服务</div>
          <div class="sub-tip">默认针对所有商品生效</div>
        </div>
        <div class="sub-title-box" v-if="$route.query.type === '2' || $route.query.type === '3'">
          <div class="sub-title">所有商品</div>
          <div class="sub-tip">默认针对所有商品生效</div>
        </div>
        <FormItem label="服务">
          <RadioGroup v-model="formData.calc_type">
            <Radio v-for="item in calc_type_desc" :label="item.id" :key="item.id">
              {{ item.desc }}
            </Radio>
          </RadioGroup>
        </FormItem>

        <!--		服务消耗业绩		-->
        <div class="sub-title-box" v-if="$route.query.type === '1'">
          <div class="sub-title">员工等级</div>
          <div class="sub-tip">按照理疗师的等级去配置不同的业绩提成比例</div>
        </div>
        <FormItem label="员工等级" v-if="$route.query.type === '1'">
          <div class="level-table">
            <table>
              <thead>
                <tr>
                  <th>员工等级</th>
                  <th>参与计算业绩比例</th>
                </tr>
              </thead>
              <tbody>
                <tr v-for="item in level_desc" :key="item.id">
                  <td>{{ item.desc }}</td>
                  <td>
                    <div class="flex flex-item-center">
                      <InputNumber v-model="item.scale" :min="0" :max="30" :precision="0"></InputNumber>
                      <div class="unit">%</div>
                    </div>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </FormItem>

        <!--		商品销售业绩		-->
        <p class="block-header" v-if="$route.query.type === '2' || $route.query.type === '3'">计算方式</p>
        <FormItem :label-width="120" v-if="$route.query.type === '2' || $route.query.type === '3'">
          <template #label>
            <div class="flex flex-item-align">
              <div>多人按比例分配</div>
              <Tooltip max-width="300" theme="light" placement="bottom" class="custom-tooltip tooltip-left-arrow">
                <div slot="content">
                  <h4>销售人员分账比例</h4>
                  <div class="mb10">最多分账5%</div>
                  <h4>多人分享规则</h4>
                  <div class="mb10">针对多人的场景，可配置不同的计算规则</div>
                  <h4>多人按比例分配</h4>
                  <div>
                    可配置多人不同的业绩比例，最多支持5人。例如：可配置共2人的方案，第1人分享比例为80%，第2人分享比例为20%。开单时选择的第1人获得80%的业绩，第2人获得20%的业绩。
                  </div>
                </div>
                <p class="flex flex-item-center cursor">
                  <svg-icon
                    iconClass="tip"
                    class="helpIcon cursor"
                    style="width: 14px; height: 14px; margin-left: 3px"
                  ></svg-icon>
                </p>
              </Tooltip>
              :
            </div>
          </template>
          <Table :columns="tableCols" :data="multipleList">
            <template v-slot:people_number="{ row, index }">
              <div>共{{ index + 1 }}人</div>
            </template>
            <template v-slot:first="{ row, index }">
              <div class="flex flex-item-align">
                <InputNumber
                  v-model="row.first_scale"
                  style="flex: 1"
                  :min="0"
                  :max="100"
                  :precision="0"
                  :disabled="index === 0"
                  @on-change="val => (multipleList[index].first_scale = val)"
                />
                <div class="unit-box">%</div>
              </div>
            </template>
            <template v-slot:second="{ row, index }">
              <div class="flex flex-item-align" v-if="index > 0">
                <InputNumber
                  v-model="row.second_scale"
                  style="flex: 1"
                  :min="0"
                  :max="100"
                  :precision="0"
                  @on-change="val => (multipleList[index].second_scale = val)"
                />
                <div class="unit-box">%</div>
              </div>
            </template>
            <template v-slot:third="{ row, index }">
              <div class="flex flex-item-align" v-if="index > 1">
                <InputNumber
                  v-model="row.third_scale"
                  style="flex: 1"
                  :min="0"
                  :max="100"
                  :precision="0"
                  @on-change="val => (multipleList[index].third_scale = val)"
                />
                <div class="unit-box">%</div>
              </div>
            </template>
            <template v-slot:forth="{ row, index }">
              <div class="flex flex-item-align" v-if="index > 2">
                <InputNumber
                  v-model="row.forth_scale"
                  style="flex: 1"
                  :min="0"
                  :max="100"
                  :precision="0"
                  @on-change="val => (multipleList[index].forth_scale = val)"
                />
                <div class="unit-box">%</div>
              </div>
            </template>
            <template v-slot:fifth="{ row, index }">
              <div class="flex flex-item-align" v-if="index > 3">
                <InputNumber
                  v-model="row.fifth_scale"
                  style="flex: 1"
                  :min="0"
                  :max="100"
                  :precision="0"
                  @on-change="val => (multipleList[index].fifth_scale = val)"
                />
                <div class="unit-box">%</div>
              </div>
            </template>
            <template v-slot:action="{ row, index }">
              <a
                v-if="index > 0 && index + 1 === multipleList.length && $route.query.action !== 'detail'"
                @click="deleteNumber"
                >删除</a
              >
            </template>
          </Table>
          <div style="text-align: center" v-if="multipleList.length < 5 && $route.query.action !== 'detail'">
            <a @click="addMoreNumber">新增更多</a>
          </div>
        </FormItem>
      </div>
    </Form>
    <div class="block_20"></div>
    <div class="block_20"></div>
    <div class="fixed-bottom-wrapper">
      <back-button class="mr10"></back-button>
      <!--      <Button type="primary" v-if="$route.query.action !== 'detail'" class="mr10" @click="savePlan('')">保存</Button>-->
      <!--      <Button type="primary" v-if="$route.query.action !== 'detail'" class="mr10" @click="savePlan('1')"-->
      <!--        >保存并启用</Button-->
      <!--      >-->
    </div>
  </div>
</template>

<script>
import S from '@/utils/util';

const init_form_data = {
  name: '',
  type: '',
  rules: {},
  calc_type: '2',
  status: ''
};
export default {
  name: 'edit',

  components: {},
  created() {
    this.formData.type = this.$route.query.type;
    this.getSharePlanOptions();
  },
  mounted() {},
  props: {},
  data() {
    return {
      formData: { ...init_form_data },
      formRules: {
        name: [{ required: true, message: '请输入方案名称', trigger: 'change' }]
      },
      calc_type_desc: [],
      level_desc: [],
      typeTitleList: [
        {
          type: '1',
          title: '服务消耗业绩',
          tip: '现金消耗业绩是指销售时顾客使用现金、微信、支付宝、POS刷卡、自定义记账方式等支付的业绩。'
        },
        {
          type: '2',
          title: '商品销售业绩',
          tip: '现金劳动业绩是指销售时顾客使用现金、微信、支付宝、POS刷卡、自定义记账方式等支付的业绩。'
        },
        {
          type: '3',
          title: '商品销售+服务消耗业绩',
          tip: '现金劳动业绩是指销售时顾客使用现金、微信、支付宝、POS刷卡、自定义记账方式等支付的业绩。'
        }
      ],
      tableCols: [
        { title: '人数', slot: 'people_number', align: 'center' },
        { title: '第1人', slot: 'first', align: 'center' },
        { title: '第2人', slot: 'second', align: 'center' },
        { title: '第3人', slot: 'third', align: 'center' },
        { title: '第4人', slot: 'forth', align: 'center' },
        { title: '第5人', slot: 'fifth', align: 'center' },
        { title: '操作', slot: 'action', align: 'center' }
      ],
      multipleList: [{ first_scale: 100, second_scale: 0, third_scale: 0, forth_scale: 0, fifth_scale: 0 }]
    };
  },
  computed: {
    typeInfo() {
      return this.typeTitleList.find(item => item.type === this.$route.query.type);
    }
  },
  watch: {},

  methods: {
    getSharePlanOptions() {
      let params = {};
      console.log('=>(detail.vue:241) params', params);
      this.$api
        .getSharePlanOptions(params)
        .then(res => {
          console.log('=>(detail.vue:242) res', res);
          this.calc_type_desc = S.descToArrHandle(res.calc_type_desc);
          let level_desc = S.descToArrHandle(res.level_desc);
          if (!this.$route.query.id) {
            level_desc.forEach(item => (item.scale = 0));
          } else {
            level_desc.forEach(item => (item.scale = Number(item.scale)));
          }
          this.level_desc = level_desc;
          if (this.$route.query.id) {
            this.getSharePlanDetail();
          }
        })
        .catch(err => this.$Message.error(err.errmsg));
    },
    savePlan(status) {
      this.$refs.form.validate(valid => {
        if (valid) {
          if (this.$route.query.type === '1') {
            let emptyIndex = this.level_desc.findIndex(item => !item.scale);
            console.log('=>(edit.vue:113) emptyIndex', emptyIndex);
            if (emptyIndex > -1) {
              this.$Message.error(`请输入员工等级${this.level_desc[emptyIndex].desc}的业绩比例`);
              return;
            }
          }

          if (this.$route.query.type === '2' || this.$route.query.type === '3') {
            let errorIndex = this.multipleList.findIndex((item, index) => {
              let total = 0;
              for (let key in item) {
                total = total += item[key];
              }
              if (total !== 100) {
                return index;
              }
            });

            console.log('=>(edit.vue:272) errorIndex', errorIndex);
            if (errorIndex > -1) {
              this.$Message.error(`多人按比例分配中${errorIndex + 1}人比例分配异常`);
              return;
            }
          }

          this.$route.query.id ? this.editSharePlanStore(status) : this.createSharePlanStore(status);
        }
      });
    },

    createSharePlanStore(status) {
      let params = {
        ...this.formData,
        rules: this.handleRules(), // 后端需求的格式较特殊
        status
      };
      console.log('=>(detail.vue:241) params', params);
      this.$api
        .createSharePlanStore(params)
        .then(res => {
          this.$Message.success('创建成功');
          this.$router.back();
        })
        .catch(err => this.$Message.error(err.errmsg));
    },
    editSharePlanStore(status) {
      let params = {
        ...this.formData,
        id: this.$route.query.id,
        rules: this.handleRules(), // 后端需求的格式较特殊
        status
      };
      console.log('=>(detail.vue:241) params', params);
      this.$api
        .editSharePlanStore(params)
        .then(res => {
          this.$Message.success('编辑成功');
          this.$router.back();
        })
        .catch(err => this.$Message.error(err.errmsg));
    },
    handleRules() {
      let rules = {};
      if (this.$route.query.type === '1') {
        rules = {
          physio: this.level_desc.reduce((acc, curr) => {
            acc[curr.id] = curr.scale;
            return acc;
          }, {})
        };
      } else if (this.$route.query.type === '2' || this.$route.query.type === '3') {
        rules = {
          sales: this.multipleList.reduce((acc, curr, index) => {
            // 根据 index 计算需要保留的比例数量
            const scales = [];
            for (let i = 0; i <= index; i++) {
              const scaleValue =
                curr[
                  `${i === 0 ? 'first' : i === 1 ? 'second' : i === 2 ? 'third' : i === 3 ? 'forth' : 'fifth'}_scale`
                ];
              scales.push(scaleValue);
            }
            // 将结果添加到输出对象中
            acc[index + 1] = scales;
            return acc;
          }, {})
        };
      }

      return rules;
    },

    addMoreNumber() {
      // let length = this.multipleList.length;
      let addInfo = {
        first_scale: 0,
        second_scale: 0,
        third_scale: 0,
        forth_scale: 0,
        fifth_scale: 0
      };
      this.multipleList.push(addInfo);
    },
    deleteNumber() {
      this.multipleList.pop();
    },
    getSharePlanDetail() {
      let params = {
        id: this.$route.query.id
      };
      console.log('=>(detail.vue:241) params', params);
      this.$api
        .getSharePlanDetail(params)
        .then(res => {
          console.log('=>(detail.vue:242) res', res);
          this.formData.name = res.name;
          this.formData.type = res.type;
          this.formData.calc_type = res.calc_type;
          if (res.type === '1') {
            this.level_desc = this.level_desc.map(item => {
              const newItem = { ...item, scale: Number(res.plans?.physio[item.id]) };
              if (res.plans?.physio[item.id]) {
                newItem.scale = Number(res.plans?.physio[item.id]); // 将scale赋值为plans中的对应值
              }
              return newItem;
            });
          } else if (res.type === '2' || res.type === '3') {
            this.multipleList = Object.entries(res.plans.sales).map(([key, value]) => {
              return {
                first_scale: Number(value[0]) || 0,
                second_scale: Number(value[1]) || 0,
                third_scale: Number(value[2]) || 0,
                forth_scale: Number(value[3]) || 0,
                fifth_scale: Number(value[4]) || 0
              };
            });
          }
        })
        .catch(err => this.$Message.error(err.errmsg));
    }
  }
};
</script>
<style scoped lang="less">
::v-deep .ivu-form-item {
  width: 800px;
}
.sub-title-box {
  display: flex;
  padding: 0 10px 10px;
  .sub-title {
    font-size: 14px;
    margin-right: 10px;
    font-weight: 700;
  }
  .sub-tip {
    font-size: 12px;
    color: #ccc;
  }
}
.level-table {
  table {
    width: 100%;
    border-collapse: collapse;
  }
  th,
  td {
    border: 1px solid #dddddd;
    text-align: center;
    padding: 8px;
  }
  th {
    background-color: #f2f2f2;
  }
}
.unit {
  width: 30px;
  height: 32px;
  background-color: #f2f2f2;
  border: 1px solid #dcdee2;
  border-left: 0;
  box-sizing: border-box;
}
.multiple-table {
  .multiple-header {
    display: flex;
    background-color: #e0e0e0;
    .header-item {
      flex: 1;
      text-align: center;
    }
  }
  .multiple-item {
    display: flex;
  }
}
.unit-box {
  width: 30px;
  height: 32px;
  background-color: #efefef;
  text-align: center;
  border: 1px solid #dcdcdc;
  border-left: 0;
  box-sizing: border-box;
  border-radius: 2px;
}
</style>
