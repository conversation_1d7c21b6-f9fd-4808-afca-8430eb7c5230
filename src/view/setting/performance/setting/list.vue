<template>
  <div>
    <!--    <Form inline :label-width="0" @submit.native.prevent @keyup.enter.native="onSearch">-->
    <!--      <Row>-->
    <!--        <FormItem style="text-align: left">-->
    <!--          <Button type="primary" @click="createPlan">新增方案</Button>-->
    <!--        </FormItem>-->
    <!--      </Row>-->
    <!--    </Form>-->

    <div>
      <Table :loading="tableLoading" :columns="tableCols" :data="list" :height="$store.state.app.clientHeight - 260">
        <template slot-scope="{ row }" slot="action">
          <!--          <Poptip-->
          <!--            confirm-->
          <!--            :title="`确定${row.status === '1' ? '停用' : '启用'}?`"-->
          <!--            transfer-->
          <!--            @on-ok="setSharePlanStatus(row)"-->
          <!--            class="mr10"-->
          <!--          >-->
          <!--            <a :style="{ color: row.status === '1' ? 'red' : '' }">{{ row.status === '1' ? '停用' : '启用' }}</a>-->
          <!--          </Poptip>-->
          <!--          <a class="mr10" @click="toDetail(row, 'edit')">编辑</a>-->
          <a @click="toDetail(row, 'detail')">详情</a>
        </template>
      </Table>
    </div>

    <div class="block_20"></div>
    <KPage
      :current="+queryFormData.page"
      :page-size="+queryFormData.pageSize"
      :total="+total"
      style="text-align: center"
      @on-change="handleCurrentChange"
      @on-page-size-change="handleSizeChange"
    />
    <select-plan-type :visible.sync="createVisible"></select-plan-type>
  </div>
</template>

<script>
import search from '@/mixins/search';
import SelectPlanType from './components/SelectPlanType';
import S from '@/utils/util';

const init_query_form_data = {
  page: 1,
  pageSize: 20
};
export default {
  name: 'Promotion-list',
  components: { SelectPlanType },
  mixins: [search],
  props: {},
  data() {
    return {
      apiName: 'getSharePlanList',
      queryFormData: {
        ...init_query_form_data
      },
      tableCols: [
        { title: '方案名称', key: 'name', align: 'center' },
        { title: '方案类型', key: 'type_desc', align: 'center' },
        { title: '创建人', key: 'operator', align: 'center' },
        { title: '创建时间', key: 'created_at', align: 'center' },
        { title: '状态', key: 'status_desc', align: 'center' },
        { title: '操作', slot: 'action', align: 'center' }
      ],
      createVisible: false
    };
  },
  computed: {},
  watch: {},
  created() {
    this.queryFormData = S.merge(this.queryFormData, this.$route.query);
    this.submitQueryForm(true);
  },
  mounted() {},
  methods: {
    // 创建活动
    createPlan() {
      this.createVisible = true;
    },

    // 重置
    onResetSearch() {
      this.queryFormData = { ...init_query_form_data };
      this.submitQueryForm();
    },
    onSearch() {
      this.queryFormData.page = 1;
      this.pageSize = 10;
      this.submitQueryForm();
    },

    // 详情/编辑
    toDetail(row, action) {
      this.$router.push({
        path: '/setting/performance/setting/edit',
        query: {
          id: row.id,
          type: row.type,
          action
        }
      });
    },
    setSharePlanStatus(row) {
      let params = {
        id: row.id,
        status: row.status === '1' ? '2' : '1'
      };
      console.log('=>(detail.vue:241) params', params);
      this.$api
        .setSharePlanStatus(params)
        .then(res => {
          this.$Message.success(`${row.status === '1' ? '停用' : '启用'}成功`);
          this.submitQueryForm();
        })
        .catch(err => this.$Message.error(err.errmsg));
    },
    submitQueryForm(replace) {
      this.queryFormData.r = S.random(6); // 只有在参数发生变化时才会触发前置守卫；所以添加随机数，保证url参数一定有修改
      let searchObj = {};
      for (const searchKey in this.queryFormData) {
        if (this.queryFormData[searchKey] || this.queryFormData[searchKey] === 0) {
          searchObj[searchKey] = this.queryFormData[searchKey];
        }
      }
      if (replace) {
        this.$router.replace({ query: searchObj });
      } else {
        this.$router.push({ query: searchObj });
      }
      this.loadList();
    }
  },
  filters: {}
};
</script>

<style lang="less" scoped>
.ml8 {
  margin-left: 8px;
}
</style>
