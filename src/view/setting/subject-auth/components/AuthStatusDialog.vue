<template>
  <Modal :value="value" :title="title" width="880px" :mask-closable="false" :closable="false">
    <div class="modal-content">
      <div class="img-wrap">
        <img src="@/assets/image/auth/auth-success.png" alt="status_img" v-show="isSuccess" />
        <img src="@/assets/image/auth/auth-fail.png" alt="status_img" v-show="!isSuccess" />
      </div>
      <div class="success-box" v-show="isSuccess">
        <div class="status-title">基础信息提交成功</div>
        <div class="status-tips">
          请等待审核，还需进行后续操作，初审通过后会发送打款信息验证信息，请在收到打款验证信息后24小时内确认打款验证。
        </div>
      </div>
      <div class="error-box" v-show="!isSuccess">
        <div class="status-title">基础信息{{ isFromList ? '审核' : '创建' }}失败</div>
        <div class="status-tip">请核对并修改以下信息后重新提交</div>
        <div class="error-content">
          <div class="error-label mb-8">您提交的内容有如下错误：</div>
          <div class="error-text">{{ reason }}</div>
        </div>
      </div>
    </div>

    <div slot="footer" class="edit-footer">
      <Button type="default" @click="closeModal">返回</Button>
      <Button type="primary" @click="reSubmit" v-if="!isSuccess">重新修改</Button>
    </div>
  </Modal>
</template>

<script>
export default {
  name: 'editOrder',
  components: {},
  mixins: [],
  props: {
    value: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      default: '基础资料审核'
    },
    auditStatus: {
      type: String,
      default: 'failed'
    },
    reason: {
      type: String,
      default: ''
    },
    isFromList: {
      type: Boolean,
      default: false
    },
    submitSuccess: {
      type: Boolean,
      default: false
    },
    out_request_no: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      companyList: []
    };
  },
  computed: {
    // getImgSrc() {
    //   return this.isSuccess
    //     ? require('@/assets/image/auth/auth-success.png')
    //     : require('@/assets/image/auth/auth-fail.png');
    // },
    isSuccess() {
      return this.auditStatus === 'succeed' || this.submitSuccess;
    }
  },
  watch: {},
  created() {},
  mounted() {},
  methods: {
    closeModal() {
      this.submitSuccess = false;
      this.$emit('on-close', false);
    },
    reSubmit() {
      this.$emit('input', false);
      if (this.isFromList) {
        const query = {
          step: 'subject-auth',
          status: 'failed'
        };
        if (this.out_request_no) {
          query.out_request_no = this.out_request_no;
          query.isReAuth = true;
        }
        this.$router.push({
          path: '/setting/subject-auth/detail',
          query
        });
      }
    }
  },
  filters: {}
};
</script>
<style lang="less" scoped>
.modal-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  height: 420px;
  box-sizing: border-box;

  .img-wrap {
    margin: 12px 0 16px;

    > img {
      display: block;
      width: 134px;
      height: 134px;
    }
  }

  .success-box {
    width: 470px;

    .status-tips {
      text-align: center;
      font-size: 12px;
      color: #999999;
      line-height: 18px;
      margin-top: 8px;
    }
  }

  .status-title {
    font-size: 18px;
    font-weight: 500;
    color: #333333;
    line-height: 22px;
    text-align: center;
  }

  .error-box {
    .status-tip {
      color: #999999;
      line-height: 16px;
      margin: 8px 0 16px;
      text-align: center;
    }

    .error-content {
      background: #f5f5f5;
      border-radius: 4px;
      padding: 16px;
      width: 470px;
      color: #333333;
      line-height: 16px;
    }
  }
}
</style>
