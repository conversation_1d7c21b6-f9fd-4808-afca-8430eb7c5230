<template>
  <Modal :value="value" :title="title" width="880px" :mask-closable="false" :closable="false">
    <div :class="['modal-content', isSuccess ? 'flex flex-item-center' : '']">
      <div class="img-wrap">
        <img :src="getImgSrc | imageStyle('B.w300')" alt="status_img" />
      </div>
      <div class="success-box mt-28" v-if="isSuccess">
        <div class="status-title">打款验证成功</div>
      </div>
      <div class="error-box" v-else>
        <div class="status-title" style="max-width: 450px">{{ reason }}</div>
        <!-- <div class="error-text" v-if="remmitStatus !== 'error'">剩余{{ frequency }}次打款次数。</div> -->
      </div>
      <!-- <div v-else class="error-box">
        <div class="status-title">金额连续输错3次，汇款验证失败</div>
        <div class="error-text">请在24小时后重新提交进件信息。</div>
      </div> -->
    </div>
    <div slot="footer" class="edit-footer">
      <Button type="default" @click="closeModal">返回</Button>
    </div>
  </Modal>
</template>

<script>
export default {
  name: 'RemitStatusDialog',
  components: {},
  mixins: [],
  props: {
    value: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      default: ''
    },
    reason: {
      type: String,
      default: ''
    },
    isFromList: {
      type: Boolean,
      default: false
    },
    isSuccess: {
      type: Boolean,
      default: false
    },
    frequency: {
      type: Number,
      default: 0
    },
    remmitStatus: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      companyList: []
    };
  },
  computed: {
    getImgSrc() {
      return this.isSuccess
        ? require('@/assets/image/auth/remitt-success.png')
        : require('@/assets/image/auth/remitt-fail.png');
    }
  },
  watch: {},
  created() {},
  mounted() {},
  methods: {
    closeModal() {
      this.$emit('input', false);
      this.$router.back();
    }
  },
  filters: {}
};
</script>
<style lang="less" scoped>
.modal-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  height: 420px;
  box-sizing: border-box;
  padding-top: 60px;
  .img-wrap {
    margin: 12px 0 16px;

    > img {
      display: block;
      width: 134px;
      height: 134px;
    }
  }

  .success-box {
    width: 470px;
  }

  .status-title {
    font-size: 18px;
    font-weight: 500;
    color: #333333;
    line-height: 22px;
    text-align: center;
  }

  .error-box {
    .error-text {
      width: 470px;
      line-height: 16px;
      text-align: center;
      color: #e83b3b;
      margin-top: 8px;
    }
  }
}
</style>
