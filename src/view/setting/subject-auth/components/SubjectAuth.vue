<template>
  <div class="subject-form">
    <Form
      ref="subjectForm"
      label-colon
      :model="subjectFormData"
      :disabled="isFormDisabled"
      :rules="subjectRules"
      :label-width="140"
    >
      <!--      <FormItem label="商户简称" prop="short_name">-->
      <!--        <Input-->
      <!--          v-model="subjectFormData.short_name"-->
      <!--          placeholder="请输入商户简称"-->
      <!--          :maxlength="10"-->
      <!--          show-word-limit-->
      <!--        ></Input>-->
      <!--      </FormItem>-->
      <FormItem label="主体类型" required prop="organization_type">
        <Select
          v-model="subjectFormData.organization_type"
          @on-change="changeOrganization"
          placeholder="请选择主体类型"
        >
          <Option
            :value="item.key"
            :label="item.val.desc"
            v-for="item in organizationTypes"
            :key="'license' + item.key"
          />
        </Select>
      </FormItem>

      <div class="form-title">营业执照信息</div>

      <FormItem label="统一社会信用代码" prop="business_license.number">
        <Input
          v-model="subjectFormData.business_license.number"
          placeholder="请输入统一社会信用代码"
          :maxlength="18"
          show-word-limit
          @input.native="limitBusinessLicenseNumber"
        ></Input>
      </FormItem>
      <FormItem label="公司名称" prop="business_license.company_name">
        <Input
          v-model="subjectFormData.business_license.company_name"
          placeholder="请输入公司名称"
          :maxlength="32"
          show-word-limit
        ></Input>
      </FormItem>
      <FormItem label="公司地址" prop="business_license.company_address">
        <Input
          v-model="subjectFormData.business_license.company_address"
          placeholder="请输入公司地址(必须和营业执照地址保持一致)"
        />
      </FormItem>

      <FormItem label="营业执照有效期" prop="" required>
        <div class="flex">
          <Select
            v-model="business_license_type"
            style="width: 120px; margin-right: 20px"
            @on-change="v => changeLicenseType(v, 'business_license')"
          >
            <Option
              :value="item.value"
              :label="item.label"
              v-for="item in validateTypes"
              :key="'license' + item.value"
            />
          </Select>
          <FormItem prop="business_license.valid_time_st">
            <DatePicker
              type="date"
              :value="subjectFormData.business_license.valid_time_st"
              @on-change="subjectFormData.business_license.valid_time_st = arguments[0]"
              format="yyyy-MM-dd"
              placeholder="请选择有效期开始日期"
              :options="{
                disabledDate: time => {
                  return time.getTime() > new Date(subjectFormData.business_license.valid_time_et).getTime();
                }
              }"
            ></DatePicker>
          </FormItem>
          <span class="ml-12 mr-12">至</span>
          <FormItem prop="business_license.valid_time_et">
            <DatePicker
              v-if="business_license_type !== 'long'"
              type="date"
              placeholder="请选择有效期截止日期"
              format="yyyy-MM-dd"
              @on-change="subjectFormData.business_license.valid_time_et = arguments[0]"
              :value="subjectFormData.business_license.valid_time_et"
              :options="{
                disabledDate: time => {
                  return (
                    time.getTime() <
                    new Date(subjectFormData.business_license.valid_time_st).getTime() + 60 * 60 * 60 * 24 * 1000
                  );
                }
              }"
            ></DatePicker>

            <Input value="长期有效" readonly v-else />
          </FormItem>
        </div>
      </FormItem>
      <FormItem label="营业执照图像" prop="business_license.copy_url">
        <Picture
          v-model="subjectFormData.business_license.copy_url"
          @on-success="v => uploadLicenseSuccess(v, 'business_license')"
          @on-remove="v => removeLicenseSuccess(v, 'business_license')"
          :limit="1"
          :isDraggable="false"
          :is-query-detail="isFormDisabled"
        />
        <div class="upload-note">
          仅支持在有效期内的中国大陆工商局监督管理局颁发的工商营业执照；必须是原件照片、扫描件或者复印件加盖企业公章后的扫描件，支持JPG、JPEG、PNG格式，图片小于3M。
        </div>
      </FormItem>

      <div class="form-title">结算账户信息</div>

      <FormItem label="账户类型" required>
        <Select v-model="subjectFormData.settle_acct.type" placeholder="请选择账户类型">
          <Option
            :value="item.key"
            :label="item.val.desc"
            v-for="item in settleTypes"
            :disabled="subjectFormData.organization_type === '1'"
            :key="'license' + item.key"
          />
        </Select>
      </FormItem>
      <FormItem label="开户银行网点" prop="settle_acct.bank_branch_code">
        <Select
          v-model="subjectFormData.settle_acct.bank_branch_code"
          filterable
          placeholder="请输入、搜索开户银行网点"
          not-found-text="无匹配数据"
          label-in-value
          :remote-method="searchBranch"
          :loading="searchLoading"
          @on-select="selectBank"
          @on-clear="clearBank"
          clearable
        >
          <Option
            v-for="(option, index) in bankList"
            :value="option.bank_code"
            :key="index"
            :tag="option.drec_code"
            :label="option.bank_name"
          ></Option>
        </Select>
        <div class="upload-note">若搜索匹配不到对应的开户行网点，请联系省公司或总部进行协调。</div>
      </FormItem>
      <FormItem label="开户(企业)名称" prop="settle_acct.name">
        <Input v-model="subjectFormData.settle_acct.name" placeholder="请输入开户(企业)名称"></Input>
      </FormItem>
      <FormItem label="银行卡号" prop="settle_acct.bank_acct_no">
        <Input v-model="subjectFormData.settle_acct.bank_acct_no" placeholder="请输入银行卡号"></Input>
      </FormItem>

      <div class="form-title">法人身份证信息</div>

      <FormItem label="身份证类型" required>
        <Select
          v-model="subjectFormData.legal_person_id_card.type"
          style="width: 120px; margin-right: 20px"
          placeholder="请选择身份证类型"
          disabled
        >
          <Option value="1" label="身份证"></Option>
        </Select>
      </FormItem>

      <FormItem label="身份证姓名" prop="legal_person_id_card.name">
        <Input v-model="subjectFormData.legal_person_id_card.name" placeholder="请输入身份证姓名"></Input>
      </FormItem>
      <FormItem label="身份证号码" prop="legal_person_id_card.number">
        <Input v-model="subjectFormData.legal_person_id_card.number" placeholder="请输入身份证号码"></Input>
      </FormItem>
      <FormItem label="身份证有效期" prop="" required>
        <div class="flex">
          <Select
            v-model="id_card_type"
            style="width: 120px; margin-right: 20px"
            @on-change="v => changeLicenseType(v, 'legal_person_id_card')"
            placeholder="请选择有效期类型"
          >
            <Option
              :value="item.value"
              v-for="item in validateTypes"
              :key="item.value + 'IdCard'"
              :label="item.label"
            ></Option>
          </Select>
          <FormItem prop="legal_person_id_card.valid_time_st">
            <DatePicker
              type="date"
              placeholder="请选择有效期开始时间"
              @on-change="subjectFormData.legal_person_id_card.valid_time_st = arguments[0]"
              :value="subjectFormData.legal_person_id_card.valid_time_st"
              format="yyyy-MM-dd"
              :options="{
                disabledDate: time => {
                  return (
                    time.getTime() > Date.now() ||
                    time.getTime() > new Date(subjectFormData.legal_person_id_card.valid_time_et).getTime()
                  );
                }
              }"
            ></DatePicker>
          </FormItem>
          <span class="ml-12 mr-12">至</span>
          <FormItem prop="legal_person_id_card.valid_time_et">
            <DatePicker
              type="date"
              v-if="id_card_type !== 'long'"
              placeholder="请选择有效期结束时间"
              :value="subjectFormData.legal_person_id_card.valid_time_et"
              format="yyyy-MM-dd"
              @on-change="subjectFormData.legal_person_id_card.valid_time_et = arguments[0]"
              :options="{
                disabledDate: time => {
                  return (
                    time.getTime() <
                    new Date(subjectFormData.legal_person_id_card.valid_time_st).getTime() + 60 * 60 * 60 * 24 * 1000
                  );
                }
              }"
            ></DatePicker>
            <Input value="长期有效" readonly v-else />
          </FormItem>
        </div>
      </FormItem>
      <FormItem label="身份证人像面照片" prop="legal_person_id_card.copy_url">
        <Picture
          v-model="subjectFormData.legal_person_id_card.copy_url"
          :limit="1"
          @on-success="v => uploadLicenseSuccess(v, 'legal_person_id_card', 'front')"
          @on-remove="v => removeLicenseSuccess(v, 'legal_person_id_card', 'front')"
          :isDraggable="false"
          :is-query-detail="isFormDisabled"
        >
        </Picture>
        <div class="upload-note">
          1.请上传彩色照片，要求正面拍摄，露出证件四角且清晰、完整，所有字符清晰可识别，不得反光或遮挡。不得翻拍、截图、镜像、PS。
          <br />
          2.图片只支持JPG、BMP、PNG格式，文件大小不能超过3M。
        </div>
      </FormItem>
      <FormItem prop="legal_person_id_card.national_url" label="身份证国徽面照片">
        <Picture
          v-model="subjectFormData.legal_person_id_card.national_url"
          :limit="1"
          @on-success="v => uploadLicenseSuccess(v, 'legal_person_id_card', 'back')"
          @on-remove="v => removeLicenseSuccess(v, 'legal_person_id_card', 'back')"
          :isDraggable="false"
          :is-query-detail="isFormDisabled"
        >
        </Picture>
        <div class="upload-note">
          1.请上传彩色照片，要求正面拍摄，露出证件四角且清晰、完整，所有字符清晰可识别，不得反光或遮挡。不得翻拍、截图、镜像、PS。
          <br />
          2.图片只支持JPG、BMP、PNG格式，文件大小不能超过3M。
        </div>
      </FormItem>
      <FormItem label="法人电话号码" prop="legal_person_mobile_number">
        <Input v-model="subjectFormData.legal_person_mobile_number" placeholder="请输入法人电话号码"></Input>
      </FormItem>
      <FormItem label="短信验证码" prop="sms_code" v-if="showSms">
        <div class="flex flex-item-align">
          <Input
            :maxlength="6"
            class="flex-1"
            v-model="subjectFormData.sms_code"
            placeholder="输入手机验证码"
            clearable
          />
          <vac ref="vac" :auto-start="false" :left-time="60000" class="ml10">
            <template slot="process" slot-scope="{ timeObj }">
              <Button type="primary" disabled>{{ timeObj.ceil.s }}s</Button>
            </template>
            <template slot="before">
              <Button type="primary" :loading="authLoading" @click="onCountDownStart">获取验证码</Button>
            </template>

            <template slot="finish">
              <Button type="primary" :loading="authLoading" @click="onCountDownStart">获取验证码</Button>
            </template>
          </vac>
        </div>
      </FormItem>
    </Form>
    <div class="fixed-bottom-wrapper">
      <back-button class="mr-12">返回</back-button>
      <Button type="primary" :loading="submitLoading" v-if="!isFormDisabled" @click="handleSubmit('subjectForm')">
        提交
      </Button>
    </div>
    <auth-status-dialog
      v-model="statusDialogVisible"
      @on-close="closeDialog"
      :submitSuccess="submitSuccess"
      :reason="failedReason"
    ></auth-status-dialog>
  </div>
</template>

<script>
import Picture from '@/components/upload/picture';
import { debounce } from 'lodash';
import { validateCNIDCard } from '@/utils/validator';
import AuthStatusDialog from './AuthStatusDialog.vue';
// 主体认证
export default {
  name: 'SubjectAuthComponent',
  components: {
    Picture,
    AuthStatusDialog
  },
  data() {
    const customValidateIDCard = (rule, value, callback) => {
      if (!value) {
        callback(new Error('请输入身份证号码'));
      } else if (!validateCNIDCard(value)) {
        callback(new Error('请输入正确的身份证号码'));
      } else {
        callback();
      }
    };
    const validatePhone = (rule, value, callback) => {
      if (!value) {
        callback(new Error('请输入法人电话号码'));
      } else if (!/^1[3456789]\d{9}$/.test(value)) {
        callback(new Error('请输入正确的法人电话号码'));
      } else {
        callback();
      }
    };
    return {
      baseStatus: '', //
      subjectFormData: {
        // short_name: '', //简称
        //营业执照信息
        business_license: {
          copy: '',
          copy_url: '',
          number: '',
          company_name: '',
          company_address: '',
          valid_time_st: '',
          valid_time_et: ''
        },
        legal_person_id_card: {
          name: '',
          type: '1',
          copy: '',
          copy_url: '',
          national: '',
          national_url: '',
          number: '',
          valid_time_st: '',
          valid_time_et: ''
        },
        // 结算账户信息
        settle_acct: {
          type: '2',
          bank_name: '',
          bank_code: '',
          name: '',
          bank_branch_code: '', //   银行卡号
          bank_acct_no: '' // drec_code
        },
        organization_type: '', //组织类型
        legal_person_mobile_number: '', //法人电话号码
        sms_code: '' //短信验证码
      },
      business_license_type: 'range', //营业执照有效期类型   range -- 有效期  long -- 长期有效
      id_card_type: 'range', //身份证有效期类型   range -- 有效期  long -- 长期有效
      subjectRules: {
        // short_name: [{ required: true, message: '请输入商户简称', trigger: 'change' }],
        /*!营业执照相关*/
        'business_license.number': [
          { required: true, message: '请输入统一社会信用代码', trigger: 'change' },
          { type: 'string', len: 18, message: '请输入18位统一社会信用代码', trigger: 'change' }
        ],
        'business_license.company_name': [{ required: true, message: '请输入公司名称', trigger: 'change' }],
        'business_license.company_address': [{ required: true, message: '请输入公司地址', trigger: 'change' }],
        'business_license.valid_time_st': [
          {
            required: true,
            message: '请输入有效期开始时间',
            trigger: 'change'
          }
        ],
        'business_license.valid_time_et': [
          {
            required: true,
            message: '请输入有效期结束时间',
            trigger: 'change'
          }
        ],
        'business_license.copy_url': [{ required: true, message: '请上传营业执照图像', trigger: 'change' }],
        /*!法人身份信息相关*/
        'legal_person_id_card.name': [{ required: true, message: '请输入身份证姓名', trigger: 'change' }],
        'legal_person_id_card.number': [
          {
            required: true,
            trigger: 'change',
            validator: customValidateIDCard
          }
        ],
        'legal_person_id_card.valid_time_st': [
          {
            required: true,
            message: '请输入有效期开始时间',
            trigger: 'change'
          }
        ],
        'legal_person_id_card.valid_time_et': [
          {
            required: true,
            message: '请输入有效期结束时间',
            trigger: 'change'
          }
        ],
        'legal_person_id_card.copy_url': [{ required: true, message: '请上传身份证人像面图像', trigger: 'change' }],
        'legal_person_id_card.national_url': [{ required: true, message: '请上传身份证国徽面图像', trigger: 'change' }],
        legal_person_mobile_number: [{ required: true, trigger: 'change', validator: validatePhone }],
        sms_code: [{ required: true, message: '请输入短信验证码', trigger: 'change' }],
        organization_type: [{ required: true, message: '请选择主体类型', trigger: 'change' }],
        /*!结算账户相关*/
        'settle_acct.bank_branch_code': [{ required: true, message: '请选择开户银行网点', trigger: 'change' }],
        'settle_acct.name': [{ required: true, message: '请输入开户(企业)名称', trigger: 'change' }],
        'settle_acct.bank_acct_no': [{ required: true, message: '请输入银行卡号', trigger: 'change' }]
      },
      searchLoading: false,
      bankList: [],
      authLoading: false,
      validateTypes: [
        { value: 'range', label: '普通有效期' },
        { value: 'long', label: '长期有效' }
      ],
      statusDialogVisible: false,
      settleTypes: [],
      organizationTypes: [],
      submitSuccess: false,
      failedReason: '',
      submitLoading: false,
      routeStatus: '',
      auditStatus: '',
      validateStatus: '',
      isReAuth: ''
    };
  },
  computed: {
    isFormDisabled() {
      if (this.$route.query.out_request_no) {
        return !this.isReAuth;
      } else {
        return (
          (this.baseStatus !== 'init' &&
            this.baseStatus !== 'failed' &&
            this.auditStatus !== 'failed' &&
            this.validateStatus !== 'failed') ||
          (this.routeStatus !== 'init' && this.routeStatus !== 'failed')
        );
      }
    },
    showSms() {
      return this.subjectFormData.organization_type === '2' && this.subjectFormData.settle_acct.type === '1';
    }
  },
  created() {
    this.$router.onReady(() => {
      this.routeStatus = this.$route.query.status;
      this.isReAuth = this.$route.query.isReAuth;
      this.getOptions();
      this.getDetail();
    });
  },
  methods: {
    limitBusinessLicenseNumber(e) {
      const pattern = /[^A-Z0-9]+/g;
      this.subjectFormData.business_license.number = e.target.value.replace(pattern, '');
      e.target.value = e.target.value.replace(pattern, '');
    },
    getOptions() {
      this.$api.getAuthOptions().then(res => {
        console.log('-> %c res  ===    %o', 'font-size: 15px;color: #fa8c16 ;', res);
        this.organizationTypes = res.organization_type;
        this.settleTypes = res.settle_type;
      });
    },
    changeLicenseType(v, type) {
      if (v !== 'long') {
        return;
      }
      if (type === 'business_license') {
        this.subjectFormData.business_license.valid_time_et = v === 'long' ? '长期有效' : '';
        this.$refs.subjectForm.validateField('business_license.valid_time_et');
      } else {
        this.subjectFormData.legal_person_id_card.valid_time_et = v === 'long' ? '长期有效' : '';
        this.$refs.subjectForm.validateField('legal_person_id_card.valid_time_et');
      }
    },
    handleSubmit(formEl) {
      this.$refs[formEl].validate(valid => {
        const params = this.$lodash.cloneDeep(this.subjectFormData);
        if (this.business_license_type === 'long') {
          params.business_license.valid_time_et = '';
        }
        if (this.id_card_type === 'long') {
          params.legal_person_id_card.valid_time_et = '';
        }
        if (valid) {
          this.submitLoading = true;
          this.$api
            .authenticationInto(params)
            .then(
              res => {
                // this.$Message.success('提交进件成功');
                // this.$router.back();
                this.submitSuccess = true;
                this.statusDialogVisible = true;
              },
              err => {
                this.submitSuccess = false;
                this.failedReason = err.errmsg;
                this.statusDialogVisible = true;
                // this.$Message.error(err.errmsg);
              }
            )
            .finally(() => {
              this.submitLoading = false;
            });
        } else {
          this.$Message.error('请按照提示正确填写表单!');
        }
      });
    },
    handleReset(name) {
      this.$refs[name].resetFields();
    },
    // 搜索开户网点
    searchBranch: debounce(function (bank_name) {
      console.log('-> %c query  ===    %o', 'font-size: 15px;color: #fa8c16 ;', bank_name);
      this.searchLoading = true;
      this.$api
        .getBankInfo({ bank_name })
        .then(res => {
          console.log('-> %c res  ===    %o', 'font-size: 15px;color: #fa8c16 ;', res);
          this.searchLoading = false;
          this.bankList = res.data;
        })
        .catch(err => {
          this.searchLoading = false;
        });
    }, 300),
    // 选择开户行
    selectBank(val) {
      this.subjectFormData.settle_acct.bank_name = val.label;
      this.subjectFormData.settle_acct.bank_branch_code = val.value;
      this.subjectFormData.settle_acct.bank_code = val.tag;
    },
    // 清空开户行信息
    clearBank(val) {
      this.subjectFormData.settle_acct.bank_name = '';
      this.subjectFormData.settle_acct.bank_branch_code = '';
      this.subjectFormData.settle_acct.bank_code = '';
      this.bankList = [];
    },
    // 获取验证码
    onCountDownStart() {
      if (!this.subjectFormData.legal_person_mobile_number) {
        this.$Message.error('请先输入法人电话号码');
        return;
      }
      let params = {
        phone: this.subjectFormData.legal_person_mobile_number
      };
      this.authLoading = true;
      this.$api
        .getAuthenticationSmsCode(params)
        .then(() => {
          this.$refs.vac.startCountdown(true);
          this.$Message.success('发送成功');
        })
        .catch(error => {
          this.$Message.error(error.errmsg);
        })
        .finally(() => (this.authLoading = false));
    },
    // 上传营业执照成功
    uploadLicenseSuccess(url, type, idSide) {
      this.$api.uploadAuthenticationImg({ url }).then(
        res => {
          if (type === 'business_license') {
            this.subjectFormData.business_license.copy = res.media_id;
          } else {
            if (idSide === 'front') {
              this.subjectFormData.legal_person_id_card.copy = res.media_id;
            } else {
              this.subjectFormData.legal_person_id_card.national = res.media_id;
            }
          }
        },
        err => {
          this.$Message.error(err.errmsg);
        }
      );
    },
    //删除营业执照成功
    removeLicenseSuccess(url, type, idSide) {
      if (type === 'business_license') {
        this.subjectFormData.business_license.copy = '';
      } else {
        if (idSide === 'front') {
          this.subjectFormData.legal_person_id_card.copy = '';
        } else {
          this.subjectFormData.legal_person_id_card.national = '';
        }
      }
    },
    closeDialog() {
      this.statusDialogVisible = false;
      this.submitSuccess && this.$router.back();
    },
    changeOrganization(type) {
      if (type === '1') {
        this.subjectFormData.settle_acct.type = '2';
      }
    },
    getDetail() {
      this.$api
        .getAuthenticationInfo({
          out_request_no: this.$route.query.out_request_no
        })
        .then(
          res => {
            this.subjectFormData = res.detail;
            this.baseStatus = res.base_status;
            this.auditStatus = res.audit_status;
            this.validateStatus = res.validate_status;
            if (res.base_status !== 'init') {
              this.business_license_type = res.detail.business_license.valid_time_et ? 'range' : 'long';
              this.id_card_type = res.detail.legal_person_id_card.valid_time_et ? 'range' : 'long';
              if (this.business_license_type === 'long') {
                this.subjectFormData.business_license.valid_time_et = '长期有效';
              }
              if (this.id_card_type === 'long') {
                this.subjectFormData.legal_person_id_card.valid_time_et = '长期有效';
              }
              this.bankList = [
                {
                  drec_code: res.detail.settle_acct.bank_code,
                  bank_code: res.detail.settle_acct.bank_branch_code,
                  bank_name: res.detail.settle_acct.bank_name
                }
              ];
            } else {
              if (this.subjectFormData.legal_person_id_card.copy_url) {
                this.uploadLicenseSuccess(
                  this.subjectFormData.legal_person_id_card.copy_url,
                  'legal_person_id_card',
                  'front'
                );
              }
              if (this.subjectFormData.legal_person_id_card.national_url) {
                this.uploadLicenseSuccess(
                  this.subjectFormData.legal_person_id_card.national_url,
                  'legal_person_id_card',
                  'back'
                );
              }
              if (this.subjectFormData.business_license.copy_url) {
                this.uploadLicenseSuccess(this.subjectFormData.business_license.copy_url, 'business_license');
              }
            }

            this.$delete(this.subjectFormData, 'validate_form');
          },
          err => {
            this.$Message.error(err.errmsg);
          }
        );
    }
  }
};
</script>

<style scoped lang="less">
.subject-form {
  width: 70%;
  padding-bottom: 80px;
  height: 100%;

  .form-title {
    font-size: 15px;
    font-weight: 500;
    color: #333333;
    line-height: 19px;
    margin: 16px 0;
  }

  .upload-note {
    width: 480px;
    color: #aaaaaa;
    line-height: 20px;
  }

  ::v-deep .picture-wrapper {
    line-height: normal;
    width: 140px;
  }

  .upload-tip {
    color: #333;
  }
}
</style>
