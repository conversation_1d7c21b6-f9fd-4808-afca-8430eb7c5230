<template>
  <div class="detail-wrap">
    <components :is="getCurrentCom" :ref="getCurrentCom"></components>
  </div>
</template>

<script>
import SubjectAuth from './components/SubjectAuth.vue';
import RemittanceAuth from './components/RemittanceAuth.vue';

export default {
  name: 'SubjectAuthDetail',
  components: {
    SubjectAuth,
    RemittanceAuth
  },
  data() {
    return {
      key: 'value',
      currentCom: 'SubjectAuth'

    };
  },
  computed: {
    getCurrentCom() {
      return this.$route.query.step === 'subject-auth' ? 'SubjectAuth' : 'RemittanceAuth';
    }
  },
  created() {},
  methods: {
    submitForm() {

    }
  },
};
</script>

<style scoped lang="less">
.detail-wrap{
  height: 100%;
}
</style>
