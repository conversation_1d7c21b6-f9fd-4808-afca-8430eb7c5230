<template>
  <div class="wrapper" style="min-width: 1150px">
    <!-- Header -->
    <header class="head-block ceiling head-flex">
      <div class="flex flex-item-align">
        <p>选择月份：</p>
        <customDatePicker v-model="formDate" :type-options="['month']" @getDateType="getDateType"></customDatePicker>
      </div>

      <div class="flex flex-item-align">
        <div>门店：</div>
        <div>
          <clinicSearch
            ref="search-cli"
            v-model="clinic_id"
            :show-all="true"
            width="300"
            @change="selectClinic"
          ></clinicSearch>
        </div>
      </div>
    </header>

    <Single ref="single" v-if="show" :clinic-id="clinic_id"></Single>
    <Multiple ref="multiple" v-if="!show"></Multiple>
  </div>
</template>

<script>
import dateChange from '../mixins/dateChange';
// import clinicSearch from '@/components/clinic-search/clinic-search.vue';
import Multiple from './components/multiple.vue';
import Single from './components/single.vue';
export default {
  name: 'PmsOpcenterBoard',
  mixins: [dateChange],
  components: {
    Multiple,
    Single
    // clinicSearch
  },
  data() {
    return {
      clinic_id: '',
      show: false
    };
  },

  mounted() {},

  methods: {
    scrollTop() {
      this.$nextTick(() => {
        window.scrollTo({ top: 0, behavior: 'smooth' }); // 平滑滚动到顶部
        // 或
        // window.scrollTo(0, 0);
      });
    },
    dateChange() {
      if (this.clinic_id) {
        this.$refs.single.initData({
          date_type: this.date_type,
          date: this.formDate,
          clinic_id: this.clinic_id
        });
      } else {
        this.$refs.multiple.initData({
          date_type: this.date_type,
          date: this.formDate
        });
      }
    },
    selectClinic() {
      if (this.clinic_id) {
        this.show = true;

        this.$nextTick(() => {
          this.$refs.single.initData({
            date_type: this.date_type,
            date: this.formDate,
            clinic_id: this.clinic_id
          });
        });
      } else {
        this.show = false;
        this.$nextTick(() => {
          this.$refs.multiple.initData({
            date_type: this.date_type,
            date: this.formDate
          });
        });
      }

      this.scrollTop();
    }
  }
};
</script>

<style lang="less" scoped>
@import url('../style/common.less');

.head-flex {
  display: flex;
  justify-content: space-between;
}
</style>
