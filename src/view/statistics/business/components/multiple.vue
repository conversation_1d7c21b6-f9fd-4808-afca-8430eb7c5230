<template>
  <div>
    <!-- 交易额分析 -->
    <template>
      <div class="light-title mt8">交易额分析</div>
      <div class="flex gap-16 mt16">
        <div class="block block-bg block-1-row1-col1" :style="{ backgroundImage: `url(${money_bg})` }">
          <h4 class="block-title flex flex-align-center">总交易额</h4>
          <div class="block-content-desc">{{ format_money(total.turnover) }}</div>
        </div>

        <div class="block flex-1 block-1-row1-col2">
          <h4 class="block-title flex flex-align-center">各门店交易额</h4>
          <div class="width100">
            <chart-view
              v-if="isShowLineEcharts(store_sales_bar_opt, 'bar', 'xAxis')"
              height="260px"
              :chart-option="store_sales_bar_opt"
            ></chart-view>
            <div class="empty" v-else>暂无数据</div>
          </div>
        </div>
      </div>

      <!-- 各门店交易额环比 -->
      <div class="block mt16">
        <h4 class="block-title flex flex-align-center">各门店交易额环比</h4>
        <div class="width100">
          <chart-view
            :chart-option="store_sale_rate_barAline_opt"
            v-if="isShowLineEcharts(store_sale_rate_barAline_opt, 'line')"
          ></chart-view>
          <div class="empty" v-else>暂无数据</div>
        </div>
      </div>
    </template>

    <!-- 交易分析 -->
    <template>
      <div class="light-title mt32">交易分析</div>

      <div class="flex gap-16 mt16">
        <div class="block block-bg w-240" :style="{ backgroundImage: `url(${personal_bg})` }">
          <h4 class="block-title flex flex-align-center">总成交人数</h4>
          <div class="block-content-desc">{{ total.paid_cv }}人</div>
        </div>

        <div class="flex gap-16 flex-1">
          <div class="block flex-1">
            <h4 class="block-title flex flex-align-center">各门店总成交人数</h4>
            <div class="width100">
              <chart-view
                height="270px"
                :chart-option="store_all_turnover_count_bar_opt"
                v-if="isShowLineEcharts(store_all_turnover_count_bar_opt, 'bar', 'xAxis')"
              ></chart-view>
              <div class="empty" v-else>暂无数据</div>
            </div>
          </div>

          <div class="block flex-1">
            <h4 class="block-title flex flex-align-center">各门店老客成交人数</h4>
            <div class="width100">
              <chart-view
                height="270px"
                :chart-option="store_oldCus_turnover_count_bar_opt"
                v-if="isShowLineEcharts(store_oldCus_turnover_count_bar_opt, 'bar', 'xAxis')"
              ></chart-view>
              <div class="empty" v-else>暂无数据</div>
            </div>
          </div>
        </div>
      </div>

      <div class="flex gap-16 mt16">
        <div class="block block-bg w-240" :style="{ backgroundImage: `url(${personal_bg})` }">
          <h4 class="block-title flex flex-align-center">老客成交人数</h4>
          <div class="block-content-desc">{{ total.paid_cv_old_customer_num }} 人</div>
        </div>

        <div class="flex gap-16 flex-1">
          <div class="block flex-1">
            <h4 class="block-title flex flex-align-center">各门店新客成交率</h4>
            <div class="width100">
              <chart-view
                height="270px"
                :chart-option="store_new_turnover_rate_line_opt"
                v-if="isShowLineEcharts(store_new_turnover_rate_line_opt, 'line')"
              ></chart-view>
              <div class="empty" v-else>暂无数据</div>
            </div>
          </div>
          <div class="block flex-1">
            <h4 class="block-title flex flex-align-center">各门店总成交率</h4>
            <div class="width100">
              <chart-view
                height="270px"
                :chart-option="store_all_turnover_rate_line_opt"
                v-if="isShowLineEcharts(store_all_turnover_rate_line_opt, 'line')"
              ></chart-view>
              <div class="empty" v-else>暂无数据</div>
            </div>
          </div>
        </div>
      </div>
      <div class="flex gap-16 mt16">
        <div class="block block-bg w-240" :style="{ backgroundImage: `url(${personal_bg})` }">
          <h4 class="block-title flex flex-align-center">月复购人数</h4>
          <div class="block-content-desc">{{ total.repurchase_cv }} 人</div>
        </div>

        <div class="flex gap-16 flex-1">
          <div class="block flex-1">
            <h4 class="block-title flex flex-align-center">各门店月复购人数</h4>
            <div class="width100">
              <chart-view
                height="270px"
                :chart-option="repurchase_cv_bar_opt"
                v-if="isShowLineEcharts(repurchase_cv_bar_opt, 'bar', 'xAxis')"
              ></chart-view>
              <div class="empty" v-else>暂无数据</div>
            </div>
          </div>

          <div class="block flex-1">
            <h4 class="block-title flex flex-align-center">各门店月用户复购率</h4>
            <div class="width100">
              <chart-view
                height="270px"
                :chart-option="repurchase_ratio_line_opt"
                v-if="isShowLineEcharts(repurchase_ratio_line_opt, 'line')"
              ></chart-view>
              <div class="empty" v-else>暂无数据</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 各门店新客流量环比 -->
      <div class="block mt16" style="min-height: 260px">
        <h4 class="block-title flex flex-align-center">各门店新客流量环比</h4>
        <div class="width100">
          <chart-view
            :chart-option="store_newcus_rate_barAline_opt"
            v-if="isShowLineEcharts(store_newcus_rate_barAline_opt, 'line')"
          ></chart-view>
          <div class="empty" v-else>暂无数据</div>
        </div>
      </div>
    </template>

    <template>
      <div class="light-title mt32">客单价分析</div>

      <div class="flex gap-16 mt16" style="min-height: 260px">
        <div class="block block-bg w-240" :style="{ backgroundImage: `url(${personal_bg})` }">
          <h4 class="block-title flex flex-align-center">总客单</h4>
          <div class="block-content-desc">{{ format_money(total.per_cv_amount) }}</div>
        </div>

        <div class="flex gap-16 flex-4">
          <div class="block flex-1">
            <h4 class="block-title flex flex-align-center">各门店客单</h4>
            <div class="width100">
              <chart-view
                height="260px"
                :chart-option="store_per_cv_bar_line_opt"
                v-if="isShowLineEcharts(store_per_cv_bar_line_opt, 'bar', 'xAxis')"
              ></chart-view>
              <div class="empty" v-else>暂无数据</div>
            </div>
          </div>

          <div class="block flex-1">
            <h4 class="block-title flex flex-align-center">各门店新客客单</h4>
            <div class="width100">
              <chart-view
                height="260px"
                :chart-option="store_per_new_bar_line_opt"
                v-if="isShowLineEcharts(store_per_new_bar_line_opt, 'line')"
              ></chart-view>
              <div class="empty" v-else>暂无数据</div>
            </div>
          </div>
        </div>
      </div>

      <div class="flex gap-16 mt16" style="min-height: 260px">
        <div class="block block-bg w-240" :style="{ backgroundImage: `url(${personal_bg})` }">
          <h4 class="block-title flex flex-align-center">会员客单</h4>
          <div class="block-content-desc">{{ format_money(total.per_cv_vip_amount) }}</div>
        </div>

        <div class="flex gap-16 flex-4">
          <div class="block flex-1">
            <h4 class="block-title flex flex-align-center">各门店会员客单</h4>
            <div class="width100">
              <chart-view
                height="260px"
                :chart-option="store_per_vip_bar_line_opt"
                v-if="isShowLineEcharts(store_per_vip_bar_line_opt, 'line')"
              ></chart-view>
              <div class="empty" v-else>暂无数据</div>
            </div>
          </div>

          <div class="block flex-1">
            <h4 class="block-title flex flex-align-center">各门店老客客单</h4>
            <div class="width100">
              <chart-view
                height="260px"
                :chart-option="store_per_old_bar_line_opt"
                v-if="isShowLineEcharts(store_per_old_bar_line_opt, 'line')"
              ></chart-view>
              <div class="empty" v-else>暂无数据</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 各门店客单价环比 -->
      <div class="block mt16" style="min-height: 260px">
        <h4 class="block-title flex flex-align-center">各门店客单价环比</h4>
        <div class="width100">
          <chart-view
            :chart-option="store_per_rate_bar_line_opt"
            v-if="isShowLineEcharts(store_per_rate_bar_line_opt, 'line')"
          ></chart-view>
          <div class="empty" v-else>暂无数据</div>
        </div>
      </div>
    </template>

    <!-- 利润分析 -->
    <template>
      <div class="light-title mt32">利润分析</div>

      <div class="flex gap-16 mt16" style="min-height: 260px">
        <div class="block block-bg w-240" :style="{ backgroundImage: `url(${money_bg})` }">
          <h4 class="block-title flex flex-align-center">总利润</h4>
          <div class="block-content-desc">{{ format_money(total.profit) }}</div>
          <!-- <div>
            <div>营收：{{ total.confirm_revenue }}</div>
            <div>固定成本：{{ total.base_cost_amount }}</div>
            <div>订单关联物料成本：{{ total.order_stock_cost_amount }}</div>
            <div>运营费用成本：{{ total.opex_cost_amount }}</div>
          </div> -->
        </div>

        <div class="flex-4">
          <div class="block">
            <h4 class="block-title flex flex-align-center">各门店利润</h4>
            <div class="width100">
              <chart-view
                :chart-option="store_net_profit_bar_opt"
                v-if="isShowLineEcharts(store_net_profit_bar_opt, 'bar', 'xAxis')"
              ></chart-view>
              <div class="empty" v-else>暂无数据</div>
            </div>
          </div>
        </div>
      </div>

      <div class="block mt16" style="min-height: 260px">
        <h4 class="block-title flex flex-align-center">各门店利润环比</h4>
        <div class="width100">
          <chart-view
            :chart-option="store_net_profit_bar_line_opt"
            v-if="isShowLineEcharts(store_net_profit_bar_line_opt, 'line')"
          ></chart-view>
          <div class="empty" v-else>暂无数据</div>
        </div>
      </div>
    </template>

    <!-- 客源分析 -->
    <template>
      <div class="light-title mt32">客源分析</div>

      <div class="flex-col gap-16 mt16">
        <div class="flex flex-1 gap-16">
          <div
            class="block block-bg w-240"
            style="min-height: 260px"
            :style="{ backgroundImage: `url(${personal_bg})` }"
          >
            <h4 class="block-title flex flex-align-center">新获客人数</h4>
            <div class="block-content-desc">{{ total.new_customers }} 人</div>
          </div>

          <div class="block flex-1 flex flex-c">
            <h4 class="block-title flex flex-align-center">收入来源构成</h4>
            <div class="width100 flex-1 flex flex-item-center">
              <chart-view
                height="300px"
                :chart-option="store_revenue_composition_stack_opt"
                v-if="isShowLineEcharts(store_revenue_composition_stack_opt, 'bar', 'xAxis')"
              ></chart-view>
              <div class="empty" v-else>暂无数据</div>
            </div>
          </div>
        </div>

        <div class="flex gap-16">
          <div class="block" style="min-height: 500px; min-width: 335px">
            <h4 class="block-title flex flex-align-center">岗位分布</h4>
            <div class="flex-1 flex flex-item-center">
              <chart-view
                height="450px"
                :chart-option="physical_therapist_type_pie_opt"
                v-if="isShowLineEcharts(physical_therapist_type_pie_opt, 'pie')"
              ></chart-view>
              <div class="empty" v-else>暂无数据</div>
            </div>
          </div>
          <div class="block flex-2" style="min-height: 500px; min-width: 370px">
            <h4 class="block-title flex flex-align-center">获客渠道占比</h4>
            <div class="width100">
              <chart-view
                height="470px"
                :chart-option="new_customers_channel_pie_opt"
                v-if="isShowLineEcharts(new_customers_channel_pie_opt, 'pie')"
              ></chart-view>
              <div class="empty" v-else>暂无数据</div>
            </div>
          </div>

          <div class="block flex-3" style="min-height: 500px; min-width: 400px">
            <h4 class="block-title flex flex-align-center">门店人力图</h4>
            <div class="width100 flex-1 flex flex-item-center">
              <chart-view
                height="470px"
                :chart-option="physical_therapist_clinic_pie_opt"
                v-if="isShowLineEcharts(physical_therapist_clinic_pie_opt, 'pie')"
              ></chart-view>
              <div class="empty" v-else>暂无数据</div>
            </div>
          </div>
        </div>
      </div>
    </template>
  </div>
</template>

<script>
import { color_enum } from '@/view/statistics/data/color';
import gennerateEchatrs from '../../mixins/multipleEcharts';
export default {
  name: 'SingleBoard',
  components: {},
  mixins: [gennerateEchatrs],
  props: {},
  data() {
    return {
      date_type: 'month',
      date: [],

      color_table_enum: color_enum, //色值表

      total: {},

      // 总交易额分析
      transaction_val_analysis: {},

      // 交易分析
      transaction_analysis: {},

      // 客单价分析
      per_transaction_analysis: {},

      // 利润分析
      profit_analysis: {},

      // 客源分析
      cus_source_analysis: {},

      // 各门店交易额-柱状
      store_sales_bar_opt: {},

      // 各门店交易额-饼图
      store_sales_pie_opt: {},

      // 各门店交易额环比
      store_sale_rate_barAline_opt: {},

      // 各门店总成交人数
      store_all_turnover_count_bar_opt: {},

      // 各门店老客成交人数
      store_oldCus_turnover_count_bar_opt: {},

      // 各门总成交人率
      store_all_turnover_rate_line_opt: {},

      // 各门店新客成交率
      store_new_turnover_rate_line_opt: {},

      // 各门店月复购人数
      repurchase_cv_bar_opt: {},
      // 各门店月复购率
      repurchase_ratio_line_opt: {},

      //各门店客单价
      store_per_cv_bar_line_opt: {},
      //各门店新客单价
      store_per_new_bar_line_opt: {},
      //各门店老客单价
      store_per_old_bar_line_opt: {},
      //各门店vip客单价
      store_per_vip_bar_line_opt: {},
      //各门店客单价环比
      store_per_rate_bar_line_opt: {},

      // 门店利润
      store_net_profit_bar_opt: {},
      // 门店利润环比
      store_net_profit_bar_line_opt: {},

      // 获取渠道
      new_customers_channel_pie_opt: {},
      // 收入来源构成
      store_revenue_composition_stack_opt: {},

      // 各门店新客流量环比
      store_newcus_rate_barAline_opt: {},
      // 岗位分布
      physical_therapist_type_pie_opt: {},
      // 门店人力
      physical_therapist_clinic_pie_opt: {},

      money_bg: 'https://static.rsjxx.com/image/2025/0711/141649_8756.png',
      personal_bg: 'https://static.rsjxx.com/image/2025/0711/152754_79225.png',
      pie_bg: 'https://static.rsjxx.com/image/2025/0711/152754_15559.png'
    };
  },
  computed: {
    format_money() {
      return value => {
        if (value) {
          return '¥ ' + value;
        } else {
          return '-';
        }
      };
    }
  },
  watch: {},
  created() {
    // this.getTradeTradedetails();
  },
  mounted() {},
  methods: {
    initData(data) {
      this.date = data.date;
      this.date_type = data.date_type;
      this.getAllrepot();
    },
    generateEchartData(arr, key) {
      const res = arr?.map(item => item[key] && item[key]);
      return res;
    },

    initEcharts(data) {
      const clinic_list = data.clinic_list || [];
      const name = this.generateEchartData(clinic_list, 'clinic_name');
      // const total = data.total;

      this.gennerateTurnoverAnalysis(clinic_list, name);

      this.gennerateTransactionAnalysis(clinic_list, name);

      this.genneratePerCvAnalysis(clinic_list, name);

      this.gennerateProfitAnalysis(clinic_list, name);

      this.gennerateCustomersAnalysis(data, clinic_list, name);
    },

    /**
     * 交易额分析
     * @param clinic_list
     */
    gennerateTurnoverAnalysis(clinic_list, name) {
      // 各门店交易额-line

      const turnover = this.generateEchartData(clinic_list, 'turnover');
      const last_turnover = this.generateEchartData(clinic_list, 'last_turnover');
      const turnover_incr = clinic_list.map(item => {
        return {
          value: item.turnover_incr,
          tooltip_text: `${item.turnover_incr}%`
        };
      });
      this.setBarConfigure_zoom(
        'store_sales_bar_opt',
        {
          xData: name,
          yData: turnover
        },
        {
          name: '交易额',
          prefix: '¥'
        }
      );

      // 各门店交易额度-pie
      const store_sales_pie = clinic_list.map(item => ({
        name: item.clinic_name,
        title: item.clinic_name,
        value: item.turnover
      }));
      this.setPieConfigure_guide('store_sales_pie_opt', store_sales_pie);

      // 各门店交易额环比
      this.setBarALineConfigure(
        'store_sale_rate_barAline_opt',
        {
          xData: name,
          barLastData: last_turnover,
          barThisData: turnover,
          lineData: turnover_incr
        },
        {
          legend: ['上月交易额', '本月交易额', '交易额环比变化率']
        }
      );
    },

    /**
     * 交易分析
     * @param clinic_list
     */
    gennerateTransactionAnalysis(clinic_list, name) {
      /**总客成交-人数 */
      const paid_cv = this.generateEchartData(clinic_list, 'paid_cv');
      this.setBarConfigure_zoom(
        'store_all_turnover_count_bar_opt',
        {
          xData: name,
          yData: paid_cv
        },
        {
          name: '总成交人数'
        }
      );

      //老客成交人数
      const paid_cv_old_customer_num = this.generateEchartData(clinic_list, 'paid_cv_old_customer_num');
      this.setBarConfigure_zoom(
        'store_oldCus_turnover_count_bar_opt',
        {
          xData: name,
          yData: paid_cv_old_customer_num
        },
        {
          name: '老客成交人数'
        }
      );

      // 复购人数
      const repurchase_cv = this.generateEchartData(clinic_list, 'repurchase_cv');
      this.setBarConfigure_zoom(
        'repurchase_cv_bar_opt',
        {
          xData: name,
          yData: repurchase_cv
        },
        {
          name: '复购人数'
        }
      );

      // 总成交率
      const arrival_pay_convert_ratio = clinic_list.map(item => ({
        value: item.arrival_pay_convert_ratio,
        tooltip_text: `${item.arrival_pay_convert_ratio}%`
      }));
      this.setLineConfigure(
        'store_all_turnover_rate_line_opt',
        {
          xData: name,
          seriesData: arrival_pay_convert_ratio
        },
        {
          name: '总成交率'
        }
      );

      // 新客成交率
      const new_cv_pay_convert_ratio = clinic_list.map(item => ({
        value: item.new_cv_pay_convert_ratio,
        tooltip_text: `${item.new_cv_pay_convert_ratio}%`
      }));
      this.setLineConfigure(
        'store_new_turnover_rate_line_opt',
        {
          xData: name,
          seriesData: new_cv_pay_convert_ratio
        },
        {
          name: '新客成交率'
        }
      );

      // 月复购率
      const repurchase_ratio = clinic_list.map(item => ({
        value: item.repurchase_ratio,
        tooltip_text: `${item.repurchase_ratio}%`
      }));
      this.setLineConfigure(
        'repurchase_ratio_line_opt',
        {
          xData: name,
          seriesData: repurchase_ratio
        },
        {
          name: '月复购率'
        }
      );

      // 各门店新客流量环比
      const last_new_customers = this.generateEchartData(clinic_list, 'last_new_customers');
      // const new_customers_incr = this.generateEchartData(clinic_list, 'new_customers_incr');
      const new_customers = this.generateEchartData(clinic_list, 'new_customers');
      const new_customers_incr = clinic_list.map(item => {
        return {
          value: item.new_customers_incr,
          tooltip_text: `${item.new_customers_incr}%`
        };
      });
      this.setBarALineConfigure(
        'store_newcus_rate_barAline_opt',
        {
          xData: name,
          barLastData: last_new_customers,
          barThisData: new_customers,
          lineData: new_customers_incr
        },
        {
          legend: ['上月新客', '本月新客', '新客流量环比变化率']
        }
      );
    },

    /**
     * 客单价分析
     * @param clinic_list
     */
    genneratePerCvAnalysis(clinic_list, name) {
      // 客单价
      const per_cv_amount = this.generateEchartData(clinic_list, 'per_cv_amount');
      this.setBarALineConfigure_single('store_per_cv_bar_line_opt', {
        xData: name,
        barData: per_cv_amount,
        lineData: per_cv_amount
      });

      const per_cv_old_amount = this.generateEchartData(clinic_list, 'per_cv_old_amount');
      this.setBarALineConfigure_single('store_per_old_bar_line_opt', {
        xData: name,
        barData: per_cv_old_amount,
        lineData: per_cv_old_amount
      });

      const per_cv_new_amount = this.generateEchartData(clinic_list, 'per_cv_new_amount');
      this.setBarALineConfigure_single('store_per_new_bar_line_opt', {
        xData: name,
        barData: per_cv_new_amount,
        lineData: per_cv_new_amount
      });

      const per_cv_vip_amount = this.generateEchartData(clinic_list, 'per_cv_vip_amount');
      this.setBarALineConfigure_single('store_per_vip_bar_line_opt', {
        xData: name,
        barData: per_cv_vip_amount,
        lineData: per_cv_vip_amount
      });

      const last_per_cv_amount = this.generateEchartData(clinic_list, 'last_per_cv_amount');
      const per_cv_amount_incr = clinic_list.map(item => {
        return {
          value: item.per_cv_amount_incr,
          tooltip_text: `${item.per_cv_amount_incr}%`
        };
      });
      this.setBarALineConfigure(
        'store_per_rate_bar_line_opt',
        {
          xData: name,
          barLastData: last_per_cv_amount,
          barThisData: per_cv_amount,
          lineData: per_cv_amount_incr
        },
        {
          legend: ['上月客单价', '本月客单价', '客单价环比变化率']
        }
      );
    },

    /**
     * 利润分析
     * @param clinic_list
     * @param name
     */
    gennerateProfitAnalysis(clinic_list, name) {
      // 利润
      const profit = this.generateEchartData(clinic_list, 'profit');
      this.setBarConfigure_zoom(
        'store_net_profit_bar_opt',
        {
          xData: name,
          yData: profit
        },
        {
          name: '利润',
          color: '#FC8333',
          prefix: '¥'
        }
      );
      // 利润环比
      const last_profit = this.generateEchartData(clinic_list, 'last_profit');
      const profit_incr = clinic_list.map(i => ({
        value: i.profit_incr,
        tooltip_text: `${i.profit_incr}%`
      }));
      this.setBarALineConfigure(
        'store_net_profit_bar_line_opt',
        {
          xData: name,
          barLastData: last_profit,
          barThisData: profit,
          lineData: profit_incr
        },
        {
          legend: ['上月利润总和', '本月利润总和', '利润环比变化率']
        }
      );
    },

    /**
     * 客源分析
     * @param clinic_list
     * @param name
     */
    gennerateCustomersAnalysis(data, clinic_list, name) {
      //获客人数
      const new_customers_channel = data?.new_customers_channel.map(i => ({
        name: i.name,
        title: i.name,
        value: i.count,
        rate: i.rate,
        tootipList: [
          { key: '', value: i.name },
          { key: '人数', value: i.count },
          { key: '占比', value: `${i.rate}%` }
        ]
      }));
      this.setPieConfigure_guide('new_customers_channel_pie_opt', new_customers_channel);

      // 岗位分布
      const physical_therapist_type = data?.physical_therapist_type.map(i => ({
        name: i.name,
        title: i.name,
        value: i.count,
        rate: i.rate,
        tootipList: [
          { key: '', value: i.name },
          { key: '人数', value: i.count },
          { key: '占比', value: `${i.rate}%` }
        ]
      }));
      this.setPieConfigure_guide('physical_therapist_type_pie_opt', physical_therapist_type);

      // 门店人力
      const physical_therapist_clinic = data?.physical_therapist_clinic.map(i => ({
        name: i.name,
        title: i.name,
        value: i.count,
        rate: i.rate,
        tootipList: [
          { key: '', value: i.name },
          { key: '人数', value: i.count },
          { key: '占比', value: `${i.rate}%` }
        ]
      }));
      this.setPieConfigure_guide('physical_therapist_clinic_pie_opt', physical_therapist_clinic);

      // 收入构成
      const kind_yi_turnover = {
        name: '医疗交易额',
        data: this.generateEchartData(clinic_list, 'kind_yi_turnover')
      };
      const kind_yang_turnover = {
        name: '养疗交易额',
        data: this.generateEchartData(clinic_list, 'kind_yang_turnover')
      };
      const kind_liao_turnover = {
        name: '理疗交易额',
        data: this.generateEchartData(clinic_list, 'kind_liao_turnover')
      };
      const kind_other_turnover = {
        name: '其他交易额',
        data: this.generateEchartData(clinic_list, 'kind_other_turnover')
      };
      this.setStackConfigure('store_revenue_composition_stack_opt', {
        xData: name,
        series: [kind_yi_turnover, kind_yang_turnover, kind_liao_turnover, kind_other_turnover]
      });
    },

    getAllrepot() {
      this.$api
        .getAllrepot({
          date_type: this.date_type,
          st: this.date[0],
          et: this.date[1]
        })
        .then(res => {
          console.log('🚀 ~ multiple.vue:699 ~ getAllrepot ~ res=>', res);
          this.initEcharts(res);
          // this.initEcharts(jsonData.data);
          this.total = res.total;
        });
    }

    // 处理商品销售额占比分析饼图数据
  },
  filters: {}
};
</script>
<style lang="less" scoped>
@import url('../../style/common.less');
.block-content-desc {
  margin-top: 40px;
  display: flex;
  justify-content: center;
  align-items: center;
  font-weight: 600;
  font-size: 32px;
  color: #333333;
  line-height: 45px;
}

.gap-16 {
  gap: 16px;
}
.w-240 {
  width: 240px;
  min-width: 240px;
}

.block-1-row1-col1 {
  min-width: 240px;
  width: 240px;
}

.block-1-row1-col2 {
  min-width: 860px;
}

.block-bg {
  background-size: 130px;
  background-repeat: no-repeat;
  background-position: right bottom;
}
</style>
