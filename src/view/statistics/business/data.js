import Mock from 'mockjs';

const data = Mock.mock({
  'clinic_list|15': [
    // 生成10条数据
    {
      'clinic_id|+1': 100, // 递增ID
      'opc_id|1-5': 1, // 1-5之间的随机数
      clinic_name: '@cword(4,8)的诊所', // 随机中文名
      turnover: '@float(1000, 10000, 2, 2)', // 1000-10000的随机金额，2位小数
      last_turnover: '@float(100, 5000, 2, 2)',
      turnover_incr: '@float(-1000, 1000, 2, 2)',
      'paid_cv|1-10': 1,
      'paid_cv_old_customer_num|1-10': 1,
      'paid_cv_new_customer_num|0-5': 1,
      arrival_pay_convert_ratio: '@float(0, 100, 2, 2)',
      new_cv_pay_convert_ratio: '@float(0, 100, 2, 2)',
      repurchase_cv: '@float(0, 10, 2, 2)',
      repurchase_ratio: '@float(0, 100, 2, 2)',
      'new_customers|0-10': 1,
      'last_new_customers|0-10': 1,
      new_customers_incr: '@float(-100, 100, 2, 2)',
      per_cv_amount: '@float(0, 1000, 2, 2)',
      last_per_cv_amount: '@float(0, 1000, 2, 2)',
      per_cv_amount_incr: '@float(-100, 100, 2, 2)',
      per_cv_old_amount: '@float(0, 1000, 2, 2)',
      per_cv_new_amount: '@float(0, 1000, 2, 2)',
      per_cv_vip_amount: '@float(0, 1000, 2, 2)',
      cost: '@float(0, 5000, 2, 2)',
      last_cost: '@float(0, 5000, 2, 2)',
      profit: '@float(0, 10000, 2, 2)',
      last_profit: '@float(0, 10000, 2, 2)',
      profit_incr: '@float(-100, 100, 2, 2)',
      kind_yi_turnover: '@float(0, 1000, 2, 2)',
      kind_yang_turnover: '@float(0, 1000, 2, 2)',
      kind_liao_turnover: '@float(0, 1000, 2, 2)',
      kind_other_turnover: '@float(0, 1000, 2, 2)'
    }
  ]
});

export const jsonData = {
  errcode: '0',
  errmsg: '',
  data: {
    total: {
      turnover: '142570.54',
      paid_cv: '30',
      paid_cv_old_customer_num: '30',
      repurchase_cv: '12',
      new_customers: '32',
      per_cv_amount: '123',
      per_cv_vip_amount: '3212',
      profit: '141311.03',
      cost: '1259.51',
      vip_turnover: '123',
      paid_cv_vip_num: '322'
    },
    new_customers_channel: [
      {
        name: '转介绍用户数',
        count: '1',
        rate: '12.5'
      },
      {
        name: '媒体报道用户数',
        count: '0',
        rate: '0'
      },
      {
        name: '来自活动推广用户数',
        count: '0',
        rate: '0'
      },
      {
        name: '来自免费义诊用户数',
        count: '0',
        rate: '0'
      },
      {
        name: '来自自然流量用户数',
        count: '0',
        rate: '0'
      },
      {
        name: '来自美团用户数',
        count: '0',
        rate: '0'
      },
      {
        name: '转介绍用户数',
        count: '0',
        rate: '0'
      },
      {
        name: '来自服务预约用户数',
        count: '0',
        rate: '0'
      },
      {
        name: '来自渠道合作用户数',
        count: '0',
        rate: '0'
      },
      {
        name: '来自【其他】用户数',
        count: '7',
        rate: '87.5'
      }
    ],
    physical_therapist_type: [
      {
        name: '医生',
        count: '38',
        rate: '39.58'
      },
      {
        name: '理疗师',
        count: '56',
        rate: '58.33'
      },
      {
        name: '店长',
        count: '2',
        rate: '2.08'
      },
      {
        name: '药剂师',
        count: '0',
        rate: '0'
      },
      {
        name: '导诊',
        count: '0',
        rate: '0'
      }
    ],
    physical_therapist_clinic: [
      {
        name: '问诊所',
        count: '5',
        rate: '5.21'
      },
      {
        name: '经纬天地01',
        count: '1',
        rate: '1.04'
      },
      {
        name: '伍伍伍榕树堂诊所1',
        count: '2',
        rate: '2.08'
      },
      {
        name: 'JJ榕树堂分账诊所',
        count: '3',
        rate: '3.13'
      },
      {
        name: 'zyd河南合伙人榕树堂',
        count: '1',
        rate: '1.04'
      },
      {
        name: 'ydp榕树堂测试',
        count: '1',
        rate: '1.04'
      },
      {
        name: 'Sean-榕树堂001',
        count: '2',
        rate: '2.08'
      },
      {
        name: '储值榕树堂',
        count: '2',
        rate: '2.08'
      },
      {
        name: 'JJ榕树堂，测试分发',
        count: '1',
        rate: '1.04'
      },
      {
        name: '齐齐哈尔榕树堂',
        count: '1',
        rate: '1.04'
      }
    ],
    clinic_list: data.clinic_list
  }
};
