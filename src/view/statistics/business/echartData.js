// ÷销售数据柱状图options;
const sale_bar_options = {
  grid: {
    left: '0',
    right: '0',
    bottom: '0',
    top: '35',
    containLabel: true
  },
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'shadow'
    }
  },
  xAxis: {
    type: 'category',
    data: ['门店A', '门店B', '门店C', '门店D', '门店E'],
    axisLine: {
      lineStyle: {
        color: '#E5E5E5'
      }
    },
    axisLabel: {
      color: '#666',
      fontSize: 12
    }
  },
  yAxis: {
    type: 'value',
    axisLine: {
      show: false
    },
    axisTick: {
      show: false
    },
    splitLine: {
      lineStyle: {
        type: 'dashed',
        color: '#E5E5E5'
      }
    },
    axisLabel: {
      color: '#666',
      fontSize: 12,
      formatter: function (value) {
        return '¥' + value;
      }
    }
  },
  series: [
    {
      data: [12000, 20000, 15000, 8000, 11000],
      type: 'bar',
      barWidth: '40%',
      itemStyle: {
        color: '#3184F1'
      },
      label: {
        show: true,
        position: 'top',
        color: '#333',
        fontSize: 12,
        formatter: function (params) {
          return '¥' + params.value;
        }
      }
    }
  ]
};
