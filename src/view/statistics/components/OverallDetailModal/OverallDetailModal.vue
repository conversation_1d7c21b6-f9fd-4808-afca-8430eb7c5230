<template>
  <div class="OverallDetailModal-wrapper">
    <Modal
      :value="visible"
      :mask-closable="false"
      class-name="vertical-center-modal"
      title=" "
      width="840px"
      @on-visible-change="handleVisibleChange"
    >
      <div slot="header">
        <Row>
          <Col>
            <el-select
              style="width: 180px"
              v-model="trendFormData.field"
              size="small"
            >
              <el-option
                v-for="(item, index) in overallSituations"
                :key="'options' + index"
                :value="item.value"
                v-show="item.value !== 'total_repurchase_cv'"
                :label="item.label"
              ></el-option>
            </el-select>
          </Col>
          <Col span="12" style="margin: 0 10px">
            <CustomDatePicker
              :is-new-dia="isNewDia"
              :isDisableDate="isDisableDate"
              v-model="trendTime"
              :propTime="localPropTime"
              :prop-date-type="localPropDateType"
              @getDateType="getDateType"
            ></CustomDatePicker>
          </Col>
          <!--          <Col span="8" >-->
          <!--            <el-select size="medium"  placeholder="请选择诊所开业时长">-->
          <!--              <el-option v-for="item in overallSituations"  :key="item.value" :value="item.value" :label="item.label"></el-option>-->
          <!--            </el-select>-->
          <!--          </Col>-->
        </Row>
      </div>
      <div class="OverallDetailModal-content">
        <div class="OverallDetailModal-content-h flex">
          <div
            class="content-item flex-1 flex"
            v-for="(item, index) in allOverList"
            :key="index"
          >
            <div style="width: 100%">
              <h5 class="title">{{ item.title }}</h5>
              <div class="value">
                <span class="price">
                  <span v-if="conceal && index == 1">-</span>
                  <span v-else
                    ><span class="symbol" v-if="isMoney">￥</span>
                    {{ item.value }}</span
                  >
                </span>
                <p style="margin-top: 6px">
                  <span
                    class="compare"
                    v-if="item.compare || item.compare === 0"
                    :style="getColor(item.compare)"
                  >
                    <Icon
                      v-if="item.compare"
                      :type="
                        item.compare > 0
                          ? 'md-arrow-dropup'
                          : 'md-arrow-dropdown'
                      "
                      size="15"
                    />
                    <span v-if="item.compare != 0"
                      >{{ isMoney ? "￥ " : ""
                      }}{{ Math.abs(item.compare) }}</span
                    >
                    <span style="text-align: center" v-if="item.compare == 0"
                      >-</span
                    >
                  </span>
                </p>
              </div>
            </div>

            <div class="line" v-show="index < 3"></div>
          </div>
        </div>
        <div class="line-chart-wrapper" style="position: relative">
          <h4 class="f-title">
            诊所排行（共{{ clinic_rank?.length || 0 }}家诊所）
          </h4>
          <div v-if="clinic_rank?.length">
            <div
              class="hidden-scroll"
              style="height: 320px; overflow-y: scroll"
            >
              <div class="chart-view">
                <chart-view
                  ref="funnel-line"
                  :chart-option="chartOptions"
                  :height="autoHeight"
                ></chart-view>
              </div>
            </div>
            <div class="show-more" v-if="hasMore" @click="showMore">
              <a
                >{{ isShowMore ? "收起" : "展开全部诊所" }}
                <Icon :type="isShowMore ? 'ios-arrow-up' : 'ios-arrow-down'" />
              </a>
            </div>
          </div>
          <div v-else class="empty">暂无数据</div>
          <Spin fix v-if="isLoading"> </Spin>
        </div>
      </div>

      <div slot="footer">
        <Button @click="closeModal">关闭</Button>
      </div>
    </Modal>
  </div>
</template>

<script>
import CustomDatePicker from "../CustomDatePicker/CustomDatePickerDia";
import S from "utils/util";
import {debounce, cloneDeep} from "lodash";
export default {
  name: "OverallDetailModal",
  mixins: [],

  components: {CustomDatePicker},

  props: {
    visible: {
      type: Boolean,
      default: true,
    },
    overallSituations: {
      type: Array,
      default: () => [],
    },
    currentField: {
      type: String,
      default: "",
    },
    companyId: {
      type: String,
      default: "",
    },
    propDateType: {
      type: String,
      default: "",
    },
    propTime: {
      type: Array,
      default: () => [],
    },
    isHide: {
      type: Boolean,
      default: () => false,
    },
  },

  data() {
    return {
      allOverList: [],
      trendFormData: {
        st: "",
        et: "",
        time_type: "day",
        field: "",
      },
      chartOptions: {},
      trendTime: [],
      _date_type: "",
      clinic_rank: [],
      isShowMore: false,
      autoHeight: "",
      isLoading: false,
      isNewDia: true,
      localPropDateType: "",
      localPropTime: [],
    };
  },

  computed: {
    // 对弹窗指定的数据做处理
    conceal() {
      let flag = false;
      if (this.isHide && this.trendFormData.field == "recharge_per_cv_real") {
        flag = true;
      }
      return flag;
    },
    isMoney() {
      const res = this.overallSituations.find(item => {
        return item.value === this.trendFormData.field;
      });
      return res.isMoney;
    },
    getColor(score) {
      return score => {
        console.log("-> %c score  === %o ", "font-size: 15px", score);
        if (score == 0) {
          return {color: "#999999"};
        } else if (score > 0) {
          return {color: "#E5634B"};
        } else {
          return {color: "#0F9B5D"};
        }
      };
    },
    hasMore() {
      return this.clinic_rank?.length > 7;
    },
    isDisableDate() {
      const blackList = [
        "repurchase_cv",
        "customer_total",
        "serv_not_used_cv",
        "recharge_grand_cv",
        "recharge_grand_real_amount",
        "recharge_grand_given_amount",
        "recharge_balance",
      ];
      return blackList.includes(this.trendFormData.field);
    },
    curTitle() {
      const item = this.overallSituations.filter(
        item => item.value === this.trendFormData.field,
      );
      return item?.length ? item[0]?.label : "";
    },
  },

  watch: {
    propTime: {
      handler(val) {
        this.localPropTime = cloneDeep(val);
      },
    },
    propDateType: {
      handler(val) {
        this.localPropDateType = val;
      },
    },
    currentField: {
      handler(val) {
        this.trendFormData.field = val;
      },
    },
    "trendFormData.field": {
      handler(val) {
        this.dateChange();
      },
    },
    visible(val) {
      val && this.dateChange();
    },
    trendTime: {
      handler(val) {
        console.log("-> %c val  === %o ", "font-size: 15px", val);
        if (val && val[0]) {
          this.isNewDia = false;
          this.trendFormData.st = val[0];
          this.trendFormData.et = val[1];
          this.setDateType();
          if (this.trendFormData.field) {
            this.dateChange();
          }
        }
      },
    },
  },

  created() {},

  mounted() {},

  destroyed() {},

  methods: {
    dateChange: debounce(function () {
      console.log(21312);
      const params = {
        ...this.trendFormData,
      };
      this.isLoading = true;
      this.$api
        .getProfile(params)
        .then(res => {
          console.log("-> %c res  === %o ", "font-size: 15px", res);
          this.clinic_rank = res.clinic_rank;
          this.initChart();
          this.handleAllover(res.current, res.yesterday);
        })
        .finally(() => {
          this.isLoading = false;
        });
    }),
    initChart() {
      this.isShowMore = false;
      this.$nextTick(() => {
        const chunkOptions = this.$lodash.chunk(this.clinic_rank, 7);
        this.autoHeight = chunkOptions[0]?.length * 45 + 50 + "px";
        console.log(
          "-> %c chunkOptions  === %o ",
          "font-size: 15px",
          chunkOptions,
        );
        chunkOptions?.length && this.handleVertical(chunkOptions[0], false);
      });
    },
    handleAllover(current, yesterday) {
      this.allOverList[0] = {
        value: current.sum,
        title: this.curTitle,
      };
      this.allOverList[1] = {
        value: current.avg,
        title: this.curTitle + "均值",
      };
      this.allOverList[2] = {
        value: current.max,
        title: "诊所最高" + this.curTitle,
        compare: S.mathSub(current.max, current.avg),
      };
      this.allOverList[3] = {
        value: current.min,
        title: "诊所最低" + this.curTitle,
        compare: S.mathSub(current.min, current.avg),
      };
      console.log(this.allOverList);
    },
    getDateType(getDateType) {
      this._date_type = getDateType;
    },
    setDateType() {
      if (this._date_type !== "daterange") {
        this.trendFormData.time_type = this._date_type;
      } else {
        this.trendFormData.time_type = "";
      }
    },
    closeModal() {
      this.$emit("update:visible", false);
    },
    showMore() {
      this.isShowMore = !this.isShowMore;
      const chunkOptions = this.$lodash.chunk(this.clinic_rank, 7);
      console.log(
        "-> %c chunkOptions  === %o ",
        "font-size: 15px",
        chunkOptions,
      );
      if (this.isShowMore) {
        this.autoHeight = this.clinic_rank?.length * 45 + 50 + "px";
      } else {
        this.autoHeight = chunkOptions[0]?.length * 45 + 50 + "px";
      }
      this.$nextTick(() => {
        if (this.isShowMore) {
          this.handleVertical(this.clinic_rank);
        } else {
          this.handleVertical(chunkOptions[0], true);
        }
      });
    },
    handleVertical(options, isMore) {
      let yData = options.map(item => item.name);
      let XData = options.map(item => ({
        value: item.value,
        itemStyle: {color: "#D5DDED"},
      }));
      this.chartOptions = this.$eChartFn.customHorizontaBar({
        tooltip: {
          trigger: "item",
          confine: true,
          formatter: params => {
            let data = params;
            console.log("-> %c params  === %o ", "font-size: 15px", params);
            return `<div style="font-size: 14px;">${data.name}   <span>${
              this.isMoney ? "￥ " : ""
            }</span> <span>${data.value}</span></div>`;
          },
        },
        grid: {
          top: "16px",
          left: "4%",
          right: "10%",
          bottom: "20px",
        },
        yAxis: {
          inverse: true,
          axisLine: {
            show: false,
          },
          data: yData,
        },

        xAxis: {
          axisLine: {
            show: false,
          },
          axisTick: {
            show: false,
          },
          splitLine: {
            show: false,
          },
        },
        series: [
          {
            barWidth: 18, //柱图宽度
            barCategoryGap: "50%", //柱图间距
            data: XData,
            label: {
              formatter: this.isMoney ? "￥ {c}" : "{c}",
            },
          },
        ],
      });
    },
    handleVisibleChange(visible) {
      if (!visible) {
        this.$emit("update:visible", false);
        this.trendFormData.currentField = "";
        this.autoHeight = "";
        this.isShowMore = false;
        this.clinic_rank = [];
        this.trendTime = [];
        this.trendFormData.field = this.currentField;
        this.localPropDateType = "";
        this.localPropTime = [];
      } else {
        this.isNewDia = true;
        this.localPropDateType = this.propDateType;
        this.localPropTime = this.propTime;
        // if ( !this.clinic_rank.length ) {
        //   this.dateChange()
        // }
      }
    },
  },
};
</script>

<style scoped lang="less">
.OverallDetailModal-content {
  .OverallDetailModal-content-h {
    .content-item {
      padding-left: 20px;

      &:last-of-type {
        border-right: none;
      }

      .title {
        font-size: 12px;
        font-weight: 400;
        color: #444444;
        line-height: 17px;
      }

      .value {
        margin-top: 12px;

        .symbol {
          font-size: 15px;
          font-weight: 500;
          color: #000000;
          line-height: 18px;
        }

        .price {
          margin-top: 12px;
          font-size: 24px;
          font-weight: 500;
          color: #000000;
          line-height: 29px;
        }

        .compare {
          font-size: 12px;
          font-weight: 400;
          color: #0f9b5d;
          line-height: 17px;
        }
      }
    }
  }

  .line-chart-wrapper {
    margin-top: 30px;

    .f-title {
      font-size: 14px;
      font-weight: 600;
      color: #000000;
      line-height: 20px;
      padding-bottom: 10px;
    }

    .show-more {
      text-align: center;
    }
  }
}

p {
  margin-bottom: 0;
}

.line {
  background: #e6e6e6;
  width: 1px;
  height: 50px;
  display: inline-block;
}
</style>
