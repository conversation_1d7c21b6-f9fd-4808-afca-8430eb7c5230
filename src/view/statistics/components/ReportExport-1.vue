<template>
  <div>
    <Modal :value="visible" :title="title" :mask-closable="false" @on-visible-change="visibleChange" width="600px">
      <div slot="header">
        <div>
          <span>正在导出:</span>
          <span v-if="title" class="titleText">{{ title }}</span>
        </div>
      </div>

      <div class="content">
        <Form ref="formData" label-colon :model="formData" :rules="formDataRuleLine" :label-width="100">
          <FormItem prop="date_type" label="统计维度">
            <RadioGroup v-model="formData.date_type" @on-change="changeDateType">
              <Radio
                :label="item.key"
                v-for="(item, index) in dimensionList"
                v-if="type !== 'PROD_COST_DETAIL' || (type == 'PROD_COST_DETAIL' && item.key == 'month')"
                :key="index"
                >{{ item.value }}</Radio
              >
            </RadioGroup>
          </FormItem>
          <FormItem prop="st" label="查询时间">
            <DateWeekRange
              autocomplete="off"
              v-show="formData.date_type === 'week'"
              v-model="timeRange"
              :clearable="false"
              range-separator="-"
              style="width: 350px"
              start-placeholder="开始周"
              end-placeholder="结束周"
              size="small"
              :picker-options="dateTypeInfo.dateOptions"
              format="yyyy第WW周"
              value-format="yyyy-MM-dd"
              @focus="focusClearRange"
              popper-class="custom-date-picker"
              :key="formData.date_type + 'week'"
            ></DateWeekRange>
            <el-date-picker
              autocomplete="off"
              v-show="formData.date_type !== 'week'"
              v-model="timeRange"
              ref="dateMonthPicker"
              size="small"
              :key="formData.date_type"
              style="width: 350px"
              :type="dateTypeInfo.type"
              :picker-options="dateTypeInfo.dateOptions"
              range-separator="-"
              :value-format="dateTypeInfo.valueFormat"
              :start-placeholder="dateTypeInfo.startPlaceholder"
              :end-placeholder="dateTypeInfo.endPlaceholder"
              @focus="focusClearRange"
              @blur="datePickerBlurEvent"
              @change="changeDateRange"
              popper-class="custom-date-picker"
            >
            </el-date-picker>
          </FormItem>

          <FormItem label="统计范围" v-if="visible" :prop="type === 'PROD_COST_DETAIL' ? 'clinic_id' : ''">
            <clinic-search
              v-model="formData.clinic_id"
              :showAll="type === 'PROD_COST_DETAIL' ? false : true"
              @change="v => clickItem(v, 'clinic')"
              class="select-403"
              is-clearable
              :extra-params="{ scope: 'department' }"
            ></clinic-search>
          </FormItem>
        </Form>
      </div>

      <div slot="footer">
        <Button type="default" @click="cancel">取消</Button>
        <Button type="primary" :loading="loading" @click="success">导出</Button>
      </div>
    </Modal>
  </div>
</template>

<script>
import { DateWeekRange } from '@/components/DateWeekRange';
import moment from 'moment';
import DownloadExcel from '@/mixins/downloadExcel';
import clinicSearch from '_c/clinic-search/k-clinic-search_data';
let initFormData = {
  st: '',
  et: '',
  date_type: 'day',
  type: '',
  clinic_id: ''
};
export default {
  name: 'ReportExport',
  mixins: [DownloadExcel],
  components: {
    DateWeekRange,
    clinicSearch
  },
  model: {
    prop: 'visible',
    event: 'input'
  },
  props: {
    title: {
      type: String,
      default: '报表导出'
    },
    visible: {
      type: Boolean,
      default: false
    },
    type: {
      type: String,
      default: ''
    },
    apiName: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      timeRange: [], // 导出时间范围
      loading: false,
      // 统计维度
      dimensionList: [
        { key: 'day', value: '日报' },
        { key: 'week', value: '周报' },
        { key: 'month', value: '月报' }
      ],
      formData: { ...initFormData },
      formDataRuleLine: {
        date_type: [{ required: true, message: '请选择统计维度', trigger: 'blur' }],
        st: [{ required: true, message: '请选择查询日期', trigger: 'blur' }]
      },
      dateOptions: {
        disabledDate(time) {
          return time.getTime() > Date.now();
        }
      },
      dateTypeInfo: {
        type: 'daterange',
        startPlaceholder: '开始日期',
        endPlaceholder: '结束日期',
        dateOptions: {
          disabledDate: times => {
            return this.updatedPickerOptions(times);
          },
          onPick: time => {
            this.handleDateLimit(time);
          },
          firstDayOfWeek: 1
        },
        valueFormat: 'yyyy-MM-dd'
      },
      handlePickerDate: '',
      downloadApiName: '',

      prodCostTipVisible: false, // 货品成本提示拦截弹窗
      is_finished_confirm: ''
    };
  },
  computed: {},
  watch: {
    timeRange(val) {
      if (val && this.formData.date_type === 'week') {
        const et = val[1];
        const firstDay = moment().weekday(0).format('YYYY-MM-DD');
        if (moment(et).isBefore(firstDay)) {
          this.formData.st = this.timeRange[0];
          this.formData.et = this.timeRange[1];
        } else {
          this.formData.st = val[0];
          this.formData.et = moment().weekday(6).format('YYYY-MM-DD');
        }
      }
    },
    visible(val) {
      if (val) {
        this.dateTypeInfo.type = 'daterange';
        this.dateTypeInfo.startPlaceholder = '开始日期';
        this.dateTypeInfo.endPlaceholder = '结束日期';
        this.dateTypeInfo.valueFormat = 'yyyy-MM-dd';
        if (this.type === 'PROD_COST_DETAIL') {
          // this.formData.date_type = 'month';
          this.changeDateType('month');
        }
      }
    }
  },
  created() {
    this.downloadApiName = this.apiName;
  },
  mounted() {},
  beforeDestroy() {},
  methods: {
    toProdCost() {
      this.cancel();
      this.$router.push('/stock/product-cost/list');
    },
    datePickerBlurEvent() {
      this.$refs.dateMonthPicker.picker &&
        (this.$refs.dateMonthPicker.picker.$children[0].rangeState.selecting = false);
    },
    changeDateRange(time) {
      if (time) {
        this.formData.st = time[0];
        this.formData.et = time[1];
        if (time[0]) {
          this.$refs.formData.validateField('st');
        }
      }
    },

    clickItem(id, type) {
      if (type === 'company') {
        this.formData.clinic_id = '';
      }
      if (this.type === 'PROD_COST_DETAIL') {
        this.$refs.formData.validateField('clinic_id');
        this.getCostStatus();
      }
    },

    focusClearRange() {
      this.handlePickerDate = '';
    },
    // 动态控制可选时间范围
    updatedPickerOptions(times) {
      const isProdEnv = process.env.VUE_APP_NODE_ENV === 'production';
      const canSelectTime = isProdEnv ? 60 * 60 * 24 * 1000 : 0;
      if (this.handlePickerDate === '') {
        // 货品成本的报表，拦截2023.7之前的月份，当前是初始化，未选择第一个日期时拦截
        if (this.type === 'PROD_COST_DETAIL') {
          let interceptDate = moment('2023-07').valueOf();
          return times.getTime() < interceptDate || times.getTime() > Date.now() - canSelectTime;
        }
        return times.getTime() > Date.now() - canSelectTime;
      }

      let maxTime = moment(this.handlePickerDate).add(11, 'months').valueOf();
      let minTime = moment(this.handlePickerDate).subtract(11, 'months').valueOf();

      // 货品成本的报表，拦截2023.7之前的月份，当前是选择了第一个月，往前后动态增加一个月的拦截
      if (this.type === 'PROD_COST_DETAIL') {
        let interceptDate = moment('2023-07').valueOf();
        minTime = interceptDate;
      }
      return times.getTime() > maxTime || times.getTime() < minTime || times.getTime() > Date.now() - canSelectTime;
    },
    handleDateLimit(time) {
      this.handlePickerDate = new Date(time.minDate).getTime();
    },
    changeDateType(type) {
      this.formData.date_type = type;
      this.timeRange = null;
      this.formData.st = '';
      this.formData.et = '';
      if (type === 'day') {
        this.dateTypeInfo.type = 'daterange';
        this.dateTypeInfo.startPlaceholder = '开始日期';
        this.dateTypeInfo.endPlaceholder = '结束日期';
        this.dateTypeInfo.valueFormat = 'yyyy-MM-dd';
      } else if (type === 'month') {
        this.dateTypeInfo.type = 'monthrange';
        this.dateTypeInfo.startPlaceholder = '开始月份';
        this.dateTypeInfo.endPlaceholder = '结束月份';
        this.dateTypeInfo.valueFormat = 'yyyy-MM';
      }
    },
    visibleChange(val) {
      if (val) {
        this.getCostStatus();
      } else {
        this.cancel();
      }
    },

    isOneYearApart(st, et) {
      let st_time_stamp = new Date(st);
      let et_time_stamp = new Date(et);
      // 计算两个日期相差的天数
      const timeDiff = Math.abs(et_time_stamp.getTime() - st_time_stamp.getTime());
      const daysDiff = Math.ceil(timeDiff / (1000 * 3600 * 24));

      // 将相差的天数转换为年数
      const yearsDiff = Math.floor(daysDiff / 365);

      // 判断相差的年数是否为 1
      return yearsDiff <= 1;
    },

    // 货品成本是否可以导出
    getCostStatus() {
      this.$api.isCostFinished().then(res => {
        this.is_finished_confirm = res.is_finished_confirm;
      });
    },

    // 关闭弹窗
    cancel() {
      this.clearData();
      this.$emit('input', false);
    },

    // 导出
    export() {
      // if (!this.downloadApiName) {
      //   this.$Message.error('请先传入请求地址');
      //   return;
      // }
      if (!this.isOneYearApart(this.formData.st, this.formData.et)) {
        this.$Message.error('查询日期不能超过一年');
        return;
      }
      const params = {
        ...this.formData,
        type: this.type
      };
      this.downloadExcel(params, () => {
        this.cancel();
      });
    },

    // 点击确定按钮
    success() {
      this.$refs.formData.validate(valid => {
        if (valid) {
          if (this.type === 'PROD_COST_DETAIL' && this.is_finished_confirm !== '1') {
            this.prodCostTipVisible = true;
          } else {
            this.export();
          }
        } else {
          console.log('-> %c valid  ===    %o', 'font-size: 15px;color: #F56C6C ;', valid);
        }
      });
    },

    // 清除数据
    clearData() {
      this.formData = { ...initFormData };
      this.timeRange = [];
      this.loading = false;
      this.$refs.formData.resetFields();
    },

    // 处理时间
    handleTimeChange(times) {
      if (times) {
        this.formData.st = times[0];
        this.formData.et = times[1];
      } else {
        this.formData.st = '';
        this.formData.et = '';
      }
    }
  },
  filters: {}
};
</script>
<style lang="less" scoped>
.titleText {
  margin-left: 4px;
  font-size: 14px;
  font-weight: bold;
}
</style>
<style lang="less" scoped>
::v-deep .ivu-modal-body {
  height: 300px;
  overflow-y: auto;
}
</style>
<style lang="less">
.custom-date-picker {
  z-index: 5000000 !important;
}
</style>
