<template>
  <Modal class-name="dataReportModel" :value="visible" :title="title" :mask-closable="false" @on-visible-change="visibleChange" width="600px">
    <div slot="header">
      <div>
        <span>正在导出:</span>
        <span v-if="title" class="titleText">{{ title }}</span>
      </div>
    </div>

    <div class="content">
      <Form ref="formData" label-colon :model="formData" :rules="formDataRuleLine" :label-width="100">
        <FormItem prop="st" label="查询时间">
          <!--            <el-date-picker-->
          <!--              v-model="timeRange"-->
          <!--              @change="handleTimeChange"-->
          <!--              value-format="yyyy-MM-dd"-->
          <!--              :picker-options="dynamicOptions"-->
          <!--              size="small"-->
          <!--              style="width: 403px"-->
          <!--              type="daterange"-->
          <!--              range-separator="至"-->
          <!--              start-placeholder="开始日期"-->
          <!--              end-placeholder="结束日期"-->
          <!--            >-->
          <!--            </el-date-picker>-->

          <el-date-picker
              autocomplete="off"
              v-model="monthTime"
              :key="type"
              ref="dateMonthPicker"
              :type=" type === 'PROD_COST_DETAIL' ? 'monthrange' : 'month'"
              value-format="yyyy-MM"
              size="small"
              popper-class="dateExportMonthPanel"
              :picker-options="dateTypeInfo.dateOptions"
              style="width: 403px"
              @change="handleTimeChange"
              placeholder="选择月"
              @focus="focusClearRange"
              @blur="datePickerBlurEvent"
              start-placeholder="开始月份"
              end-placeholder="结束月份"
              :append-to-body='true'
          >
          </el-date-picker>
        </FormItem>

        <div v-if="reportIsNeedType">
          <FormItem label="统计范围"  v-if="visible" :prop=" type === 'PROD_COST_DETAIL' ? 'clinic_id' : ''">
            <clinic-search
                v-model="formData.clinic_id"
                :showAll=" type === 'PROD_COST_DETAIL' ? false : true"
                @change="v => clickItem(v, 'clinic')"
                class="select-403"
                is-clearable
                :extra-params="{ scope: 'department' }"
            ></clinic-search>
          </FormItem>
        </div>
      </Form>
    </div>

    <div slot="footer">
      <Button type="default" @click="cancel">取消</Button>

      <Tooltip v-if="isDisabledExport && formData.clinic_id" placement="top" max-width="300" content="诊所必须完成期初成本初始化，才能导出相关报表">
        <Button class="ml8" type="primary" :loading="loading" @click="success" :disabled="isDisabledExport">导出</Button>
      </Tooltip>
      <Button v-else type="primary" :loading="loading" @click="success" :disabled="isDisabledExport">导出</Button>
    </div>
  </Modal>
</template>

<script>
import { getEnv } from '@/utils/runtime';
import clinicSearch from '_c/clinic-search/k-clinic-search_data';
import moment from 'moment';
let initFormData = {
  st: '',
  et: '',
  clinic_id: ''
};
export default {
  name: 'ReportExport',
  components: {
    clinicSearch
  },
  mixins: [],
  model: {
    prop: 'visible',
    event: 'input'
  },
  props: {
    title: {
      type: String,
      default: '报表导出'
    },
    visible: {
      type: Boolean,
      default: () => false
    },
    // 导出接口
    apiName: {
      type: String,
      default: ''
    },
    // 是否需要显示省公司，诊所
    reportIsNeedType: {
      type: Boolean,
      default: () => true
    },
    // 报表类型
    type: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      timeRange: '', // 导出时间范围
      monthTime: '',
      value: '',
      loading: false,
      maxDate: Date.now(),
      minDate: null,

      formData: { ...initFormData },
      formDataRuleLine: {
        st: [{ required: true, message: '请选择查询日期', trigger: 'blur' }],
        clinic_id: [{ required: true, message: '请选择诊所', trigger: 'change' }]
      },

      dateTypeInfo: {
        dateOptions: {
          disabledDate: times => {
            return this.updatedPickerOptions(times);
          },
          onPick: ({ maxDate, minDate }) => {
            this.handleDateLimit({ maxDate, minDate });
          },
          firstDayOfWeek: 1
        },
        valueFormat: 'yyyy-MM-dd'
      },

      handlePickerDate: '',
      is_finished_confirm: '',
    };
  },
  computed: {
    isDisabledExport () {
      return this.type === 'PROD_COST_DETAIL' && this.is_finished_confirm !== '1'
    },
  },
  watch: {
    'formData.st'(val) {
      this.minDate = null;
      this.maxDate = new Date();
    }
  },
  created() {},
  mounted() {
    this.openCurrentDay();
  },
  beforeDestroy() {},
  methods: {
    // 货品成本是否可以导出
    getCostStatus() {
      let params = {
        clinic_id: this.formData.clinic_id
      }
      this.$api.isCostFinished(params).then(res => {
        this.is_finished_confirm = res.is_finished_confirm
      });
    },

    datePickerBlurEvent () {
      this.$refs.dateMonthPicker.picker &&( this.$refs.dateMonthPicker.picker.$children[0].rangeState.selecting = false);
    },
    focusClearRange() {
      this.handlePickerDate = '';
    },
    // 动态控制可选时间范围
    updatedPickerOptions(times) {
      const isProdEnv = process.env.VUE_APP_NODE_ENV === 'production';
      const canSelectTime = isProdEnv ? 60 * 60 * 24 * 1000 : 0;
      if (this.handlePickerDate == '') {
        // 货品成本的报表，拦截2023.7之前的月份，当前是初始化，未选择第一个日期时拦截
        if ( this.type === 'PROD_COST_DETAIL' ) {
          let interceptDate = moment('2023-07').valueOf();
          return times.getTime() < interceptDate || times.getTime() > Date.now() - canSelectTime;;
        }
        return times.getTime() > Date.now() - canSelectTime;
      }
      let maxTime = moment(this.handlePickerDate).add(11, 'months').valueOf();
      let minTime = moment(this.handlePickerDate).subtract(11, 'months').valueOf();

      // 货品成本的报表，拦截2023.7之前的月份，当前是选择了第一个月，往前后动态增加一个月的拦截
      if ( this.type === 'PROD_COST_DETAIL' ) {
        let interceptDate = moment('2023-07').valueOf();
        minTime = interceptDate
      }

      return times.getTime() > maxTime || times.getTime() < minTime || times.getTime() > Date.now() - canSelectTime;
    },
    handleDateLimit(time) {
      console.log("-> time", time);
      this.handlePickerDate = new Date(time.minDate).getTime();
    },

    visibleChange(val) {
      if (!val) {
        this.cancel();
      }
    },

    // 默认在测试,74环境打开今日
    openCurrentDay() {
      if (getEnv() === 'production') {
        this.maxDate = new Date() - 86400000;
      } else {
        this.maxDate = new Date();
      }
    },

    clickItem(id, type) {
      if ( type === 'company' ) {
        this.formData.clinic_id = ''
      }
      if ( this.type === 'PROD_COST_DETAIL' ) {
        this.$refs.formData.validateField('clinic_id');
        this.getCostStatus()
      }
    },

    // 关闭弹窗
    cancel() {
      this.clearData();
      this.$emit('input', false);
    },

    // 导出
    export() {
      if (!this.apiName) {
        this.$Message.error('请先传入请求地址');
        return;
      }
      let query = { ...this.formData, type: this.type };
      this.loading = true;
      this.$api[this.apiName](query)
        .then(
          res => {
            window.open(res.url, '_blank');
            this.$emit('success');
            this.cancel();
          },
          rej => this.$Message.error(rej.errmsg)
        )
        .finally(() => (this.loading = false));
    },

    // 点击确定按钮
    success() {
      this.$refs.formData.validate(valid => {
        if (valid) {
          this.export();
        }
      });
    },

    // 清除数据
    clearData() {
      this.formData = { ...initFormData };
      this.timeRange = [];
      this.monthTime = ''
      this.$refs.formData.resetFields()
    },

    // 处理时间
    handleTimeChange(times) {
      if (this.reportIsNeedType && this.type !== 'PROD_COST_DETAIL') {
        this.formData.st = times;
        this.formData.et = times;
      } else {
        if (times) {
          this.formData.st = times[0];
          this.formData.et = times[1];
        } else {
          this.formData.st = '';
          this.formData.et = '';
        }
      }
      this.$refs.formData.validateField('st')
    }
  },
  filters: {}
};
</script>
<style lang="less" scoped>
.titleText {
  margin-left: 4px;
  font-size: 14px;
  font-weight: bold;
}
</style>
<style lang="less" scoped>
::v-deep .ivu-modal-body {
  height: 300px;
  overflow-y: auto;
}
.ml8 {
  margin-left: 8px;
}
</style>

<style lang="less">
.select-403 {
   .el-select {
    width: 403px !important;
  }
}
.dateExportMonthPanel {
  z-index: 9999 !important;
}
</style>
