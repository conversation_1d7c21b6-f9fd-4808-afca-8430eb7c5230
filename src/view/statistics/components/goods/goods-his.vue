<template>
  <div class="wrapper">
    <!-- content -->
    <div class="block pt0">
      <!-- card -->
      <div class="mt16 flex flex-warp">
        <div 
          class="solid-card-block mb16" 
          v-for="(card_item, card_index) in his_card_options" 
          :key="'card'+card_index"
          :class="{'solid-bottom--none': card_item.hideBottomLine }"
          >
         <!-- @click.prevent="cardChange(card_item, card_index)" -->
          <div class="card-title flex">
            {{ card_item.label }}
            <Tooltip max-width="300" v-if="card_index == 13" content="成功为客户开具处方，不考虑处方是否支付、退款" theme="light" placement="bottom" class="custom-tooltip tooltip-left-arrow ml6">
              <p class="flex flex-item-center cursor">
                <svg-icon iconClass="tip" class="helpIcon cursor"></svg-icon>
              </p>
            </Tooltip>
          </div>
          <p class="card-content mt12 solid-right-line" v-if="card_item.isMoney">￥{{ card_item.current | number_format }}</p>
          <p class="card-content mt12 solid-right-line" v-else>{{ card_item.current }}</p>
        </div>
      </div>

      <p class="transverse-line"></p>

      <div class="mt12">
        <div class="block-title flex">
          处方类型排行
          <Tooltip max-width="300" content="统计成功交易的处方数量及关联金额，排除已退款的处方" theme="light" placement="right" class="custom-tooltip tooltip-left-arrow ml6">
            <p class="flex flex-item-center cursor">
              <svg-icon iconClass="tip" class="helpIcon cursor"></svg-icon>
            </p>
          </Tooltip>
        </div>

        <div>
          <chart-view :chart-option="normalOptions" v-if="isShowLineEcharts(normalOptions, 'line')"></chart-view>
          <div class="empty" v-else >
            暂无数据
          </div>
        </div>
      </div>

      <p class="transverse-line"></p>

      <!-- 诊疗 -->
      <div class="flex mt30">
        <div class="flex-1 right-line pr30">
          <p class="block-title">AI问诊症状分布</p>
          <div class="mt12 flex flex-item-center">
            <chart-view :chart-option="symptomSeriesData" v-if="isShowLineEcharts(symptomSeriesData, 'line')"></chart-view>
            <div class="empty" v-else >
              暂无数据
            </div>
          </div>
          
        </div>
        <div class="flex-1 ml50">
          <p class="block-title">AI辨证 分析</p>
          <div class="mt12 flex flex-item-center">
            <chart-view :chart-option="dialecticalSeriesData" v-if="isShowLineEcharts(dialecticalSeriesData, 'line')"></chart-view>
            <div class="empty" v-else >
              暂无数据
            </div>
          </div>
        </div>
      </div>

       <p class="transverse-line"></p>

      <!-- 诊疗 -->
      <div class="flex mt50">
        <div class="flex-1 right-line pr30">
          <p class="block-title height31 flex flex-item-align">诊疗方剂常用（TOP10）</p>
          <div class="mt12">
            <Table :columns="recipeCols" :data="recipe_lists" @on-sort-change="recipeSortChange" v-if="recipe_lists.length" class="custom-table-line">
              <template slot-scope="{row,index}" slot="rank">
                <div class="top-three" v-if="index<3">
                  <img src="@/assets/image/data/<EMAIL>" alt="rank1" v-show="index === 0">
                  <img src="@/assets/image/data/<EMAIL>" alt="rank2" v-show="index === 1">
                  <img src="@/assets/image/data/<EMAIL>" alt="rank3" v-show="index === 2">
                </div>
                <div style="text-align: center;color:#F0A522;" v-else>{{ index + 1 }}</div>
              </template>
              <template slot-scope="{row}" slot="content">
                {{ row.name }}
              </template>
              <template slot-scope="{row}" slot="num">
                {{ row.num }}
              </template>
            </Table>
            <div class="empty" v-else >
              暂无数据
            </div>
          </div>
        </div>
        <div class="flex-1 ml50">
          <div class="flex flex-item-between flex-item-align">
            <p class="block-title">诊疗治疗品常用排行（TOP10）</p>
            <div class="flex">
              <div 
                class="custom-tag--default ml10"
                v-for="(tag_item, tag_index) in tag_treats" 
                :key="'tag'+tag_index"
                :class="{'custom-tag--actived' : current_tag_index === tag_index, 'custom-tag--hover': current_tag_index !== tag_index }"
                @click="tagChange(tag_item, tag_index)"
              >{{ tag_item.label }}</div>
            </div>
          </div>
          <div class="mt12">
            <Table :columns="treatCols" :data="treat_lists" @on-sort-change="treatSortChange" v-if="treat_lists.length" class="custom-table-line">
              <template slot-scope="{row,index}" slot="rank">
                <div class="top-three" v-if="index<3">
                  <img src="@/assets/image/data/<EMAIL>" alt="rank1" v-show="index === 0">
                  <img src="@/assets/image/data/<EMAIL>" alt="rank2" v-show="index === 1">
                  <img src="@/assets/image/data/<EMAIL>" alt="rank3" v-show="index === 2">
                </div>
                <div style="text-align: center;color:#F0A522;" v-else>{{ index + 1 }}</div>
              </template>
              <template slot-scope="{row}" slot="content">
                {{ row.name }}
              </template>
              <template slot-scope="{row}" slot="num">
                {{ row.num }}
              </template>
            </Table>
            <div class="empty" v-else >
              暂无数据
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { goods_card_his_options, goods_his_treat_tag } from '../../data/goods_data';
import { number_format } from '@/utils/filters';
import showEcharts from "@/mixins/showEcharts";

  export default {
    name: "goodsHis",
    components: {
    },
    mixins: [showEcharts],
    props: {
      formDate: {
        type: Array,
        default: []
      },
      clinic_id: {
        type: [String, Number],
        default: ''
      },
      date_type: {
        type: String,
        default: ''
      }
    },
    data () {
      return {
        his_card_options: goods_card_his_options, // his卡片数据展示
        
        /* 处方类型排行 */ 
        normalOptions: {},

        /* ai */
        symptomSeriesData: {},
        dialecticalSeriesData: {},

        /* 诊疗 */
        recipeCols: [
          {title: '排名', slot: 'rank', width: 40, className: 'rank',align:'center'},
          {title: '方剂名称', key: 'name', align: 'center', minWidth: 200, tooltip: true},
          {title: '应用次数', key: 'num', align: 'center', width: 100, sortable: true},
        ],
        recipe_lists: [],

        treatCols: [
          {title: '排名', slot: 'rank', width: 40, className: 'rank',align:'center'},
          {title: '药名', key: 'goods_name', align: 'center', minWidth: 200, tooltip: true},
          {title: '应用次数', key: 'num', align: 'center', width: 100, sortable: true},
          {title: '库存消耗', key: 'quantity', align: 'center', width: 100, sortable: true},
          {title: '金额', key: 'money', align: 'right', width: 100, sortable: true},
        ],
        treat_lists: [],

        tag_treats: goods_his_treat_tag,
        current_tag_index: 0,
      }
    },
    computed: {
      // 底部bottom隐藏
      isBottomNone () {
        return (card_index) => {
          if ( card_index == 10 || card_index == 11 || card_index == 12 || card_index == 13 ) {
            return true
          }else {
            return false
          }
        }
      }
    },
    watch: {
      'formDate': {
        immediate: true,
        deep: true,
        handler (val) {
          if ( val && val[0] ) {
            this.initData()
          }
        }
      },
      'clinic_id': {
        deep: true,
        handler (val) {
          this.initData()
        }
      }
    },
    created() {

    },
    mounted() {
    },
    methods: {
      // 初始化数据
      initData () {
        this.getGoodsHisOrderanalysis()
        this.getGoodsPresrank()
        this.getGoodsPrestreatrank()
      },

      // 诊疗方剂常用排序事件
      recipeSortChange (column, key, order) {
        if ( column.order == 'normal' ) {
          this.getGoodsPresrank('')
        }else{
          this.getGoodsPresrank(column.order.toUpperCase())
        }
      },
       // 诊疗治疗品常用排行排序事件
      treatSortChange (column, key, order) {
        if ( column.order == 'normal' ) {
          this.getGoodsPrestreatrank('', column.key)
        }else{
          this.getGoodsPrestreatrank(column.order.toUpperCase(), column.key)
        }
      },

      // his诊疗品tag点击事件
      tagChange ( tag_item, tag_index ) {
        this.current_tag_index = tag_index
        let current_title = tag_item.subTitle
        this.treatCols[1].title = current_title
        this.getGoodsPrestreatrank('DESC', 'money')
      },

      // 处理处方类型排行
      handleHisPresRank (dataSource, list, xName, yName) {
        let xData = []
        let yData = []
        let numData = []

        list && list.forEach( item => {
          xData.push(item[xName])
          yData.push(item[yName])
          numData.push(item.num)
        } )

        xData = xData.reverse()
        yData = yData.reverse()
        numData = numData.reverse()

        this[dataSource] = this.$eChartFn.horizontalBar( {
          grid: {
            right: '218px',
          },
          xAxis: {
            name: '单位:元',
            data: yData,
          },
          yAxis: {
            data: yData,
          },
          tooltip: {
            formatter:(a)=>{
              let numIndex = yData.indexOf(a.name)
              let val = `处方数:${numData[numIndex]}个  ￥${number_format(a.value,2)}`
              return val
            }
          },
          series: [{
            data: xData,
            label: {
              formatter:(a)=>{
                let numIndex = yData.indexOf(a.name)
                let val = `处方数:${numData[numIndex]}个   ￥${number_format(a.value,2)}`
                return val
              },
            }
          }]
        })
      },

      // 处理AI排行
      aiHandleHisPresRank (dataSource, list, xName, yName) {
        let xData = []
        let yData = []
        let percentData = []
        list && list.forEach( item => {
          xData.push(item[xName])
          yData.push(item[yName])
          percentData.push(item['percent'])
        } )

        xData = xData.reverse()
        yData = yData.reverse()
        percentData = percentData.reverse()
        this[dataSource] = this.$eChartFn.horizontalBar( {
          tooltip: {
            formatter:(a)=>{
              let text = ''
              let index = xData.indexOf(a.value)
              text = `${a.name}: ${a.value}人 占比${percentData[index]}%`
              return `${text}`
            }
          },
          xAxis: {
            data: yData,
          },
          yAxis: {
            data: yData,
          },
          series: [{
            data: xData,
            label: {
              formatter:(a)=>{
                let text = `${a.value}人`
                return text
              },
            },
          }]
        })
      },

      /* api */
      // api-his开方应用分析
      getGoodsHisOrderanalysis () {
        let params = {
          st: this.formDate[0],
          et: this.formDate[1],
          clinic_id: this.clinic_id
        }
        this.$api.getGoodsHisOrderanalysis(params).then( res => {
          this.his_card_options.forEach( item => {
            item.current = res.current[item.value]
          } )

          // 处方类型排行
          this.handleHisPresRank('normalOptions',res.his_pres_rank, 'turnover', 'type_text')

          // AI
          this.aiHandleHisPresRank('symptomSeriesData',res.ai_symptom_distribution, 'num', 'name')
          // AI
          this.aiHandleHisPresRank('dialecticalSeriesData',res.ai_dialectical_distribution, 'num', 'name')
        } )
      },
      // api-诊疗方剂常用
      getGoodsPresrank (sort = 'DESC') {
        let params = {
          st: this.formDate[0],
          et: this.formDate[1],
          sort,
          clinic_id: this.clinic_id
        }
        this.$api.getGoodsPresrank(params).then( res => {
          this.recipe_lists = res.rank
        } )
      },
      // api-诊疗治疗品常用排行
      getGoodsPrestreatrank (sort = 'DESC', sort_field) {
        let params = {
          st: this.formDate[0],
          et: this.formDate[1],
          sort,
          sort_field,
          type: this.tag_treats[this.current_tag_index].kw,
          clinic_id: this.clinic_id
        }
        this.$api.getGoodsPrestreatrank(params).then( res => {
          this.treat_lists = res.rank
        } )
      },

    },
    filters: {

    }
  }
</script>
<style lang="less" scoped>
@import url('../../style/common.less');
</style>