<template>
  <div class="wrapper">
    <!-- Header -->
    <header class="head-block ceiling flex flex-item-between">
      <div class="flex flex-item-align">
        <p>统计时间：</p>
        <customDatePicker v-model="formDate" @getDateType="getDateType"></customDatePicker>
      </div>
      <clinic-search
          v-model="clinic_id"
          className="statistics-sel"
          placeholder="全部诊所"
          @change="changeClinicId"
          is-clearable
          :extra-params="{ scope: 'department' }"
      />
    </header>

    <!-- 整体情况 -->
    <div class="block">
      <h4 class="block-title flex flex-align-center">
        <p>整体情况</p>
        <Tooltip max-width="300" theme="light" placement="bottom" :offset="100" class="custom-tooltip tooltip-left-arrow ml6">
          <div slot="content">
            <p v-for="(item, index) in situation_tooltip_list" :key="index+'tooltip'">{{ item }}</p>
          </div>
          <p class="flex flex-item-center cursor">
            <svg-icon iconClass="tip" class="helpIcon cursor"></svg-icon>
          </p>
        </Tooltip>
      </h4>
      

      <!-- 卡片数据 -->
      <div class="mt16 flex flex-warp">
        <div 
          class="card-block mb16" 
          v-for="(card_item, card_index) in customer_card_options" 
          :key="'card'+card_index"
          :class="{
            'card-block--actived' : current_card_index === card_index ,
            'card-block--hover':  card_item.readOnly !== true,
            'cursor': card_item.readOnly !== true,
          }"
         @click.prevent="cardChange(card_item, card_index)"
          >
          <p class="card-title card-title flex flex-item-between pr20" >
            <span>{{ card_item.label }}</span>
            <svg-icon
              v-if="!clinic_id && card_item.value !== 'total_repurchase_cv'"
              @click.stop="arrowEvent(card_item, card_index)"
              iconClass="circleArrow"
              class="circle-arrow cursor"
              :class="{'circle-arrow--actived': current_card_index === card_index}"
            ></svg-icon>
          </p>
          <p class="card-content mt12" v-if="card_item.isMoney">￥{{ card_item.current | number_format(2) }}</p>
          <p class="card-content mt12" v-else>{{ card_item.current || 0 }}</p>
          <div v-show="date_type&&!card_item.hideCompare">
            <p class="card-tip mt6" v-if="card_item.isMoney">{{ custom_lastText }}：￥{{ card_item.last | number_format(2) }}</p>
            <p class="card-tip mt6" v-else>{{ custom_lastText }}：{{ card_item.last || 0 }}</p>
          </div>
        </div>
      </div>

      <!-- 折线趋势图 -->
      <div class="mt30">
        <chart-view height="260px" :chart-option="line_options"></chart-view>
      </div>
    </div>

    <!-- 客户消费情况分析 -->
    <div class="block mt16">
      <h4 class="block-title">客户消费情况分析</h4>

      <!-- tag -->
      <div class="mt16 flex">
        <div 
          class="custom-tag--default ml10"
          v-for="(tag_item, tag_index) in tag_consumptions" 
          :key="'tag'+tag_index"
          :class="{'custom-tag--actived' : current_tag_index === tag_index, 'custom-tag--hover': current_tag_index !== tag_index }"
          @click="tagChange(tag_item, tag_index)"
        >{{ tag_item.label }}</div>
      </div>

      <!-- content -->
      <div class="mt16 flex">

        <div class="flex-1 pr30 mt30">
          <Table :columns="consumptionCols" :data="consumption_lists" v-if="consumption_lists.length" class="custom-table-line">
            <template slot-scope="{row, index}" slot="title">
              <!--              <i class="circle" :style="{borderColor:color_table_enum[index]}"></i>-->
              {{ row.title }}</span>
            </template>
            <template slot-scope="{row}" slot="rate">
              {{ row.rate }}%
            </template>
          </Table>
          <div class="empty" v-else >
            暂无数据
          </div>
        </div>

        <div class="flex-1 flex flex-item-align">
          <!-- right bar -->
          <div class="flex-1">
            <div class="width100">
              <chart-view height="240px" :chart-option="consumption_bar_options"></chart-view>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 客户到店意向分析 -->
    <!-- <div class="block mt16">
      <h4 class="block-title">客户到店意向分析-可能不需要,产品正在确认</h4>
      <div class="mt16 flex">

        <div class="flex-1 pr30 mt30">
          <Table :columns="arrivalCols" :data="arrival_lists" v-if="arrival_lists.length">
            <template slot-scope="{row}" slot="content">
              {{ row.name }}
            </template>
            <template slot-scope="{row}" slot="num">
              {{ row.num }}
            </template>
          </Table>
          <div class="empty" v-else >
            暂无数据
          </div>
        </div>

        <div class="flex-1">
          <div class="flex-1 flex">
            <div class="width100">
              <K-Pie-Chart :id="Date.parse(new Date()).toString()+'arrival_pie'" :options="arrival_bar_options" key="arrival_pie"></K-Pie-Chart>
            </div>
          </div>
        </div>
      </div>
    </div> -->

    <!-- 剩余储值金额分布 -->
    <div class="block mt16">
      <h4 class="block-title">剩余储值金额分布</h4>
      <!-- content -->
      <div class="mt30 flex">
        <div class="flex-1 pr30">
          <chart-view :chart-option="surplus_data" v-if="isShowLineEcharts(surplus_data, 'line')"></chart-view>
          <div class="empty" v-else >
            暂无数据
          </div>
        </div>

        <div class="flex-1">
          <Table class="mt16 custom-table-line" :columns="surplusCols" :data="surplus_lists" v-if="surplus_lists.length">
            <template slot-scope="{row, index}" slot="title">
              <!--              <i class="circle" :style="{borderColor:color_table_enum[index]}"></i>-->
              <span>{{ row.title }}</span>
            </template>
            <template slot-scope="{row}" slot="rate">
              {{ row.rate }}%
            </template>
          </Table>
          <div class="empty" v-else >
            暂无数据
          </div>
        </div>
      </div>
    </div>

    <!-- 待服务客户分布 -->
    <div class="block mt16">
      <h4 class="block-title">待服务客户分布</h4>
      <!-- content -->
      <div class="mt26 flex">

        <div class="flex-1 pr30">
          <div class="flex-1">
            <div class="card-show-block card-show-block--small flex">
              <div class="flex-1 right-line">
                <p class="show-title">待服务客户数(人)</p>
                <p class="show-number mt7">{{ wait_serv_info.num }}</p>
              </div>
              <div class="flex-1">
                <p class="show-title pl60">待服务卡券剩余价值</p>
                <p class="show-number mt7 pl60">￥{{ wait_serv_info.money }}</p>
              </div>
            </div>
          </div>
          
          <Table class="mt16 custom-table-line" :columns="serviceCols" :data="service_lists" v-if="service_lists.length">
            <template slot-scope="{row, index}" slot="title">
<!--              <i class="circle" :style="{borderColor:color_table_enum[index]}" v-if="index !== (service_lists.length -1)"></i>-->
              <span>{{ row.title }}</span>
            </template>
            <template slot-scope="{row}" slot="rate">
              {{ row.rate }}%
            </template>
          </Table>
          <div class="empty" v-else >
            暂无数据
          </div>
        </div>

        <div class="flex-1 flex flex-item-align">
          <!-- right bar -->
          <div class="flex-1 flex">
            <div class="width100">
              <chart-view height="240px" :chart-option="service_bar_options"></chart-view>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 客户首购周期分布 -->
    <div class="block mt16">
      <h4 class="block-title flex flex-align-center">
        <p>客户首购周期分布</p>
        <Tooltip content="客户注册后，距离首次消费的时长" max-width="300" theme="light" placement="bottom" class="custom-tooltip ml6">
          <p class="flex flex-item-center cursor">
            <svg-icon iconClass="tip" class="helpIcon cursor"></svg-icon>
          </p>
        </Tooltip>
      </h4>

      <div class="flex">
        <div class="flex-1">
          <chart-view :chart-option="first_data" v-if="isShowLineEcharts(first_data, 'line')"></chart-view>
          <div class="empty" v-else >
            暂无数据
          </div>
        </div>
        <div class="flex-1">
          <Table class="mt16 custom-table-line" :columns="firstCols" :data="first_lists" v-if="first_lists.length">
            <template slot-scope="{row, index}" slot="title">
              <!--              <i class="circle" :style="{borderColor:color_table_enum[index]}"></i>-->
              <span>{{ row.title }}</span>
            </template>
            <template slot-scope="{row}" slot="rate">
              {{ row.rate }}%
            </template>
          </Table>
          <div class="empty" v-else >
            暂无数据
          </div>
        </div>
      </div>
    </div>

    <p class="block-title mt16">客户画像</p>

    <!-- 客户来源渠道排行 -->
    <div class="block flex mt16">
      <div class="flex-1 pr30">
        <h4 class="block-title">客户来源渠道排行</h4>
        <div class="mt12">
          <Table :columns="fromCols" :data="from_lists" v-if="from_lists.length" class="custom-table-line">
            <template slot-scope="{row,index}" slot="rank">
              <div class="top-three" v-if="index<3">
                <img src="@/assets/image/data/<EMAIL>" alt="rank1" v-show="index === 0">
                <img src="@/assets/image/data/<EMAIL>" alt="rank2" v-show="index === 1">
                <img src="@/assets/image/data/<EMAIL>" alt="rank3" v-show="index === 2">
              </div>
              <div style="color:#F0A522;marginLeft: 5px" v-else>{{ index + 1 }}</div>
            </template>
            <template slot-scope="{row, index}" slot="text" class="flex">
              <!--              <i class="circle" :style="{borderColor:color_table_enum[index]}"></i>-->
              <span>{{ row.text }}</span>
            </template>
            <template slot-scope="{row}" slot="rate">
              {{ row.rate }}%
            </template>
          </Table>
          <div class="empty" v-else >
            暂无数据
          </div>
        </div>
      </div>

      <div class="flex-1 flex flex-item-align">
        <!-- right bar -->
        <div class="flex-1 flex ">
          <div class="width100">
            <chart-view height="240px" :chart-option="from_bar_options"></chart-view>
          </div>
        </div>
      </div>
    </div>

    <!-- 客户性别分布 -->
    <div class="block mt16 flex">
      <div class="flex-1 pr30">
        <h4 class="block-title">客户性别分布</h4>
        <!-- bar -->
        <div class="flex-1 flex right-line mt16 pr30 height100">
          <div class="width50 flex flex-item-align">
            <chart-view height="240px" :chart-option="sex_bar_options"></chart-view>
          </div>
          <div class="width50 flex flex-item-align">
            <Table :columns="sexBarCols" :data="sex_bar_list" class="custom-table">
              <template slot-scope="{row,index}" slot="text" class="flex">
                <!--              <i class="circle" :style="{borderColor:color_table_enum[index]}"></i>-->
                <span>{{ row.text }}</span>
              </template>
              <template slot-scope="{row,index}" slot="rate" class="flex">
                <span>{{ row.rate }}%</span>
              </template>
            </Table>
          </div>
        </div>
      </div>

      <div class="flex-1">
        <h4 class="block-title">客户年龄分布</h4>
        <!-- bar -->
        <div class="flex-1 mt16 flex">
          <div class="width50 flex flex-item-align">
            <chart-view height="240px" :chart-option="age_bar_options"></chart-view>
          </div>
          <div class="width50 flex flex-item-align">
            <Table :columns="ageBarCols" :data="age_bar_list" class="custom-table">
              <template slot-scope="{row,index}" slot="age_text" class="flex">
                <!--              <i class="circle" :style="{borderColor:color_table_enum[index]}"></i>-->
                <span>{{ row.age_text }}</span>
              </template>
              <template slot-scope="{row,index}" slot="rate" class="flex">
                <span>{{ row.rate }}%</span>
              </template>
            </Table>
          </div>
        </div>
      </div>
    </div>
    <OverallDetailModal :propDateType="propDateType" :propTime="formDate"   :overallSituations="customer_card_options" :visible.sync="overAllVisible" :current-field="currentField" :field-title="fieldTitle"></OverallDetailModal>
  </div>
</template>

<script>
import customerData from './mixins/customer/data';
import dateChange from './mixins/dateChange';
import { $operator } from '@/utils/operation';
  export default {
    name: "customer",
    components: {
    },
    mixins: [customerData, dateChange],
    props: {

    },
    data () {
      return {
      }
    },
    computed: {

    },
    watch: {

    },
    created() {
    },
    mounted() {
    },
    methods: {
      // 当时间发生变化，进行得操作
      dateChange () {
        // 趋势图
        this.getTrend(this.customer_card_options, this.current_card_index)
        this.getCustomerOverview()
        // 客户消费情况分析
        this.tagChange(this.tag_consumptions[0], 0)
        // 客户分布分析(画像,待服务,首购)
        this.getCustomerAnalysis()
      },

      /* 卡片切换事件 */
      cardChange ( card_item, card_index ) {
        if ( card_item.readOnly ) return
        if ( card_index == this.current_card_index ) return

        this.current_card_index = card_index
        this.getTrend(this.customer_card_options, this.current_card_index)
      },

      /* 客户消费tag点击事件 */ 
      tagChange ( tag_item, tag_index ) {
        this.current_tag_index = tag_index
        this.getCustomerConsumeinfo(tag_item.kw)
      },

      // 待服务客户分布
      handleWaitCustomer (list) {
        let cloneList = this.$lodash.cloneDeep(list)
        this.service_lists = list
        if ( this.service_lists.length ) {
          let total_num = 0
          let total_rate = 0
          this.service_lists.forEach( item => {
            total_num = $operator.add(Number(total_num), Number(item.num))
            total_rate = $operator.add(Number(total_rate), Number(item.rate))
          } )
          this.service_lists.push({
            title: '合计',
            num: total_num,
            rate: total_rate
          })
        }
        let resultList = []
        cloneList && cloneList.forEach( item => {
          resultList.push({
            ...item,
            value: item.num,
            tootipList: [
              { key: '待服务次数:', value: item.title},
              { key: item.num, value: '人'},
              { key: '占比', value: `${item.rate}%`}
            ]
          })
        } )
        // 待服务客户分布
        this.setPieConfigure('service_bar_options', resultList, '待服务客户分布')
      },

      // 客户首购周期分布
      handleFirst (list) {
        this.first_lists = list
        let xData = list.map( x_item => x_item.title )
        let yData = list.map( y_item => y_item.num )
        this.first_data = this.$eChartFn.verticalBar( {
          xAxis: {
            data: xData
          },
          series: [{
            data: yData
          }]
        })
      },

      /* 客户画画像 */
      // 客户来源渠道排行
      handleCustomerChannl (list) {
        this.from_lists = list
        let resultList = []
        list && list.forEach( item => {
          resultList.push({
            ...item,
            value: item.num,
            tootipList: [
              { key: '来源渠道:', value: item.text },
              { key: item.num, value: '人' },
              { key: '占比', value: `${item.rate}%` }
            ]
          })
        } )
        // 客户来源渠道排行
        this.setPieConfigure('from_bar_options', resultList, '客户来源渠道排行')
      },
      // 客户性别分布排行
      handleCustomerSex (list) {
        this.sex_bar_list = list
        let resultList = []
        list && list.forEach( item => {
          resultList.push({
            ...item,
            value: item.num,
            tootipList: [
              { key: '性别:', value: item.text },
              { key: item.num, value: '人' },
              { key: '占比', value: `${item.rate}%` }
            ]
          })
        } )
        // 客户性别分布
        this.setPieConfigure('sex_bar_options', resultList, '客户性别分布')
      },
      // 客户年龄分布
      handleCustomerAge (list) {
        this.age_bar_list = list
        let resultList = []
        list && list.forEach( item => {
          resultList.push({
            ...item,
            value: item.num,
            tootipList: [
              { key: '年龄:', value: item.age_text },
              { key: item.num, value: '人' },
              { key: '占比', value: `${item.rate}%` }
            ]
          })
        } )
        // 客户年龄分布
        this.setPieConfigure('age_bar_options', resultList, '客户年龄分布')
      },

      /* API */
      // 获取整体情况
      getCustomerOverview () {
        let params = {
          st: this.formDate[0],
          et: this.formDate[1],
          date_type: this.date_type,
          clinic_id: this.clinic_id
        }
        this.$api.getCustomerOverview(params).then( res => {
          this.customer_card_options.forEach( item => {
            item.current = res.current[item.value]
            item.last = res.last[item.value]
          } )
        } ).catch( error => this.$Message.error(error.errmsg) )
      },
      // 剩余储值金额分布柱状图
      handleSurplus (list) {
        this.surplus_lists = list
        let xData = list.map( x_item => x_item.title )
        let yData = list.map( y_item => y_item.num )
        this.surplus_data = this.$eChartFn.verticalBar( {
          xAxis: {
            data: xData
          },
          series: [{
            data: yData
          }]
        })
      },

      // api - 客户消费情况分析
      getCustomerConsumeinfo (type) {
        let params = {
          st: this.formDate[0],
          et: this.formDate[1],
          type,
          clinic_id: this.clinic_id
        }
        this.$api.getCustomerConsumeinfo(params).then( res => {
          const { tag_consumptions, current_tag_index} = this
          this.consumption_lists = res.list
          let current_title = tag_consumptions[current_tag_index].label
          
          if ( current_title == '支付金额' || current_title == '消耗金额' || current_title == '笔单价' || current_title == '成功退款金额' ) {
            this.consumptionCols[0].title = current_title + '（元）'
          }else {
            this.consumptionCols[0].title = current_title
          }

          let list = res.list.map( (item, index) => (
              { ...item, 
                value: +item.num,
                tootipList: [
                  { key: `${current_title}:`, value: item.title },
                  { key: item.num, value: '人' },
                  { key: '占比', value: `${item.rate}%` }
                ]
              }
          ) )
          
          // 客户消费情况分析绘制饼图
          this.setPieConfigure('consumption_bar_options', list, current_title)
        } )
      },

      // api - 客户分布分析(画像,待服务,首购)
      getCustomerAnalysis () {
        let params = {
          st: this.formDate[0],
          et: this.formDate[1],
          clinic_id: this.clinic_id
        }
        this.$api.getCustomerAnalysis(params).then( res => {
          // 待服务客户分布
          this.handleWaitCustomer(res.serv_used_num_distribution)
          this.wait_serv_info = res.wait_serv_info
          // 剩余储值金额分布
          this.handleSurplus(res.recharge_balance_distribution)

          // 客户首购周期分布
          this.handleFirst(res.first_purchase_distribution)
          // 客户来源渠道排行
          this.handleCustomerChannl(res.customer_channel_analysis)
          // 客户性别分布
          this.handleCustomerSex(res.gender_distribution)
          // 客户年龄分布
          this.handleCustomerAge(res.age_distribution)
        } )
      },
    },
    filters: {

    }
  }
</script>
<style lang="less" scoped>
@import url('./style/common.less');
</style>