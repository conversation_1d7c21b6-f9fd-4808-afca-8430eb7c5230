export const overviewOptions_MIL = [
  {
    value: 'order_cv',
    label: '下单人数',
  },
  {
    value: 'paid_cv',
    label: '支付人数',
  },
  {
    value: 'customer_incr',
    label: '新增客户数',
  },
  {
    value: 'repurchase_cv',
    label: '复购客户数',
  },
  {
    value: 'customer_total',
    label: '客户总数',
  },
]
export const overviewOptions_HIS = [
  {
    value: 'order_cv',
    label: '下单人数',
  },
  {
    value: 'paid_cv',
    label: '支付人数',
  },
  {
    value: 'customer_incr',
    label: '新增客户数',
  },
  {
    value: 'repurchase_cv',
    label: '复购客户数',
  },
  {
    value: 'customer_total',
    label: '客户总数',
  },
]
export const allOverviewOptions = [
  {
    value: 'order_cv',
    label: '下单人数',
  },
  {
    value: 'paid_cv',
    label: '支付人数',
  },
  {
    value: 'customer_incr',
    label: '新增客户数',
    isPercent: true
  },
  {
    value: 'repurchase_cv',
    label: '复购客户数',
  },
  {
    value: 'customer_total',
    label: '客户总数',
  },
]

/**
 * @description: 客户分析卡片枚举数据
 * isPercent: { 当前为百分比展示 }
 * isThousandth: { 当前为千分位展示 }
 * isMoney: { 当前为金额展示 }
 * 
*/
export const customer_card_options = [
  {
    label: '新增客户数(人)',
    value: 'customer_incr',
    current: '',
    last: '',
  },
  {
    label: '到店客户数(人)',
    value: 'arrival_cv',
    current: '',
    last: '',
  },
  {
    label: '到店次数(次)',
    value: 'arrival_num',
    current: '',
    last: '',
  },
  // {
  //   label: '下单客户数(人)',
  //   value: 'order_cv',
  //   current: '',
  //   last: '',
  // },
  {
    label: '支付客户数(人)',
    value: 'total_paid_cv',
    current: '',
    last: '',
  },
  {
    label: '消费客户数(人)',
    value: 'paid_cv',
    current: '',
    last: '',
  },
  {
    label: '储值客户数(人)',
    value: 'recharge_cv',
    current: '',
    last: '',
  },
  {
    label: '实操客户数(人)',
    value: 'serv_used_cv',
    current: '',
    last: '',
  },
  {
    label: '消费退款客户数(人)',
    value: 'refunded_finish_cv',
    current: '',
    last: '',
  },
  {
    label: '储值退款客户数(人)',
    value: 'recharge_refund_cv',
    current: '',
    last: '',
  },
  {
    label: '复购客户数(人)',
    value: 'total_repurchase_cv',
    current: '',
    last: '',
    readOnly: true,
    hideCompare: true,
  },
  {
    label: '总客户数(人)',
    value: 'customer_total',
    current: '',
    last: '',
    readOnly: true,
    hideCompare: true,
  },
  {
    label: '累计储值客户数(人)',
    value: 'recharge_grand_cv',
    current: '',
    last: '',
    readOnly: true,
    hideCompare: true,
  },
  {
    label: '待服务客户数(人)',
    value: 'serv_not_used_cv',
    current: '',
    last: '',
    readOnly: true,
    hideCompare: true,
  },
]

export const customer_tag_consumptions = [
  {
    label: '消费次数',
    kw: 'paid_num',
  },
  {
    label: '消费金额',
    kw: 'paid_money',
  },
  {
    label: '消耗次数',
    kw: 'serv_used_num',
  },
  {
    label: '消耗金额',
    kw: 'serv_used_money',
  },
  {
    label: '笔单价',
    kw: 'order_unit_price',
  },
  {
    label: '退款次数',
    kw: 'refunded_order_num',
  },
  {
    label: '成功退款金额',
    kw: 'refunded_finish_amount',
  },
]

// export const situation_tooltip_list = [
//   "新增客户数：获取到手机号的客户人数",
//   "到店客户数：到店的客户人数(基于日常模块录入的到店数据)",
//   "下单客户数：已成功下单的客户人数",
//   "支付客户数：已成功支付的客户人数",
//   "支付人数：完成订单支付的人数",
//   "实操客户数：为客户操作理疗服务，并成功完成卡券核销的客户人数",
//   "成功退款客户数：为客户成功操作订单退款的客户人数",
//   "复购客户数：统计周期内，发生过2次或以上支付行为的客户",
//   "累计客户数：截止统计区间结束时间，诊所全部客户数",
//   "累计已支付客户数：截止统计区间结束时间，诊所全部已成功支付的客户人数",
//   "复购客户数：待服务客户数，截止统计区间结束时间，诊所还需服务客户人数(有卡券待核销的客户)"
// ]

export const situation_tooltip_list = [
  "新增客户数：获取到手机号的客户人数",
  "到店客户数：到店的客户人数(基于日常模块录入的到店人数+消费下单人数+已完成储值订单充值人数+理疗消耗人数 根据用户去重)",
  "到店次数：客户到店的次数(基于日常模块录入的到店次数+消费下单订单数+已完成储值订单数+理疗消耗次数，1天内同一客户有多次行为记1次)",
  "支付客户数：消费支付或储值支付的客户人数",
  "消费客户数：完成消费订单支付的人数",
  "储值客户数：完成储值订单支付的人数",
  "实操客户数：为客户操作理疗服务，并成功完成卡券核销的客户人数",
  "消费退款客户数：完成消费订单退款的客户人数",
  "储值退款客户数：完成储值退款的客户人数",
  "复购客户数：统计累计发生过2次或以上支付行为的客户(支付指消费或充值)",
  "总客户数：诊所累计的客户总数(不随选择周期变化)",
  "累计储值客户数：不随时间周期变动，诊所累计成功储值的客户数，不考虑储值后退款的情况",
  "待服务客户数：诊所还需服务客户人数(有卡券待核销的客户)，不随选择周期变化",
]

const overViewEnum ={
  "trendItemsDesc": {
  "turnover": {
    "kw": "turnover",
      "desc": "营业额"
  },
  "pres_num": {
    "kw": "pres_num",
      "desc": "已开处方数"
  },
  "arrival_cv": {
    "kw": "arrival_cv",
      "desc": "到店客户数"
  },
  "paid_pres_num": {
    "kw": "paid_pres_num",
      "desc": "已支付处方数"
  },
  "assigned_pres_num": {
    "kw": "assigned_pres_num",
      "desc": "已取药处方数"
  },
  "paid_order_num": {
    "kw": "paid_order_num",
      "desc": "支付订单数"
  },
  "paid_his_order_num": {
    "kw": "paid_his_order_num",
      "desc": "诊疗已付订单数"
  },
  "his_order_price_pre": {
    "kw": "his_order_price_pre",
      "desc": "诊疗订单均价"
  },
  "paid_shop_order_num": {
    "kw": "paid_shop_order_num",
      "desc": "商城已付订单数"
  },
  "paid_goods_num": {
    "kw": "paid_goods_num",
      "desc": "商城已售商品数"
  },
  "shop_order_price_pre": {
    "kw": "shop_order_price_pre",
      "desc": "诊疗订单均价"
  },
  "order_num": {
    "kw": "order_num",
      "desc": "订单数"
  },
  "paid_cv": {
    "kw": "paid_cv",
      "desc": "支付客户数"
  },
  "refunded_amount": {
    "kw": "refunded_amount",
      "desc": "成功退款金额"
  },
  "customer_visit": {
    "kw": "customer_visit",
      "desc": "访客量"
  },
  "customer_incr": {
    "kw": "customer_incr",
      "desc": "新增客户数"
  },
  "price_per_customer": {
    "kw": "price_per_customer",
      "desc": "客单价"
  },
  "repurchase_cv": {
    "kw": "repurchase_cv",
      "desc": "复购客户数"
  },
  "order_cv": {
    "kw": "order_cv",
      "desc": "下单人数"
  },
  "cv_pay_conv_rate": {
    "kw": "cv_pay_conv_rate",
      "desc": "访问支付转化率"
  },
  "customer_total": {
    "kw": "customer_total",
      "desc": "客户总数"
  },
  "pres_type_num": {
    "kw": "pres_type_num",
      "desc": "处方数"
  },
  "serv_used_num": {
    "kw": "serv_used_num",
      "desc": "服务卡券消费次数"
  },
  "serv_used_money": {
    "kw": "serv_used_money",
      "desc": "服务卡券消费金额"
  },
  "serv_used_cv": {
    "kw": "serv_used_cv",
      "desc": "实操客户数"
  },
  "pur_order_num": {
    "kw": "pur_order_num",
      "desc": "采购次数"
  },
  "pur_order_money": {
    "kw": "pur_order_money",
      "desc": "采购金额"
  },
  "joinin_info": {
    "kw": "joinin",
      "desc": "加盟采购信息"
  },
  "total_pur_money": {
    "kw": "total_pur_money",
      "desc": "全部采购金额"
  },
  "total_pur_num": {
    "kw": "total_pur_num",
      "desc": "全部采购订单数量"
  },
  "ostock_num": {
    "kw": "stock_num",
      "desc": "入库数量"
  },
  "ostock_money": {
    "kw": "stock_money",
      "desc": "入库金额"
  },
  "turnover_amount": {
    "kw": "turnover_amount",
      "desc": "营业额"
  },
  "received_fee": {
    "kw": "received_fee",
      "desc": "实收金额"
  },
  "receivable_fee": {
    "kw": "receivable_fee",
      "desc": "应收金额"
  },
  "discount_fee": {
    "kw": "discount_fee",
      "desc": "优惠金额"
  },
  "order_total_amount": {
    "kw": "order_total_amount",
      "desc": "下单订单金额"
  },
  "refunded_finish_amount": {
    "kw": "refunded_finish_amount",
      "desc": "成功退款金额"
  },
  "refunded_finish_num": {
    "kw": "refunded_finish_num",
      "desc": "成功退款订单数"
  },
  "refunded_finish_order_rate": {
    "kw": "cv_pay_conv_rate",
      "desc": "退款订单占比"
  },
  "his_pres_info": {
    "kw": "his_pres_info",
      "desc": "处方数量信息"
  },
  "item_trade_info": {
    "kw": "item_trade_info",
      "desc": "类目交易信息"
  }
}
}