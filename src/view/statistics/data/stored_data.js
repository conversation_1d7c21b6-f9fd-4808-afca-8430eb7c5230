  /**
 * @description: 商品分析卡片枚举数据
 * isPercent: { 当前为百分比展示 }
 * isThousandth: { 当前为千分位展示 }
 * isMoney: { 当前为金额展示 }
 * 
*/
export const stored_card_options = [
  {
    label: '储值营收金额',
    value: 'recharge_turnover',
    current: '',
    last: '',
    isMoney: true,
  },
  {
    label: '实充金额',
    value: 'recharge_real_amount',
    current: '',
    last: '',
    isMoney: true,
  },
  {
    label: '赠送金额',
    value: 'recharge_given_amount',
    current: '',
    last: '',
    isMoney: true,
  },
  {
    label: '储值金额',
    value: 'recharge_amount',
    current: '',
    last: '',
    isMoney: true,
  },
  {
    label: '已完成储值订单',
    value: 'recharge_paid_num',
    current: '',
    last: '',
  },
  {
    label: '储值退款单',
    value: 'recharge_refund_num',
    current: '',
    last: '',
  },
  {
    label: '实际退款金额',
    value: 'recharge_refund_receive',
    current: '',
    last: '',
    isMoney: true,
  },
  {
    label: '实充退款金额',
    value: 'recharge_refund_real',
    current: '',
    last: '',
    isMoney: true,
  },
  {
    label: '赠送撤回金额',
    value: 'recharge_refund_given',
    current: '',
    last: '',
    isMoney: true,
  },
  {
    label: '储值客户数',
    value: 'recharge_cv',
    current: '',
    last: '',
  },
  {
    label: '平均实充金额',
    value: 'recharge_per_cv_real',
    current: '',
    last: '',
    isMoney: true,
  },
  {
    label: '累计储值客户数',
    value: 'recharge_grand_cv',
    current: '',
    last: '',
    readOnly: true,
    hideCompare: true
  },
  // {
  //   label: '累计实充金额',
  //   value: 'recharge_grand_real_amount',
  //   current: '',
  //   last: '',
  //   readOnly: true,
  //   isMoney: true,
  //   hideCompare: true
  // },
  // {
  //   label: '累计赠送金额',
  //   value: 'recharge_grand_given_amount',
  //   current: '',
  //   last: '',
  //   readOnly: true,
  //   isMoney: true,
  //   hideCompare: true
  // },
  {
    label: '剩余储值金额',
    value: 'recharge_balance',
    current: '',
    last: '',
    readOnly: true,
    isMoney: true,
    hideCompare: true
  },
]


export const stored_tooltip_list = [
  "储值营收金额：实充金额-实际退款金额",
  "实充金额：统计周期内，储值订单中已完成状态下实充金额合计",
  "赠送金额：统计周期内，储值订单中已完成状态下赠送金额合计",
  "储值金额：统计周期内，实充金额+赠送金额",
  "已完成储值订单：统计周期内，成功为用户储值的订单数量合计(已完成储值订单数合计)",
  "储值退款单：统计周期内，成功为用户储值退款的订单合计",
  "实际退款金额：统计周期内，成功退款的储值退款单，实际退款金额合计",
  "实充退款金额：统计周期内，成功退款的储值退款单，实充金额退款金额合计",
  "赠送撤回金额：统计周期内，成功退款的储值退款单，已撤回的赠送金额合计",
  "储值客户数：统计周期内，已完成储值的客户人数",
  "平均实充金额：实充金额/储值客户数",
  "累计储值客户数：不随时间周期变动，诊所累计成功储值的客户数，不考虑储值后退款的情况",
  // "累计实充金额：不随时间周期变动，诊所累计成功储值的实充金额-实充退款金额",
  // "累计赠送金额：不随时间周期变动，诊所累计赠送金额-赠送撤回金额",
  "剩余储值金额：统计到昨日，所有客户剩余储值金额合计",
]

export const surplus_tooltip_list = [
  "截止到昨天，在有储值余额的客户中，各个余额区间内的客户人数(不随时间周期变动变化)",
]

export const money_tooltip_list = [
  "统计周期内，客户单次储值的实充金额范围分布",
]

export const source_tooltip_list = [
  "统计周期内，不同渠道下客户储值的实充金额占比",
]

export const num_tooltip_list = [
  "统计周期内，客户在门店成功储值次数的统计分布",
]