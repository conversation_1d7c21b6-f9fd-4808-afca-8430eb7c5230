/**营业计划 */
const goals_flow_tip = `（订单实收金额（包括储值充值）- 订单退款） -  储值消耗金额`;
const goals_used_tip = `完成核销且已D+1后确认结算。`;
const goals_num_tip = `1、统计服务消耗，shop订单(不包括购买树家商品)，his订单，储值充值+消耗；
2、同一天同一人做去重；
3、从以支付成功的订单（包括支付成功后退款）。`;

/**医生人效 */

const doctorTip = {
  doctor_labar: `见诊人数：该医生his中只要创建过问诊计一人，当天做去重。
初诊人数：该医生当日his中首次创建问诊的用户。
初诊成交人数：该医生当日his中首次创建问诊的用户是否有支付成功的行为(发生退款不影响成交人数)。
初诊转化率：该医生当日成交初诊人数(退款行为不计算)/当日总初诊人数。
复诊人数：该医生接诊的老患者人数（排除首次就诊）。
复诊转化率：该医生统计周期内复诊人数/该医生历史患者总数。`,

  doctor_fg: `复购率：该医生患者再次(在his)购买的人数/有过(在his)购买的人数。`,
  doctor_ys: `成交业绩：该医生当日his中患者支付总额 - 退款金额。
初诊成交业绩：该医生当日his首次就诊的患者支付总额 - 退款金额。
复诊成交业绩：该医生接诊的老患者支付总额（排除首次支付金额）- 退款金额。`,

  doctor_zc: `中药饮片：该医生his中饮片开单收费金额 - 退款金额。
中成药：该医生his中成药开单收费金额 - 退款金额。
理疗：该医生his中理疗类开单收费金额 - 退款金额。
养疗：该医生his中养疗开单收费金额 - 退款金额。
挂号费：该医生his挂号费开单收费金额 - 退款金额。
问诊费：该医生his问诊费开单收费金额 - 退款金额。
加工费：该医生his加工费开单收费金额 - 退款金额。`,

  doctor_fx: `实耗服务数量：服务确认核销的数量 - 撤销核销数量。
实耗业绩：核销完成d+1结算的金额。
销售商品数量：实物确认签收的商品数量 - 退款数量。
销售业绩：签收完成d+7结算的金额。`
};

/**理疗师人效 */

const phyTip = {
  phy_kq: `服务客户数：提供服务核销完成的数量，包含撤回核销和退款，同日做去重。
老客数：老用户完成核销。
新客数：新用户完成核销。
会员客数：会员完成核销。
非会员客数：非会员完成核销。
预约客户数：通过预约单生成的预约且完成核销，包括美团、抖音等三方平台。
非预约客户数：未通过系统发起预约，线下到店直接服务核销。`,
  phy_pj: `评价数：通过小程序或客户端，用户实际提交评价的数量，包括带文字。
带文字评价数：通过榕树堂或客户端，用户实际提交带文字评价的数量。`,
  phy_mx: `服务项目数：核销完成的服务次数。`,
  phy_fg: `复购人数：统计周期内消费≥2次的客户数。
复购率：统计周期内消费≥2次的客户数/总消费客户数。`,
  phy_fx: `实耗服务数量：服务确认核销的数量 - 撤销核销数量。
实耗业绩：核销完成d+1结算的金额。
销售商品数量：实物确认签收的商品数量 - 退款数量。
销售业绩：签收完成d+7结算的金额。`
};

export default {
  goals_flow_tip,
  goals_used_tip,
  goals_num_tip,

  doctorTip,
  phyTip
};
