export const transaction_card_options = [
  {
    label: '总营收金额',
    value: 'gross_turnover',
    current: '',
    last: '',
    isMoney: true,
  },
  {
    label: '消费营收金额',
    value: 'consume_turnover_amount',
    current: '',
    last: '',
    isMoney: true,
  },
  {
    label: '储值营收金额',
    value: 'recharge_turnover',
    current: '',
    last: '',
    isMoney: true,
  },
  {
    label: '消费客户数(人)',
    value: 'paid_cv',
    current: '',
    last: '',
  },
  {
    label: '储值客户数(人)',
    value: 'recharge_cv',
    current: '',
    last: '',
    isMoney: false,
  },
  {
    label: '已支付消费订单数(单)',
    value: 'paid_order_num',
    current: '',
    last: '',
  },
  {
    label: '实际消费金额',
    value: 'turnover_amount',
    current: '',
    last: '',
    isMoney: true,
  },
  {
    label: '实收金额',
    value: 'received_fee',
    current: '',
    last: '',
    isMoney: true,
  },
  {
    label: '应收金额',
    value: 'receivable_fee',
    current: '',
    last: '',
    isMoney: true,
  },
  {
    label: '优惠金额',
    value: 'discount_fee',
    current: '',
    last: '',
    isMoney: true,
  },
  {
    label: '储值消耗金额',
    value: 'recharge_pay_amount',
    current: '',
    last: '',
    isMoney: true,
  },
  {
    label: '全部消费订单数(单)',
    value: 'order_num',
    current: '',
    last: '',
  },
  {
    label: '消费下单订单金额',
    value: 'order_total_amount',
    current: '',
    last: '',
    isMoney: true,
  },
  {
    label: '消费客单价',
    value: 'price_per_customer',
    current: '',
    last: '',
    isMoney: true,
  },
  {
    label: '成功退款订单数',
    value: 'refunded_finish_num',
    current: '',
    last: '',
  },
  {
    label: '微信/现金退款金额',
    value: 'cash_pay_refund',
    current: '',
    last: '',
    isMoney: true,
  },
  {
    label: '储值支付退款金额',
    value: 'recharge_pay_refund',
    current: '',
    last: '',
    isMoney: true,
  },
  {
    label: '理疗消耗次数',
    value: 'serv_used_num',
    current: '',
    last: '',
  },
  {
    label: '理疗消耗金额',
    value: 'serv_used_money',
    current: '',
    last: '',
    isMoney: true,
  },
]

export const transaction_tooltip_list = [
  "总营收金额：消费营收金额+储值营收金额",
  "消费营收金额：实收金额-储值消耗金额-现金/微信退款金额",
  "储值营收金额：实充金额-实际退款金额",
  "消费客户数：完成消费订单支付的人数",
  "储值客户数：完成储值订单支付的人数",
  "已支付消费订单数：已支付状态的订单，退款关闭的不排除；订单=0的不排除",
  "实际消费金额：实收金额-退款金额(现金/微信退款金额+储值支付退款金额）",
  "实收金额：应收金额-优惠金额",
  "应收金额：已支付订单金额原价合计",
  "优惠金额：订单创建、支付时，给客户的优惠金额合计(支付优惠，开方折扣，储值价优惠等)",
  "储值消耗金额：已支付订单实收金额中使用储值余额支付的部分，不考虑退款订单",
  "全部消费订单数：消费订单列表中下单时间在统计周期内的全部订单数",
  "消费订单下单金额：全部消费订单的应收金额合计",
  "消费客单价：实收金额/消费客户数",
  "成功退款订单数：已完成退款流程的订单数量",
  "现金/微信退款金额：用现金/微信方式支付的消费订单中的成功退款金额合计",
  "储值支付退款金额：用储值余额方式支付的消费订单中的成功退款金额合计",
  "理疗消耗次数：为客户操作理疗服务，并成功完成卡券核销的次数",
  "理疗消耗金额：为客户操作理疗服务，并成功完成核销的卡券关联价值",
]

export const surplus_tooltip_list = [
  "剩余储值金额分布：截止到昨天，在有储值余额的客户中，各个余额区间内的客户人数(不随时间周期变动变化)",
]

export const money_tooltip_list = [
  "单次储值金额区间分布：统计周期内，客户单次储值的实充金额范围分布",
]

export const source_tooltip_list = [
  "储值场景分布：统计周期内，不同渠道下客户储值的实充金额占比",
]

export const num_tooltip_list = [
  "储值次数分析：统计周期内，客户在门店成功储值次数的统计分布",
]


