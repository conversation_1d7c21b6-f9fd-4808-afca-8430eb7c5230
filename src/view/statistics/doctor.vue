<template>
  <div v-loading="pageLoading" class="app-page-wrapper">
    <!-- Header -->

    <div v-show="!pageLoading">
      <header class="flex gap-20">
        <Form inline label-position="left" :label-width="70" :label-colon="true">
          <div class="flex gap-20">
            <Form-item label="统计范围">
              <clinic-search
                ref="search-cli"
                v-model="clinic_id"
                :show-all="false"
                :is-clearable="false"
                width="300"
                :is-default="true"
                @change="selectClinic"
                @once="once"
              ></clinic-search>
            </Form-item>

            <Form-item label="统计时间">
              <customDatePicker
                v-model="formDate"
                :type-options="['date', 'week', 'month']"
                default-type="date"
                @getDateType="getDateType"
              ></customDatePicker>
            </Form-item>
            <div>
              <Button size="small" type="primary" style="width: 106px; height: 32px" @click="onReport">
                <img class="export-icon" src="https://img-sn-i01s-cdn.rsjxx.com/rsjxx/2024/0702/154428_97382.png" />
                导出数据
              </Button>
            </div>
          </div>
        </Form>
      </header>

      <div>
        <div class="table-item">
          <div class="title">
            <div class="line"></div>
            见诊客群
            <Tooltip max-width="400" theme="light" placement="bottom" class="custom-tooltip tooltip-left-arrow">
              <div style="white-space: pre-wrap" slot="content">{{ tips.doctor_labar }}</div>
              <svg-icon iconClass="tip" class="helpIcon cursor"></svg-icon>
            </Tooltip>
          </div>
          <Table
            :style="{ maxWidth: tableWidth(consultation_customer_groupCols.length) }"
            :loading="loading"
            :columns="consultation_customer_groupCols"
            :data="consultation_customer_group"
          >
            <template v-slot:employee_level="{ row }"> {{ row.employee }} / {{ row.level }}</template>
          </Table>
        </div>

        <div class="table-item">
          <div class="title">
            <div class="line"></div>
            复购
            <Tooltip max-width="400" theme="light" placement="bottom" class="custom-tooltip tooltip-left-arrow">
              <div style="white-space: pre-wrap" slot="content">{{ tips.doctor_fg }}</div>
              <svg-icon iconClass="tip" class="helpIcon cursor"></svg-icon>
            </Tooltip>
          </div>
          <Table
            :style="{ maxWidth: tableWidth(repurchaseCols.length) }"
            :loading="loading"
            :columns="repurchaseCols"
            :data="repurchase"
          >
            <template v-slot:employee_level="{ row }"> {{ row.employee }} / {{ row.level }}</template>
          </Table>
        </div>

        <div class="table-item">
          <div class="title">
            <div class="line"></div>
            业绩营收
            <Tooltip max-width="400" theme="light" placement="bottom" class="custom-tooltip tooltip-left-arrow">
              <div style="white-space: pre-wrap" slot="content">{{ tips.doctor_ys }}</div>
              <svg-icon iconClass="tip" class="helpIcon cursor"></svg-icon>
            </Tooltip>
          </div>
          <Table
            :style="{ maxWidth: tableWidth(performance_revenueCols.length) }"
            :loading="loading"
            :columns="performance_revenueCols"
            :data="performance_revenue"
          >
            <template v-slot:employee_level="{ row }"> {{ row.employee }} / {{ row.level }}</template>
          </Table>
        </div>

        <div class="table-item">
          <div class="title">
            <div class="line"></div>
            业绩组成
            <Tooltip max-width="400" theme="light" placement="bottom" class="custom-tooltip tooltip-left-arrow">
              <div style="white-space: pre-wrap" slot="content">{{ tips.doctor_zc }}</div>
              <svg-icon iconClass="tip" class="helpIcon cursor"></svg-icon>
            </Tooltip>
          </div>
          <Table
            :style="{ maxWidth: tableWidth(performance_compositionCols.length) }"
            :loading="loading"
            :columns="performance_compositionCols"
            :data="performance_composition"
          >
            <template v-slot:employee_level="{ row }"> {{ row.employee }} / {{ row.level }}</template>
          </Table>
        </div>

        <div class="table-item">
          <div class="title">
            <div class="line"></div>
            收入分析
            <Tooltip max-width="400" theme="light" placement="bottom" class="custom-tooltip tooltip-left-arrow">
              <div style="white-space: pre-wrap" slot="content">{{ tips.doctor_fx }}</div>
              <svg-icon iconClass="tip" class="helpIcon cursor"></svg-icon>
            </Tooltip>
          </div>
          <Table
            border
            :style="{ maxWidth: tableWidth(revenue_analysisCols.length) }"
            :loading="loading"
            :columns="revenue_analysisCols"
            :data="revenue_analysis"
          >
            <template v-slot:employee_level="{ row }"> {{ row.employee }} / {{ row.level }}</template>
          </Table>
        </div>
      </div>
    </div>

    <ReportExport v-model="reportModalVisible" title="医生统计表" :api-name="apiName"></ReportExport>
  </div>
</template>

<script>
import tip from './data/tip';
import ReportExport from './components/ReportExport-1.vue';
import clinicSearch from '@/components/clinic-search/clinic-search.vue';
import customDatePicker from './components/CustomDatePicker/CustomDatePicker.vue';

export default {
  name: 'Doctor',
  components: {
    ReportExport,
    clinicSearch,
    customDatePicker
  },
  data() {
    return {
      clinic_id: '',
      reportModalVisible: false,
      pageLoading: false,
      loading: false,
      apiName: 'getDoctorReport',

      tips: {
        ...tip.doctorTip
      },

      formDate: [],

      month: '',
      date_type: 'month',
      typeOpts: [],

      date: '',

      type: 'month',
      format: '',

      st: '',
      et: '',

      // 见诊客群
      consultation_customer_group: [],
      consultation_customer_groupCols: [
        {
          title: '员工 / 职级',
          slot: 'employee_level',
          width: 150,
          fixed: 'left'
        },
        {
          title: '见诊人数',
          key: 'consultation_num',
          align: 'center',
          width: 150
        },
        {
          title: '初诊人数',
          key: 'initial_consult_num',
          align: 'center',
          width: 150
        },
        {
          title: '初诊成交人数',
          key: 'initial_deal_num',
          align: 'center',
          width: 150
        },
        {
          title: '初诊转化率',
          key: 'initial_conversion_rate',
          align: 'center',
          width: 150
        },
        {
          title: '复诊人数',
          key: 'return_consult_num',
          align: 'center',
          width: 150
        },
        {
          title: '复诊转化率',
          key: 'return_conversion_rate',
          align: 'center',
          width: 150
        }
      ],

      // 复购
      repurchase: [],
      repurchaseCols: [
        {
          title: '员工 / 职级',
          slot: 'employee_level',
          width: 150,
          fixed: 'left'
        },
        {
          title: '复购率',
          key: 'repurchase_rate',
          align: 'center',
          width: 150
        }
      ],
      //业绩营收
      performance_revenue: [],
      performance_revenueCols: [
        {
          title: '员工 / 职级',
          slot: 'employee_level',
          width: 150,
          fixed: 'left'
        },
        {
          title: '成交业绩',
          key: 'deal_performance',
          align: 'center',
          width: 150
        },
        {
          title: '初诊成交业绩',
          key: 'initial_deal_performance',
          align: 'center',
          width: 150
        },
        {
          title: '复诊成交业绩',
          key: 'return_deal_performance',
          align: 'center',
          width: 150
        }
      ],

      // 业绩组成
      performance_composition: [],
      performance_compositionCols: [
        {
          title: '员工 / 职级',
          slot: 'employee_level',
          width: 150,
          fixed: 'left'
        },
        {
          title: '中药饮片',
          key: 'chinese_herbal_pieces',
          align: 'center',
          width: 150
        },
        {
          title: '中成药',
          key: 'chinese_patent_medicine',
          align: 'center',
          width: 150
        },
        {
          title: '理疗',
          key: 'physiotherapy',
          align: 'center',
          width: 150
        },
        {
          title: '养疗',
          key: 'health_care',
          align: 'center',
          width: 150
        },
        {
          title: '挂号费',
          key: 'registration_fee',
          align: 'center',
          width: 150
        },
        {
          title: '问诊费',
          key: 'consultation_fee',
          align: 'center',
          width: 150
        },
        {
          title: '加工费',
          key: 'processing_fee',
          align: 'center',
          width: 150
        }
      ],

      // 收入分析
      revenue_analysis: [],
      revenue_analysisCols: [
        {
          title: '员工 / 职级',
          slot: 'employee_level',
          width: 150,
          fixed: 'left'
        },
        {
          title: '实耗服务数量',
          key: 'actual_service_quantity',
          align: 'center',
          width: 150
        },
        {
          title: '实耗业绩',
          key: 'actual_performance',
          align: 'center',
          width: 150
        },
        // {
        //   title: '销售商品数量',
        //   key: 'sales_goods_quantity',
        //   align: 'center',
        //   width: 150
        // },
        {
          title: '销售业绩',
          key: 'sales_performance',
          align: 'center',
          width: 150
        }
      ],
      hasFetchedTargets: false
    };
  },
  watch: {
    formDate: {
      handler(val) {
        console.info('🚀 ~ file:doctor method:handler line:352 -----', val);
        if (val && val[0]) {
          this.st = val[0];
          this.et = val[1];
          this.getList();
        }
      }
    }
  },
  mounted() {
    this.scrollTop();
  },
  methods: {
    tableWidth(length, minWidth = 150) {
      // 根据 colums 的数量和每列的最小宽度来计算
      return `${length * minWidth}px`;
    },
    scrollTop() {
      this.$nextTick(() => {
        const wrapper = document.querySelector('.app-wrapper');
        if (wrapper) wrapper.scrollTop = 0;
      });
    },

    getDateType(type) {
      console.info('🚀 ~ file:doctor method:getDateType line:372 -----', type);
      if (this.formDate && this.formDate[0]) {
        this.st = this.formDate[0];
        this.et = this.formDate[1];
        this.date_type = type;
        this.getList();
      }
    },

    selectClinic() {
      this.getList();
    },
    // 选中诊所回调执行一次
    once() {
      this.getList();
    },

    // getMonthRa(date) {
    //   const year = date.getFullYear();
    //   const month = date.getMonth() + 1;
    //   const pad = n => (n < 10 ? '0' + n : n);
    //   const st = `${year}-${pad(month)}-01`;
    //   const lastDay = new Date(year, month, 0).getDate();
    //   const et = `${year}-${pad(month)}-${lastDay}`;
    //   return {
    //     st,
    //     et
    //   };
    // },
    // intiData() {
    //   const now = new Date();
    //   this.date = `${now.getFullYear()}-${now.getMonth() + 1}`;
    //   this.st = this.getMonthRa(now).st;
    //   this.et = this.getMonthRa(now).et;
    //
    //   this.$api.getDoctorOpt().then(res => {
    //     this.typeOpts = S.descToArrHandle(res.date_type);
    //   });
    // },

    getList() {
      if (!this.clinic_id) return;
      this.loading = true;
      this.pageLoading = true;
      this.$api
        .getDoctorList({
          st: this.st,
          et: this.et,
          date_type: this.date_type,
          clinic_id: this.clinic_id
        })
        .then(res => {
          this.consultation_customer_group = res.consultation_customer_group || [];
          this.repurchase = res.repurchase || [];
          this.revenue_analysis = res.revenue_analysis || [];
          this.performance_revenue = res.performance_revenue || [];
          this.performance_composition = res.performance_composition || [];
        })
        .finally(() => {
          this.loading = false;
          this.pageLoading = false;
        });
    },

    // onChangeType() {
    //   const date_type_map = {
    //     day: {
    //       type: 'date',
    //       format: ''
    //     },
    //     week: {
    //       type: 'week',
    //       curDate: '',
    //       format: 'yyyy 第 WW 周'
    //     },
    //     month: {
    //       type: 'month',
    //       format: ''
    //     }
    //   };
    //
    //   this.type = date_type_map[this.date_type].type;
    //   this.date = '';
    //   this.format = date_type_map[this.date_type].format;
    // },

    // handleTimeChange(value) {
    //   console.log('🚀 ~ doctor.vue:379 ~ handleTimeChange ~ value---->', value);
    //   let st = '';
    //   let et = '';
    //   let date = value;
    //
    //   // 防止 date 解析失败
    //   if (!(date instanceof Date) || isNaN(date.getTime())) {
    //     return;
    //   }
    //
    //   if (this.date_type == 'day') {
    //     // 日
    //     st = et = moment(value).format('YYYY-MM-DD');
    //   } else if (this.date_type == 'week') {
    //     // 周
    //     const day = date.getDay() || 7;
    //     const monday = new Date(date);
    //     monday.setDate(date.getDate() - day + 1);
    //     const sunday = new Date(monday);
    //     sunday.setDate(monday.getDate() + 6);
    //     st = moment(monday).format('YYYY-MM-DD');
    //     et = moment(sunday).format('YYYY-MM-DD');
    //   } else if (this.date_type == 'month') {
    //     // 月
    //     const year = date.getFullYear();
    //     const month = date.getMonth() + 1;
    //     const pad = n => (n < 10 ? '0' + n : n);
    //     st = `${year}-${pad(month)}-01`;
    //     const lastDay = new Date(year, month, 0).getDate();
    //     et = `${year}-${pad(month)}-${lastDay}`;
    //   }
    //   this.st = st;
    //   this.et = et;
    //   console.log('🚀 ~ doctor.vue:384 ~ handleTimeChange ~ this.et ---->', this.et);
    //
    //   this.getList();
    // },
    onReport() {
      this.reportModalVisible = true;
    }
  }
};
</script>
<style scoped lang="less">
.gap-20 {
  gap: 20px;
}
.export-icon {
  width: 14px;
  height: 13px;
  margin-right: 6px;
}

.app-page-wrapper {
  background-color: #fff;
  min-height: 90vh;
  padding: 24px;
  margin-top: 10px;

  .table-item {
    margin-top: 24px;

    .title {
      display: flex;
      gap: 8px;

      .line {
        width: 4px;
        height: 18px;
        background: linear-gradient(90deg, #155bd4 0%, #4988fd 100%);
        border-radius: 2px;
      }

      font-size: 16px;
      font-weight: 600;
      color: #222;
      margin-top: 14px;
      margin-bottom: 18px;
    }
  }
}
</style>
