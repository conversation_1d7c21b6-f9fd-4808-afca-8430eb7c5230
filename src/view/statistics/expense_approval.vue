<template>
  <div class="expense-wrapper">
    <Form inline @submit.native.prevent @keyup.enter.native="onSearch">
      <FormItem>
        <DatePicker
          v-model="times"
          class="time-range"
          clearable
          format="yyyy-MM-dd"
          placeholder="请选择日期"
          type="daterange"
          @on-change="onTimesChange"
        ></DatePicker>
      </FormItem>
      <FormItem>
        <Select v-model="queryFormData.status" placeholder="审批状态" clearable>
          <Option v-for="item in statusOpt" :value="item.id" :key="item.value">{{ item.desc }}</Option>
        </Select>
      </FormItem>
      <FormItem style="text-align: left">
        <Button type="primary" class="mr10" @click="onSearch">筛选</Button>
        <Button type="default" class="mr10" @click="onResetSearch">重置</Button>
      </FormItem>
    </Form>
    <div class="table-wrapper">
      <Table :loading="tableLoading" :columns="tableCols" :data="list" :height="$store.state.app.clientHeight - 230">
        <template v-slot:check_operator="{ row }">
          {{ row.check_operator || '-' }}
        </template>
        <template v-slot:action="{ row }">
          <template v-if="row.status == 1">
            <a @click="onAction(row, 2)">通过</a>
            <a class="ml-16" style="color: red" @click="onAction(row, 3)">驳回</a>
          </template>
          <span v-else>-</span>
        </template>
      </Table>
      <div class="block_20"></div>
      <KPage
        :total="total"
        :page-size="+queryFormData.pageSize"
        :current="+queryFormData.page"
        @on-change="handleCurrentChange"
        @on-page-size-change="handleSizeChange"
        style="text-align: center"
      />
    </div>
    <refuse-modal
      v-model="refuseModalVisible"
      title="确认驳回这条费用记录？"
      :maxlength="20"
      @ok="review"
    ></refuse-modal>
  </div>
</template>

<script>
import S from 'utils/util';
import search from '@/mixins/search';
import renderHeader from '@/mixins/renderHeader';
import RefuseModal from '../../components/refuseModal/refuseModal.vue';
const init_query_form_data = {
  page: 1,
  pageSize: 20,
  st: '',
  ent: '',
  status: '1',
  r: ''
};
export default {
  name: 'list',
  mixins: [search, renderHeader],
  components: { RefuseModal },
  data() {
    return {
      apiName: 'getFeeList', // 获取明细列表
      queryFormData: { ...init_query_form_data },

      refuseModalVisible: false,

      times: [],
      status_remark: '',
      status: '',
      rowData: {},

      statusOpt: [],
      tableCols: [
        {
          title: '序号',
          type: 'index',
          width: 60,
          align: 'center'
        },
        { title: '费用类型', key: 'fee_type_text', align: 'center', width: 90 },
        { title: '费用金额', key: 'amount', align: 'center', width: 150 },
        { title: '平摊时间', key: 'divide_month_text', align: 'center', width: 80 },
        { title: '记录时间', key: 'record_time', align: 'center', width: 140 },
        {
          title: '提交人',
          key: 'operator',
          align: 'center'
        },
        {
          title: '提交门店',
          key: 'clinic_name',
          align: 'center'
        },
        {
          title: '状态',
          key: 'status_text',
          align: 'center'
        },
        {
          title: '操作人',
          slot: 'check_operator',
          align: 'center'
        },

        { title: '操作', slot: 'action', align: 'center', fixed: 'right', width: 150 }
      ]
    };
  },
  computed: {},
  watch: {},
  created() {
    this.getFeeOptions();
    this.queryFormData = S.merge(this.queryFormData, this.$route.query);
    this.submitQueryForm(true);
  },
  mounted() {},
  methods: {
    onTimesChange(times) {
      this.handleTimeChange(times);
    },

    onResetSearch() {
      this.queryFormData = { ...init_query_form_data };
      this.submitQueryForm();
    },

    getFeeOptions() {
      this.$api.getFeeOpt().then(res => {
        this.statusOpt = S.descToArrHandle(res.status_desc);
      });
    },

    review(reason) {
      this.$api
        .postCheck({
          id: this.rowData.id,
          status_remark: reason,
          status: this.status
        })
        .then(() => {
          this.submitQueryForm(true);
        })
        .catch(res => {
          this.$Message.error(res.errmsg);
        });
    },

    onAction(row, status) {
      this.status = status;
      this.rowData = row;
      if (status == 2) {
        this.$Modal.confirm({
          title: '通过审核',
          content: '您确定要通过该审核吗？',
          onOk: () => {
            this.review();
          }
        });
      } else if (status == 3) {
        this.refuseModalVisible = true;
      }
    }
  },
  beforeRouteUpdate(to, from, next) {
    this.loadList();
    next();
  }
};
</script>

<style scoped lang="less">
.expense-wrapper {
  background-color: #fff;
  padding: 12px;
  margin-top: 12px;
}

:deep(.ivu-form-inline .ivu-input-wrapper) {
  width: 200px;
}
</style>
