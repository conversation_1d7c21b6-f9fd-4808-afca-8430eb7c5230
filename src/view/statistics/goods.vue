<template>
  <div class="wrapper">
    <!-- Header -->
    <header class="head-block ceiling flex flex-item-between">
      <div class="flex flex-item-align">
        <p>统计时间：</p>
        <customDatePicker v-model="formDate" @getDateType="getDateType"></customDatePicker>
      </div>
      <clinic-search
          v-model="clinic_id"
          className="statistics-sel"
          placeholder="全部诊所"
          @change="changeClinicId"
          is-clearable
          :extra-params="{ scope: 'department' }"
      />
    </header>

    <!-- 整体情况 -->
    <div class="block">
      <h4 class="block-title">整体情况</h4>
      <!-- 卡片数据 -->
      <div class="mt16 flex flex-warp">
        <div 
          class="card-block cursor mb16 card-block--hover" 
          v-for="(card_item, card_index) in goods_card_options" 
          :key="'card'+card_index"
          :class="{'card-block--actived' : current_card_index === card_index }"
         @click.prevent="cardChange(card_item, card_index)"
          >
          <p class="card-title flex flex-item-between pr20" >
            <span>{{ card_item.label }}</span>
            <svg-icon
              v-if="!clinic_id"
              @click.stop="arrowEvent(card_item, card_index)"
              iconClass="circleArrow"
              class="circle-arrow"
              :class="{'circle-arrow--actived': current_card_index === card_index}"
            ></svg-icon>
          </p>
          <p class="card-content mt12" v-if="card_item.isMoney">￥{{ card_item.current | number_format(2) }}</p>
          <p class="card-content mt12" v-else>{{ card_item.current || 0 }}</p>
          <div v-show="date_type">
            <p class="card-tip mt6" v-if="card_item.isMoney">{{ custom_lastText }}：￥{{ card_item.last | number_format(2) }}</p>
            <p class="card-tip mt6" v-else>{{ custom_lastText }}：{{ card_item.last || 0 }}</p>
          </div>
        </div>
      </div>

      <!-- 折线趋势图 -->
      <div class="mt30">
        <chart-view height="260px" :chart-option="line_options"></chart-view>
      </div>
    </div>

    <!-- 商品销售额占比分析 -->
    <div class="block mt16">
      <h4 class="block-title">商品销售额占比分析</h4>
      <!-- content -->
      <div class="mt30 flex">

        <div class="flex-1 pr30">
          <Table class="custom-table-line" :columns="saleCols" :data="sale_lists" v-if="sale_lists.length" :span-method="handleSpan" border>
            <template slot-scope="{row,index}" slot="title" class="flex">
              <!--              <i class="circle" :style="{borderColor:color_table_enum[index]}"></i>-->
              <span>{{ row.title }}</span>
            </template>
            <template slot-scope="{row}" slot="turnover">
              ￥{{ row.turnover }}
            </template>
            <template slot-scope="{row}" slot="inner_turnover">
              ￥{{ row.inner_turnover }}
            </template>
          </Table>
          <div class="empty" v-else >
            暂无数据
          </div>
        </div>

        <div class="flex-1 flex flex-item-align">
          <!-- right bar -->
          <div class="flex-1 flex">
            <div class="width100">
              <chart-view height="260px" :chart-option="sale_bar_options"></chart-view>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 商品销售场景分析 -->
    <div class="block mt16">
      <h4 class="block-title">商品销售场景分析</h4>

      <!-- tag -->
      <div class="mt16 flex">
        <div 
          class="custom-tag--default ml10"
          v-for="(tag_item, tag_index) in tag_sales" 
          :key="'tag'+tag_index"
          :class="{'custom-tag--actived' : current_tag_index === tag_index, 'custom-tag--hover': current_tag_index !== tag_index }"
          @click="tagChange(tag_item, tag_index)"
        >{{ tag_item.label }}</div>
      </div>

      <!-- content -->
      <div class="mt30 flex">
        <div class="flex-3 pr30 flex flex-item-align">
          <div class="card-show-block flex ml50">
            <div class="flex-1 width50 right-line">
              <p class="show-title">销售件数
                <span v-if="tag_sales[current_tag_index].label == '中药饮片' ">（kg）</span>
                <span v-else>（件）</span>
              </p>
              <p class="show-number mt7">{{ tag_sales_current.quantity }}</p>
            </div>
            <div class="flex-1 width50">
              <p class="show-title pl60">销售金额</p>
              <p class="show-number mt7 pl60">￥{{ tag_sales_current.money }}</p>
            </div>
          </div>
        </div>

        <div class="flex-4">
          <!-- right bar -->
          <div class="flex-1 flex">
            <div class="width40">
              <chart-view height="260px" :chart-option="consumption_bar_options"></chart-view>
            </div>
            <div class="width60 flex flex-item-align">
              <Table :columns="consumptionBarCols" :data="consumption_bar_list" v-if="consumption_bar_list.length" class="custom-table">
                <template slot-scope="{row,index}" slot="title" class="flex">
                  <!--              <i class="circle" :style="{borderColor:color_table_enum[index]}"></i>-->
                  <span>{{ row.title }}</span>
                </template>

                <template slot-scope="{row,index}" slot="money" class="flex">
                  <span v-if="row.money">￥{{ row.money }}</span>
                  <span v-else>-</span>
                </template>

                <template slot-scope="{row,index}" slot="percent" class="flex">
                  <span>{{ row.percent }}%</span>
                </template>
              </Table>
              <div class="empty width100" v-else >
                暂无数据
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- tab切换 -->
    <div class="mt16">
      <Tabs :value="currentTab" class="custom-tabs" @on-click="tabChange" >
        <TabPane label="HIS开方应用分析" name="HIS">
          <goodsHis v-if="currentTab == 'HIS'" :formDate="formDate" :clinic_id="clinic_id" :date_type="date_type"></goodsHis>
        </TabPane>
        <TabPane label="商城订单分析" name="SHOP">
          <goodsShop v-if="currentTab == 'SHOP'" :formDate="formDate" :clinic_id="clinic_id" :date_type="date_type"></goodsShop>
        </TabPane>
      </Tabs>
    </div>

    <!-- 商品交易明细 -->
    <div class="block mt16">
      <div class="flex flex-item-between">
        <div class="flex custom-tabs-small">
          <p class="cursor custom-tabs-small--default" @click="shopTabChange('trading')" :class="{'custom-tabs-small--actived': shopCurrent == 'trading'}">商品交易明细</p>
          <p class="cursor ml20 custom-tabs-small--default" @click="shopTabChange('refund')" :class="{'custom-tabs-small--actived': shopCurrent == 'refund'}">商品退款明细</p>
        </div>
        <div>
          <!-- <a v-if="shopCurrent == 'trading'" @click="exportExcel()">导出交易明细</a>
          <a v-if="shopCurrent == 'refund'" @click="exportExcel()">导出退款明细</a> -->
        </div>
      </div>
      <Table v-if="shopCurrent == 'trading'" class="mt9" :columns="goodsTradingCols" :data="goods_trading_list" :loading="tabLoading">
        <template slot-scope="{row, index}" slot="money">
          <p v-if="row.money" >￥{{ row.money }}</p>
          <p v-else>-</p>
        </template>

        <template slot-scope="{row, index}" slot="his_money">
          <p v-if="row.money" >￥{{ row.his_money }}</p>
          <p v-else>-</p>
        </template>

        <template slot-scope="{row, index}" slot="shop_money">
          <p v-if="row.money" >￥{{ row.shop_money }}</p>
          <p v-else>-</p>
        </template>
      </Table>
      <Table v-if="shopCurrent == 'refund'" class="mt9" :columns="goodsRefundCols" :data="goods_refund_list" :loading="tabLoading">
        <template slot-scope="{row, index}" slot="money">
          <p v-if="row.money" >￥{{ row.money }}</p>
          <p v-else>-</p>
        </template>

        <template slot-scope="{row, index}" slot="his_money">
          <p v-if="row.money" >￥{{ row.his_money }}</p>
          <p v-else>-</p>
        </template>

        <template slot-scope="{row, index}" slot="shop_money">
          <p v-if="row.money" >￥{{ row.shop_money }}</p>
          <p v-else>-</p>
        </template>
      </Table>
      <div class="page-wrapper">
        <KPage 
          :current="+queryFormData.page"
          :page-size="+queryFormData.pageSize"
          :total="+total"
          @on-change="onPageChange"
          @on-page-size-change="handleSizeChange"
        />
      </div>
    </div>
    <OverallDetailModal :propDateType="propDateType" :propTime="formDate"  :overallSituations="goods_card_options" :visible.sync="overAllVisible" :current-field="currentField" :field-title="fieldTitle"></OverallDetailModal>
  </div>
</template>

<script>
import { goods_card_options, goods_tag_sales } from './data/goods_data';
import { color_enum } from "@/view/statistics/data/color";
import goodsHis from './components/goods/goods-his';
import goodsShop from './components/goods/goods-shop';
import dateChange from './mixins/dateChange';
const init_query_form_data = {
  page: 1,
  pageSize: 10,
}
  export default {
    name: "goods",
    components: {
      goodsHis,
      goodsShop,
    },
    mixins: [dateChange],
    props: {

    },
    data () {
      return {
        color_table_enum: color_enum,//色值表

        /* 整体情况 */
        goods_card_options: goods_card_options, // 卡片数据枚举
        current_card_index: 0, // 当前选中的卡片索引
        line_options: {},

        /* 商品销售额占比分析 */
        saleCols: [
          {title: '销售来源', slot: 'title', align: 'center',minWidth: 80},
          {title: '总金额(元)', slot: 'turnover', align: 'center',minWidth: 80},
          {title: '类型', key: 'type_text', align: 'center', minWidth: 60},
          {title: '订单数', key: 'num', align: 'center', minWidth: 60},
          {title: '金额(元)', slot: 'inner_turnover', align: 'center', minWidth: 60},
        ],
        sale_lists: [],
        sale_bar_options: {}, // 商品销售额占比分析饼图options

        /* 商品销售场景分析 */
        tag_sales: goods_tag_sales, // tag
        tag_sales_current: {
          money: '',
          quantity: '',
          order_num: '',
          source: []
        }, // tag对应的显示数据
        current_tag_index: 0, // 当前选中的tag索引
        consumption_bar_options: {}, // 商品销售场景分析饼图options
        consumptionBarCols: [
          {title: '销售途径', slot: 'title', align: 'left',tooltip: true,minWidth:110},
          {title: '销售件数', key: 'quantity', align: 'center',minWidth: 64},
          {title: '销售金额', slot: 'money', align: 'center', minWidth: 100},
          {title: '关联订单', key: 'order_num', align: 'center', minWidth: 64},
          {title: '占比', slot: 'percent', align: 'right', minWidth: 60},
        ],
        consumption_bar_list: [],

        /* tab切换 */
        currentTab: 'HIS',

        /* 商品 */
        shopCurrent: 'trading', // 商品当前展示得tab  
        goodsTradingCols: [
          { title: '商品名称', key: 'name', align: 'center' },
          { title: '类型', key: 'type_text', align: 'center' },
          { title: '单位', key: 'unit', align: 'center' },
          { title: '销售总件数', key: 'num', align: 'center' },
          { title: '销售总金额', slot: 'money', align: 'center' },
          { title: 'HIS诊疗销售数', key: 'his_num', align: 'center' },
          { title: 'HIS诊疗销售金额', slot: 'his_money', align: 'center' },
          { title: '商城销售件数', key: 'shop_num', align: 'center' },
          { title: '商城销售金额', slot: 'shop_money', align: 'center' },
        ],
        goods_trading_list: [], // 商品交易

        goodsRefundCols: [
          { title: '商品名称', key: 'name', align: 'center' },
          { title: '类型', key: 'type_text', align: 'center' },
          { title: '单位', key: 'unit', align: 'center' },
          { title: '退款总件数', key: 'num', align: 'center' },
          { title: '退款总金额', slot: 'money', align: 'center' },
          { title: 'HIS诊疗退款数', key: 'his_num', align: 'center' },
          { title: 'HIS诊疗退款金额', slot: 'his_money', align: 'center' },
          { title: '商城退款件数', key: 'shop_num', align: 'center' },
          { title: '商城退款金额', slot: 'shop_money', align: 'center' },
        ],
        goods_refund_list: [], // 商品退款
        queryFormData: {...init_query_form_data},
        total: 0, // 表格总条数
        tabLoading: false,

        alloverIsMoney: true,
      }
    },
    computed: {

    },
    watch: {
      
    },
    created() {

    },
    mounted() {
    },
    methods: {
      // 当时间发生变化，进行得操作
      dateChange () {
        // 趋势图
        this.getTrend(this.goods_card_options, this.current_card_index)
        this.getGoodsOverview()
        this.getGoodsSalesanalysis()
        // 商品销售场景分析
        this.tagChange(this.tag_sales[0], 0)
        // 商品明细
        this.shopTabChange(this.shopCurrent)
      },

      /* 卡片切换事件 */
      cardChange ( card_item, card_index ) {
        if ( card_index == this.current_card_index ) return

        this.current_card_index = card_index
        this.getTrend(this.goods_card_options, this.current_card_index)
      },

      /* tag点击事件 */ 
      tagChange ( tag_item, tag_index ) {
        this.current_tag_index = tag_index
        this.getGoodsItemanalysis(tag_item.kw)
      },
      
      // 处理商品销售额占比分析饼图数据
      handleSaleOptions (list) {
        let resultList = []
        list && list.forEach( item => {
          resultList.push({
            ...item,
            value: item.money,
            tootipList: [
              { key: '销售来源:', value: item.title },
              { key: '订单数', value: `${item.num}` },
              { key: '金额', value: `￥${item.money}` },
              { key: '金额占比', value: `${item.percent}%` }
            ]
          })
        } )
        this.setPieConfigure('sale_bar_options', resultList, '商品销售额占比分析')
      },

      // 商品销售场景分析 tag切换时，改变饼图数据
      setConsumptionOptions (tag_item) {
        let current_title = this.tag_sales[this.current_tag_index].label
        let title = `${current_title}销售场景分析`
        let resultList = []
        let unit = current_title == '中药饮片' ? 'kg' : '件'
        tag_item && tag_item.source.forEach( item => {
          resultList.push({
            ...item,
            value: item.order_num,
            tootipList: [
              { key: '销售途径:', value: item.title },
              { key: '关联订单:', value: item.order_num },
              { key: '累计销售', value: `${item.quantity}${unit}  ￥${item.money}` },
              { key: '金额占比', value: `${item.percent}%` }
            ]
          })
        } )

        // 待服务客户分布
        this.setPieConfigure('consumption_bar_options', resultList, title)
      },

      /* tab切换 */ 
      tabChange ( name ) {
        this.currentTab = name
      },

      // 表格合并单元格
      handleSpan ( { row, column, rowIndex, columnIndex } ) {
        if ( columnIndex === 0 ) {
          // 计算合并的行数列数
          let x = row.colsLength === 0 ? 0 : row.colsLength
          let y = row.colsLength === 0 ? 0 : 1
          return [x, y]
        }

        if ( columnIndex === 1 ) {
          // 计算合并的行数列数
          let x = row.colsLength === 0 ? 0 : row.colsLength
          let y = row.colsLength === 0 ? 0 : 1
          return [x, y]
        }
      },

      // 处理商品销售合并单元格的表格数据
      handleSaleLists (list) {
        let resultList = []
        list && list.forEach( first_item => {
          first_item.list.forEach( (inner_item, inner_index) => {
            resultList.push({
              title: first_item.title,
              turnover: first_item.money || 0,
              type_text: inner_item.title,
              num: inner_item.num,
              inner_turnover: inner_item.money || 0,
              colsLength: inner_index == 0 ? first_item.list && first_item.list.length : 0
            })
          } )
        } )
        return resultList
      },

      // 商品tab切换
      shopTabChange (val) {
        this.queryFormData = {...init_query_form_data}
        this.shopCurrent = val
        if ( val === 'trading' ) {
          this.getGoodsTradelist()
          this.goods_refund_list = []
        }
        if ( val === 'refund' ) {
          this.getGoodsRefundlist()
          this.goods_trading_list = []
        }
      },

      // 导出-drop
      exportExcel () {

      },
       // 表格分页
      handleSizeChange (pageSize) {
        this.queryFormData.pageSize = pageSize
        if ( this.queryFormData.page == 1 ) {
          this.getList()
        }
      },
      onPageChange (page) {
        this.queryFormData.page = page
        this.getList()
      },

      getList () {
        if ( this.shopCurrent === 'trading' ) {
          this.getGoodsTradelist()
        }
        if ( this.shopCurrent === 'refund' ) {
          this.getGoodsRefundlist()
        }
      },

      /* API */
      // 获取整体情况
      getGoodsOverview () {
        let params = {
          st: this.formDate[0],
          et: this.formDate[1],
          date_type: this.date_type,
          clinic_id: this.clinic_id
        }
        this.$api.getGoodsOverview(params).then( res => {
          this.goods_card_options.forEach( item => {
            item.current = res.current[item.value]
            item.last = res.last[item.value]
          } )
        } ).catch( error => this.$Message.error(error.errmsg) )
      },

      // 商品销售分析
      getGoodsSalesanalysis () {
        let params = {
          st: this.formDate[0],
          et: this.formDate[1],
          date_type: this.date_type,
          clinic_id: this.clinic_id
        }
        this.$api.getGoodsSalesanalysis(params).then( res => {
          this.sale_lists = this.handleSaleLists(res.goods_sales_distribution)
          // 处理商品销售额占比分析饼图数据
          this.handleSaleOptions(res.goods_sales_list)
        } )
      },
      // 商品销售场景分析
      getGoodsItemanalysis (type) {
        this.consumption_bar_list = []
        let params = {
          st: this.formDate[0],
          et: this.formDate[1],
          type,
          date_type: this.date_type,
          clinic_id: this.clinic_id
        }
        this.$api.getGoodsItemanalysis(params).then( res => {
          this.tag_sales_current = res
          
          let resultList = []
          res.source.forEach( item => {
            resultList.push({
              ...item,
              quantity: item.quantity
            })
          } )
          this.consumption_bar_list = resultList
          this.setConsumptionOptions(res)
        } )
      },
      // api-商品交易明细
      getGoodsTradelist () {
        this.tabLoading = true
        let params = {
          ...{
            st: this.formDate[0],
            et: this.formDate[1],
          },
          ...this.queryFormData,
          clinic_id: this.clinic_id
        }
        this.$api.getGoodsTradelist(params).then( res => {
          this.goods_trading_list = res.list
          this.total = res.total
        } ).finally( () => this.tabLoading = false )
      },
      // api-商品退款明细
      getGoodsRefundlist () {
        this.tabLoading = true
        let params = {
          ...{
            st: this.formDate[0],
            et: this.formDate[1],
          },
          ...this.queryFormData,
          clinic_id: this.clinic_id
        }
        this.$api.getGoodsRefundlist(params).then( res => {
          this.goods_refund_list = res.list
          this.total = res.total
        } ).finally( () => this.tabLoading = false )
      }
    },
    filters: {

    }
  }
</script>
<style lang="less" scoped>
@import url('./style/common.less');
</style>