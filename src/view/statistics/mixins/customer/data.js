import { situation_tooltip_list, customer_card_options, customer_tag_consumptions } from '../../data/customer_data';
import { color_enum } from "@/view/statistics/data/color";
export default {
  data () {
    return {
      situation_tooltip_list: situation_tooltip_list, // 整体情况字段说明
      color_table_enum: color_enum,//色值表
      
      /* 整体情况 */
      customer_card_options: customer_card_options, // 卡片数据枚举
      current_card_index: 0, // 当前选中的卡片索引
      line_options: {}, // 卡片下方的趋势图

      /* 客户消费情况分析 */
      tag_consumptions: customer_tag_consumptions, // tag
      current_tag_index: 0, // 当前选中的tag索引
      consumptionCols: [
        {title: '支付次数', slot: 'title', align: 'center', minWidth: 200, tooltip: true},
        {title: '人数', key: 'num', align: 'center', width: 80},
        {title: '占比', slot: 'rate', align: 'center', width: 80},
      ],
      consumption_lists: [],
      consumption_bar_options: {}, // 客户消费情况饼图options

      /* 客户到店意向分析 */
      arrivalCols: [
        {title: '客户意向', key: 'title', tooltip: true,minWidth: 100},
        {title: '人数', key: 'num', align: 'left',width: 80},
        {title: '次数', key: 'num', align: 'left',width: 80},
        {title: '占比', key: 'rate', align: 'right', width: 60},
      ],
      arrival_lists: [{num: 100}],
      arrival_bar_options: {
        seriesData: [],
        color: color_enum,
        legendData: [],
        name: '客户支付金额占比',
      }, // 客户到店意向分析饼图options
      arrivalBarCols: [
        {title: '客户意向', slot: 'title', tooltip: true,width: 100},
        {title: '人数', key: 'num', align: 'left',width: 80},
        {title: '次数', key: 'num', align: 'left',width: 50},
        {title: '占比', key: 'rate', align: 'right', width: 50},
      ],
      arrival_bar_list: [{title: 'title', num: 10}],

      /* 剩余储值金额分布 */ 
      surplus_data: {},
      surplusCols: [
        {title: '剩余储值金额', slot: 'title', tooltip: true,minWidth: 100},
        {title: '人数', key: 'num', align: 'center',width: 80},
        {title: '人数占比', slot: 'rate', align: 'right', width: 80},
      ],
      surplus_lists: [],
      
      /* 待服务客户分布 */
      serviceCols: [
        {title: '待服务次数', slot: 'title', tooltip: true,minWidth: 100},
        {title: '人数', key: 'num', align: 'center',minWidth: 80},
        {title: '占比', slot: 'rate', align: 'center', minWidth: 60},
      ],
      wait_serv_info: {},
      service_lists: [],
      service_bar_options: {}, // 待服务客户分布饼图options

      /* 客户首购周期分布 */
      first_data: {},
      firstCols: [
        {title: '首购周期(天)', slot: 'title', tooltip: true,minWidth: 100},
        {title: '人数', key: 'num', align: 'left',width: 80},
        {title: '占比', slot: 'rate', align: 'right', width: 60},
      ],
      first_lists: [],
      
      /* 客户来源渠道排行 */ 
      fromCols: [
        {title: '排名', slot: 'rank', width: 160, align:'left'},
        {title: '来源渠道', slot: 'text', align: 'left', minWidth: 200, tooltip: true},
        {title: '客户数', key: 'num', align: 'center', width: 80},
        {title: '占比', slot: 'rate', align: 'right', width: 80},
      ],
      from_lists: [],
      from_bar_options: {}, // 客户消费次数饼图options

      /* 客户性别，年龄分布 */ 
      sex_bar_options: {}, // 客户性别饼图options
      sexBarCols: [
        {title: '性别', slot: 'text', tooltip: true,width: 70},
        {title: '人数', key: 'num', align: 'center',width: 80},
        {title: '占比', slot: 'rate', align: 'right', width: 60},
      ],
      sex_bar_list: [],

      age_bar_options: {}, // 客户性别饼图options
      ageBarCols: [
        {title: '年龄', slot: 'age_text', tooltip: true,width: 100},
        {title: '人数', key: 'num', align: 'center',width: 50},
        {title: '占比', slot: 'rate', align: 'right', width: 60},
      ],
      age_bar_list: [],
    }
  }
}