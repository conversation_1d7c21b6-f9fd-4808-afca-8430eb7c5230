import customDatePicker from '../components/CustomDatePicker/CustomDatePicker';
import clinicSearch from '_c/clinic-search/k-clinic-search_data';
import OverallDetailModal from '../components/OverallDetailModal/OverallDetailModal';
import showEcharts from '@/mixins/showEcharts';
export default {
  mixins: [showEcharts],
  data() {
    return {
      formDate: [],
      date_type: '', // 获取数据后的确认的状态
      _date_type: '', // 实时状态
      custom_lastText: '',
      clinic_id: '', // 诊所id
      alloverIsMoney: true,

      // 弹窗
      overAllVisible: false,
      currentField: '',
      fieldTitle: '',
      propDateType: ''
    };
  },
  components: {
    customDatePicker,
    clinicSearch,
    OverallDetailModal
  },
  watch: {
    formDate: {
      handler(val) {
        if (val && val[0]) {
          this.setDateType();
          this.dateChange();
          this.custom_lastText = this.__lastText;
        }
      }
    }
  },
  computed: {
    __lastText() {
      switch (this.date_type) {
        case 'day':
          return '前一日';
        case 'week':
          return '前一周';
        case 'month':
          return '前一月';
        default:
          return '';
      }
    }
  },
  methods: {
    // 统计时间
    getDateType(getDateType) {
      this._date_type = this.propDateType = getDateType;
    },
    setDateType() {
      if (this._date_type !== 'daterange') {
        this.date_type = this._date_type;
      } else {
        this.date_type = '';
      }
    },
    // 切换诊所
    changeClinicId() {
      // this.dateChange();
      if (this.clinic_id) {
        this.show = true;
      } else {
        this.show = false;
      }
    },

    // x文本超出隐藏
    formatterText(value) {
      // 超过7个字就截断加省略号
      return value.length > 8 ? value.slice(0, 8) + '...' : value;
    },

    // 箭头点击事件
    arrowEvent(card_item, card_index) {
      // 如果不相等，说明点击了未选中的卡片箭头
      if (card_index !== this.current_card_index) {
        this.cardChange(card_item, card_index);
      }
      this.currentField = card_item.value;
      this.fieldTitle = card_item.label;
      this.overAllVisible = true;
      this.alloverIsMoney = card_item.isMoney ? true : false;
    },

    // 获取趋势图
    getTrend(cardList, currentIndex) {
      let params = {
        st: this.formDate[0],
        et: this.formDate[1],
        date_type: this.date_type,
        item: cardList[currentIndex].value,
        clinic_id: this.clinic_id
      };
      this.$api.getTrend(params).then(res => {
        const xData = res.list.map(item => item.x);
        const yData = res.list.map(item => item.y);

        this.line_options = this.$eChartFn.areaLineOptions(
          {
            xAxis: {
              data: xData
            },
            series: [{ data: yData }]
          },
          cardList[currentIndex].label,
          cardList[currentIndex].isMoney || false
        );
      });
    },

    /**
     * 单个折线图配置
     * @param {*} key
     * @param {*} data 数据来源
     * @param {*} params  额外配置
     */
    setLineConfigure(key, data, params) {
      const opt = {
        color: '#2AC769',
        name: '示例',
        ...params
      };

      // 数据根据-实际处理
      const xData = data.xData;
      const seriesData = data.seriesData;

      this[key] = this.$eChartFn.areaLineOptions({
        color: [opt.color], // 柱状图绿色，
        legend: {
          data: [opt.name],
          top: 0,
          right: 10,
          itemWidth: 16,
          itemHeight: 8,
          textStyle: {
            color: '#333'
          }
        },
        grid: {
          left: 0,
          right: 0,
          bottom: 45,
          top: 30,
          containLabel: true
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: { type: 'shadow' },
          position: null,
          backgroundColor: '#fff',
          padding: 8,
          formatter: null
        },
        dataZoom: [
          {
            type: 'slider',
            show: true,
            xAxisIndex: 0,
            height: 20,
            left: 40,
            right: 40,
            bottom: 10,
            startValue: 0,
            endValue: 12
          }
        ],
        xAxis: {
          type: 'category',
          data: xData,
          axisLabel: {
            color: '#666',
            formatter: this.formatterText,
            show: true,
            interval: 'auto'
          },
          axisLine: {
            show: true,
            lineStyle: { color: '#E5E5E5' }
          }
        },
        yAxis: {
          type: 'value',
          show: true,
          axisLine: { show: false },
          axisTick: { show: false },
          splitLine: { show: false },
          axisLabel: {
            color: '#666',
            fontSize: 12,
            formatter: function (value) {
              return value + '%';
            }
          }
        },
        series: [
          {
            name: opt.name,
            type: 'line',
            data: seriesData,
            symbol: 'circle',
            symbolSize: 8,
            smooth: false,
            itemStyle: {
              normal: null,
              color: opt.color
            },
            showSymbol: true,

            areaStyle: null,
            lineStyle: {
              color: opt.color,
              width: 2
            },
            label: {
              show: true,
              position: 'top',
              color: opt.color,
              fontSize: 12
              // formatter: function (params) {
              //   return params.value ;
              // }
            }
          }
        ]
      });
    },

    // echarts配置
    /**
     * @description: 饼图配置
     * value: 配置函数模板
     * data: 数据源
     * title: 饼图title
     * isShow: 是否显示toolTip, 默认显示
     * */
    setPieConfigure(value, data, title, isShow = true) {
      data = data.map(item => ({
        name: item.title,
        value: item.thisMonth
      }));
      this[value] = this.$eChartFn.orderPie({
        legend: {
          type: 'scroll',
          show: true,
          bottom: '24px',
          left: 'center',
          name: 'consumption'
        },
        series: [
          {
            data,
            name: 'consumption'
          }
        ],
        title: { text: title },
        tooltip: {
          formatter: a => {
            let content = '';
            a.data.tootipList &&
              a.data.tootipList.forEach((item, index) => {
                if (index == 0) {
                  content =
                    content +
                    `<span style="font-size: 13px;font-weight: bold;color: #333333;line-height: 17px;">${item.key}</span>
                <span style="font-size: 13px;font-weight: bold;color: #333333;line-height: 17px;">${item.value}</span></br>`;
                } else {
                  content =
                    content +
                    `<span style="font-size: 11px;font-weight: 300;color: #A7AFC0;line-height: 15px;">${item.key}</span>
                <span style="font-size: 11px;font-weight: 300;color: #A7AFC0;line-height: 15px;margin-right: 10px">${item.value}</span>`;
                }
              });
            return `<div class="flex-col">
                      ${content}
                    </div>
                    `;
          },
          show: isShow
        }
      });
    },

    /**
     * @description: 饼图配置, 默认配置引导线展示，不做tooltip的饼图
     * value: 配置函数模板
     * data: 数据源
     * title: 饼图title
     * */
    setPieConfigure_guide(value, data, title) {
      const isSmallScreen = window.innerWidth < 1200;
      const sorted = [...data].sort((a, b) => b.value - a.value);
      const radius = isSmallScreen ? [10, '35%'] : [15, '45%'];
      const legendOrient = isSmallScreen ? 'vertical' : 'horizontal';
      const legendBottom = isSmallScreen ? 0 : '24px';

      const newData = sorted.map((item, idx) => ({
        ...item,
        labelLine: { show: idx < 10 },
        label: { show: idx < 10 },
        emphasis: {
          label: { show: true },
          labelLine: { show: true }
        }
      }));
      this[value] = this.$eChartFn.orderPie({
        legend: {
          type: 'scroll',
          show: true,
          name: 'consumption',
          orient: legendOrient,
          left: isSmallScreen ? 'right' : 'center',
          bottom: legendBottom,
          top: isSmallScreen ? 20 : undefined
        },
        series: [
          {
            data: newData,
            name: '',
            radius,
            center: ['50%', '50%'],
            label: {
              // alignTo: 'edge', // 文字对齐，文字的边距由 label.edgeDistance 决定。
              // edgeDistance: 10,
              show: true,
              lineHeight: 15,
              formatter: function (e) {
                let data = e.data;
                return `{a|${data.title}: }{b|${data.value}} {c|(${data.value})%}`;
              },
              rich: {
                a: {
                  color: '#A7AFC0',
                  fontSize: 11,
                  lineHeight: 16,
                  fontWeight: '400',
                  align: 'left'
                },
                b: {
                  fontSize: 11,
                  lineHeight: 16,
                  align: 'left',
                  fontWeight: 300,
                  color: '#A7AFC0'
                },
                c: {
                  fontSize: 11,
                  lineHeight: 16,
                  align: 'left',
                  fontWeight: 300,
                  color: '#A7AFC0'
                }
              }
            },
            labelLine: {
              length: 20,
              length2: 40
            }
          }
        ],
        title: { text: title },
        tooltip: {
          // formatter: function (params) {
          //   console.log('🚀 ~ dateChange.js:367 ~ setPieConfigure_guide ~ params=>', params);
          //   // params.percent 是 ECharts 自动计算的百分比
          //   return `${params.marker}${params.name}<br/>数量: ${params.value}<br/>占比: ${params.percent}%`;
          // },
          formatter: a => {
            let content = '';
            a.data.tootipList &&
              a.data.tootipList.forEach((item, index) => {
                if (index == 0) {
                  content =
                    content +
                    `<span style="font-size: 13px;font-weight: bold;color: #333333;line-height: 17px;">${item.key}</span>
                <span style="font-size: 13px;font-weight: bold;color: #333333;line-height: 17px;">${item.value}</span></br>`;
                } else {
                  content =
                    content +
                    `<span style="font-size: 11px;font-weight: 300;color: #A7AFC0;line-height: 15px;">${item.key}</span>
                <span style="font-size: 11px;font-weight: 300;color: #A7AFC0;line-height: 15px;margin-right: 10px">${item.value}</span>`;
                }
              });
            return `<div class="flex-col">
                      ${content}
                    </div>
                    `;
          },
          show: data[0]?.tooltip?.length
          // show: true
        }
      });
    },

    /**
     * @description: x轴滚动柱状图配置
     * key: 配置函数模板
     * data: 数据源
     * title: title
     * */
    setBarConfigure_zoom(key, data, params = {}) {
      const opt = {
        name: '示例',
        color: '#3184F1',
        ...params
      };

      const xData = data.xData;
      const yData = data.yData;

      this[key] = this.$eChartFn.verticalBar({
        color: [opt.color],
        legend: {
          data: [opt.name],
          top: 0,
          right: 0,
          itemWidth: 14,
          itemHeight: 14,
          textStyle: {
            color: '#666'
          }
        },
        grid: {
          left: 0,
          right: 0,
          bottom: 40,
          top: 30,
          containLabel: true
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          }
        },
        dataZoom: [
          {
            type: 'slider',
            show: true,
            xAxisIndex: 0,
            height: 20,
            left: 40,
            right: 40,
            bottom: 5,
            startValue: 0,
            endValue: 12
          }
        ],

        xAxis: {
          type: 'category',
          data: xData,
          axisLabel: {
            color: '#666',
            fontSize: 12,
            formatter: this.formatterText,
            show: true,
            interval: 'auto'
          },
          axisLine: {
            lineStyle: { color: '#E5E5E5' }
          }
        },
        yAxis: {
          type: 'value',
          name: '',
          show: true,
          axisLine: { show: false },
          axisTick: { show: false },
          splitLine: { show: false },
          axisLabel: {
            color: '#666',
            fontSize: 12,
            formatter: function (value) {
              return '¥' + value;
            }
          }
        },
        series: [
          {
            name: opt.name,
            data: yData,
            type: 'bar',
            barWidth: '30%',
            itemStyle: {
              color: opt.color
            },
            label: {
              show: true,
              position: 'top',
              color: '#333',
              fontSize: 12,
              textStyle: {
                color: opt.color
              }
            }
          }
        ]
      });
    },

    /**
     * @description: x轴滚动-双柱状+折线图配置
     * key: 配置函数模板
     * data: 数据源
     * params: 额外参数
     * */
    setBarALineConfigure(key, data, params = {}) {
      // 数据处理根据接口数据调整
      const xData = data.xData;
      const bar_last_Data = data.barLastData;
      const bar_this_Data = data.barThisData;
      const lineData = data.lineData;

      const opt = {
        color: ['#3184F1', '#2AC769', '#FFB800'],
        names: ['上月同期总收入（求和）', '本月总收入（求和）', '总收入同比增长率（求和）'],
        dataZoom: true,
        ...params
      };

      this[key] = this.$eChartFn.verticalBar({
        color: opt.color, // 柱状图绿色，折线图黄色
        legend: {
          data: ['上月同期总收入（求和）', '本月总收入（求和）', '总收入同比增长率（求和）'],
          top: 0,
          right: 'center',
          itemWidth: 16,
          itemHeight: 8,
          textStyle: {
            color: '#333'
          }
        },
        grid: {
          left: 40,
          right: 40,
          bottom: 40,
          top: 50,
          containLabel: true
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: { type: 'shadow' }
        },
        dataZoom: opt.dataZoom
          ? [
              {
                type: 'slider',
                show: true,
                xAxisIndex: 0,
                height: 20,
                left: 40,
                right: 40,
                bottom: 10,
                startValue: 0,
                endValue: 12
              }
            ]
          : [],
        xAxis: {
          type: 'category',
          data: xData,
          axisLabel: {
            color: '#666',
            fontSize: 12,
            overflow: 'truncate'
          },
          axisLine: {
            lineStyle: { color: '#E5E5E5' }
          }
        },
        yAxis: [
          {
            type: 'value',
            name: '',
            axisLine: { show: false },
            axisTick: { show: false },
            splitLine: { show: false },

            axisLabel: {
              color: '#666',
              fontSize: 12,
              formatter: function (value) {
                return '¥' + value;
              }
            }
          },
          {
            type: 'value',
            position: 'right',
            axisLine: { show: false },
            axisTick: { show: false },
            splitLine: { show: false },
            axisLabel: {
              color: opt.color[2],
              fontSize: 12,
              formatter: function (value) {
                return value + '%';
              }
            }
          }
        ],
        series: [
          {
            name: opt.names[0],
            type: 'bar',
            data: bar_this_Data,
            barWidth: '30%',
            itemStyle: {
              color: opt.color[0]
            },
            label: {
              show: true,
              position: 'top',
              color: opt.color[0],
              fontSize: 12
              // formatter: function (params) {
              //   return params.value;
              // }
            }
          },
          {
            name: opt.names[1],
            type: 'bar',
            data: bar_last_Data,
            barWidth: '30%',
            itemStyle: {
              color: opt.color[1]
            },
            label: {
              show: true,
              position: 'top',
              color: opt.color[1],
              fontSize: 12
            }
          },
          {
            name: opt.names[2],
            type: 'line',
            yAxisIndex: 1,
            data: lineData,
            symbol: 'circle',
            symbolSize: 8,
            itemStyle: {
              color: opt.color[2] // 设置折点颜色
            },
            lineStyle: {
              color: opt.color[2],
              width: 2
            },
            label: {
              show: true,
              position: 'top',
              color: opt.color[2],
              fontSize: 12,
              formatter: function (e) {
                return e.value + '%';
              }
            },
            fontSize: 12
          }
        ]
      });
    },

    /**
     * @description: x轴滚动柱状-折线图配置-单个柱状图
     * key: 配置函数模板
     * data: 数据源
     * title: title
     * */
    // 单个柱状+单个折线（双Y轴）
    setBarALineConfigure_single(key, data, params = {}) {
      if (!data) return;

      const opt = {
        name: '客单',
        color: ['#3184F1', '#2AC769'],
        ...params
      };
      const xData = data.xData;
      const barData = data.barData;
      const lineData = data.lineData;

      this[key] = this.$eChartFn.verticalBar({
        color: ['#3184F1'],
        legend: {
          data: [opt.name],
          top: 10,
          right: 20,
          itemWidth: 14,
          itemHeight: 14,
          textStyle: {
            color: '#333'
          }
        },
        grid: {
          left: 0,
          right: 0,
          bottom: 40,
          top: 50,
          containLabel: true
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: { type: 'shadow' }
        },
        dataZoom: [
          {
            type: 'slider',
            show: true,
            xAxisIndex: 0,
            height: 20,
            left: 40,
            right: 40,
            bottom: 10,
            startValue: 0,
            endValue: 12
          }
        ],
        xAxis: {
          type: 'category',
          data: xData,
          axisLabel: {
            color: '#666',
            formatter: this.formatterText,
            show: true,
            interval: 'auto'
          },
          axisLine: {
            lineStyle: { color: '#E5E5E5' }
          }
        },
        yAxis: [
          {
            type: 'value',
            nameTextStyle: {
              color: '#666',
              fontSize: 16,
              padding: [0, 0, 0, 0]
            },
            axisLine: { show: false },
            axisTick: { show: false },
            splitLine: { show: false },
            axisLabel: {
              color: '#666',
              fontSize: 12,
              formatter: function (value) {
                return '¥' + value;
              }
            }
          },
          {
            type: 'value',

            position: 'right',
            axisLine: { show: false },
            axisTick: { show: false },
            splitLine: { show: false },
            axisLabel: {
              show: false
            }
          }
        ],
        series: [
          {
            name: opt.name,
            type: 'bar',
            data: barData,
            barWidth: '30%',
            itemStyle: {
              color: opt.color[0]
            },
            label: {
              show: true,
              position: 'top',
              color: opt.color[0],
              fontSize: 12
            }
          },
          {
            name: '趋势',
            type: 'line',
            yAxisIndex: 1,
            data: lineData,
            symbol: 'circle',
            symbolSize: 8,
            itemStyle: {
              color: opt.color[1]
            },
            lineStyle: {
              color: opt.color[1],
              width: 2
            },
            label: {
              show: false
            }
          }
        ]
      });
    },

    /**
     * 堆叠图
     * @param {*} key
     * @param {*} data
     * @param {*} params
     */
    setStackConfigure(key, data, params) {
      console.log('🚀 ~ dateChange.js:781 ~ setStackConfigure ~ data=>', data);
      const opt = {
        name: '收入金额',
        color: ['#AA63F6', '#FC8333', '#2AC769', '#3184F1'],
        ...params
      };

      const xData = data.xData;
      const series = data.series.map(i => ({
        name: i.name,
        type: 'bar',
        barWidth: '30%',
        stack: 'Search Engine',
        emphasis: {
          focus: 'series'
        },
        label: {
          // show: true,
          // position: 'inside',
          // color: '#979797',
          // fontSize: 12
        },
        data: i.data
      }));

      this[key] = this.$eChartFn.verticalBar({
        color: opt.color,
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          }
        },
        legend: {},
        grid: {
          top: 40,
          left: 10,
          right: 10,
          bottom: 55,
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: xData,
          axisLabel: {
            color: '#666',
            fontSize: 12,
            formatter: this.formatterText,
            interval: 'auto'
          }
        },
        dataZoom: [
          {
            type: 'slider',
            show: true,
            xAxisIndex: 0,
            height: 20,
            left: 40,
            right: 40,
            startValue: 0,
            endValue: 12
          }
        ],
        yAxis: {
          type: 'value',
          name: '',
          show: true,
          axisLine: { show: false },
          axisTick: { show: false },
          splitLine: { show: false },
          axisLabel: {
            show: true,
            color: '#666',
            fontSize: 12,
            interval: 0,
            overflow: 'truncate',
            rotate: 0,
            formatter: function (value) {
              return '¥' + value; // 这里加单位;
            }
          }
        },
        series
      });
    }
  }
};
