import showEcharts from '@/mixins/showEcharts';
export default {
  mixins: [showEcharts],
  data() {
    return {};
  },
  components: {},
  watch: {},
  computed: {},
  methods: {
    // x文本超出隐藏
    formatterText(value) {
      // 超过7个字就截断加省略号
      return value.length > 8 ? value.slice(0, 8) + '...' : value;
    },

    /**
     * 单个折线图配置-1
     * @param {*} key
     * @param {*} data 数据来源
     * @param {*} params  额外配置
     */
    setLineConfigure(key, data, params) {
      const opt = {
        color: '#2AC769',
        name: '示例',
        ...params
      };

      // 数据根据-实际处理
      const xData = data.xData;
      const seriesData = data.seriesData;

      this[key] = this.$eChartFn.areaLineOptions({
        color: [opt.color], // 柱状图绿色，
        legend: {
          data: [opt.name],
          top: 0,
          right: 10,
          itemWidth: 16,
          itemHeight: 8,
          textStyle: {
            color: '#333'
          }
        },
        grid: {
          left: 30,
          right: 20,
          bottom: 55,
          top: 40,
          containLabel: true
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: { type: 'shadow' },
          // position: null,
          backgroundColor: '#fff',
          padding: 8,
          formatter: (a, index) => {
            let content = `<span style="font-size: 13px;font-weight: bold;color: #333333;line-height: 17px;">${a[0]?.axisValueLabel}</span><br>`;
            a?.forEach((item, index) => {
              content =
                content +
                `
                  <span style="display:inline-block;width: 10px;height: 10px;border-radius: 50%;background: ${
                    item.color
                  }"></span>
                  <span style="font-size: 13px;line-height: 15px;">${item.seriesName}</span>
                  <span style="font-size: 13px;line-height: 15px;font-weight: bold">${
                    item?.data?.tooltip_text ? `${item.data.tooltip_text}` : item.value
                  }</span></br>`;
            });
            return `<div class="flex-col">
                      ${content}
                    </div>
                    `;
          }
        },
        dataZoom: [
          {
            type: 'slider',
            show: true,
            xAxisIndex: 0,
            height: 20,
            left: 90,
            right: 90,
            // bottom: 5,
            startValue: 0,
            endValue: 12,
            textStyle: {
              height: 20,
              width: 90,
              overflow: 'truncate'
              // overflow: 'breakAll'
              // ellipsis:'...' //
            }
          }
        ],
        xAxis: {
          type: 'category',
          data: xData,
          axisLabel: {
            color: '#666',
            formatter: this.formatterText,
            show: true,
            interval: 'auto'
          },
          axisLine: {
            show: true,
            lineStyle: { color: '#E5E5E5' }
          }
        },
        yAxis: {
          type: 'value',
          show: true,
          axisLine: { show: false },
          axisTick: { show: false },
          splitLine: { show: false },
          axisLabel: {
            color: '#666',
            fontSize: 12,
            formatter: function (value) {
              return value + '%';
            }
          }
        },
        series: [
          {
            name: opt.name,
            type: 'line',
            data: seriesData,
            symbol: 'circle',
            symbolSize: 8,
            smooth: false,
            itemStyle: {
              normal: null,
              color: opt.color
            },
            showSymbol: true,

            areaStyle: null,
            lineStyle: {
              color: opt.color,
              width: 2
            },
            label: {
              show: true,
              position: 'top',
              color: opt.color,
              fontSize: 12,
              formatter: function (params) {
                return params.value + '%';
              }
            }
          }
        ]
      });
    },

    // echarts配置
    /**
     * @description: 饼图配置
     * value: 配置函数模板
     * data: 数据源
     * title: 饼图title
     * isShow: 是否显示toolTip, 默认显示
     * */
    setPieConfigure(value, data, title, isShow = true) {
      data = data.map(item => ({
        name: item.title,
        value: item.thisMonth
      }));
      this[value] = this.$eChartFn.orderPie({
        legend: {
          type: 'scroll',
          show: true,
          bottom: '24px',
          left: 'center',
          name: 'consumption'
        },
        series: [
          {
            data,
            name: 'consumption'
          }
        ],
        title: { text: title },
        tooltip: {
          formatter: a => {
            let content = '';
            a.data.tootipList &&
              a.data.tootipList.forEach((item, index) => {
                if (index == 0) {
                  content =
                    content +
                    `<span style="font-size: 13px;font-weight: bold;color: #333333;line-height: 17px;">${item.key}</span>
                <span style="font-size: 13px;font-weight: bold;color: #333333;line-height: 17px;">${item.value}</span></br>`;
                } else {
                  content =
                    content +
                    `<span style="font-size: 11px;font-weight: 300;color: #A7AFC0;line-height: 15px;">${item.key}</span>
                <span style="font-size: 11px;font-weight: 300;color: #A7AFC0;line-height: 15px;margin-right: 10px">${item.value}</span>`;
                }
              });
            return `<div class="flex-col">
                      ${content}
                    </div>
                    `;
          },
          show: isShow
        }
      });
    },

    /**
     * @description: 饼图配置, 默认配置引导线展示
     * value: 配置函数模板
     * data: 数据源
     * name: '', // 名称
     * rate: '', // 比例
     * value: '', // 参与默认计算的字段
     * */

    setPieConfigure_guide(value, data, title) {
      data = data.map((item, idx) => ({
        ...item,
        name: item.title || item.text || item.name
      }));
      this[value] = this.$eChartFn.orderPie({
        legend: {
          type: 'scroll',
          show: true,
          bottom: -5,
          left: 'center',
          name: 'consumption'
        },
        series: [
          {
            data,
            name: '',
            radius: [15, '45%'],
            // minShowLabelAngle: 1,
            label: {
              alignTo: 'edge', // label对齐到扇区外缘
              edgeDistance: 10, // label距离画布边缘的最小距离（像素或百分比）
              minMargin: 2,
              show: true,
              width: 80,
              overflow: 'truncate',
              ellipsis: '...',
              formatter: function (e) {
                let data = e.data;
                return `{a|${data.name}：}\n{b|${data.value}} {c|(${data.rate}%)}`;
              },
              rich: {
                a: {
                  color: '#737373',
                  fontSize: 11,
                  fontWeight: '400',
                  align: 'center'
                },
                b: {
                  fontSize: 11,
                  align: 'center',
                  fontWeight: 300,
                  color: '#737373'
                },
                c: {
                  fontSize: 11,
                  align: 'center ',
                  fontWeight: 300,
                  color: '#737373'
                }
              }
            },
            labelLine: {
              show: true //引导线显示
              // length: 15,
              // length2: 0
            },
            labelLayout: function (params) {
              const isLeft = params.labelRect.x < 260;
              // const isLeft = params.labelRect.x < myChart.getWidth() / 2;
              const points = params.labelLinePoints;
              // Update the end point.
              points[2][0] = isLeft ? params.labelRect.x : params.labelRect.x + params.labelRect.width;
              return {
                labelLinePoints: points,
                hideOverlap: true // 自动隐藏重叠的label
                // moveOverlap: 'shiftY'
              };
            }
            // labelLayout: function (params) {
            //   const isLeft = params.labelRect.x < 260;
            //   // const isLeft = params.labelRect.x < myChart.getWidth() / 2;
            //   const points = params.labelLinePoints;
            //   // Update the end point.
            //   points[2][0] = isLeft ? params.labelRect.x : params.labelRect.x + params.labelRect.width;
            //   return {
            //     labelLinePoints: points
            //   };
            // }
          }
        ],
        title: { text: title },
        tooltip: {
          formatter: a => {
            let content = '';
            a.data.tootipList &&
              a.data.tootipList.forEach((item, index) => {
                if (index == 0) {
                  content =
                    content +
                    `<span style="font-size: 13px;font-weight: bold;color: #333333;line-height: 17px;">${item.key}</span>
                <span style="font-size: 13px;font-weight: bold;color: #333333;line-height: 17px;">${item.value}</span></br>`;
                } else {
                  content =
                    content +
                    `<span style="font-size: 11px;font-weight: 300;color: #A7AFC0;line-height: 15px;">${item.key}</span>
                <span style="font-size: 11px;font-weight: 300;color: #A7AFC0;line-height: 15px;margin-right: 10px">${item.value}</span>`;
                }
              });
            return `<div class="flex-col">
                      ${content}
                    </div>
                    `;
          },
          show: data[0]?.tooltip?.length
        }
      });
    },
    /**
     * @description: x轴滚动柱状图配置
     * key: 配置函数模板
     * data: 数据源
     * title: title
     * */
    setBarConfigure_zoom(key, data, params = {}) {
      const opt = {
        name: '示例',
        color: '#3184F1',
        prefix: '',
        ...params
      };

      const xData = data.xData;
      const yData = data.yData;

      this[key] = this.$eChartFn.verticalBar({
        color: [opt.color],
        legend: {
          data: [opt.name],
          right: 0,
          itemWidth: 14,
          itemHeight: 14,
          textStyle: {
            color: '#666'
          }
        },
        grid: {
          left: 30,
          right: 20,
          bottom: 35,
          top: 40,
          containLabel: true
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          }
        },
        dataZoom: [
          {
            type: 'slider',
            show: true,
            xAxisIndex: 0,
            height: 20,
            left: 90,
            right: 90,
            bottom: 10,
            startValue: 0,
            endValue: 12,
            textStyle: {
              width: 90,
              height: 20,
              overflow: 'truncate'
              // overflow: ''
            }
          }
        ],

        xAxis: {
          type: 'category',
          data: xData,
          axisLabel: {
            color: '#666',
            fontSize: 12,
            formatter: this.formatterText,
            show: true,
            interval: 'auto'
          },
          axisLine: {
            lineStyle: { color: '#E5E5E5' }
          }
        },
        yAxis: {
          type: 'value',
          name: '',
          show: true,
          axisLine: { show: false },
          axisTick: { show: false },
          splitLine: { show: false },
          axisLabel: {
            color: '#666',
            fontSize: 12,
            formatter: function (value) {
              return `${opt.prefix}${value}`;
            }
          }
        },
        series: [
          {
            name: opt.name,
            data: yData,
            type: 'bar',
            barWidth: '30%',
            itemStyle: {
              color: opt.color
            },
            label: {
              show: true,
              position: 'top',
              color: '#333',
              fontSize: 12,
              textStyle: {
                color: opt.color
              }
            }
          }
        ]
      });
    },

    /**
     * @description: x轴滚动-双柱状+折线图配置
     * key: 配置函数模板
     * data: 数据源
     * params: 额外参数
     * */
    setBarALineConfigure(key, data, params = {}) {
      // 数据处理根据接口数据调整
      const xData = data.xData;
      const bar_last_Data = data.barLastData;
      const bar_this_Data = data.barThisData;
      const lineData = data.lineData;

      const opt = {
        color: ['#3184F1', '#2AC769', '#FFB800'],
        legend: ['上月）', '本月', '同比'],
        dataZoom: true,
        ...params
      };

      this[key] = this.$eChartFn.verticalBar({
        color: opt.color, // 柱状图绿色，折线图黄色
        legend: {
          data: opt.legend,
          top: '0',
          right: '0',
          show: true
        },
        grid: {
          left: 30,
          right: 20,
          bottom: 55,
          top: 40,
          containLabel: true
        },

        dataZoom: opt.dataZoom
          ? [
              {
                type: 'slider',
                show: true,
                xAxisIndex: 0,
                height: 20,
                left: 90,
                right: 90,
                startValue: 0,
                endValue: 12,
                textStyle: {
                  width: 90,
                  height: 20,
                  overflow: 'truncate'
                }
              }
            ]
          : [],
        xAxis: {
          type: 'category',
          data: xData,
          axisLabel: {
            overflow: 'truncate'
            // formatter: function (value, index) {
            //   if (data.leftSymbol == '%') {
            //     return `${value}${data.leftSymbol}`;
            //   } else {
            //     return `¥${value}`;
            //   }
            // }
          },
          axisLine: {
            lineStyle: { color: '#E5E5E5' }
          }
        },
        yAxis: [
          {
            type: 'value',
            name: '',
            axisLine: { show: false },
            axisTick: { show: false },
            splitLine: { show: false },
            axisLabel: {
              color: '#666',
              fontSize: 12,
              formatter: function (value) {
                return '¥' + value;
              }
            }
          },
          {
            type: 'value',
            position: 'right',
            axisLine: { show: false },
            axisTick: { show: false },
            splitLine: { show: false },
            axisLabel: {
              color: opt.color[2],
              fontSize: 12,
              formatter: function (value) {
                return value + '%';
              }
            }
          }
        ],
        series: [
          {
            name: opt.legend[0],
            type: 'bar',
            data: bar_last_Data,
            barWidth: '30%',
            itemStyle: {
              color: opt.color[0]
            },
            label: {
              show: true,
              position: 'top',
              color: opt.color[0],
              fontSize: 12
              // formatter: function (params) {
              //   return params.value;
              // }
            },
            labelLayout: {
              hideOverlap: true,
              moveOverlap: 'shiftY'
            }
          },
          {
            name: opt.legend[1],
            type: 'bar',
            data: bar_this_Data,
            barWidth: '30%',
            itemStyle: {
              color: opt.color[1]
            },
            label: {
              show: true,
              position: 'top',
              color: opt.color[1],
              fontSize: 12
            },
            labelLayout: {
              hideOverlap: true,
              moveOverlap: 'shiftY'
            }
          },
          {
            name: opt.legend[2],
            type: 'line',
            yAxisIndex: 1,
            data: lineData,
            symbol: 'circle',
            symbolSize: 8,
            itemStyle: {
              color: opt.color[2] // 设置折点颜色
            },
            lineStyle: {
              color: opt.color[2],
              width: 2
            },
            label: {
              show: true,
              position: 'top',
              color: opt.color[2],
              fontSize: 12,
              formatter: function (e) {
                return e.value + '%';
              }
            },
            fontSize: 12
            // labelLayout: {
            //   hideOverlap: true,
            //   moveOverlap: 'shiftY'
            // }
          }
        ],
        // tooltip: {
        //   trigger: 'axis',
        //   axisPointer: { type: 'shadow' }
        // },
        tooltip: {
          show: true, // 开启悬浮框
          trigger: 'axis',
          position: function (point, params, dom, rect, size) {
            // 其中point为当前鼠标的位置，size中有两个属性：viewSize和contentSize，分别为外层div和tooltip提示框的大小
            var x = point[0]; //
            var y = point[1];
            var boxWidth = size.contentSize[0];
            var boxHeight = size.contentSize[1];
            var posX = 0; // x坐标位置
            var posY = 0; // y坐标位置
            if (x < boxWidth) {
              // 左边放不开
              posX = 5;
            } else {
              // 左边放的下
              posX = x - boxWidth;
            }

            if (y < boxHeight) {
              // 上边放不开
              posY = 5;
            } else {
              // 上边放得下
              posY = y - boxHeight - 25;
            }

            return [posX, posY];
          },
          formatter: a => {
            let content = `<span style="font-size: 13px;font-weight: bold;color: #333333;line-height: 17px;">${a[0]?.axisValueLabel}</span><br>`;
            a?.forEach((item, index) => {
              content =
                content +
                `
                  <span style="display:inline-block;width: 10px;height: 10px;border-radius: 50%;background: ${
                    item.color
                  }"></span>
                  <span style="font-size: 13px;line-height: 15px;">${item.seriesName}</span>
                  <span style="font-size: 13px;line-height: 15px;font-weight: bold">${
                    item?.data?.tooltip_text ? `${item.data.tooltip_text}` : item.value
                  }</span></br>`;
            });
            return `<div class="flex-col">
                      ${content}
                    </div>
                    `;
          },
          padding: 10, // 边框距离内容的距离
          left: 'center',
          backgroundColor: '#fff', // 边框色
          borderRadius: 4
        }
      });
    },

    /**
     * @description: x轴滚动柱状-折线图配置-单个柱状图
     * key: 配置函数模板
     * data: 数据源
     * title: title
     * */
    // 单个柱状+单个折线（双Y轴）
    setBarALineConfigure_single(key, data, params = {}) {
      if (!data) return;

      const opt = {
        name: '客单',
        color: ['#3184F1', '#2AC769'],
        ...params
      };
      const xData = data.xData;
      const barData = data.barData;
      const lineData = data.lineData;

      this[key] = this.$eChartFn.verticalBar({
        color: ['#3184F1'],
        legend: {
          data: [opt.name],
          top: 10,
          right: 20,
          itemWidth: 14,
          itemHeight: 14,
          textStyle: {
            color: '#333'
          }
        },
        grid: {
          left: 30,
          right: 20,
          bottom: 35,
          top: 45,
          containLabel: true
        },
        dataZoom: [
          {
            type: 'slider',
            show: true,
            xAxisIndex: 0,
            height: 20,
            left: 90,
            right: 90,
            bottom: 10,
            startValue: 0,
            endValue: 12,
            textStyle: {
              width: 90,
              height: 20,
              overflow: 'truncate'
            }
          }
        ],
        xAxis: {
          type: 'category',
          data: xData,
          axisLabel: {
            color: '#666',
            formatter: this.formatterText,
            show: true,
            interval: 'auto'
          },
          axisLine: {
            lineStyle: { color: '#E5E5E5' }
          }
        },
        yAxis: [
          {
            type: 'value',
            nameTextStyle: {
              color: '#666',
              fontSize: 16,
              padding: [0, 0, 0, 0]
            },
            axisLine: { show: false },
            axisTick: { show: false },
            splitLine: { show: false },
            axisLabel: {
              color: '#666',
              fontSize: 12,
              formatter: function (value) {
                return '¥' + value;
              }
            }
          },
          {
            type: 'value',
            position: 'right',
            axisLine: { show: false },
            axisTick: { show: false },
            splitLine: { show: false },
            axisLabel: {
              show: false
            }
          }
        ],
        series: [
          {
            name: opt.name,
            type: 'bar',
            data: barData,
            barWidth: '30%',
            itemStyle: {
              color: opt.color[0]
            },
            label: {
              show: true,
              position: 'top',
              color: opt.color[0],
              fontSize: 12
            }
          },
          {
            name: '趋势',
            type: 'line',
            yAxisIndex: 1,
            data: lineData,
            symbol: 'circle',
            symbolSize: 8,
            itemStyle: {
              color: opt.color[1]
            },
            tooltip: {
              show: false // 关键配置：隐藏该系列的tooltip
            },
            lineStyle: {
              color: opt.color[1],
              width: 2
            },
            label: {
              show: false
            }
          }
        ],
        tooltip: {
          show: true, // 开启悬浮框
          trigger: 'axis',
          position: function (point, params, dom, rect, size) {
            // 其中point为当前鼠标的位置，size中有两个属性：viewSize和contentSize，分别为外层div和tooltip提示框的大小
            var x = point[0]; //
            var y = point[1];
            var boxWidth = size.contentSize[0];
            var boxHeight = size.contentSize[1];
            var posX = 0; // x坐标位置
            var posY = 0; // y坐标位置
            if (x < boxWidth) {
              // 左边放不开
              posX = 5;
            } else {
              // 左边放的下
              posX = x - boxWidth;
            }

            if (y < boxHeight) {
              // 上边放不开
              posY = 5;
            } else {
              // 上边放得下
              posY = y - boxHeight - 25;
            }

            return [posX, posY];
          },
          formatter: a => {
            let content = `<span style="font-size: 13px;font-weight: bold;color: #333333;line-height: 17px;">${a[0]?.axisValueLabel}</span><br>`;
            a?.forEach((item, index) => {
              content =
                content +
                `
                  <span style="display:inline-block;width: 10px;height: 10px;border-radius: 50%;background: ${
                    item.color
                  }"></span>
                  <span style="font-size: 13px;line-height: 15px;">${item.seriesName}</span>
                  <span style="font-size: 13px;line-height: 15px;font-weight: bold">${
                    item?.data?.tooltip_text ? `${item.data.tooltip_text}` : item.value
                  }</span></br>`;
            });
            return `<div class="flex-col">
                      ${content}
                    </div>
                    `;
          },
          padding: 10, // 边框距离内容的距离
          left: 'center',
          backgroundColor: '#fff', // 边框色
          borderRadius: 4
        }
      });
    },

    /**
     * 堆叠图
     * @param {*} key
     * @param {*} data
     * @param {*} params
     */
    setStackConfigure(key, data, params) {
      console.log('🚀 ~ dateChange.js:781 ~ setStackConfigure ~ data=>', data);
      const opt = {
        name: '收入金额',
        color: ['#AA63F6', '#FC8333', '#2AC769', '#3184F1'],
        ...params
      };

      const xData = data.xData;
      const series = data.series.map(i => ({
        name: i.name,
        type: 'bar',
        barWidth: '30%',
        stack: 'Search Engine',
        emphasis: {
          focus: 'series'
        },
        label: {
          // show: true,
          // position: 'inside',
          // color: '#979797',
          // fontSize: 12
        },
        data: i.data
      }));

      this[key] = this.$eChartFn.verticalBar({
        color: opt.color,
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          }
        },
        legend: {
          type: 'scroll',
          top: 0,
          left: '10%'
        },
        grid: {
          top: 40,
          left: 30,
          right: 20,
          bottom: 55,
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: xData,
          axisLabel: {
            color: '#666',
            fontSize: 12,
            formatter: this.formatterText,
            interval: 'auto'
          }
        },
        dataZoom: [
          {
            type: 'slider',
            show: true,
            xAxisIndex: 0,
            height: 20,
            left: 90,
            right: 90,
            startValue: 0,
            endValue: 12,
            textStyle: {
              width: 90,
              height: 20,
              overflow: 'truncate'
            }
          }
        ],
        yAxis: {
          type: 'value',
          name: '',
          show: true,
          axisLine: { show: false },
          axisTick: { show: false },
          splitLine: { show: false },
          axisLabel: {
            show: true,
            color: '#666',
            fontSize: 12,
            interval: 0,
            overflow: 'truncate',
            rotate: 0,
            formatter: function (value) {
              return '¥' + value; // 这里加单位;
            }
          }
        },
        series
      });
    }
  }
};
