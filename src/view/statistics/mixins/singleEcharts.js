import showEcharts from '@/mixins/showEcharts';
export default {
  mixins: [showEcharts],
  data() {
    return {};
  },
  components: {},
  watch: {},
  computed: {},
  methods: {
    // 获取趋势图
    getTrend(cardList, currentIndex) {
      let params = {
        st: this.formDate[0],
        et: this.formDate[1],
        date_type: this.date_type,
        item: cardList[currentIndex].value
      };
      this.$api.getTrend(params).then(res => {
        const xData = res.list.map(item => item.x);
        const yData = res.list.map(item => item.y);

        this.line_options = this.$eChartFn.areaLineOptions(
          {
            xAxis: {
              data: xData
            },
            series: [{ data: yData }]
          },
          cardList[currentIndex].label,
          cardList[currentIndex].isMoney || false
        );
      });
    },

    // echarts配置
    /**
     * @description: 饼图配置
     * value: 配置函数模板
     * data: 数据源
     * title: 饼图title
     * isShow: 是否显示toolTip, 默认显示
     * */

    setPieConfigure(value, data, title, isShow = true) {
      data.map(item => {
        item.name = item.title || item.text || item.age_text;
      });
      this[value] = this.$eChartFn.orderPie({
        legend: {
          type: 'scroll',
          show: true,
          bottom: '24px',
          left: 'center',
          name: 'consumption'
        },
        series: [
          {
            data,
            name: 'consumption'
          }
        ],
        title: { text: title },
        tooltip: {
          formatter: a => {
            let content = '';
            a.data.tootipList &&
              a.data.tootipList.forEach((item, index) => {
                if (index == 0) {
                  content =
                    content +
                    `<span style="font-size: 13px;font-weight: bold;color: #333333;line-height: 17px;">${item.key}</span>
                <span style="font-size: 13px;font-weight: bold;color: #333333;line-height: 17px;">${item.value}</span></br>`;
                } else {
                  content =
                    content +
                    `<span style="font-size: 11px;font-weight: 300;color: #A7AFC0;line-height: 15px;">${item.key}</span>
                <span style="font-size: 11px;font-weight: 300;color: #A7AFC0;line-height: 15px;margin-right: 10px">${item.value}</span>`;
                }
              });
            return `<div class="flex-col">
                      ${content}
                    </div>
                    `;
          },
          show: isShow
        }
      });
    },

    /**
     * @description: 饼图配置, 默认配置引导线展示
     * value: 配置函数模板
     * data: 数据源
     * name: '', // 名称
     * rate: '', // 比例
     * value: '', // 参与默认计算的字段
     * */

    setPieConfigure_guide(value, data, title) {
      data.map(item => {
        item.name = item.title || item.text || item.name;
      });
      this[value] = this.$eChartFn.orderPie({
        legend: {
          type: 'scroll',
          show: true,
          bottom: -5,
          left: 'center',
          name: 'consumption'
        },
        series: [
          {
            data,
            name: '',
            radius: [15, '45%'],
            // minShowLabelAngle: 1,
            label: {
              alignTo: 'edge', // 文字对齐，文字的边距由 label.edgeDistance 决定。
              edgeDistance: 10,
              minMargin: 10,
              show: true,
              formatter: function (e) {
                let data = e.data;
                return `{a|${data.name}: }\n{b|${data.value}} {c|(${data.rate}%)}`;
              },
              rich: {
                a: {
                  color: '#444',
                  fontSize: 11,
                  fontWeight: '400',
                  align: 'center'
                },
                b: {
                  fontSize: 11,
                  align: 'center',
                  fontWeight: 300,
                  color: '#444'
                },
                c: {
                  fontSize: 11,
                  align: 'center',
                  fontWeight: 300,
                  color: '#444'
                }
              }
            },
            labelLine: {
              show: true, //引导线显示
              // maxSurfaceAngle: 85
              // length: 10,
              // length2: 10
            },

            labelLayout: function (params) {
              const isLeft = params.labelRect.x < 260;
              // const isLeft = params.labelRect.x < myChart.getWidth() / 2;
              const points = params.labelLinePoints;
              // Update the end point.
              points[2][0] = isLeft ? params.labelRect.x : params.labelRect.x + params.labelRect.width;
              return {
                labelLinePoints: points,
                hideOverlap: true, // 自动隐藏重叠的label
                // moveOverlap: 'shiftY'
              };
            }
          }
        ],
        title: { text: title },
        tooltip: {
          formatter: a => {
            let content = '';
            a.data.tootipList &&
              a.data.tootipList.forEach((item, index) => {
                if (index == 0) {
                  content =
                    content +
                    `<span style="font-size: 13px;font-weight: bold;color: #333333;line-height: 17px;">${item.key}</span>
                <span style="font-size: 13px;font-weight: bold;color: #333333;line-height: 17px;">${item.value}</span></br>`;
                } else {
                  content =
                    content +
                    `<span style="font-size: 11px;font-weight: 300;color: #A7AFC0;line-height: 15px;">${item.key}</span>
                <span style="font-size: 11px;font-weight: 300;color: #A7AFC0;line-height: 15px;margin-right: 10px">${item.value}</span>`;
                }
              });
            return `<div class="flex-col">
                      ${content}
                    </div>
                    `;
          },
          show: data[0]?.tooltip?.length
        }
      });
    },

    /**
     * @description: 处理双柱状图+折线
     * value: 配置函数模板
     * data: 数据源
     * {
     *  date: [],
     *  legend: [],
     *  xData: [],
     *  yData: [],
     *  zData: [],
     *  value_text: 柱体上方显示的文本
     *  tooltip_text: tooltip中数值显示的文本
     *}
     * */
    handleBarLine(value, data) {
      this[value] = this.$eChartFn.verticalBar({
        grid: {
          top: '40',
          left: '60',
          right: '40',
          bottom: '80',
          show: true
        },
        legend: {
          top: '0',
          right: '0',
          show: true,
          data: data?.legend
        },
        tooltip: {
          show: true, // 开启悬浮框
          trigger: 'axis',
          position: function (point, params, dom, rect, size) {
            // 其中point为当前鼠标的位置，size中有两个属性：viewSize和contentSize，分别为外层div和tooltip提示框的大小
            var x = point[0]; //
            var y = point[1];
            var viewWidth = size.viewSize[0];
            var viewHeight = size.viewSize[1];
            var boxWidth = size.contentSize[0];
            var boxHeight = size.contentSize[1];
            var posX = 0; // x坐标位置
            var posY = 0; // y坐标位置
            if (x < boxWidth) {
              // 左边放不开
              posX = 5;
            } else {
              // 左边放的下
              posX = x - boxWidth;
            }

            if (y < boxHeight) {
              // 上边放不开
              posY = 5;
            } else {
              // 上边放得下
              posY = y - boxHeight - 25;
            }

            return [posX, posY];
          },
          formatter: (a, index) => {
            let content = `<span style="font-size: 13px;font-weight: bold;color: #333333;line-height: 17px;">${a[0]?.axisValueLabel}</span><br>`;
            a?.forEach((item, index) => {
              content =
                content +
                `
                  <span style="display:inline-block;width: 10px;height: 10px;border-radius: 50%;background: ${
                    item.color
                  }"></span>
                  <span style="font-size: 13px;line-height: 15px;">${item.seriesName}</span>
                  <span style="font-size: 13px;line-height: 15px;font-weight: bold">${
                    item?.data?.tooltip_text ? `${item.data.tooltip_text}` : item.value
                  }</span></br>`;
            });
            return `<div class="flex-col">
                      ${content}
                    </div>
                    `;
          },
          padding: 10, // 边框距离内容的距离
          left: 'center',
          backgroundColor: '#fff', // 边框色
          borderRadius: 4
        },
        dataZoom: [
          {
            show: true,
            type: 'slider',
            startValue: 0,
            endValue: 12,
            height: 20,
            left: 80,
            right: 80
          }
        ],
        xAxis: {
          data: data.date,
          axisLabel: {
            formatter: '{value}'
          }
        },
        yAxis: [
          {
            name: '',
            axisLine: {
              show: false,
              lineStyle: {
                color: '#999'
              }
            },
            splitLine: {
              show: false
            },
            axisLabel: {
              formatter: function (value, index) {
                if (data.leftSymbol == '%') {
                  return `${value}${data.leftSymbol}`;
                } else {
                  return `¥${value}`;
                }
              }
            }
          },
          {
            type: 'value',
            name: '',
            // min: 0,
            // max: 100,
            // interval: 5,
            splitLine: {
              show: false
            },
            axisLine: {
              show: false,
              lineStyle: {
                color: '#FCC324'
              }
            },
            axisLabel: {
              formatter: '{value}%'
            }
          }
        ],
        series: [
          {
            data: data.xData,
            name: data?.legend[0],
            type: 'bar',
            color: '#3184F1', // 更改柱体的颜色
            barWidth: '20%', // 设置柱子的宽度
            label: {
              textStyle: {
                color: '#3184F1'
              },
              formatter: function (value, index) {
                let value_text = value?.data?.value_text;
                return value_text ? value_text : value.value;
              }
            }
          },
          {
            data: data.yData,
            name: data?.legend[1],
            type: 'bar',
            color: '#4DCC6E', // 更改柱体的颜色
            barWidth: '20%', // 设置柱子的宽度
            label: {
              show: true,
              formatter: function (value, index) {
                let value_text = value?.data?.value_text;
                return value_text ? value_text : value.value;
              },
              position: 'top',
              textStyle: {
                color: '#4DCC6E',
                fontSize: 14
              }
            }
          },
          {
            data: data.zData,
            name: data?.legend[2],
            yAxisIndex: 1,
            type: 'line',
            color: '#FCC324', // 更改柱体的颜色
            label: {
              show: true,
              position: 'top',
              textStyle: {
                color: '#FCC324'
              },
              formatter: function (value, index) {
                return `${value.value}%`;
              }
            }
          }
        ]
      });
    },

    /**
     * @description: 处理单柱状图, 展示横竖柱，柱体显示数字
     * value: 配置函数模板
     * data: 数据源
     * */
    handleBar(value, data) {
      console.log('🚀 ~ singleEcharts.js:401 ~ handleBar ~ data=>', data);
      this[value] = this.$eChartFn.verticalBar({
        grid: {
          top: '40',
          left: '60',
          right: '40',
          bottom: '80',
          show: true
        },
        legend: {
          top: '0',
          right: '0',
          show: true,
          data: data.legend
        },
        tooltip: {
          show: true // 开启悬浮框
        },
        dataZoom: [
          {
            show: true,
            type: 'slider',
            startValue: 0,
            endValue: 12,
            height: 20,
            left: 80,
            right: 80
          }
        ],
        xAxis: {
          data: data.date
        },
        yAxis: {
          name: '',
          axisLine: {
            show: false,
            lineStyle: {
              color: '#999'
            }
          },
          // splitLine: {
          //   show: true, // 去除y轴的网格线
          //   lineStyle: {
          //     type: 'dashed',
          //   },
          // },
          axisLabel: {
            formatter: '¥{value}'
          }
        },
        series: [
          {
            data: data.yData,
            name: '利润',
            color: '#FC8333', // 更改柱体的颜色
            barWidth: '20%', // 设置柱子的宽度
            label: {
              color: '#FC8333'
            }
          }
        ]
      });
    },

    /**
     * @description: x轴滚动柱状图配置
     * key: 配置函数模板
     * data: 数据源
     * title: title
     * */
    setBarConfigure_zoom(key, data, params = {}) {
      const opt = {
        name: '示例',
        color: '#3184F1',
        ...params
      };

      const xData = data.xData;
      const yData = data.yData;

      this[key] = this.$eChartFn.verticalBar({
        color: [opt.color],
        legend: {
          data: [opt.name],
          top: 0,
          right: 0,
          itemWidth: 14,
          itemHeight: 14,
          textStyle: {
            color: '#666'
          }
        },
        grid: {
          top: '40',
          left: '60',
          right: '40',
          bottom: '40',
          containLabel: true
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          }
        },
        dataZoom: [
          {
            type: 'slider',
            show: true,
            xAxisIndex: 0,
            height: 20,
            left: 80,
            right: 80,
            bottom: 5,
            startValue: 0,
            endValue: 12
          }
        ],

        xAxis: {
          type: 'category',
          data: xData,
          axisLabel: {
            color: '#666',
            fontSize: 12,
            formatter: this.formatterText,
            show: true,
            interval: 'auto'
          },
          axisLine: {
            lineStyle: { color: '#E5E5E5' }
          }
        },
        yAxis: {
          type: 'value',
          name: '',
          show: true,
          axisLine: { show: false },
          axisTick: { show: false },
          splitLine: { show: false },
          axisLabel: {
            color: '#666',
            fontSize: 12,
            formatter: function (value) {
              return '¥' + value;
            }
          }
        },
        series: [
          {
            name: opt.name,
            data: yData,
            type: 'bar',
            barWidth: '30%',
            itemStyle: {
              color: opt.color
            },
            label: {
              show: true,
              position: 'top',
              color: '#333',
              fontSize: 12,
              textStyle: {
                color: opt.color
              }
            }
          }
        ]
      });
    }
  }
};
