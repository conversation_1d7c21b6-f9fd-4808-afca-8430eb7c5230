<template>
  <div class="overview-wrapper">
    <!--    实时概况-->
    <section class="data-h common-bg">

      <div class="head-box flex flex-item-between">
        <div class="ow-head flex">
          <h3 class="h-title">实时概况</h3>
          <span class="h-time">更新时间：{{ update_time }}</span>
        </div>
        <div class="flex flex-item-align" style="margin-right: 20px;">
          <clinic-search
              v-model="clinic_id"
              className="statistics-sel"
              placeholder="全部诊所"
              @change="changeClinicId"
              is-clearable
              :extra-params="{ scope: 'department' }"
          />
        </div>

      </div>
      <div class="head-wrapper flex">
        <div class="turnover-graph">
          <div class="turnover-graph-head flex flex-item-between" style="padding: 0 10px;line-height: 17px;">
            <h4 class="graph-title flex flex-item-between" style="padding: 0 10px;line-height: 17px;">
              <p>今日营收(元)</p>
              <Tooltip max-width="300" theme="light" placement="bottom" :offset="100" class="custom-tooltip tooltip-left-arrow ml6">
                <div slot="content">
                  <p v-for="(item, index) in overview_tooltip_list" :key="index+'tooltip'">{{ item }}</p>
                </div>
                <p class="flex flex-item-center cursor">
                  <svg-icon iconClass="tip" class="helpIcon cursor"></svg-icon>
                  <!-- <span class="cursor tip-text"></span> -->
                </p>
              </Tooltip>
            </h4>
            <div class="flex">
              <p class="graph-num-today" style="color: #000000;font-size: 14px;margin-right: 20px;">
                今日：￥{{ gross_turnover.today }}</p>
              <p class="graph-num-last" style="font-size: 14px;color: #AAAAAA;">昨日：￥{{ gross_turnover.last }}</p>
            </div>
          </div>
          <div>
            <chart-view height="260px" :chart-option="line_options_turover"></chart-view>
          </div>
        </div>
        <div class="flex flex-warp data-sta">
          <Row class="items-box">
            <Col :span="8" v-for="(item,index) in overviewOptions" :key="item.value"
                 :class="[index<5?'has-line sta-item-box':'sta-item-box']">
              <div class="sta-item flex flex-c">
                <h4 class="sta-t">{{ item.label }}</h4>
                <span class="data-v">
                <span v-if="item.isMoney">¥ {{
                    overview[item.value] && overview[item.value].today|number_format(2)
                  }}</span>
                <span v-else>{{ overview[item.value] && overview[item.value].today }}</span>
                <span v-show="item.isPercent">%</span>
        </span>
                <span class="data-t">
          昨日：<span v-if="item.isMoney">￥{{
                    overview[item.value] && overview[item.value].last|number_format(2)
                  }}</span><span v-else>{{ overview[item.value] && overview[item.value].last }}</span><span
                    v-show="item.isPercent">%</span>
        </span>
              </div>
            </Col>
          </Row>

        </div>

      </div>

    </section>
    <!--    转化分析-->
    <section class="data-trend-wrapper common-bg">
      <div class="trend-h">
        <h3 class="h-title">数据趋势</h3>
        <div class="trend-filter-box flex flex-item-between flex-item-align">
          <div class="trend-tag-box">
            <span class="trend-tag" v-for="(item,index) in trendItems"
                  :class="[current_card_index===index?'tag-active':'']"
                  :key="item.id" @click="changeTrendTag(item,index)">{{ item.desc }}</span>
          </div>
          <div class="trend-date">
            <!--            <Select style="width:60px;margin-right: 10px;" placeholder="日期" >-->
            <!--              <Option v-for="item in ['日','月','周']" :label="item" :key="item" :value="item"></Option>-->
            <!--            </Select>-->
            <!--            <DatePicker type="daterange" placeholder="请选择日期区间" style="width: 200px" :clearable="false"-->
            <!--                        v-model="trendTime" @on-change="changeTrendTime" :options="timeOptions"></DatePicker>-->
            <CustomDatePicker v-model="trendTime" @getDateType="getDateType"></CustomDatePicker>
          </div>
        </div>
        <div class="line-wrapper">
          <chart-view height="260px" :chart-option="line_options"></chart-view>
        </div>
        <div class="conversion-rate-box flex">
          <div class="conversion-rate-item " v-for="item in customer_analysis" :key="item.kw">
            <i-circle :percent="Number(item.current)" :size="180" :stroke-color="item.color" :stroke-width="13"
                      :trail-width="13">
              <span class="percent-text">{{ item.current }}%</span>
            </i-circle>
            <div class="conversion-rate-item-desc flex flex-c flex-item-align">
              <h4 class="item-title flex flex-item-center">
                <p style="padding-top: 1px;">{{ item.text }}</p>
                <Tooltip max-width="300" theme="light" placement="bottom" class="ml6 flex flex-item-center">
                  <div slot="content">
                    <p v-for="(item, index) in item.tooltip_lists" :key="index+'tooltip'">{{ item }}</p>
                  </div>
                  <p class="flex flex-item-center cursor">
                    <svg-icon iconClass="tip" class="helpIcon cursor"></svg-icon>
                    <!-- <span class="cursor tip-text"></span> -->
                  </p>
                </Tooltip>
              </h4>
              <p class="item-num" :style="getColor(item.before) " v-if="showCompare"> {{ getCompareText }}
                <Icon v-if="item.before!=0" :type="item.before>0?'md-arrow-dropup':'md-arrow-dropdown'" size="16"/>
                <span v-if="item.before!=0">{{ Math.abs( item.before ) }}%</span> <span
                  v-if="item.before==0">-</span>
              </p>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 账户收款分布 -->
    <section class="data-trend-wrapper common-bg">
      <div class="block-title flex">
        <p>账户收款分布</p>
        <Tooltip max-width="300" theme="light" placement="bottom" :offset="50"  class="ml6 flex flex-item-center">
          <div slot="content">
            <p v-for="(item, index) in collection_tooltip_list" :key="index+'tooltip'">{{ item }}</p>
          </div>
          <p class="flex flex-item-center cursor">
            <svg-icon iconClass="tip" class="helpIcon cursor"></svg-icon>
            <!-- <span class="cursor tip-text"></span> -->
          </p>
        </Tooltip>
      </div>
      <div class="flex">
        <div class="flex-1 pr30 mt30">
          <Table class="custom-table-line" :columns="collectionCols" :data="collection_lists" v-if="collection_lists.length">
            <template slot-scope="{row, index}" slot="text">
              <div class="flex flex-item-center flex-item-align">
                <!--              <i class="circle" :style="{borderColor:color_table_enum[index]}"></i>-->
                <svg-icon iconClass="weixin" class="svg-block20 ml10" v-if="row.text == '微信支付'"></svg-icon>
                <svg-icon iconClass="money" class="svg-block20 ml10" v-if="row.text == '现金支付'"></svg-icon>
                <span class="ml10">{{ row.text }}</span>
              </div>
            </template>

            <!-- 金额 -->
            <template slot-scope="{row, index}" slot="money">
              <p v-if="row.money" >￥{{ row.money }}</p>
              <p v-else>-</p>
            </template>
          </Table>
          <div class="empty" v-else >
            暂无数据
          </div>
        </div>

        <div class="flex-1 flex flex-item-align">
          <div class="width100">
            <chart-view height="240px" :chart-option="collection_bar_options"></chart-view>
          </div>
        </div>
      </div>
    </section>

    <!--   销售数据     -->
    <section class="sales-data-wrapper common-bg">
      <h3 class="h-title">销售数据</h3>
      <div class="flex" style="width: 100%;">
        <div class="flex-1 pr30 mt30">
          <Table class="custom-table-line" border :span-method="handleSpan" :columns="saleCols" :data="sale_lists" v-if="sale_lists.length">
            <template slot-scope="{row,index}" slot="title" class="flex">
              <!--              <i class="circle" :style="{borderColor:color_table_enum[index]}"></i>-->
              <span>{{ row.title }}</span>
            </template>
            <template slot-scope="{row}" slot="turnover">
              ￥{{ row.turnover }}
            </template>
            <template slot-scope="{row}" slot="inner_turnover">
              ￥{{ row.inner_turnover }}
            </template>
            <template slot-scope="{ row }" slot="ratio">
              {{ row.ratio || 0 }}%
            </template>
          </Table>
          <div class="empty" v-else>
            暂无数据
          </div>
        </div>
        <div class="flex-1 flex flex-item-align">
          <div class="width100">
            <chart-view height="260px" :chart-option="sale_bar_options"></chart-view>
          </div>
        </div>
      </div>
    </section>
    <!-- 理疗消耗数据 -->
    <section class="block mt16">
      <div class="block-title">消耗数据</div>
      <div class="flex">
        <div class="flex-1 pr30 mt30">
          <Table :columns="consumeCols" :data="consume_lists" v-if="consume_lists.length" class="custom-table-line">
            <template slot-scope="{row,index}" slot="title" class="flex">
              <!--              <i class="circle" :style="{borderColor:color_table_enum[index]}"></i>-->
              <span>{{ row.title||row.type_text }}</span>
            </template>
            <template slot-scope="{row}" slot="content">
              {{ row.name }}
            </template>
            <template slot-scope="{row}" slot="amount">
              ￥{{ row.money }}
            </template>
            <template slot-scope="{ row,index }" slot="percent">
              <span v-if="consume_lists.length != index+1 && consume_lists.length-1 != index+1">{{ row.percent || 0 }}%</span>
              <span v-else>-</span>
            </template>
          </Table>
          <div class="empty" v-else>
            暂无数据
          </div>
        </div>

        <div class="flex-1 flex ">
          <div class="width100 flex flex-item-align">
            <chart-view height="260px" :chart-option="consume_bar_options"></chart-view>
          </div>
        </div>
      </div>
    </section>
    <!-- 客户性别分布 -->
    <section class="block mt16">
      <div class="head-title ">
        <h4 class="block-title">客户消费/消耗分析</h4>

      </div>
      <div class="trend-filter-box flex block-mt16  flex-item-align">
        <div class="trend-tag-box" j>
            <span class="trend-tag" v-for="(item,index) in consumption_lists"
                  :class="[current_cuson_index===index?'tag-active':'']"
                  :key="item.id" @click="changeCusTag(item,index)">{{ item.title }}</span>
        </div>
      </div>
      <div class="flex block-mt16">
        <div class="flex-1 pr30">
          <!-- bar -->
          <!--        <div class="flex-1 flex right-line mt16 pr30 height100">-->
          <!--          <div class="width100 flex flex-item-align">-->
          <!--            <chart-view height="240px" :chart-option="arrival_intention_options"></chart-view>-->
          <!--          </div>-->
          <!--        </div>-->

          <Table :span-method="handleSpan" :columns="consumeCols2" :data="consumeList" v-if="consumeList.length" class="custom-table-line">
            <template slot-scope="{row,index}" slot="title" class="flex">
              <!--              <i class="circle" :style="{borderColor:color_table_enum[index]}"></i>-->
              <span>{{ row.title||row.type_text }}</span>
            </template>
            <template slot-scope="{row}" slot="rate">
              {{ row.rate }}%
            </template>
          </Table>
          <div class="empty" v-else>
            暂无数据
          </div>
        </div>

        <div class="flex-1 flex flex-item-center flex-item-align width100">
          <!-- bar -->
          <div class="mt16 flex width100">
            <div class="width100 flex flex-item-align flex-item-center">
              <chart-view height="240px" :chart-option="consumption_analysis_options"></chart-view>
            </div>
          </div>
        </div>
      </div>

    </section>
  </div>
</template>

<script>
import KBrokenLine from '_c/k-echarts/k-broken-line'
import KPieChart from '_c/k-echarts/k-pie-chart'
import { color_enum } from '@/view/statistics/data/color'
import CustomDatePicker from './components/CustomDatePicker/CustomDatePicker'
import { $operator } from 'utils/operation'
import Set from '../store/home/<USER>'
import clinicSearch from '_c/clinic-search/k-clinic-search_data';
import { overview_tooltip_list, overviewOptions, customer_analysis, trendItems, collection_tooltip_list } from './data/overview_data';

import S from 'utils/util'
export default {
  name: 'overview',
  mixins: [],
  components: { Set, KBrokenLine, KPieChart, CustomDatePicker ,clinicSearch},
  data() {
    return {
      timeOptions: {
        disabledDate( date ) {
          return date && date.valueOf() > Date.now() - 86400000
        }
      },

      overview_tooltip_list: overview_tooltip_list,
      overviewOptions: overviewOptions,

      collection_tooltip_list: collection_tooltip_list, // 账户分析tooltip menu

      turnover_amount: {},
      gross_turnover: {},

      trendTime: [],
      trendFormData: {
        st: '',
        et: '',
        date_type: 'day',
      },
      line_options: {},//折线图
      line_options_turover: {}, // 顶部折线图
      color_table_enum: color_enum,//色值表
      // 曲线options
      customer_analysis: customer_analysis,

      /* 账户收款分布 */
      collectionCols: [
        {title: '收款类型', slot: 'text', align: 'center', minWidth: 200, tooltip: true},
        {title: '金额(元)', slot: 'money', align: 'center', width: 100},
      ],
      collection_lists: [],
      collection_bar_options: {}, // 账户收款明细饼图options

      /* 销售数据 */
      saleCols: [
        { title: '销售来源', slot: 'title', align: 'center', minWidth: 80 },
        { title: '总金额(元)', slot: 'turnover', align: 'center', minWidth: 80 },
        { title: '类型', key: 'type_text', align: 'center', minWidth: 60 },
        { title: '订单数', key: 'num', align: 'center', minWidth: 60 },
        { title: '金额(元)', slot: 'inner_turnover', align: 'center', minWidth: 60 },
        { title: '金额占比', slot: 'ratio', align: 'center', minWidth: 60 },
      ],
      update_time: '',//更新时间
      trendItems: trendItems, //数据趋势
      overview: {},
      dateType: '',
      current_card_index: 0,//数据概览
      sale_lists: [],
      sale_bar_options: {},//销售数据饼图
      consume_lists: [],
      consumeCols: [
        { title: '消耗类型', slot: 'title', align: 'center', minWidth: 200 },
        { title: '次数', key: 'quantity', align: 'center', width: 80 },
        { title: '金额(元)', slot: 'amount', align: 'center', width: 100 },
        { title: '金额占比', slot: 'percent', align: 'center', width: 100 },
      ],
      consume: [
        { title: '消耗类型', key: 'title', align: 'center', minWidth: 200 },
        { title: '次数', key: 'num', align: 'center', width: 80 },
        { title: '金额(元)', slot: 'amount', align: 'center', width: 100 },
      ],
      consumeCols2:[
        { title: '支付次数', slot: 'title', align: 'center', minWidth: 200 },
        { title: '人数', key: 'num', align: 'center', width: 80 },
        { title: '占比', slot: 'rate', align: 'center', width: 100 },
      ],
      consume_bar_options: {},
      arrival_intention_list: [],
      arrival_intention_options:{},//到店意向饼图
      consumption_analysis_list: [],
      consumption_analysis_options:{},//到店消耗/消费饼图
      current_cuson_index: 0,
      consumption_lists: [],
      consumeList: [],
      showCompare: true,
      date_type: '',
      _date_type: '',
      compareDateType: '',
      clinic_id: ''
    }
  },
  watch: {
    trendTime: {
      handler( val ) {
        console.log( '-> %c val  === %o ', 'font-size: 15px', val )
        if ( val && val[0] ) {
          this.setDateType()
          this.dateChange()
        }
      }
    }
  },
  computed: {
    getColor( score ) {
      return ( score ) => {
        console.log( '-> %c score  === %o ', 'font-size: 15px', score )
        if ( score == 0 ) {
          return { color: '#999999' }
        } else if ( score > 0 ) {
          return { color: '#E5634B' }
        } else {
          return { color: '#0F9B5D' }
        }
      }
    },
    getCompareText(){
      switch(this.compareDateType){
        case 'day':
          return '较前一日'
        case 'week':
          return '较上一周'
        case 'month':
          return '较上一月'
        case 'daterange':
          return ''
      }
    }
  },
  created() {
    this.initData()
  },
  methods: {
    changeClinicId(){
      this.initData()
      this.dateChange()
    },
    getDateType( getDateType ) {
      this._date_type = getDateType
    },
    setDateType() {
      if ( this._date_type !== 'daterange' ) {
        this.date_type = this._date_type
      } else {
        this.date_type = ''
      }
    },
    getTrend( cardList, currentIndex ) {
      let params = {
        st: this.trendTime[0],
        et: this.trendTime[1],
        date_type: this.date_type,
        item: cardList[currentIndex].id,
        clinic_id: this.clinic_id
      }
      this.$api.getTrend( params ).then( res => {
        const xData = res.list.map( item => item.x )
        const yData = res.list.map( item => item.y )

        this.line_options = this.$eChartFn.areaLineOptions( {
          xAxis: {
            data: xData
          },
          series: [{ data: yData }]
        }, cardList[currentIndex].desc, cardList[currentIndex].isMoney || false )
      } )
    },
    dateChange() {
      // 趋势图
      this.getTrend( this.trendItems, this.current_card_index )
      // this.getItemTrendInfo()
      // this.setTooltip( this.trendItems[0].desc )
      this.getOverViewRatio()
    },
    // 设置账户收款分布饼图数据
    handlerCollection (data) {
      let cloneData = this.$lodash.cloneDeep( data )
      this.collection_lists = data || []
      let resultList = []
      cloneData.forEach( ( item ) => {
        resultList.push( {
          ...item,
          title: item.text,
          value: item.money,
          tootipList: [
            { key: '', value: item.text },
            { key: '', value: `${ item.num }单  ￥${ item.money }` },
            { key: '金额占比：', value: `${ item.percent }%` },
          ],
        } )
      } )
      this.setPieConfigure( 'collection_bar_options', resultList, '' )
    },
    setAmountLine( list ) {
      const xData = list.map( item => item.x )
      const yData = list.map( item => item.y )
      this.line_options_turover = this.$eChartFn.areaLineOptions( {

        xAxis: {
          data: xData,
          axisLabel: {
            interval: 3
          }
        },
        series: [{
          areaStyle: {
            color: '#1157E5', // 设置面积色
          },
          itemStyle: {
            normal: {
              color: '#1157E5', // 拐点颜色
              borderColor: '#1157E5', // 拐点边框色
              borderWidth: 3, // 拐点边框大小
              lineStyle:{
                color: '#1157E5', // 设置线条颜色
                width: 2
              }
            }
          },
          data: yData
        }]
      }, '今日营收', true )
    },
    // 设置面积图tooltil的title
    setTooltip( title ) {
      this.options.tooltip.title = title
    },
    // 表格合并单元格
    handleSpan( { row, column, rowIndex, columnIndex } ) {
      if ( columnIndex === 0 ) {
        // 计算合并的行数列数
        let x = row.colsLength === 0 ? 0 : row.colsLength
        let y = row.colsLength === 0 ? 0 : 1
        return [x, y]
      }

      if ( columnIndex === 1 ) {
        // 计算合并的行数列数
        let x = row.colsLength === 0 ? 0 : row.colsLength
        let y = row.colsLength === 0 ? 0 : 1
        return [x, y]
      }
    },
    initData() {
      this.$api.getOverviewOptions().then( res => {
        this.getOverViewInfo()
      }, err => this.$message.error( err.errmsg ) )
    },
    getOverViewInfo() {
      this.$api.getOverviewInfo({clinic_id:this.clinic_id}).then( info => {
        console.log( info )
        this.update_time = info.update_time
        this.overview = info
        this.turnover_amount = info.turnover_amount
        this.gross_turnover = info.gross_turnover
        this.setAmountLine( info.gross_turnover.list )
        console.log( '-> %c this.overview  === %o ', 'font-size: 15px', this.overview )
      }, err => this.$message.error( err.errmsg ) )

    },
    getOverViewRatio() {
      // const { st, et } = this.trendFormData
      const params = {
        st: this.trendTime[0],
        et: this.trendTime[1],
        date_type: this.date_type,
        clinic_id: this.clinic_id
      }
      this.$api.getOverviewRatio( params ).then( info => {
        console.log( '-> %c info  === %o ', 'font-size: 15px', info )
          this.customer_analysis.map( item => {
            item.before =  S.mathSub(info.customer_analysis[item.id].current ,info.customer_analysis[item.id].before,2)
            item.current = info.customer_analysis[item.id].current
          } )
        this.compareDateType = this._date_type
        console.log("-> %c this.compareDateType  === %o ", "font-size: 15px", this.compareDateType)
        if ( this._date_type === 'daterange' ) {
          this.showCompare = false
        } else {
          this.showCompare = true
        }
        console.log( '-> %c this.customer_analysis  === %o ', 'font-size: 15px', this.customer_analysis )
        this.sale_lists = this.handleSaleLists( info.goods_sales_distribution )
        
        // 设置账户收款分布
        this.handlerCollection(info.pay_type)
        
        this.handleSaleOptions( info.goods_sales_list )
        this.consumption_analysis_list = info.consume_analysis
        this.arrival_intention_list = info.customer_arrives
        // 理疗消耗数据
        this.handlerConsume( info.consume_data_analysis )
        // this.handleArrival( info.customer_arrives )
        this.consumption_lists = info.consume_analysis
        this.handlerConsumption( info.consume_analysis )
      } )
    },
    // 处理商品销售合并单元格的表格数据
    handleSaleLists( list ) {
      let resultList = []
      list && list.forEach( first_item => {
        first_item.list.forEach( ( inner_item, inner_index ) => {
          resultList.push( {
            title: first_item.title,
            turnover: first_item.money,
            type_text: inner_item.title,
            num: inner_item.num,
            inner_turnover: inner_item.money,
            ratio: inner_item.ratio,
            colsLength: inner_index == 0 ? first_item.list && first_item.list.length : 0
          } )
        } )
      } )
      return resultList
    },
    changeTrendTag( item, index ) {
      // this.trendFormData.item = item.id
      // this.setTooltip( item.desc )
      this.current_card_index = index
      this.getTrend( this.trendItems, this.current_card_index )
    },
    // 处理商品销售额占比分析饼图数据
    handleSaleOptions( list ) {
      console.log("-> %c list  === %o ", "font-size: 15px", list)
      let resultList = []
      list && list.forEach( item => {
        resultList.push( {
          ...item,
          value: item.money,
          tootipList: [
            { key: '销售来源:', value: item.title },
            { key: '订单数', value: `${ item.num }` },
            { key: '金额', value: `￥${ item.money }` },
            { key: '金额占比', value: `${ item.percent }%` }
          ]
        } )
      } )
      this.setPieConfigure( 'sale_bar_options', resultList, '销售数据' )
    },
    setPieConfigure( value, data, title, isShow = true ) {
      data.map(item=>item.name=item.title)

      this[value] = this.$eChartFn.orderPie( {
        legend: {
          type:'scroll',
          show: true,
          bottom: '24px',
          left: 'center',
          name: 'consumption'
        },
        series: [{
          name: 'consumption',
          data
        }],
        title: { text: title },
        tooltip: {
          formatter: ( a ) => {
            let content = ''
            a.data.tootipList && a.data.tootipList.forEach( ( item, index ) => {
              if ( index == 0 ) {
                content = content +
                    `<span style="font-size: 13px;font-weight: bold;color: #333333;line-height: 17px;">${ item.key }</span>
                <span style="font-size: 13px;font-weight: bold;color: #333333;line-height: 17px;">${ item.value }</span></br>`
              } else {
                content = content +
                    `<span style="font-size: 11px;font-weight: 300;color: #A7AFC0;line-height: 15px;">${ item.key }</span>
                <span style="font-size: 11px;font-weight: 300;color: #A7AFC0;line-height: 15px;margin-right: 10px">${ item.value }</span>`
              }
            } )
            return `<div class="flex-col">
                      ${ content }
                    </div>
                    `
          },
          show: isShow
        },
      } )
    },
    changeCusTag( item, index ) {
      this.current_cuson_index = index
      this.handlerConsumption(this.consumption_lists)
    },
    // 理疗消耗数据
    handlerConsume( data ) {
      let cloneData = this.$lodash.cloneDeep( data )
      this.consume_lists = data.list || []
      this.consume_lists.push( {
        title: '合计',
        quantity: data.total.quantity,
        money: data.total.money,
        percent: data.total.percent
      } )
      this.consume_lists.push( {
        title: '已过期',
        quantity: data.expired_total.quantity,
        money: data.expired_total.money,
        percent: '-'
      } )
      let resultList = []
      cloneData.list && cloneData.list.forEach( item => {
        console.log("-> %c item  === %o ", "font-size: 15px", item)
        resultList.push( {
          ...item,
          value: item.money,
          tootipList: [
            { key: '消耗占比：', value: item.title },
            { key: '', value: `${ item.num }单  ￥${ item.money }` },
            { key: '', value: `${ item.percent }%` }
          ]
        } )
      } )
      this.setPieConfigure( 'consume_bar_options', resultList, '消耗数据' )
    },
    handlerConsumption( data ) {
      console.log("-> %c data  === %o ", "font-size: 15px", data)
      let cloneData = this.$lodash.cloneDeep( data )[this.current_cuson_index]
      console.log("-> %c cloneData  === %o ", "font-size: 15px", cloneData)
      
      if ( cloneData.title == '消费金额' || cloneData.title == '消耗金额' ) {
        this.consumeCols2[0].title = cloneData.title + '（元）'
      }else {
        this.consumeCols2[0].title = cloneData.title
      }

      this.consumeList = cloneData.list||[]
      let resultList = []
      cloneData.list&&cloneData.list.forEach( item => {
        console.log("-> %c item  === %o ", "font-size: 15px", item)
        resultList.push( {
          ...item,
          value: item.num,
          tootipList: [
            { key: cloneData.title, value: item.title },
            { key: '', value: `${ item.num }人` },
            { key: '占比', value: `${ item.rate }%` }
          ]
        } )
      } )
      console.log("-> %c resultList  === %o ", "font-size: 15px", resultList)

      this.setPieConfigure( 'consumption_analysis_options', resultList, cloneData.title )
    },
    handleArrival( data ) {
      console.log("-> %c data  === %o ", "font-size: 15px", data)
      let cloneData = this.$lodash.cloneDeep( data )
      let resultList = []
      cloneData.length && cloneData.forEach( item => {
        console.log("-> %c item  === %o ", "font-size: 15px", item)
        item.percent = $operator.multiply($operator.divide(item.num_people,item.num_arrives,2),100,0)
        resultList.push( {
          ...item,
          value: item.num_people,
          tootipList: [
            { key: item.title+'：', value: item.num_people+'到店' },
            { key: '消费人数：', value: `${ item.num_people }人` },
            { key: '次数占比：', value: `${ item.percent }%` }
          ]
        } )
      } )
      this.setPieConfigure( 'arrival_intention_options', resultList, '到店数据' )
    },
  },

}
</script>
<style scoped lang="less">
@import url('./style/common.less');

.overview-wrapper {
  margin-top: 20px;

  .data-h {
    .head-wrapper {
      .turnover-graph {
        flex: 1;
      }

      .data-sta {
        flex-wrap: wrap;
        padding: 0 9px;
        flex: 1;

        .has-line {
          border-bottom: 1px solid #EFEFEF;
        }

        .sta-item-box {
          &:nth-child(3n) {
            .data-v {
              border-right: none;
            }
          }

        }

        .sta-item {
          width: 100%;
          padding: 20px 0 20px 60px;

          .sta-t {
            font-size: 12px;
            color: #444444;
            line-height: 17px;
            font-weight: 400;
          }

          .data-v {
            font-size: 20px;
            font-family: OPPOSans;
            font-weight: 500;
            line-height: 29px;
            -webkit-text-stroke: 0px #000000;
            text-stroke: 0px #000000;
            padding: 12px 0 6px;
            border-right: 1px solid #EFEFEF;
            color: #000000;
          }


          .data-t {
            font-weight: 400;
            color: #AAAAAA;
            line-height: 17px;
          }
        }

      }
    }

    .ow-head {
      padding: 20px 10px 16px;

      .h-time {
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #999999;
        line-height: 18px;
        align-self: flex-end;
        transform: scale(0.8)
      }
    }


  }

  .data-trend-wrapper {
    padding: 20px 23px 0px 20px;
    margin-top: 16px;

    .trend-h {
      .h-title {
        margin-bottom: 16px;
      }



      .line-wrapper {
        margin-top: 46px;
        margin-bottom: 30px;
      }
    }
  }

  .conversion-rate-box {
    padding: 50px 0 20px;
    border-top: 1px solid rgba(229, 229, 229, 0.3);

    .conversion-rate-item {
      height: 100%;
      flex: 1;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;

      .percent-text {
        font-size: 25px;
        color: #000000;
      }

      .conversion-rate-item-desc {
        margin-top: 8px;

        .item-title {
          font-size: 12px;
          margin-bottom: 3px;
          font-weight: 400;
          color: #333333;
          line-height: 17px;
        }

        .item-num {
          font-size: 12px;
          line-height: 17px;
          font-weight: 400;
        }
      }
    }
  }

  .sales-data-wrapper {
    margin-top: 16px;
    padding: 20px;
  }
}
</style>
<style lang="less" scoped>
.h-title {
  font-size: 14px;
  font-weight: 600;
  color: #000000;
  line-height: 20px;
}

.common-bg {
  background: #ffffff;
  border-radius: 4px;
}

.width50 {
  width: 50%;
}

.right-line {
  border-right: 1px solid #EFEFEF;
}

.item-icon {
  width: 20px;
  height: 20px;
  color: #D7D7D7;
  margin-left: 5px;

  &:hover {
    cursor: pointer;
  }
}

.circle {
  border-radius: 50%;
  border: 2px solid red;
  width: 10px;
  height: 10px;
  display: inline-block;
}

.bar-table {
  padding-top: 40px;
  margin-right: 100px;

  ::v-deep .ivu-table {
    thead {
      th {
        background: #FFFFFF;
        color: #999999;
      }

      td {
        height: 40px;
      }
    }

    &::before {
      height: 0;
    }

    td {
      border-bottom: none;
      height: 40px;
    }

    th {
      border-bottom: none;
      height: 40px;
    }
  }
}
.trend-filter-box {
  .trend-tag-box {
    display: flex;
    flex:1;
    flex-wrap: wrap;
    height: auto;
    .trend-tag {
      padding: 6px;
      border-radius: 2px;
      opacity: 0.8;
      border: 1px solid #bcc3d7;
      margin-right: 10px;
      cursor: pointer;
      margin-bottom: 8px;
    }

    .tag-active {
      background: #155bd4;
      opacity: 1;
      color: #ffffff;
      border-color: #155bd4;
    }
  }

  .trend-date {
    width: 355px;
    align-self: flex-start;
    ::v-deep .ivu-select-selection {
      height: 29px;
    }

    ::v-deep .ivu-input-with-suffix {
      height: 29px;
    }
  }
}
.cursor {
  cursor: pointer;
}
</style>
