<template>
  <div v-loading="pageLoading" class="app-page-wrapper">
    <!-- Header -->

    <div v-show="!pageLoading">
      <header class="flex gap-20">
        <Form inline label-position="left" :label-width="70" :label-colon="true">
          <div class="flex gap-20">
            <Form-item label="统计范围">
              <clinic-search
                ref="search-cli"
                v-model="clinic_id"
                :show-all="false"
                :is-clearable="false"
                width="300"
                :is-default="true"
                @change="selectClinic"
                @once="once"
              ></clinic-search>
            </Form-item>

            <Form-item label="统计时间">
              <customDatePicker
                v-model="formDate"
                :type-options="['date', 'week', 'month']"
                default-type="date"
                @getDateType="getDateType"
              ></customDatePicker>
            </Form-item>
            <div>
              <Button size="small" type="primary" style="width: 106px; height: 32px" @click="onReport">
                <img class="export-icon" src="https://img-sn-i01s-cdn.rsjxx.com/rsjxx/2024/0702/154428_97382.png" />
                导出数据
              </Button>
            </div>
          </div>
        </Form>
      </header>

      <div>
        <div class="table-item">
          <div class="title">
            <div class="line"></div>
            服务客群
            <Tooltip max-width="400" theme="light" placement="bottom" class="custom-tooltip tooltip-left-arrow">
              <div style="white-space: pre-wrap" slot="content">{{ tips.phy_kq }}</div>
              <svg-icon iconClass="tip" class="helpIcon cursor"></svg-icon>
            </Tooltip>
          </div>
          <Table
            :style="{ maxWidth: tableWidth(service_customer_groupCols.length) }"
            :loading="loading"
            :columns="service_customer_groupCols"
            :data="service_customer_group"
          >
            <template v-slot:employee_level="{ row }"> {{ row.employee }} / {{ row.level }}</template>
          </Table>
        </div>
        <div class="table-item">
          <div class="title">
            <div class="line"></div>
            评价
            <Tooltip max-width="400" theme="light" placement="bottom" class="custom-tooltip tooltip-left-arrow">
              <div style="white-space: pre-wrap" slot="content">{{ tips.phy_pj }}</div>
              <svg-icon iconClass="tip" class="helpIcon cursor"></svg-icon>
            </Tooltip>
          </div>
          <Table
            :style="{ maxWidth: tableWidth(appraiseCols.length) }"
            :loading="loading"
            :columns="appraiseCols"
            :data="appraise"
          >
            <template v-slot:employee_level="{ row }"> {{ row.employee }} / {{ row.level }}</template>
          </Table>
        </div>
        <div class="table-item">
          <div class="title">
            <div class="line"></div>
            服务明细
            <Tooltip max-width="400" theme="light" placement="bottom" class="custom-tooltip tooltip-left-arrow">
              <div style="white-space: pre-wrap" slot="content">{{ tips.phy_mx }}</div>
              <svg-icon iconClass="tip" class="helpIcon cursor"></svg-icon>
            </Tooltip>
          </div>
          <Table
            :style="{ maxWidth: tableWidth(service_detailsCols.length) }"
            :loading="loading"
            :columns="service_detailsCols"
            :data="service_details"
          >
            <template v-slot:employee_level="{ row }"> {{ row.employee }} / {{ row.level }}</template>
          </Table>
        </div>
        <div class="table-item">
          <div class="title">
            <div class="line"></div>
            复购
            <Tooltip max-width="400" theme="light" placement="bottom" class="custom-tooltip tooltip-left-arrow">
              <div style="white-space: pre-wrap" slot="content">{{ tips.phy_fg }}</div>
              <svg-icon iconClass="tip" class="helpIcon cursor"></svg-icon>
            </Tooltip>
          </div>
          <Table
            :style="{ maxWidth: tableWidth(repurchaseCols.length) }"
            :loading="loading"
            :columns="repurchaseCols"
            :data="repurchase"
          >
            <template v-slot:employee_level="{ row }"> {{ row.employee }} / {{ row.level }}</template>
          </Table>
        </div>
        <div class="table-item">
          <div class="title">
            <div class="line"></div>
            收入分析
            <Tooltip max-width="400" theme="light" placement="bottom" class="custom-tooltip tooltip-left-arrow">
              <div style="white-space: pre-wrap" slot="content">{{ tips.phy_fx }}</div>
              <svg-icon iconClass="tip" class="helpIcon cursor"></svg-icon>
            </Tooltip>
          </div>
          <Table
            :style="{ maxWidth: tableWidth(revenue_analysisCols.length) }"
            :loading="loading"
            :columns="revenue_analysisCols"
            :data="revenue_analysis"
          >
            <template v-slot:employee_level="{ row }"> {{ row.employee }} / {{ row.level }}</template>
          </Table>
        </div>
      </div>
    </div>

    <ReportExport v-model="reportModalVisible" title="理疗师统计表" :api-name="apiName"></ReportExport>
  </div>
</template>

<script>
import tip from './data/tip';
import S from 'utils/util';
import ReportExport from './components/ReportExport-1.vue';
import moment from 'moment';
import clinicSearch from '@/components/clinic-search/clinic-search.vue';
import customDatePicker from './components/CustomDatePicker/CustomDatePicker.vue';

export default {
  name: 'Physio',
  components: {
    ReportExport,
    clinicSearch,
    customDatePicker
  },
  data() {
    return {
      clinic_id: '',
      reportModalVisible: false,
      pageLoading: false,
      loading: false,
      apiName: 'getPhysioReport',
      formDate: [],

      month: '',
      date_type: 'month',
      typeOpts: [],

      date: '',

      tips: {
        ...tip.phyTip
      },

      type: 'month',
      format: '',

      st: '',
      et: '',

      // 服务客群
      service_customer_group: [],
      service_customer_groupCols: [
        {
          title: '员工 / 职级',
          slot: 'employee_level',
          width: 150,
          align: 'center',
          fixed: 'left'
        },
        {
          title: '服务客户总数',
          key: 'total_customer',
          align: 'center',
          width: 150
        },
        {
          title: '老客数',
          key: 'old_customer',
          align: 'center',
          width: 150
        },
        {
          title: '新客数',
          key: 'new_customer',
          align: 'center',
          width: 150
        },
        {
          title: '会员客数',
          key: 'member_customer',
          align: 'center',
          width: 150
        },
        {
          title: '非会员客数',
          key: 'no_member_customer',
          align: 'center',
          width: 150
        },
        {
          title: '预约客户数',
          key: 'reservation_customer',
          align: 'center',
          width: 150
        },
        {
          title: '非预约客户数',
          key: 'no_reservation_customer',
          align: 'center',
          width: 150
        }
      ],
      //评价
      appraise: [],
      appraiseCols: [
        {
          title: '员工 / 职级',
          slot: 'employee_level',
          width: 150,
          align: 'center',
          fixed: 'left'
        },
        {
          title: '评价数',
          key: 'total',
          align: 'center',
          width: 150
        },
        {
          title: '带文评价数',
          key: 'have_text',
          align: 'center',
          width: 150
        }
      ],

      // 服务详情
      service_details: [],
      service_detailsCols: [
        {
          title: '员工 / 职级',
          slot: 'employee_level',
          width: 150,
          align: 'center',
          fixed: 'left'
        },
        {
          title: '服务项目数',
          key: 'service_items',
          align: 'center',
          width: 150
        }
      ],

      // 复购
      repurchase: [],
      repurchaseCols: [
        {
          title: '员工 / 职级',
          slot: 'employee_level',
          width: 150,
          align: 'center',
          fixed: 'left'
        },
        {
          title: '复购人数',
          key: 'repurchase_customer',
          align: 'center',
          width: 150
        },
        {
          title: '复购率',
          key: 'repurchase_rate',
          align: 'center',
          width: 150
        }
      ],
      // 收入分析
      revenue_analysis: [],
      revenue_analysisCols: [
        {
          title: '员工 / 职级',
          slot: 'employee_level',
          width: 150,
          align: 'center',
          fixed: 'left'
        },
        {
          title: '实耗服务数量',
          key: 'serv_used_num',
          align: 'center',
          width: 150
        },
        {
          title: '实耗业绩',
          key: 'serv_used_money',
          align: 'center',
          width: 150
        },
        // {
        //   title: '销售商品数量',
        //   key: 'product_quantity',
        //   align: 'center',
        //   width: 150
        // },
        {
          title: '销售业绩',
          key: 'product_revenue',
          align: 'center',
          width: 150
        }
      ]
    };
  },
  watch: {
    formDate: {
      handler(val) {
        console.info('🚀 ~ file:doctor method:handler line:352 -----', val);
        if (val && val[0]) {
          this.st = val[0];
          this.et = val[1];
          this.getList();
        }
      }
    }
  },
  // created() {
  //   this.intiData();
  // },
  mounted() {
    this.scrollTop();
  },
  methods: {
    tableWidth(length, minWidth = 150) {
      // 根据 colums 的数量和每列的最小宽度来计算
      return `${length * minWidth}px`;
    },
    scrollTop() {
      this.$nextTick(() => {
        const wrapper = document.querySelector('.app-wrapper');
        if (wrapper) wrapper.scrollTop = 0;
      });
    },

    getDateType(type) {
      console.info('🚀 ~ file:doctor method:getDateType line:372 -----', type);
      if (this.formDate && this.formDate[0]) {
        this.st = this.formDate[0];
        this.et = this.formDate[1];
        this.date_type = type;
        this.getList();
      }
    },

    selectClinic() {
      this.getList();
    },
    // 选中诊所回调执行一次
    once() {
      this.getList();
    },

    getMonthRa(date) {
      const year = date.getFullYear();
      const month = date.getMonth() + 1;
      const pad = n => (n < 10 ? '0' + n : n);
      const st = `${year}-${pad(month)}-01`;
      const lastDay = new Date(year, month, 0).getDate();
      const et = `${year}-${pad(month)}-${lastDay}`;
      return {
        st,
        et
      };
    },

    intiData() {
      const now = new Date();
      this.date = `${now.getFullYear()}-${now.getMonth() + 1}`;
      this.st = this.getMonthRa(now).st;
      this.et = this.getMonthRa(now).et;

      this.$api.getPhysioOpt().then(res => {
        this.typeOpts = S.descToArrHandle(res.date_type);
      });
    },

    getList() {
      if (!this.clinic_id) return;
      this.loading = true;
      this.pageLoading = true;
      this.$api
        .getPhysioList({
          st: this.st,
          et: this.et,
          date_type: this.date_type,
          clinic_id: this.clinic_id
        })
        .then(res => {
          this.appraise = res.appraise || [];
          this.repurchase = res.repurchase || [];
          this.revenue_analysis = res.revenue_analysis || [];
          this.service_customer_group = res.service_customer_group || [];
          this.service_details = res.service_details || [];
        })
        .finally(() => {
          this.loading = false;
          this.pageLoading = false;
        });
    },

    onChangeType() {
      const date_type_map = {
        day: {
          type: 'date',
          curDate: '',
          format: ''
        },
        week: {
          type: 'week',
          curDate: '',
          format: 'yyyy 第 WW 周'
        },
        month: {
          type: 'month',
          curDate: '',
          format: ''
        }
      };

      this.type = date_type_map[this.date_type].type;
      this.date = '';
      this.format = date_type_map[this.date_type].format;
    },
    handleTimeChange(value) {
      let st = '';
      let et = '';
      let date = value;

      // 防止 date 解析失败
      if (!(date instanceof Date) || isNaN(date.getTime())) {
        return;
      }

      if (this.date_type == 'day') {
        // 日
        st = et = moment(value).format('YYYY-MM-DD');
      } else if (this.date_type == 'week') {
        // 周
        const day = date.getDay() || 7;
        const monday = new Date(date);
        monday.setDate(date.getDate() - day + 1);
        const sunday = new Date(monday);
        sunday.setDate(monday.getDate() + 6);
        st = moment(monday).format('YYYY-MM-DD');
        et = moment(sunday).format('YYYY-MM-DD');
      } else if (this.date_type == 'month') {
        // 月
        const year = date.getFullYear();
        const month = date.getMonth() + 1;
        const pad = n => (n < 10 ? '0' + n : n);
        st = `${year}-${pad(month)}-01`;
        const lastDay = new Date(year, month, 0).getDate();
        et = `${year}-${pad(month)}-${lastDay}`;
      }
      this.st = st;
      this.et = et;
      this.getList();
    },
    onReport() {
      this.reportModalVisible = true;
    }
  }
};
</script>
<style scoped lang="less">
.gap-20 {
  gap: 20px;
}
.export-icon {
  width: 14px;
  height: 13px;
  margin-right: 6px;
}

.app-page-wrapper {
  background-color: #fff;
  min-height: 90vh;
  padding: 24px;
  margin-top: 10px;

  .table-item {
    margin-top: 24px;

    .title {
      display: flex;
      gap: 8px;

      .line {
        width: 4px;
        height: 18px;
        background: linear-gradient(90deg, #155bd4 0%, #4988fd 100%);
        border-radius: 2px;
      }

      font-size: 16px;
      font-weight: 600;
      color: #222;
      margin-top: 12px;
      margin-bottom: 10px;
    }
  }
}
</style>
