<template>
  <div class="statements-wrapper">
    <div class="statements-item">
      <div class="statements-label">店铺业绩报表</div>
      <Row style="flex: 1">
        <Col v-for="(item, index) in storePerformanceList" :key="item.url" :span="6" class="item-box">
          <div
            class="report-item flex flex-item-align"
            @click="changeType({ type: item.url, title: item.title, apiName: 'getReportManagesalesurl' })"
          >
            <div class="svg-box">
              <svg-icon class="report-svg" :icon-class="item.svgUrl"></svg-icon>
            </div>
            <div class="content-box">
              <h4 class="title">
                {{ item.title }}
              </h4>
              <p class="content">
                {{ item.content }}
              </p>
            </div>
          </div>
        </Col>
      </Row>
    </div>

    <ReportExport
      v-model="reportModalVisible"
      :apiName="apiName"
      :title="reportTitle"
      :type="type"
      :reportIsNeedType="reportIsNeedType"
    ></ReportExport>
  </div>
</template>

<script>
import { getEnv } from '@/utils/runtime';
import ReportExport from './components/ReportExport';

export default {
  name: 'report',
  components: {
    ReportExport
  },
  data() {
    return {
      apiName: 'getReportManagesalesurl',

      // 店铺业绩报表
      storePerformanceList: [
        {
          title: '店铺经营报表',
          url: 'MANAGE',
          content: '支持导出诊所关键业绩及客户数据',
          svgUrl: 'performance',
          isDown: true
        },
        {
          title: '店铺销售类型分布表',
          url: 'SALES_TYPE',
          content: '支持导出销售的各分类数据，与实际消费金额对应',
          svgUrl: 'sale',
          isDown: true
        },
        {
          title: '店铺核销类型分布表',
          url: 'USE_TYPE',
          content: '支持导出核销的各分类数据，与理疗消耗金额对应',
          svgUrl: 'writeOff',
          isDown: true
        },
        {
          title: '店铺货品成本明细报表',
          url: 'PROD_COST_DETAIL',
          content: '店铺货品成本月报，显示月度期末库存、成本价及金额',
          svgUrl: 'goods',
          isDown: true
        }
      ],

      reportModalVisible: false,
      reportTitle: '',
      reportIsNeedType: true,
      type: '' //  导出表的类型
    };
  },
  computed: {},
  watch: {},
  created() {
    this.storePerformanceList = this.storePerformanceList.filter(item => item.isDown);
  },
  mounted() {},
  methods: {
    changeType({ type, title, reportIsNeedType = true, apiName }) {
      this.apiName = apiName;
      this.type = type;
      this.reportTitle = title;
      this.reportModalVisible = true;
      this.reportIsNeedType = reportIsNeedType;
    }
  }
};
</script>

<style lang="less" scoped>
.statements-item {
  padding-left: 10px;
  .statements-label {
    font-size: 16px;
    font-weight: 500;
    color: #333333;
    margin-bottom: 12px;
  }
}

.margin-bottom20 {
  margin-bottom: 20px;
}

.import-content {
  height: 200px;

  .isDown {
    background: #fff8f0;
    color: #e18517;
    padding: 4px 4px 4px 14px;
    border: 1px solid #f2d0a5;
    border-radius: 4px;
  }

  .choose {
    color: rgba(17, 87, 229, 0.5);
  }

  .download {
    margin-left: 20px;
    color: rgb(155, 141, 141);
    border-bottom: 1px solid #ccc;
  }

  .error-report {
    margin-top: 20px;

    .download-error {
      margin-left: 15px;
      color: rgba(17, 87, 229, 0.5);
    }
  }
}
</style>

<style lang="less" scoped>
.statements-wrapper {
  margin-top: 10px;
  background: #fff;
  height: e('calc(100vh - 110px)');
  padding: 10px;
  overflow: auto;
}
// common less
.cursor {
  cursor: pointer;
}

.hover {
  &:hover {
    color: #155bd4 !important;
  }
}

p {
  margin-bottom: 0px;
}

.item-box {
  margin-right: 24px;
  .report-item {
    padding: 20px 10px;
    margin-bottom: 24px;
    border: 1px solid #edeefc;
    border-radius: 6px;
    height: 100px;
    cursor: pointer;
    .svg-box {
      margin-right: 14px;
      .report-svg {
        font-size: 30px;
      }
    }
    .content-box {
      .title {
        font-size: 13px;
        font-weight: 800;
        //color: #155BD4;
        line-height: 20px;
        margin-bottom: 8px;
        color: #262626;
      }
      .content {
        color: #999;
      }
    }
  }
}
.active {
  box-shadow: 0px 0px 8px 0px rgba(21, 91, 212, 0.3);
  border-color: #155bd4 !important;
  border-width: 1.5px !important;
  .title {
    color: #155bd4 !important;
  }
  .content {
    color: #155bd4 !important;
  }
}
.cursor {
  cursor: pointer;
}
</style>
