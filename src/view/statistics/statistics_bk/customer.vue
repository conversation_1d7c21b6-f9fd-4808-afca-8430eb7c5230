<template>
  <div class="customer-wrapper">
    <section class="g-h flex flex-item-between flex-item-center ceiling">
      <div class="g-h-l flex flex-item-align">
        <!-- <span style="margin-left: 20px;">省公司：</span>
        <span class="company-name">{{$store.state.app.companyName}}</span> -->
        <clinic-search ref="qwer" className="statistics-sel" v-model="all_formData.clinic_id" @change="changeClinicId" height="30px"></clinic-search>
      </div>
      <div class="g-h-r">
        <Button :type="dataType===''?'primary':'default'" @click="changeDataType('')">全部</Button>
        <Button :type="dataType==='his'?'primary':'default'" style="margin: 0 8px;" @click="changeDataType('his')">
          HIS诊疗
        </Button>
        <Button :type="dataType==='shop'?'primary':'default'" @click="changeDataType('shop')">商城购物</Button>
      </div>
    </section>

    <!-- 整体情况 -->
    <div class="overall-wrapper common-bg">
      <div class="flex flex-item-between flex-item-align">
        <h3 class="h-title">整体情况</h3>
        <div>
          <span class="label">统计时间：</span>
          <DatePicker type="daterange" placeholder="请选择日期区间" style="width: 200px" :options="timeOptions"
                      v-model="allTimes"
                      @on-change="changeAllTime"></DatePicker>
        </div>
      </div>

      <div class="card-exhibition-wrapper flex">
        <div v-for="(item,index) in overviewOptions" @click="changeOverview(item.value, item.label)" :key="item.id"
             class="card-exhibition" :class="{'exhi-active':actOverItem === item.value}">
          <p class="exhibition-title">{{ item.label }}</p>
          <p class="exhibition-money">
            <span v-if="item.isMoney">{{ overview[item.value] && overview[item.value]|number_format }}</span>
            <span v-else>{{ overview[item.value] && (overview[item.value]||'-') }}</span>
            <!-- <span v-show="item.isPercent">%</span> -->
          </p>
        </div>
      </div>
      <div class="line-wrapper" v-show="activeItem !== '复购客户数'">
        <K-Broken-Line :id="Date.parse(new Date()).toString()+'line1'"
                       :options="line_options" :key="activeItem"></K-Broken-Line>
      </div>
      <div class="line-wrapper flex flex-item-center" v-show="activeItem === '复购客户数'" style="height: 300px">
        暂无数据趋势
      </div>
    </div>

    <!-- 营业额排行榜 -->
    <div class="card margin-top20" v-show="clinic_rank.length > 1">
      <h3 class="h-title" style="margin-bottom: 16px;">客户数排行榜</h3>
      <div>
        <k-Normal-Bar :id="Date.parse(new Date()).toString()+'normal1'" :is-customer="true" :options="normalOptions"></K-Normal-Bar>
      </div>
    </div>

    <!-- 消费分布 -->
    <div class="card margin-top16">
      <div class="flex flex-item-between flex-item-align">
        <h3 class="h-title">消费分布</h3>
        <div>
          <span class="label">统计时间：</span>
          <DatePicker type="daterange" placeholder="请选择日期区间" style="width: 200px" :options="timeOptions"
                      v-model="costTimes"
                      @on-change="changeCusTime"></DatePicker>
        </div>
      </div>
      <!-- 客户类型统计 -->
      <div class="statistics-wrapper">
        <h3 class="h-title s-title-margin">客户类型统计</h3>
        <Button style="margin-right: 10px;" :type="pay_type==='pay_num'?'primary':'default'"
                @click="changePayType('pay_num','客户支付次数占比')">支付次数
        </Button>
        <Button :type="pay_type==='pay_amount'?'primary':'default'" @click="changePayType('pay_amount','客户支付金额占比')">
          支付金额
        </Button>
        <div class="echarts-wrapper flex">
          <!-- left bar -->
          <div class="line-echarts flex-1">
            <K-Vertical-Bar :id="Date.parse(new Date()).toString()+'vertical1'"
                            :dataSource="pay_type_chart_data"></K-Vertical-Bar>
          </div>
          <!-- right pie -->
          <div class="bar-echarts flex-1 flex">
            <div class="width50">
              <K-Pie-Chart :id="Date.parse(new Date()).toString()+'pie1'" :options="optionsPie_pay"></K-Pie-Chart>
            </div>
            <div class="bar-table width50">
              <Table :columns="pieCols" :data="channelData">
                <template slot-scope="{row,index}" slot="title">
                  <!--              <i class="circle" :style="{borderColor:color_table_enum[index]}"></i>-->
                  <span>{{ row.title }}</span>
                </template>
                <template slot-scope="{row}" slot="num">
                  {{ row.num }}
                </template>
                <template slot-scope="{row}" slot="rate">
                  {{ row.rate }}%
                </template>
              </Table>
            </div>
          </div>
        </div>
      </div>

      <!-- 客户来源渠道排行 -->
      <div class="channel-wrapper">
        <h3 class="h-title s-title-margin">客户来源渠道排行</h3>
        <div v-if="!dataType">
          <Button style="margin-right: 10px;" :type="source_type==='HIS'?'primary':'default'"
                  @click="changeSourceType('HIS')">HIS诊疗
          </Button>
          <Button :type="source_type==='MIL'?'primary':'default'" @click="changeSourceType('MIL')">商城购物</Button>
        </div>
        <div class="echarts-wrapper flex" style="margin-top: 30px;">
          <!-- left bar -->
          <div class="flex-1 has-border-t flex flex-item-l-center">
            <Table :columns="sourceCols" :data="sourceData" v-if="sourceData.length" style="width: 100%;">
              <template slot-scope="{row,index}" slot="rank">
                <div class="top-three" v-if="index<3">
                  <img src="@/assets/image/data/<EMAIL>" alt="rank1" v-show="index === 0">
                  <img src="@/assets/image/data/<EMAIL>" alt="rank2" v-show="index === 1">
                  <img src="@/assets/image/data/<EMAIL>" alt="rank3" v-show="index === 2">
                </div>
                <div style="text-align: center;color:#F0A522;" v-else>{{ index + 1 }}</div>
              </template>
              <template slot-scope="{row}" slot="text">
                {{ row.text }}
              </template>
              <template slot-scope="{row}" slot="num">
                {{ row.num }}
              </template>
              <template slot-scope="{row}" slot="rate">
                {{ row.rate }}%
              </template>
            </Table>
            <div class="empty" v-else>暂无数据</div>
          </div>

          <!-- right pie -->
          <div class="bar-echarts flex-1 flex">
            <div class="width50">
              <K-Pie-Chart :id="Date.parse(new Date()).toString()+'pie2'" :options="optionsPie_source"></K-Pie-Chart>
            </div>
            <div class="bar-table width50">
              <Table :columns="sourceCols_r" :data="sourceData">
                <template slot-scope="{row,index}" slot="text">
                  <!--              <i class="circle" :style="{borderColor:color_table_enum[index]}"></i>-->
                  <span>{{ row.text }}</span>
                </template>
                <template slot-scope="{row}" slot="num">
                  {{ row.num }}
                </template>
                <template slot-scope="{row}" slot="rate">
                  {{ row.rate }}%
                </template>
              </Table>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 客户画像 -->
    <div class="card margin-top16">
      <h3 class="h-title">客户画像</h3>
      <!-- 客户性别分布 -->
      <div class="statistics-wrapper">
        <div class="flex">
          <h3 class="h-title s-title-margin flex-1">客户性别分布</h3>
          <h3 class="h-title s-title-margin flex-1">客户年龄分布</h3>
        </div>
        <div class="echarts-wrapper flex">
          <!-- left bar -->
          <div class="line-echarts flex-1 flex">
            <div class="width50">
              <K-Pie-Chart :id="Date.parse(new Date()).toString()+'pie3'" :options="optionsPie_sex"></K-Pie-Chart>
            </div>
            <div class="bar-table width50">
              <Table :columns="age_cols" :data="gender_list">
                <template slot-scope="{row,index}" slot="text">
                  <!--              <i class="circle" :style="{borderColor:color_table_enum[index]}"></i>-->
                  <span>{{ row.text }}</span>
                </template>
                <template slot-scope="{row}" slot="num">
                  {{ row.num }}
                </template>
                <template slot-scope="{row}" slot="rate">
                  {{ row.rate }}%
                </template>
              </Table>
            </div>
          </div>

          <!-- right pie -->
          <div class="bar-echarts flex-1 flex">
            <div class="width50">
              <K-Pie-Chart :id="Date.parse(new Date()).toString()+'pie4'" :options="optionsPie_age"></K-Pie-Chart>
            </div>
            <div class="bar-table width50">
              <Table :columns="gender_cols" :data="age_list">
                <template slot-scope="{row,index}" slot="text">
                  <!--              <i class="circle" :style="{borderColor:color_table_enum[index]}"></i>-->
                  <span>{{ row.text }}</span>
                </template>
                <template slot-scope="{row}" slot="num">
                  {{ row.num }}
                </template>
                <template slot-scope="{row}" slot="rate">
                  {{ row.rate }}%
                </template>
              </Table>
            </div>
          </div>
        </div>
      </div>

      <!-- 客户支付方式偏好 -->
      <div class="statistics-wrapper">
        <h3 class="h-title s-title-margin">客户支付方式偏好</h3>
        <div class="echarts-wrapper flex">
          <!-- left bar -->
          <div class="line-echarts flex-1 flex">
            <div class="width50">
              <K-Pie-Chart :id="Date.parse(new Date()).toString()+'pie5'" key="optionsPie_like"
                           :options="optionsPie_like"></K-Pie-Chart>
            </div>
            <div class="bar-table width50">
              <Table :columns="paytype_cols" :data="pay_type_list">
                <template slot-scope="{row,index}" slot="text">
                  <!--              <i class="circle" :style="{borderColor:color_table_enum[index]}"></i>-->
                  <span>{{ row.text }}</span>
                </template>
                <template slot-scope="{row}" slot="num">
                  {{ row.num }}
                </template>
                <template slot-scope="{row}" slot="rate">
                  {{ row.rate }}%
                </template>
              </Table>
            </div>
          </div>
          <!-- empty area -->
          <div class="flex-1"></div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import KBrokenLine from '_c/k-echarts/k-broken-line';
import KVerticalBar from '_c/k-echarts/k-vertical-bar';
import KPieChart from '_c/k-echarts/k-pie-chart';
import {overviewOptions_HIS, overviewOptions_MIL, allOverviewOptions} from './data/customer_data'
import clinicSearch from '_c/clinic-search/k-clinic-search_data'
import KNormalBar from '_c/k-echarts/k-normal-bar.vue';
import {color_enum} from "@/view/statistics/data/color";
export default {
  name: "customer",
  components: {KBrokenLine, KVerticalBar, KPieChart, clinicSearch,KNormalBar},
  data() {
    return {
      allTimes: [],
      costTimes: [],
      all_formData: {
        biz_type: '',//业务类型 his-HIS诊疗 shop-商城购物
        st: '',
        et: '',
        item: 'order_cv',
        clinic_id: ''
      },
      cost_formData: {
        biz_type: '',//业务类型 his-HIS诊疗 shop-商城购物
        st: '',
        et: '',
        item: 'order_cv',
      },
      timeOptions: {
        disabledDate(date) {
          return date && date.valueOf() > Date.now()  - 86400000
        }
      },
      normalOptions: {//排行
        xData:[],
        yData: [],
        xLabel: '单位：人'
      },
      overviewOptions: allOverviewOptions,
      dataType: '',//数据类型
      activeItem: '', // 选中的卡片
      // 客户来源渠道表格column
      sourceCols: [
        {title: '排名', slot: 'rank', width: 40,},
        {title: '来源渠道', slot: 'text', width: 190},
        {title: '客户数', slot: 'num', align: 'center', minWidth: 200},
        {title: '占比', slot: 'rate', align: 'right', width: 80},
      ],
      sourceCols_r: [
        {title: '客户来源渠道', slot: 'text',minWidth: 80},
        {title: '人数', slot: 'num', align: 'center'},
        {title: '占比', slot: 'rate', align: 'right', width: 80},
      ],
      pieCols: [],
      pie1Cols: [
        {title: '支付次数', slot: 'title',minWidth: 80},
        {title: '人数', slot: 'num', align: 'center',},
        {title: '占比', slot: 'rate', align: 'right', width: 80},
      ],
      pie1Cols_amount: [
        {title: '支付金额', slot: 'title',minWidth: 80},
        {title: '人数', slot: 'num', align: 'center',},
        {title: '占比', slot: 'rate', align: 'right', width: 80},
      ],
      pie2Cols: [
        {title: '客户来源渠道', slot: 'rank',},
        {title: '人数', slot: 'content', align: 'center',},
        {title: '占比', slot: 'num', align: 'right', width: 80},
      ],
      age_cols: [
        {title: '性别', slot: 'text',},
        {title: '人数', slot: 'num', align: 'center',},
        {title: '占比', slot: 'rate', align: 'right', width: 80},
      ],
      gender_cols: [
        {title: '年龄', slot: 'text',},
        {title: '人数', slot: 'num', align: 'center',},
        {title: '占比', slot: 'rate', align: 'right', width: 80},
      ],
      paytype_cols: [
        {title: '支付方式', slot: 'text',},
        {title: '次数', slot: 'num', align: 'center',},
        {title: '占比', slot: 'rate', align: 'right', width: 80},
      ],
      channelData: [],
      sourceData: [],//来源数据
      line_options: {
        xAxisData: [],
        seriesData: [],
        tooltip: {title: allOverviewOptions[0].label},
        color: {lineColor: '#1157E5', areaColor: '#1157E5'},
      },
      optionsPie_pay: {
        seriesData: [],
        color: color_enum,
        legendData: [],
        name: '客户支付次数占比',
      },
      optionsPie_source: {
        seriesData: [],
        color: color_enum,
        legendData: [],
        name: '客户来源渠道占比',
      },
      optionsPie_sex: {
        seriesData: [],
        color: color_enum,
        legendData: [],
        name: '客户支付次数占比',
      },
      optionsPie_age: {
        seriesData: [],
        color: color_enum,
        legendData: [],
        name: '客户支付次数占比',
      },
      optionsPie_like: {
        seriesData: [],
        color: color_enum,
        legendData: [],
        name: '客户支付次数占比',
      },
      overview: {},
      purchase_num_rates: [],//支付次数
      purchase_amount_rates: [],//支付金额
      color_table_enum: color_enum,//色值表
      pay_type: 'pay_num',//支付次数/金额
      source_type: 'HIS',//来源类型
      sorce_his_list: [],
      sorce_shop_list: [],
      age_list: [],//性别分布
      gender_list: [],//性别分布
      pay_type_list: [],//性别分布
      actOverItem: 'order_cv',
      chart_data_pur_num: {},
      chart_data_pur_amount: {},
      pay_type_chart_data: {},
      line_key: '',
      clinic_rank: [],

    }
  },
  mounted() {
    this.pieCols = this.pie1Cols
    this.getInitData()
  },
  methods: {
    // 设置面积图tooltil的title
    setTooltip (title) {
      this.line_options.tooltip.title = title
    },
    getCusRank(){//排行
      this.$api.getCusNumRank(this.all_formData).then(res=>{
        console.log("-> res", res)
        this.clinic_rank = res.clinic_rank
        this.normalOptions.xData = []
        this.normalOptions.yData = []
        for (const item of res.clinic_rank) {
          console.log(item)
          this.normalOptions.xData.push(item.value)
          this.normalOptions.yData.push(item.name)
        }
        this.normalOptions.xData = this.normalOptions.xData.reverse()
        this.normalOptions.yData = this.normalOptions.yData.reverse()
        
      })
    },
    changeClinicId(clinic_id){
      console.log("-> clinic_id", clinic_id)
      this.all_formData.clinic_id = clinic_id
      this.pay_type='pay_num'
      this.source_type = 'HIS'
      this.getInitData()
    },
    getInitData() {
      this.getCusOverview()
      this.getCusPort()
      this.getCusAnsis()
      this.getCusRank()
    },
    getCusOverview() {
      this.$api.getCustomerOverview(this.all_formData).then(res => {
        console.log(res)
        this.overview = res.overview
        this.allTimes = [res.st, res.et]
        this.all_formData.st = res.st
        this.all_formData.et = res.et
        this.getTrendData()
      })
    },
    getCusPort() {
      const {biz_type,clinic_id } = this.all_formData
      this.$api.getCustomerPortrait({biz_type,clinic_id }).then(res => {
        console.log(res)
        this.age_list = res.customer_portrait.age
        console.log("-> this.age_list", this.age_list)
        this.gender_list = res.customer_portrait.gender
        console.log("-> this.gender_list", this.gender_list)
        this.pay_type_list = res.customer_portrait.pay_type
        console.log("-> this.pay_type_list", this.pay_type_list)
        this.handlePieChart(this.age_list, 'optionsPie_age', '客户年龄分布占比')
        this.handlePieChart(this.gender_list, 'optionsPie_sex', '客户性别分布占比')
        this.handlePieChart(this.pay_type_list, 'optionsPie_like', '支付方式偏好占比',)
      })
    },
    getCusAnsis() {
      const {company_id, clinic_id} = this.all_formData
      this.$api.getCustomerAnalysis({...this.cost_formData,clinic_id,company_id}).then(res => {
        console.log(res)
        this.costTimes = [res.st, res.et]
        this.cost_formData.st = res.st
        this.cost_formData.et = res.et
        this.sorce_his_list = res.customer_channel_analysis.his
        this.sorce_shop_list = res.customer_channel_analysis.shop
        if(this.dataType !== 'shop'){
          this.sourceData = this.sorce_his_list
        }else {
          this.sourceData = this.sorce_shop_list
        }
        this.channelData = this.purchase_num_rates = res.purchase_analysis.purchase_num_rates
        this.chart_data_pur_amount = this.handDataToChartData(res.purchase_analysis.purchase_amount_rates)
        this.chart_data_pur_num = this.handDataToChartData(this.purchase_num_rates)
        this.purchase_amount_rates = res.purchase_analysis.purchase_amount_rates
        if(this.pay_type === 'pay_num'){
          this.pay_type_chart_data = this.chart_data_pur_num
        }else {
          this.pay_type_chart_data = this.chart_data_pur_amount
        }
        this.handlePieChart(this.purchase_num_rates, 'optionsPie_pay', '客户支付次数占比')
        this.handlePieChart(this.sourceData, 'optionsPie_source', '客户来源渠道占比')
      }).finally(()=>{
        this.$forceUpdate()
      })
    },
    changeOverview(overview, label) {
      this.actOverItem = overview
      this.all_formData.item = overview
      this.activeItem = label
      this.setTooltip(label)
      this.getTrendData()
    },
    handlePieChart(data, type, name) {
      console.log(data)
      console.log("-> this[type]", this[type])
      let seriesData = data.map(item => {
        return {
          name: item.text || item.title,
          value: item.num,
          rate: item.rate && (item.rate + '%')
        }
      })
      console.log(type, seriesData)
      this[type].seriesData = seriesData
      name && (this[type].name = name)
    },
    changeDataType(type) {
      this.dataType = type
      this.all_formData.biz_type = type
      this.cost_formData.biz_type = type
      if (!type) {
        this.overviewOptions = this.all_formData.item = allOverviewOptions
        this.actOverItem = this.all_formData.item = 'order_cv'
      }
      if (type === 'his') {
        this.overviewOptions = overviewOptions_HIS
        this.actOverItem = this.all_formData.item = 'order_cv'
      } else if (type === 'shop') {
        this.overviewOptions = overviewOptions_MIL
        this.actOverItem = this.all_formData.item = 'order_cv'
      }
      this.setTooltip(this.overviewOptions[0].label)
      this.getInitData()
    },
    changePayType(type, name) {
      this.pay_type = type
      if (type === 'pay_num') {
        this.pieCols = this.pie1Cols
        this.channelData = this.purchase_num_rates
        this.pay_type_chart_data = this.chart_data_pur_num
      } else if (type === 'pay_amount') {
        this.pieCols = this.pie1Cols_amount
        this.channelData = this.purchase_amount_rates
        this.pay_type_chart_data = this.chart_data_pur_amount
      }
      this.handlePieChart(this.channelData, 'optionsPie_pay', name)
    },
    //改变来源类型
    changeSourceType(type) {
      this.source_type = type
      if (type === 'HIS') {
        this.sourceData = this.sorce_his_list
      } else if (type === 'MIL') {
        this.sourceData = this.sorce_shop_list
      }
      this.handlePieChart(this.sourceData, 'optionsPie_source')
    },
    changeAllTime(times) {
      console.log(times)
      if (times) {
        this.all_formData.st = times[0]
        this.all_formData.et = times[1]
      } else {
        this.all_formData.st = ''
        this.all_formData.et = ''
      }
      this.getCusOverview()
    },
    changeCusTime(times) {
      console.log(times)
      if (times) {
        this.cost_formData.st = times[0]
        this.cost_formData.et = times[1]
      } else {
        this.cost_formData.st = ''
        this.cost_formData.et = ''
      }
      this.getCusAnsis()
    },
    getTrendData() {
      const {item, st, et,clinic_id,biz_type} = this.all_formData
      const params = {item, st, et,clinic_id,biz_type}
      let xData = []
      let yData = []
      this.$api.getItemTrendInfo(params).then(res => {
        console.log(res)
        for (const obj of res.list) {
          xData.push(obj.x)
          yData.push(obj.y)
        }
        this.line_options.xAxisData = xData
        this.line_options.seriesData = yData
      }, error => {
        this.$Message.error(error.errmsg)
      })
    },
    handDataToChartData(data = []) {
      console.log("-> data", data)
      if (!data.length) {
        throw new Error('图标数据不是数组')
        return
      }
      let dataObj = {
        xData: [],
        yData: []
      }
      for (const datum of data) {
        if (datum.title) {
          dataObj.xData.push(datum.title)
        } else {
          dataObj.xData.push(datum.text)
        }
        dataObj.yData.push(datum.num)
      }
      return dataObj
    }
  },
}
</script>

<style scoped lang="less">
.customer-wrapper {
  .g-h {
    padding: 20px 0 16px;
    .company-name{
      min-width: 126px;
      background: rgba(255, 255, 255, 0.7);
      opacity: 0.8;
      border-radius: 2px;
      line-height: 30px;
      text-indent: 12px;
      margin: 0 10px;
    }
  }
  .card {
    margin-top: 16px;
    padding: 20px;
    background: #FFFFFF;
    border-radius: 4px;
  }
  // 整体情况
  .overall-wrapper {
    padding: 20px;

    .card-exhibition-wrapper {
      margin-top: 16px;
      flex-wrap: nowrap;

      .card-exhibition {
        height: 100px;
        border-radius: 4px;
        border: 1px solid #EAEAEA;
        padding: 20px 16px 16px 40px;
        margin-right: 20px;
        font-size: 12px;
        width: 18.83%;
        margin-bottom: 16px;
        cursor: pointer;

        &:nth-of-type(5) {
          margin-right: 0;
        }

        &:not(.exhi-active):hover {
          border: 1px solid rgba(17, 87, 229, 0.12156862745098039);
          box-shadow: 0px 2px 10px rgba(108, 129, 166, 0.12);
        }

        .exhibition-title {
          height: 17px;
          font-weight: 400;
          color: #444444;
          line-height: 17px;
        }

        .exhibition-money {
          margin-top: 12px;
          height: 29px;
          font-size: 24px;
          font-weight: 500;
          color: #000000;
          line-height: 29px;
          font-family: OPPOSans;
        }

        .exhibition-yesterday {
          margin-top: 6px;
          height: 17px;
          font-weight: 400;
          color: #AAAAAA;
          line-height: 17px;
        }
      }

      .exhi-active {
        border: 2px solid #1157E5;

        &:hover {
          box-shadow: 0px 2px 10px rgba(108, 129, 166, 0.12);
        }
      }
    }

    .line-wrapper {
      margin-top: 30px;
    }
  }

  // 客户统计类型
  .echarts-wrapper {

  }
}

.top-three {
  > img {
    width: auto;
    height: 20px;
    margin: 0px;
  }
}
</style>
<style lang="less" scoped>
// common
.h-title {

  font-weight: bolder;
}

.common-bg {
  background: #ffffff;
  border-radius: 4px;
}

.s-title-margin {
  margin-top: 30px;
  margin-bottom: 20px
}

.card {
  padding: 20px;
  background: #FFFFFF;
  border-radius: 4px;
}

.margin-top16 {
  margin-top: 16px;
}

.width50 {
  width: 50%;
}

.circle {
  border-radius: 50%;
  border: 2px solid red;
  width: 10px;
  height: 10px;
  display: inline-block;
  vertical-align: middle;
  margin-right: 6px;
}

.bar-table {
  padding-top: 10px;
  width: 280px;
}
</style>
<style lang="less" scoped>
::v-deep .ivu-table {
  thead {
    th {
      background: #FFFFFF;
      color: #999999;
    }

    td {
      height: 40px;
    }
  }

  &::before {
    height: 0;
  }

  td {
    border-bottom: none;
    height: 40px;
  }

  th {
    border-bottom: none;
    height: 40px;
  }
}

.has-border-t {
  ::v-deep .ivu-table {
    td, th {
      border-bottom: 1px solid #EFEFEF;
    }
  }
}
::v-deep .ivu-select-input{
  height: 30px;
}
::v-deep .ivu-input{
  height: 30px;
}
::v-deep .ivu-select-selection{
  height: 30px;
}
</style>
