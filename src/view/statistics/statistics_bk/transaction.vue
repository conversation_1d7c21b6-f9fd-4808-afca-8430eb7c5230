<template>
  <div class="transaction-wrapper">
    <section class="g-h flex flex-item-between flex-item-center ceiling">

      <div class="g-h-l flex flex-item-align ">
        <!-- <span style="margin-left:20px">省公司：</span>
        <span class="company-name">{{ $store.state.app.companyName }}</span> -->
        <clinic-search v-model="overParams.clinic_id" className="statistics-sel" placeholder="全部诊所" @change="changeClinicId"
                       is-clearable></clinic-search>
        <span class="label" style="margin-left: 12px;">统计时间：</span>
        <DatePicker type="daterange" placeholder="请选择日期区间" style="width: 200px" v-model="transTime" @on-change="changeTrendTime" :clearable="false" :options="timeOptions"></DatePicker>
      </div>
      <div class="g-h-r">
        <Button :type="overParams.biz_type === '' ? 'primary' : 'default'" @click="clickBtnEvent('')" style="margin-right: 8px;">全部</Button>
        <Button :type="overParams.biz_type ==='his' ? 'primary' : 'default'" @click="clickBtnEvent('his')" style="margin-right: 8px;">HIS诊疗</Button>
        <Button :type="overParams.biz_type ==='shop' ? 'primary' : 'default'" @click="clickBtnEvent('shop')">商城购物</Button>
      </div>
    </section>
    <!-- 整体情况 -->
    <div class="card card-overall">
      <h3 class="title">整体情况</h3>
      <div class="card-exhibition-wrapper flex">
        <div v-for="(item, index) in overview" :key="index" class="flex-1 card-exhibition cursor" :class="{'active-color': activeIndex == index }" @click="exhibitionEvent(item.key, index, item.title)">
          <p class="exhibition-title">{{ item.title }}</p>
          <p class="exhibition-money" v-if="item.key === 'paid_cv' || item.key === 'paid_order_num'">{{ item.today }}</p>
          <p class="exhibition-money" v-else>{{ item.today | number_format }}</p>
          <!-- <p class="exhibition-yesterday">昨日：￥{{ item.last || 0 }}</p> -->
        </div>
      </div>
      <!-- echarts line -->
      <div class="line-wrapper">
        <K-Broken-Line :id="Date.parse(new Date()).toString()+'line1'" :options="options" :key="item"></K-Broken-Line>
      </div>
    </div>
    <!-- 营业额排行榜 -->
    <div class="card margin-top20" v-show="normalOptions.yData.length > 1">
      <h3 class="h-title" style="margin-bottom: 16px;">营业额排行榜</h3>
      <!--       <div class="turnover-btn">-->
      <!--         <Button :type="turnover_type === 'COM' ? 'primary' : 'default'" @click="changeTurnoverType('COM')" style="margin-right: 8px;height: 28px">省公司</Button>-->
      <!--         <Button :type="turnover_type ==='CLINIC' ? 'primary' : 'default'" @click="changeTurnoverType('CLINIC')" style="height: 28px;">诊所</Button>-->
      <!--       </div>-->
      <div>
        <k-Normal-Bar :id="Date.parse(new Date()).toString()+'normal1'" :options="normalOptions"></K-Normal-Bar>
      </div>
    </div>
    <div class="margin-top20 flex two-card">
      <!-- 交易转化 -->
      <div class="card margin-right20 card-transform">
        <div class="title">交易漏斗</div>
        <div>
          <K-Funnel-Chart :id="Date.parse(new Date()).toString()+'funnel1'" :options="funnelOptions"></K-Funnel-Chart>
        </div>
      </div>

      <div class="card card-consume">
        <div class="title">消费类型分布</div>
        <div>
          <K-Transverse-Bar :id="Date.parse(new Date()).toString()+'transverse1'" :options="transverseOptions"></K-Transverse-Bar>
        </div>
      </div>
    </div>

    <!-- 交易明细 -->
    <div class="card margin-top20">
      <header class="flex flex-item-between">
        <div class="title">交易明细</div>
<!--        <a>导出交易明细</a>-->
      </header>
      <Table class="margin-top8" :columns="tradeColumns" :data="tradeData" border></Table>
      <div class="page-wrapper">
        <KPage
          :total="total"
          :page-size.sync="queryFormData.pageSize"
          :current.sync="queryFormData.page"
          @on-change="handleCurrentChange"
          @on-page-size-change="handleSizeChange"
        />
      </div>
    </div>

    <!-- 退款 -->
    <!-- <div class="flex refund margin-top20">
      <div class="card margin-right20 refund-statistics">
        <div class="title">退款统计</div>
        <div>
          <K-Annular-Pie :id="Date.parse(new Date()).toString()+'annular1'"></K-Annular-Pie>
        </div>
        <div class="bar-table">
          <Table :columns="pie1Cols" :data="channelData">
            <template slot-scope="{row,index}" slot="rank">
              <div class="circle"></div>
            </template>
            <template slot-scope="{row}" slot="content">
              {{ row.name }}
            </template>
            <template slot-scope="{row}" slot="num">
              {{ row.num }}
            </template>
          </Table>
        </div>
      </div>

      <div class="card refund-details">
        <div class="title">退款明细</div>
        <Table class="margin-top8" :columns="columns1" :data="data1" border></Table>
        <div class="page-wrapper">
          <K-Page></K-Page>
        </div>

      </div>
    </div> -->
  </div>
</template>

<script>
import KBrokenLine from '_c/k-echarts/k-broken-line';
import KFunnelChart from '_c/k-echarts/k-funnel-chart';
import KAnnularPie from '_c/k-echarts/k-annular-pie';
import KTransverseBar from '_c/k-echarts/k-transverse-bar';
import KPage from '_c/k-page/index';
import clinicSearch from '_c/clinic-search/k-clinic-search_data'
import KNormalBar from '_c/k-echarts/k-normal-bar.vue';

const init_query_form_data = {
  page: 1,
  pageSize: 20,
}
export default {
  name: "transaction",
  components: {KBrokenLine,KFunnelChart,KAnnularPie,KPage,KTransverseBar,clinicSearch,KNormalBar},
  data () {
    return {
      overviewArr:['营业额（元)','支付订单数','支付人数','客单价（元）','成功退款金额（元）'],
      overview: [],
      overParams: {
        biz_type: '',
        st: '',
        et: '',
        clinic_id: ''
      },
      transTime: [],
      timeOptions: {
        disabledDate(date) {
          return date && date.valueOf() > Date.now() - 86400000
        }
      },
      item: '',
      activeIndex: 0,
      queryFormData: {...init_query_form_data},
      total: 0, // 表格总条数
      transverseOptions: {
        seriesData: {
          order_num: 0, // 总订单
          his_pres_num: 0, // his订单
          shop_order_num: 0, // 商城订单
        }
      },
      // 表格的mock数据
      tradeColumns: [
        {
          title: '日期',
          key: 'date',
          align: 'center'
        },
        {
          title: '订单数',
          key: 'order_num',
          align: 'center'

        },
        {
          title: '支付订单数',
          key: 'paid_order_num',
          align: 'center'
        },
        {
          title: '营业额',
          key: 'turnover',
          align: 'center'
        },
        {
          title: '客单价',
          key: 'price_per_customer',
          align: 'center'
        }
      ],
      tradeData: [], // 交易明细表格数据
      // 曲线options
      options: {
        xAxisData: [],
        seriesData: [],
        tooltip: {title: '成功退款金额（元)'},
        color: {lineColor: '#1157E5', areaColor: '#1157E5'},
      },
      pie1Cols: [
        {title: '类型', slot: 'rank', className: 'rank', tooltip: true},
        {title: '次数', slot: 'content', textAlign: 'left',},
        {title: '金额', slot: 'num', textAlign: 'right', width: 80},
        {title: '占比', slot: 'num', textAlign: 'right', width: 80},
      ],
      channelData: [{rank: 1}],
      // 漏斗options
      funnelOptions: {
        seriesData: {
          order_num: 0, //访客量
          paid_order_num: 0, //下单人数
          refund_order_num: 0, //支付人数
        }
      },
      normalOptions: {
        xData:[],
        yData: [],
        xLabel: '单位：元'
      },
    }
  },
  mounted () {
    this.init()
  },
  methods: {
    // 设置面积图tooltil的title
    setTooltip (title) {
      this.options.tooltip.title = title
    },
    //营业排行
    getTurnover(){
      this.$api.getTurnoverRank(this.overParams).then(res=>{
        console.log("-> res", res)
        this.normalOptions.xData = []
        this.normalOptions.yData = []
        for (const item of res.clinic_rank) {
          console.log(item)
          this.normalOptions.xData.push(item.value)
          this.normalOptions.yData.push(item.name)
        }
        this.normalOptions.xData = this.normalOptions.xData.reverse()
        this.normalOptions.yData = this.normalOptions.yData.reverse()
      })
    },
    changeClinicId(){
      this.init()
    },
    // 初始化数据
    async init () {
      await this.getTradeOverview()
      await this.getTradeTradedetails()
      await this.getTurnover()
    },
    hendleObj (obj) {
      if ( typeof obj !== 'object' )  return
      const { overviewArr } = this
      let arr = []
      let keysArr = Object.keys(obj)
      keysArr.map( (item, index) => {
        if (index === 0) {
          // 首次获取趋势图
          this.getItemTrendInfo(item)
          this.item= item
        }
        arr.push({key: item, today: obj[item],title: overviewArr[index] })
      } )
      return arr
    },
    // 切换类型
    clickBtnEvent(type) {
      this.overParams.biz_type = type
      // 默认选中第一个
      this.activeIndex = 0
      this.init()
    },
    // 时间区间
    changeTrendTime(times) {
      if (times) {
        this.overParams.st = times[0]
        this.overParams.et = times[1]
      } else {
        this.overParams.st = ''
        this.overParams.et = ''
      }
      this.init()
    },
    // 点击整体情况卡片事件
    exhibitionEvent (key, index, title) {
      this.getItemTrendInfo(key)
      this.item = key
      this.activeIndex = index
      this.setTooltip(title)
    },
    // 表格分页
    handleCurrentChange(page) {
      console.log("-> page, pageSize", page, pageSize)
      this.queryFormData.page = page
      this.getTradeTradedetails()
    },
    handleSizeChange(val) {
      console.log('-> val', val)
      this.queryFormData.pageSize = val - 0
      this.queryFormData.page = 1
      this.getTradeTradedetails()
    },
    // *api获取整体情况数据
    getTradeOverview () {
      const { overParams } = this
      this.$api.getTradeOverview(overParams).then( res => {
        this.overview = this.hendleObj(res.overview)
        this.transTime = [res.st,res.et]
        this.overParams.st = res.st
        this.overParams.et = res.et
        this.setTooltip(this.overview[0].title)
        this.getTradeTradedetails()
      } )
    },
    // *api获取趋势图数据
    getItemTrendInfo (item) {
      this.options.xAxisData = []
      this.options.seriesData = []
      const { st, et,biz_type,clinic_id } = this.overParams
      let params = {
        item,
        st,
        et,
        biz_type,clinic_id
      }
      this.$api.getItemTrendInfo(params).then( res => {
        res.list.forEach( item => {
          this.options.xAxisData.push(item.x)
          this.options.seriesData.push(item.y)
        } )
      } )
    },
    // 处理消费类型数据
    handlerTranserver(options) {
      this.transverseOptions.seriesData.order_num = options.order_num
      this.transverseOptions.seriesData.his_pres_num = options.his_pres_num
      this.transverseOptions.seriesData.shop_order_num = options.shop_order_num
    },
    // 处理交易转化类型数据
    handlerFunnel(options) {
      this.funnelOptions.seriesData.order_num = options.order_num
      this.funnelOptions.seriesData.paid_order_num = options.paid_order_num
      this.funnelOptions.seriesData.refund_order_num = options.refund_order_num
    },
    // *api 交易相关信息
    getTradeTradedetails () {

      this.$api.getTradeTradedetails({...this.overParams,...this.queryFormData}).then( res => {
        // 消费类型数据
        this.handlerTranserver( res.purchase_analysis)
        this.tradeData = res.details.list
        // 表格数据
        this.total = Number(res.details.total)
        // 交易转化漏斗数据
        this.handlerFunnel(res.trade_analysis)
      } )
    }
  }
}
</script>

<style lang="less" scoped>
.transaction-wrapper {
  .g-h{
    padding: 20px 0 16px;
  }
  .company-name {
    min-width: 126px;
    background: rgba(255, 255, 255, 0.7);
    opacity: 0.8;
    border-radius: 2px;
    line-height: 30px;
    text-indent: 12px;
    margin: 0 8px;
  }
  // 整体情况
  .card-overall {
    .card-exhibition-wrapper {
      margin-top: 16px;
      .card-exhibition {
        height: 100px;
        border-radius: 4px;
        border: 1px solid #EAEAEA;
        padding: 20px 16px 16px 40px;
        margin-right: 20px;
        font-size: 12px;
        &:nth-last-of-type(1) {
          margin-right: 0;
        }

        &:hover {
          border: 1px solid rgba(17, 87, 229, 0.12156862745098039);
          box-shadow: 0px 2px 10px rgba(108, 129, 166, 0.12);
        }

        .exhibition-title {
          height: 17px;
          font-weight: 400;
          color: #444444;
          line-height: 17px;
        }
        .exhibition-money {
          margin-top: 12px;
          height: 29px;
          font-size: 24px;
          font-weight: 500;
          color: #000000;
          line-height: 29px;
          font-family: OPPOSans;
        }
        .exhibition-yesterday {
          margin-top: 6px;
          height: 17px;
          font-weight: 400;
          color: #AAAAAA;
          line-height: 17px;
        }
      }
    }

    .line-wrapper {
      margin-top: 46px;
    }
  }

  .two-card {
    width: 100%;
    .card-transform {
      flex: 1;
    }
    .card-consume {
      flex: 1.2;
    }
  }

  // 退款
  .refund {
    height: auto;
    width: 100%;
    // 退款统计
    .refund-statistics {
      min-width: 200px;
      // flex: 1;
      width: 25%;
    }

    // 退款明细
    .refund-details {
      // flex: 3;
      width: 75%;
    }
  }
}
</style>
<style lang="less" scoped>
// common scss
.title {
  height: 25px;
  font-size: 14px;
  font-weight: 600;
  color: #000000;
  line-height: 25px;
}
.card {
  padding: 20px;
  background: #FFFFFF;
  border-radius: 4px;
}
.page-wrapper {
  margin-top: 20px;
  text-align: right;
}
.margin-top8 {
  margin-top: 8px;
}
.margin-top20 {
  margin-top: 16px;
}
.margin-right20 {
  margin-right: 20px;
}
.circle {
  border-radius: 50%;
  border: 2px solid red;
  width: 10px;
  height: 10px;
}
.active-color {
  border: 2px solid #1157E5 !important;
}
.cursor {
  cursor: pointer;
}
</style>

<style lang="less" scoped>
.bar-table {
  ::v-deep .ivu-table {
    thead {
      th {
        background: #FFFFFF;
        color: #999999;
      }

      td {
        height: 40px;
      }
    }

    &::before {
      height: 0;
    }

    td {
      border-bottom: none;
      height: 40px;
    }

    th {
      border-bottom: none;
      height: 40px;
    }
  }
}
</style>
