<template>
  <div class="test-wrapper">
    采购分析
    <K-Pie-Chart :id="Date.parse(new Date()).toString()+'pie1'"></K-Pie-Chart>
    <K-Funnel-Chart :id="Date.parse(new Date()).toString()+'funnel1'"></K-Funnel-Chart>
    <K-Annular-Pie :id="Date.parse(new Date()).toString()+'annular1'"></K-Annular-Pie>
    <K-Broken-Line :id="Date.parse(new Date()).toString()+'line1'"></K-Broken-Line>
    <K-Vertical-Bar :id="Date.parse(new Date()).toString()+'vertical1'"></K-Vertical-Bar>
    <K-Transverse-Bar :id="Date.parse(new Date()).toString()+'transverse1'"></K-Transverse-Bar>
  </div>
</template>

<script>
import KPieChart from '_c/k-echarts/k-pie-chart'
import KFunnelChart from '_c/k-echarts/k-funnel-chart'
import KAnnularPie from '_c/k-echarts/k-annular-pie'
import KBrokenLine from '_c/k-echarts/k-broken-line'
import KVerticalBar from '_c/k-echarts/k-vertical-bar'
import KTransverseBar from '_c/k-echarts/k-transverse-bar'


export default {
  name: "test",
  components: {KPieChart,KFunnelChart,KAnnularPie,KBrokenLine,KVerticalBar,KTransverseBar},
  data () {
    return {

    }
  },
}
</script>

<style scoped lang="less">

</style>
