<template>
  <div class="wrapper">
    <!-- Header -->
    <header class="head-block ceiling flex flex-item-between">
      <div class="flex flex-item-align">
        <p>统计时间：</p>
        <customDatePicker
          v-model="formDate"
          @getDateType="getDateType"
        ></customDatePicker>
      </div>
      <clinic-search
        v-model="clinic_id"
        className="statistics-sel"
        placeholder="全部诊所"
        @change="changeClinicId"
        is-clearable
        :extra-params="{ scope: 'department' }"
      ></clinic-search>
    </header>

    <!-- 整体情况 -->
    <div class="block">
      <h4 class="block-title flex flex-align-center">
        <p>整体情况</p>
        <Tooltip
          max-width="300"
          theme="light"
          placement="bottom"
          :offset="100"
          class="custom-tooltip tooltip-left-arrow ml6"
        >
          <div slot="content">
            <p
              v-for="(item, index) in transaction_tooltip_list"
              :key="index + 'tooltip'"
            >
              {{ item }}
            </p>
          </div>
          <p class="flex flex-item-center cursor">
            <svg-icon iconClass="tip" class="helpIcon cursor"></svg-icon>
            <!-- <span class="cursor tip-text"></span> -->
          </p>
        </Tooltip>
      </h4>

      <!-- 卡片数据 -->
      <div class="mt16 flex flex-warp">
        <div
          class="card-block cursor mb16 card-block--hover"
          v-for="(card_item, card_index) in transaction_card_options"
          :key="'card' + card_index"
          :class="{ 'card-block--actived': current_card_index === card_index }"
          @click.prevent="cardChange(card_item, card_index)"
        >
          <p class="card-title flex flex-item-between pr20">
            <span>{{ card_item.label }}</span>
            <svg-icon
              v-if="!clinic_id"
              @click.stop="arrowEvent(card_item, card_index)"
              iconClass="circleArrow"
              class="circle-arrow"
              :class="{
                'circle-arrow--actived': current_card_index === card_index,
              }"
            ></svg-icon>
          </p>
          <p class="card-content mt12" v-if="card_item.isMoney">
            ￥{{ card_item.current | number_format(2) }}
          </p>
          <p class="card-content mt12" v-else>{{ card_item.current || 0 }}</p>
          <div v-show="date_type">
            <p class="card-tip mt6" v-if="card_item.isMoney">
              {{ custom_lastText }}：￥{{ card_item.last | number_format(2) }}
            </p>
            <p class="card-tip mt6" v-else>
              {{ custom_lastText }}：{{ card_item.last || 0 }}
            </p>
          </div>
        </div>
      </div>

      <!-- 折线趋势图 -->
      <div class="mt30">
        <chart-view height="260px" :chart-option="line_options"></chart-view>
      </div>
    </div>

    <!-- 交易转化 和 消费场景分析 -->
    <div class="flex mt16">
      <!-- 交易转化 -->
      <div class="block flex-1 mr16">
        <p class="block-title">交易转化</p>
        <div>
          <chart-view ref="funnel" :chart-option="funnelOptions"></chart-view>
        </div>
      </div>

      <!-- 消费场景分析 -->
      <div class="block flex-1">
        <p class="block-title">消费场景分析</p>
        <div>
          <chart-view
            :chart-option="transverseOptions"
            v-if="isShowLineEcharts(transverseOptions, 'bar')"
          ></chart-view>
          <div class="empty" v-else>暂无数据</div>
        </div>
      </div>
    </div>

    <!-- 销售数据 -->
    <div class="block mt16">
      <div class="block-title">销售数据</div>
      <div class="flex">
        <div class="flex-1 pr30 mt30">
          <Table
            class="custom-table-line"
            border
            :span-method="handleSpan"
            :columns="saleCols"
            :data="sale_lists"
            v-if="sale_lists.length"
          >
            <template slot-scope="{ row, index }" slot="title">
              <!--              <i class="circle" :style="{borderColor:color_table_enum[index]}"></i>-->
              <span>{{ row.title }}</span>
            </template>
            <template slot-scope="{ row }" slot="turnover">
              ￥{{ row.turnover }}
            </template>
            <template slot-scope="{ row }" slot="inner_turnover">
              ￥{{ row.inner_turnover }}
            </template>
            <template slot-scope="{ row }" slot="ratio">
              {{ row.ratio || 0 }}%
            </template>
          </Table>
          <div class="empty" v-else>暂无数据</div>
        </div>

        <div class="flex-1 flex flex-item-align">
          <div class="width100">
            <chart-view
              height="260px"
              :chart-option="sale_bar_options"
            ></chart-view>
          </div>
        </div>
      </div>
    </div>

    <!-- 理疗消耗数据 -->
    <div class="block mt16">
      <div class="block-title">理疗消耗数据</div>
      <div class="flex">
        <div class="flex-1 pr30 mt30">
          <Table
            class="custom-table-line"
            :columns="consumeCols"
            :data="consume_lists"
            v-if="consume_lists.length"
          >
            <template slot-scope="{ row, index }" slot="title">
              <!--              <i class="circle" :style="{borderColor:color_table_enum[index]}" v-if="index !== (consume_lists.length - 1) && index !== (consume_lists.length - 2)"></i>-->
              <span>{{ row.title || row.type_text }}</span>
            </template>
            <template slot-scope="{ row }" slot="content">
              {{ row.name }}
            </template>
            <template slot-scope="{ row }" slot="amount">
              ￥{{ row.money }}
            </template>
            <template slot-scope="{ row, index }" slot="percent">
              <span
                v-if="
                  consume_lists.length != index + 1 &&
                  consume_lists.length - 1 != index + 1
                "
                >{{ row.percent || 0 }} %</span
              >
              <span v-else>-</span>
            </template>
          </Table>
          <div class="empty" v-else>暂无数据</div>
        </div>

        <div class="flex-1 flex flex-item-center">
          <div class="width100">
            <chart-view
              height="260px"
              :chart-option="consume_bar_options"
            ></chart-view>
          </div>
        </div>
      </div>
    </div>

    <!-- 账户收款明细 -->
    <!-- <div class="block mt16">
      <div class="block-title">账户收款明细</div>
      <div class="flex">
        <div class="flex-1 pr30 mt30">
          <Table class="custom-table-line" :columns="collectionCols" :data="collection_lists" v-if="collection_lists.length">
            <template slot-scope="{row, index}" slot="text">
              <div class="flex flex-item-center flex-item-align">
                <svg-icon iconClass="weixin" class="svg-block20 ml10" v-if="row.text == '微信支付'"></svg-icon>
                <svg-icon iconClass="money" class="svg-block20 ml10" v-if="row.text == '现金支付'"></svg-icon>
                <span class="ml10">{{ row.text }}</span>
              </div>
            </template>

            <template slot-scope="{row, index}" slot="order_money">
              <span v-if="row.order_money" >￥{{ row.order_money }}</span>
              <span v-else>-</span>
            </template>
          </Table>
          <div class="empty" v-else >
            暂无数据
          </div>
        </div>

        <div class="flex-1 flex flex-item-center">
          <div class="width100">
            <chart-view height="240px" :chart-option="collection_bar_options"></chart-view>
          </div>
        </div>
      </div>
    </div> -->

    <!-- 交易统计 -->
    <div class="block mt16">
      <div class="flex flex-item-between">
        <p class="block-title">交易统计</p>
        <a @click="exportTran">导出交易统计</a>
      </div>
      <Table
        class="mt9"
        :columns="tranCols"
        :data="tran_list"
        :loading="tranTabLoading"
      >
        <!-- 日期 -->
        <template slot-scope="{ row, index }" slot="date">
          <p>{{ row.date | date_format("YYYY-MM-DD") }}</p>
        </template>

        <!-- 全部订单数 -->
        <template slot-scope="{ row, index }" slot="order_num">
          <p>{{ row.order_num.total || 0 }}</p>
        </template>

        <!-- 下单订单金额 -->
        <template slot-scope="{ row, index }" slot="order_total_amount">
          <p v-if="row.order_total_amount.total">
            ￥{{ row.order_total_amount.total }}
          </p>
          <p v-else>-</p>
        </template>

        <!-- 支付订单数 -->
        <template slot-scope="{ row, index }" slot="paid_order_num">
          <p>{{ row.paid_order_num.total || 0 }}</p>
        </template>

        <!-- 应收金额 -->
        <template slot-scope="{ row, index }" slot="receivable_fee">
          <p v-if="row.receivable_fee.total">
            ￥{{ row.receivable_fee.total }}
          </p>
          <p v-else>-</p>
        </template>

        <!-- 优惠金额 -->
        <template slot-scope="{ row, index }" slot="discount_fee">
          <p v-if="row.discount_fee.total">￥{{ row.discount_fee.total }}</p>
          <p v-else>-</p>
        </template>

        <!-- 实收金额 -->
        <template slot-scope="{ row, index }" slot="order_money">
          <p v-if="row.order_money.total">￥{{ row.order_money.total }}</p>
          <p v-else>-</p>
        </template>

        <!-- 退款金额 -->
        <template slot-scope="{ row, index }" slot="refunded_fee">
          <p v-if="row.refunded_fee">￥{{ row.refunded_fee }}</p>
          <p v-else>-</p>
        </template>

        <!-- 成功退款金额 -->
        <template slot-scope="{ row, index }" slot="refunded_finish_amount">
          <p v-if="row.refunded_finish_amount.total">
            ￥{{ row.refunded_finish_amount.total }}
          </p>
          <p v-else>-</p>
        </template>

        <!-- 现金/微信退款金额 -->
        <template slot-scope="{ row, index }" slot="refund_cash_amount">
          <p v-if="row.turnover_amount.total">
            ￥{{ row.refunded_money.cash_amount }}
          </p>
          <p v-else>-</p>
        </template>
        <!-- 储值支付退款金额 -->
        <template slot-scope="{ row, index }" slot="refund_recharge_amount">
          <p v-if="row.turnover_amount.total">
            ￥{{ row.refunded_money.recharge_amount }}
          </p>
          <p v-else>-</p>
        </template>
        <!-- 实际消费金额 -->
        <template slot-scope="{ row, index }" slot="turnover_total">
          <p v-if="row.turnover_amount.total">
            ￥{{ row.turnover_amount.total }}
          </p>
          <p v-else>-</p>
        </template>
        <!-- 储值消耗金额 -->
        <template slot-scope="{ row, index }" slot="consume_recharge_amount">
          <p v-if="row.turnover_amount.total">
            ￥{{ row.order_money.recharge_amount }}
          </p>
          <p v-else>-</p>
        </template>
        <!-- 消费营收金额 -->
        <template slot-scope="{ row, index }" slot="consume_turnover_amount">
          <p v-if="row.consume_turnover_amount.total">
            ￥{{ row.consume_turnover_amount.total }}
          </p>
          <p v-else>-</p>
        </template>

        <!-- 储值营收金额 -->
        <template slot-scope="{ row, index }" slot="recharge_info">
          <p v-if="row.recharge_info.revenue">
            ￥{{ row.recharge_info.revenue }}
          </p>
          <p v-else>-</p>
        </template>

        <!-- 总营收金额 -->
        <template slot-scope="{ row, index }" slot="gross_turnover">
          <p v-if="row.gross_turnover">￥{{ row.gross_turnover }}</p>
          <p v-else>-</p>
        </template>
      </Table>
      <div class="page-wrapper">
        <KPage
          :total="tran_total"
          :page-size.sync="tran_queryFormData.pageSize"
          :current.sync="tran_queryFormData.page"
          @on-change="tranOnPageChange"
          @on-page-size-change="handleSizeChange"
        />
      </div>
    </div>

    <!-- 订单明细和退款明细 -->
    <!--     <div class="block mt16">-->
    <!--   <div class="flex flex-item-between">
        <div class="flex custom-tabs-small">
          <p class="cursor custom-tabs-small&#45;&#45;default" @click="shopTabChange('order')" :class="{'custom-tabs-small&#45;&#45;actived': shopCurrent == 'order'}">订单明细</p>
          <p class="cursor ml20 custom-tabs-small&#45;&#45;default" @click="shopTabChange('refund')" :class="{'custom-tabs-small&#45;&#45;actived': shopCurrent == 'refund'}">退款明细</p>
        </div>
        <div>
          <a v-if="shopCurrent == 'order'">导出交易明细</a>
          <a v-if="shopCurrent == 'refund'">导出退款明细</a>
        </div>
      </div>
      <Table v-if="shopCurrent == 'order'" class="mt9" :columns="orderCols" :data="order_list" :loading="shopCurrenTabLoading">

      </Table>
      <Table v-if="shopCurrent == 'refund'" class="mt9" :columns="refundCols" :data="refund_list" :loading="shopCurrenTabLoading">
        <template slot-scope="{row, index}" slot="refunded_fee">
          <p v-if="row.refunded_fee" >￥{{ row.refunded_fee }}</p>
          <p v-else>-</p>
        </template>-->

    <!--        <template slot-scope="{row, index}" slot="refunded_time">-->
    <!--          <p>{{ row.refunded_time | date_format('YYYY-MM-DD') }}</p>-->
    <!--        </template>-->
    <!--      </Table>-->
    <!--      <div class="page-wrapper">-->
    <!--        <KPage-->
    <!--          :total="total"-->
    <!--          :page-size.sync="queryFormData.pageSize"-->
    <!--          :current.sync="queryFormData.page"-->
    <!--          @on-change="onPageChange"-->
    <!--        />-->
    <!--      </div>-->
    <!--    </div>-->
    <OverallDetailModal
      :propDateType="propDateType"
      :propTime="formDate"
      :overallSituations="transaction_card_options"
      :visible.sync="overAllVisible"
      :current-field="currentField"
      :field-title="fieldTitle"
    ></OverallDetailModal>
  </div>
</template>

<script>
import { transaction_card_options } from "./data/transaction_data";
import { transaction_tooltip_list } from "./data/transaction_data";
import { color_enum } from "@/view/statistics/data/color";
import dateChange from "./mixins/dateChange";
import download from "@/mixins/downloadExcel";
import OverallDetailModal from "./components/OverallDetailModal/OverallDetailModal";
const init_query_form_data = {
  page: 1,
  pageSize: 10,
};
export default {
  name: "transaction",
  components: {
    OverallDetailModal,
  },
  mixins: [download, dateChange],
  props: {},
  data() {
    return {
      downloadApiName: "",
      downloadLoading: false,

      transaction_tooltip_list: transaction_tooltip_list, // 整体情况字段说明
      color_table_enum: color_enum, //色值表

      // 整体情况
      transaction_card_options: transaction_card_options, // 卡片数据枚举
      current_card_index: 0, // 当前选中的卡片索引
      line_options: {}, // 卡片下方的趋势图
      /* 交易转化 */
      funnelOptions: {}, // 漏斗配置数据

      /* 消费场景分析 */
      transverseOptions: {},

      /* 销售数据 */
      saleCols: [
        { title: "销售来源", slot: "title", align: "center", minWidth: 80 },
        {
          title: "总金额(元)",
          slot: "turnover",
          align: "center",
          minWidth: 80,
        },
        { title: "类型", key: "type_text", align: "center", minWidth: 60 },
        { title: "订单数", key: "num", align: "center", minWidth: 60 },
        {
          title: "金额(元)",
          slot: "inner_turnover",
          align: "center",
          minWidth: 60,
        },
        { title: "金额占比", slot: "ratio", align: "center", minWidth: 60 },
      ],
      sale_lists: [],
      sale_bar_options: {}, // 销售数据饼图options

      /* 理疗消耗数据 */
      consumeCols: [
        { title: "消耗类型", slot: "title", align: "center", minWidth: 200 },
        { title: "次数", key: "quantity", align: "center", width: 80 },
        { title: "金额(元)", slot: "amount", align: "center", width: 100 },
        { title: "金额占比", slot: "percent", align: "center", width: 100 },
      ],
      consume_lists: [],
      consume_bar_options: {}, // 消耗数据饼图options

      /* 账户收款明细 */
      // collectionCols: [
      //   {title: '收款类型', slot: 'text', align: 'center', minWidth: 200, tooltip: true},
      //   {title: '金额(元)', slot: 'order_money', align: 'center', width: 80},
      // ],
      // collection_lists: [],
      // collection_bar_options: {}, // 账户收款明细饼图options

      /* 交易统计 */
      tranCols: [
        { title: "日期", key: "date_format", align: "center" },
        { title: "全部订单数", slot: "order_num", align: "center" },
        { title: "下单订单金额", slot: "order_total_amount", align: "center" },
        { title: "支付订单数", slot: "paid_order_num", align: "center" },
        { title: "应收金额", slot: "receivable_fee", align: "center" },
        { title: "优惠金额", slot: "discount_fee", align: "center" },
        { title: "实收金额", slot: "order_money", align: "center" },
        {
          title: "成功退款金额",
          slot: "refunded_finish_amount",
          align: "center",
        },
        {
          title: "现金/微信退款金额",
          slot: "refund_cash_amount",
          align: "center",
        },
        {
          title: "储值支付退款金额",
          slot: "refund_recharge_amount",
          align: "center",
        },
        { title: "实际消费金额", slot: "turnover_total", align: "center" },
        {
          title: "储值消耗金额",
          slot: "consume_recharge_amount",
          align: "center",
        },
        {
          title: "消费营收金额",
          slot: "consume_turnover_amount",
          align: "center",
        },
        { title: "储值营收金额", slot: "recharge_info", align: "center" },
        { title: "总营收金额", slot: "gross_turnover", align: "center" },
      ],
      tran_list: [], // 商品交易
      tran_queryFormData: { ...init_query_form_data },
      tran_total: 0, // 表格总条数
      tranTabLoading: false,

      /* 交易明细和退款明细 */
      shopCurrent: "order",
      orderCols: [
        { title: "订单编号", key: "out_trade_no", align: "center" },
        { title: "诊所名称", key: "clinic_name", align: "center" },
        { title: "用户姓名", key: "name", align: "center" },
        { title: "用户昵称", key: "nickname", align: "center" },
        { title: "用户手机号", key: "mobile", align: "center" },
        { title: "订单类型", key: "from_type_text", align: "center" },
        { title: "订单商品明细", key: "order_detail_text", align: "center" },
        { title: "订单总价", key: "total_fee", align: "center" },
        { title: "优惠金额", key: "discount_fee", align: "center" },
        { title: "订单实收", key: "real_payment_fee", align: "center" },
        { title: "状态", key: "status_text", align: "center" },
        { title: "下单时间", key: "create_time", align: "center" },
        { title: "支付时间", key: "pay_time", align: "center" },
      ],
      order_list: [], // 商品交易

      refundCols: [
        { title: "订单编号", key: "out_trade_no", align: "center" },
        { title: "退款编号", key: "date", align: "center" },
        { title: "诊所名称", key: "clinic_name", align: "center" },
        { title: "用户姓名", key: "name", align: "center" },
        { title: "用户昵称", key: "nickname", align: "center" },
        { title: "用户手机号", key: "mobile", align: "center" },
        { title: "订单类型", key: "date", align: "center" },
        { title: "退款商品明细", key: "date", align: "center" },
        { title: "已退金额", slot: "refunded_fee", align: "center" },
        { title: "退款时间", slot: "refunded_time", align: "center" },
      ],
      refund_list: [], // 商品退款

      queryFormData: { ...init_query_form_data },
      total: 0, // 表格总条数
      shopCurrenTabLoading: false,
      overAllVisible: false,
      currentField: "",
      fieldTitle: "",
      alloverIsMoney: true,
    };
  },
  computed: {},
  watch: {},
  created() {},
  mounted() {},
  methods: {
    // 当时间发生变化，进行得操作
    dateChange() {
      (this.tran_queryFormData = { ...init_query_form_data }),
        // 趋势图
        this.getTrend(this.transaction_card_options, this.current_card_index);
      // 整体情况
      this.getTradeOverview();
      // 交易相关信息
      this.getTradeTradedetails();
      // 交易统计
      this.getTradeTradesummary();
    },

    /* 卡片切换事件 */
    cardChange(card_item, card_index) {
      if (card_index == this.current_card_index) return;

      this.current_card_index = card_index;
      this.getTrend(this.transaction_card_options, this.current_card_index);
    },

    // 导出交易明细
    exportTran() {
      this.downloadApiName = "tradeExporttradesummary";
      let params = {
        st: this.formDate[0],
        et: this.formDate[1],
        clinic_id: this.clinic_id,
      };
      this.downloadExcel(params);
    },

    // 订单明细和退款明细tab切换
    shopTabChange(val) {
      this.shopCurrent = val;
      this.queryFormData = { ...init_query_form_data };
      if (val === "order") {
        this.getTradeOrders();
        this.refund_list = [];
      }
      if (val === "refund") {
        this.getTradeRefundorders();
        this.order_list = [];
      }
    },

    // 表格分页
    // 交易统计分页事件
    handleSizeChange(pageSize) {
      this.tran_queryFormData.pageSize = pageSize;
      if (this.tran_queryFormData.page == 1) {
        this.getTradeTradesummary();
      }
    },
    tranOnPageChange(page) {
      this.tran_queryFormData.page = page;
      this.getTradeTradesummary();
    },
    // 订单明细退款明细分页事件
    onPageChange(page, pageSize) {
      this.queryFormData.page = page;
      this.queryFormData.pageSize = pageSize;

      if (this.shopCurrent === "order") {
        this.getTradeOrders();
      }
      if (this.shopCurrent === "refund") {
        this.getTradeRefundorders();
      }
    },

    /* API */
    // 获取整体情况
    getTradeOverview() {
      let params = {
        st: this.formDate[0],
        et: this.formDate[1],
        date_type: this.date_type || "range",
        clinic_id: this.clinic_id,
      };
      this.$api
        .getTradeOverview(params)
        .then(res => {
          this.transaction_card_options.forEach(item => {
            item.current =
              res.overview[item.value] && res.overview[item.value].current;
            item.last =
              res.overview[item.value] && res.overview[item.value].before;
          });
        })
        .catch(error => this.$Message.error(error.errmsg));
    },

    // api - 交易相关信息
    getTradeTradedetails() {
      let params = {
        st: this.formDate[0],
        et: this.formDate[1],
        clinic_id: this.clinic_id,
        date_type: this.date_type || "range",
      };
      this.$api.getTradeTradedetails(params).then(res => {
        // 交易转化
        this.handlerFunnel(res.customer_analysis);
        // 消费场景分析
        this.handleVertical(res.consumption_scene);
        // 获取销售数据
        this.sale_lists = this.handleSaleLists(res.goods_sales_distribution);
        this.handleSaleOptions(res.goods_sales_list);

        // 理疗消耗数据
        this.handlerConsume(res.consume_data_analysis);

        // 账户收款明细
        // this.handleCollection(res.pay_type)
      });
    },
    // 处理交易转化类型数据
    handlerFunnel(options) {
      let seriesData = [
        { name: "到店客户数：", value: options.arrival_cv },
        { name: "下单客户数：", value: options.order_cv },
        { name: "消费客户数：", value: options.paid_cv },
      ];
      this.funnelOptions = this.$eChartFn.funnerlOptions({
        series: [{ data: seriesData }],
      });
    },
    // 处理消费场景分析横向柱状图
    handleVertical(options) {
      this.transverseOptions = this.$eChartFn.customHorizontaBar({
        formatter: a => {
          let text = `${a.name}   金额占比 ${a.value}%`;
          return text;
        },
        yAxis: {
          data: [
            `小程序自主下单   ${options.weapp.num}单   ￥${options.weapp.order_money}`,
            `HIS就诊支付   ${options.his.num}单   ￥${options.his.order_money}`,
            `后台创建订单(到店支付)   ${options.clinic_pc.num}单  ￥${options.clinic_pc.order_money}`,
          ],
        },
        series: [
          {
            data: [
              { value: `${options.weapp.rate}` },
              { value: `${options.his.rate}` },
              { value: `${options.clinic_pc.rate}` },
            ],
          },
        ],
      });
    },

    // 处理商品销售额占比分析饼图数据
    handleSaleOptions(list) {
      let resultList = [];
      list &&
        list.forEach(item => {
          resultList.push({
            ...item,
            value: item.money,
            tootipList: [
              { key: "销售来源:", value: item.title },
              { key: "订单数", value: `${item.num}` },
              { key: "金额", value: `￥${item.money}` },
              { key: "金额占比", value: `${item.percent}%` },
            ],
          });
        });
      this.setPieConfigure("sale_bar_options", resultList, "销售数据");
    },

    // 处理商品销售合并单元格的表格数据
    handleSaleLists(list) {
      let resultList = [];
      list &&
        list.forEach(first_item => {
          first_item.list.forEach((inner_item, inner_index) => {
            resultList.push({
              title: first_item.title,
              turnover: first_item.money,
              type_text: inner_item.title,
              num: inner_item.num,
              inner_turnover: inner_item.money,
              ratio: inner_item.ratio,
              colsLength:
                inner_index == 0
                  ? first_item.list && first_item.list.length
                  : 0,
            });
          });
        });
      return resultList;
    },

    // 表格合并单元格
    handleSpan({ row, column, rowIndex, columnIndex }) {
      if (columnIndex === 0) {
        // 计算合并的行数列数
        let x = row.colsLength === 0 ? 0 : row.colsLength;
        let y = row.colsLength === 0 ? 0 : 1;
        return [x, y];
      }

      if (columnIndex === 1) {
        // 计算合并的行数列数
        let x = row.colsLength === 0 ? 0 : row.colsLength;
        let y = row.colsLength === 0 ? 0 : 1;
        return [x, y];
      }
    },

    // 理疗消耗数据
    handlerConsume(data) {
      let cloneData = this.$lodash.cloneDeep(data);
      this.consume_lists = data.list || [];
      this.consume_lists.push({
        title: "合计",
        quantity: data.total.quantity,
        money: data.total.money,
        percent: data.total.percent,
      });
      this.consume_lists.push({
        title: "已过期",
        quantity: data.expired_total.quantity,
        money: data.expired_total.money,
        percent: "-",
      });
      let resultList = [];
      cloneData.list &&
        cloneData.list.forEach(item => {
          resultList.push({
            ...item,
            value: item.money,
            tootipList: [
              { key: "消耗占比：", value: item.title },
              { key: "", value: `${item.num}单  ￥${item.money}` },
              { key: "", value: `${item.percent}%` },
            ],
          });
        });
      this.setPieConfigure("consume_bar_options", resultList, "消耗数据");
    },

    // 处理账户收款明细
    handleCollection(source) {
      this.collection_lists = source || [];
      let resultList = [];
      source &&
        source.forEach(item => {
          resultList.push({
            ...item,
            value: item.order_money,
            tootipList: [
              { key: "收款类型:", value: item.text },
              { key: "", value: `${item.num}单  ￥${item.order_money}` },
              { key: "金额占比", value: `${item.money_rate}%` },
            ],
          });
        });
      this.setPieConfigure(
        "collection_bar_options",
        resultList,
        "账户收款明细",
      );
    },

    // api-交易统计
    getTradeTradesummary() {
      this.tranTabLoading = true;
      let params = {
        ...{
          st: this.formDate[0],
          et: this.formDate[1],
        },
        ...this.tran_queryFormData,
        clinic_id: this.clinic_id,
        date_type: this.date_type || "range",
      };
      this.$api
        .getTradeTradesummary(params)
        .then(res => {
          this.tran_list = res.list;
          this.tran_total = Number(res.total);
        })
        .finally(() => (this.tranTabLoading = false));
    },

    // api-订单明细
    getTradeOrders() {
      this.shopCurrenTabLoading = true;
      let params = {
        ...{
          st: this.formDate[0],
          et: this.formDate[1],
        },
        ...this.queryFormData,
        clinic_id: this.clinic_id,
        date_type: this.date_type || "range",
      };
      this.$api
        .getTradeOrders(params)
        .then(res => {
          this.order_list = res.list;
          this.total = Number(res.total);
        })
        .finally(() => (this.shopCurrenTabLoading = false));
    },
    // api-退款明细
    getTradeRefundorders() {
      this.shopCurrenTabLoading = true;
      let params = {
        ...{
          st: this.formDate[0],
          et: this.formDate[1],
        },
        ...this.queryFormData,
        clinic_id: this.clinic_id,
        date_type: this.date_type || "range",
      };
      this.$api
        .getTradeRefundorders(params)
        .then(res => {
          this.refund_list = res.list;
          this.total = res.total;
        })
        .finally(() => (this.shopCurrenTabLoading = false));
    },
  },
  filters: {},
};
</script>
<style lang="less" scoped>
@import url("./style/common.less");
</style>
