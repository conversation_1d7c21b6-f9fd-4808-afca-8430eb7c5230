<template>
  <div>
    <div class="block-header"><span>门店信息</span></div>
    <KWidget label="门店名称:" required>
      <Input v-model="formData.name" maxlength="15" show-word-limit/>
    </KWidget>

    <KWidget label="客服电话:">
      <Input v-model="formData.clinic_phone_no" number/>
    </KWidget>

    <KWidget>
      <Button type="primary" @click="onSave">保存</Button>
    </KWidget>
  </div>
</template>

<script>
/* eslint-disable */
import S from 'utils/util' // Some commonly used tools
import io from "utils/request" // Http request
import * as runtime from 'utils/runtime' // Runtime information
/* eslint-disable */

let init_form_data = {
  name: '',
  clinic_phone_no: '',
}

export default {
  name: "general_set",

  data() {
    return {
      formData: {...init_form_data},
    }
  },

  created() {
    this.get()
  },

  methods: {
    get: function () {
      io.get('clinic/clinic.get').then(data => {
        this.formData.name = data.name
        this.formData.clinic_phone_no = data.clinic_phone_no
      }).catch(error => {
        this.$Message.error(error.errmsg)
      })
    },

    onSave: function () {
      let formData = {...this.formData}

      io.post('clinic/clinic.update', formData).then(() => {
        this.$Message.success('保存成功')
      }).catch(error => {
        this.$Message.error(error.errmsg)
      })
    },
  }
}
</script>

<style lang="less">

</style>
