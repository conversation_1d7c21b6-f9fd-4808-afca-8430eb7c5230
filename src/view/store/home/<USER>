<template>
  <div class="config-wrapper">
    <div class="config-tabs">
      <Tabs type="card" @on-click="handleTabClick" :value="activeTab">
        <TabPane v-for="tab in configurationTabs" :key="tab.componentName" :label="tab.label" >
        </TabPane>
      </Tabs>
      <carousel v-if="activeTab === 0"></carousel>
      <physician v-else-if="activeTab === 1"></physician>
    </div>
  </div>
</template>

<script>
import carousel from './components/carousel'
import physician from './components/physician'
export default {
  name: 'configuration',
  components: {carousel,physician},
  data () {
    return {
      configurationTabs: [
        {
          id:1,
          label: '轮播图',
          componentName: 'carousel'
        },
        {
          id:2,
          label: '诊所医师',
          componentName: 'physician'
        }
      ],
      showComponentName: 'carousel',
      activeTab: this.$route.query.tab-0||0
    }
  },
  created(){
    const {tab} =this.$route.query
    if(tab){
      this.activeTab = tab-0
    }else {
      this.$route.query.tab = this.activeTab-0
    }
  },
  methods: {
    handleTabClick(name){
        this.showComponentName = this.configurationTabs[name].componentName
        this.$route.query.tab = this.activeTab =  name
    },
  },
  beforeRouteUpdate (to, from, next) {
    next()
  },
}
</script>

<style scoped lang="less" >
//::v-deep .ivu-tabs-tab{
//  margin-right: 0!important;
//  border-bottom: 1px solid #dcdee2;;
//}
//::v-deep .ivu-tabs-bar{
//  border-bottom: none;
//}
</style>
