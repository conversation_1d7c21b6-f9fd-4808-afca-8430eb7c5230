<template>
  <div class="edit-wrapper">
    <Form :model="formData" :label-width="90" ref="carouselForm" :rules="ruleValidate" :disabled="!$route.query.isEdit&&!!$route.query.id">
      <FormItem label="轮播图标题" required prop="title">
        <Input v-model="formData.title" placeholder="轮播图标题" style="width: 80%" :maxlength="15" show-word-limit
        />
      </FormItem>
      <FormItem label="图片" required prop="img_url" >
        <Picture v-model="formData.img_url" :limit="1" :isQueryDetail="isQuery"/>
        <div class="note">建议尺寸：750*350像素，图片大小不超过3.0M</div>
      </FormItem>
      <FormItem label="展示时间" required prop="show_end_time">
        <DatePicker type="datetimerange" :options="disabledTime" format="yyyy-MM-dd HH:mm" placeholder="请选择展示时间"
                    v-model="showTimes" @on-change="timeChange"
        ></DatePicker>
      </FormItem>
      <FormItem label="跳转" required prop="type">
        <div class="jump-type">
          <Select v-model="formData.type" class="link-type" @on-change="selectType">
            <Option  v-for="item in slideimg_type_desc" :lala="item" :value="item[1].type" :key="item[0]">{{ item[1].desc }}</Option>
          </Select>
          <FormItem :prop="dataRequired?'data':''">
            <Input v-model="formData.data" :placeholder="linkTypePlaceholder" class="link-params"/>
          </FormItem>
        </div>

      </FormItem>
      <FormItem label="展示顺序" required prop="order">
        <div class="carousel-order">
          <Input v-model="formData.order" placeholder="展示顺序" type="number"/>
          <span style="margin-left: 20px">(数字越大，展示顺序越靠前)</span>
        </div>
      </FormItem>
    </Form>
    <div class="fixed-bottom-wrapper" >
      <Button @click="back">返回</Button>
      <Dvd/>
      <Button v-show="$route.query.isEdit||!$route.query.id" type="primary" @click="handleSubmit('carouselForm')" :loading="submitLoading">保存</Button>
    </div>

  </div>
</template>

<script>
import Picture from '@/components/upload/picture'
import moment from 'moment'
export default {
  name: 'edit_carousel',
  components: {Picture},
  data() {
     const validatorOrder = (rule, value, callback) => {
       console.log(value)
       if(value === ''){
         callback(new Error('请输入展示顺序'));
       }else {
         if(value > 255){
           callback(new Error('展示顺序不能超过255'))
         }else {
           console.log(221)
           callback()
         }
       }
     }
    return {
      linkTypePlaceholder: '跳转参数',
      dataRequired: false,
      disabledTime: {
        disabledDate(date) {
          return date && date.valueOf() < Date.now() - 86400000
        },
      },
      formData: {
        title: '',
        show_start_time: '',
        show_end_time: '',
        order: 0,
        img_url: '',
        type: '',
        data: '',
      },
      showTimes: [],
      ruleValidate: {
        title: [
          {required: true, message: '请输入轮播图标题', trigger: 'blur'},
        ],
        img_url: [
          {required: true, message: '请上传轮播图片', trigger: 'blur'},
        ],
        type: [
          {required: true, message: '请选择跳转类型', trigger: 'change'},
        ],
        data: [
          {required: true, message: '请输入跳转类型参数', trigger: 'change'},
        ],
        order: [
          {validator: validatorOrder,trigger: 'change'},
        ],
        show_end_time: [
          {required: true, type: 'string', message: '请选择展示时间', trigger: 'change'},
        ],
      },
      submitLoading: false,
      slideimg_type_desc: [],
    }
  },
  created() {
    const id = this.$route.query.id
    if(id){
      this.getDetail(id)
    }
    this.getStatus()
  },
  computed:{
    isQuery(){
      const {id,isEdit} = this.$route.query
      return id&&!isEdit?true:false
    }
  },
  methods: {
    selectType(type){
     let currentTypeItem = this.slideimg_type_desc.find((item)=>item[0]===type)
     this.linkTypePlaceholder = currentTypeItem[1].tips
     this.dataRequired = currentTypeItem[1].required==='1'?true:false
    },
    getDetail(id){
      this.$api.getCarouselDetail({id}).then(res => {
        console.log(res)
        res = res.slide_img
        this.formData.title = res.title
        this.formData.img_url = res.img_url
        res.show_end_time = moment(res.show_end_time*1000).format('YYYY-MM-DD HH:mm')
        res.show_start_time = moment(res.show_start_time*1000).format('YYYY-MM-DD HH:mm')
        this.formData.show_end_time = res.show_end_time
        this.formData.show_start_time = res.show_start_time
        this.showTimes = [res.show_start_time,res.show_end_time]
        this.formData.order = res.order - 0
        this.formData.data = res.data
        this.formData.type = res.type
        this.formData.title = res.title
      }).catch(err=> {
        console.log(err)
        this.$Message.error(err)
      })
    },
    getStatus() {
      this.$api.getCarouselStatusList().then(res => {
        console.log("-> res", res);
        this.slideimg_type_desc = Object.entries(res.slideimg_type_desc)
        console.log("-> slideimg_type_desc", this.slideimg_type_desc);
      })
    },
    handleSubmit(name) {
      this.$refs[name].validate((valid) => {
        if (valid) {
          if(this.dataRequired&&!this.formData.data){
            this.$Message.error('该跳转类型不能为空')
            return
          }
          if(this.$route.query.id){
            this.formData = Object.assign(this.formData,{id:this.$route.query.id})
          }
          this.$api.editCarousel(this.formData).then(res => {
            console.log(res)
              this.$Message.success(this.$route.query.isEdit?'编辑轮播图成功':'新增轮播图成功')
            this.$router.push({
              path: '/store/home/<USER>',
              // query: {
              //   tab:
              // }
            })
          },(err)=>{
            this.$Message.error(err.errmsg)
          })
        } else {
          console.log(3212)
          this.$Message.error('请正确填写轮播图信息')
        }
      })
      console.log(this.formData)

    },
    handleReset(name) {
      this.$refs[name].resetFields()
    },
    back() {
      this.$router.push({
        path: '/store/home/<USER>',
      })
    },
    timeChange(values){
      console.log(values)
      if(values){
        this.formData.show_start_time = values[0]
        this.formData.show_end_time = values[1]
      }else {
        this.formData.show_start_time = ''
        this.formData.show_end_time = ''
      }
    }
  },
  beforeDestroy() {
    this.handleReset('carouselForm')
  },
}
</script>
<style scoped lang="less">
.jump-type {
  display: flex;

  .link-type {
    width: 300px;
    margin-right: 20px;
  }

  ::v-deep .ivu-select-selection {
    width: 300px;
  }

  .link-params {
    width: 130px !important;
  }
}

::v-deep .ivu-input-wrapper {
  width: 450px;
}
</style>
