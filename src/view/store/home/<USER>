<template>
  <div class="home-set">
    <KWidget label="轮播图:" text :width="330">
      <div class="note">最多添加{{ this.slide_limit }}张图片，鼠标拖拽调整顺序，建议宽度750*400像素</div>
      <draggable v-if="formData.slide_imgs.length > 0" v-model="formData.slide_imgs" style="display: inline-block">
        <div v-for="(item, key) in formData.slide_imgs" :key="key">
          <div class="slide-node media">
            <div class="media-left">
              <a :href="item.img_url" target="_blank">
                <img :src="item.img_url | imageStyle('B.w300')" style="width: 50px" />
              </a>
            </div>
            <div class="media-body media-middle">
              <span>链接：</span>
              <div v-if="item.goods_id" class="title1 space4">
                <KLink :to="{ path: '/goods/item/list', query: { id: item.goods_id } }" target="_blank">
                  <a>{{ item.goods_name }}</a>
                </KLink>
                <a class="del" @click="onDelSlideGoods(key)">×</a>
              </div>
              <div v-else class="title2 space4">
                <a
                  @click="
                    slideGoodsSelectModal = true;
                    slideGoodsSelectIndex = key;
                  "
                  >请选择链接到的商品</a
                >
              </div>
              <a
                v-if="item.goods_id"
                @click="
                  slideGoodsSelectModal = true;
                  slideGoodsSelectIndex = key;
                "
                >修改</a
              >
            </div>
            <div class="btn-delete" @click="onDelSlide(key)">×</div>
          </div>
        </div>
      </draggable>

      <div class="block_20"></div>
      <QiniuUpload @on-success="onUploadSlideImageSuccess" multiple>
        <Button type="primary" ghost long>+ 添加图片</Button>
      </QiniuUpload>
    </KWidget>

    <KWidget label="商品节点:" text :width="330">
      <div class="note">最多添加{{ this.node_limit }}个商品节点，鼠标拖拽商品进行排序</div>

      <div v-for="(node, nodeKey) in formData.nodes" :key="nodeKey" class="node">
        <div class="title">
          <Input v-model="node.title" placeholder="填写节点标题" style="width: 200px" />
          <Dvd /><Dvd /><Dvd /><Dvd /><a class="node-del" @click="onDelNode(nodeKey)">删除节点</a>
        </div>
        <div class="items">
          <draggable v-model="node.goods_ids" style="display: inline-block">
            <div class="item" v-for="(goodsid, itemKey) in node.goods_ids" :key="itemKey">
              <KLink :to="{ path: '/goods/item/list', query: { id: goodsid } }" target="_blank">
                <img :src="formData.goods_items[goodsid].main_img | imageStyle('B.w300')" />
              </KLink>
              <div class="btn-delete" @click="onDelNodeGoods(nodeKey, itemKey)">×</div>
            </div>
          </draggable>
          <div class="item">
            <a class="node-add" @click="onOpenAddNodeGoodsModal(nodeKey)">+</a>
          </div>
        </div>
        <Divider v-if="nodeKey != formData.nodes.length - 1" style="margin: 20px 0 20px 0; width: 340px" />
      </div>

      <div class="block_20"></div>
      <Button type="primary" ghost long @click="onAddNode">+ 增加节点</Button>
    </KWidget>

    <div class="block_45"></div>

    <div class="fixed-bottom-wrapper">
      <Button type="primary" @click="onSave">保存</Button>
      <Dvd />
      <Dvd />
      <Button @click="$router.back()">返回</Button>
    </div>

    <KGoodsSelect v-model="slideGoodsSelectModal" @on-selected="onAddSlideGoods" />
    <KGoodsSelectMultiple
      v-model="nodeGoodsSelectMultipleModal"
      @on-selected="onAddNodeGoods"
      :disabled-item-ids="disabledItemIds"
    />
  </div>
</template>

<script>
/* eslint-disable */
import S from 'utils/util'; // Some commonly used tools
import io from 'utils/request'; // Http request
import * as runtime from 'utils/runtime'; // Runtime information
/* eslint-disable */
import QiniuUpload from '@/components/upload/qiniu-upload/qiniu-upload';
import draggable from 'vuedraggable';
import KGoodsSelect from '@/components/k-goods-select';
import KGoodsSelectMultiple from '@/components/k-goods-select-multiple';

let init_form_data = {
  id: '',
  slide_imgs: [],
  nodes: [],
  goods_items: {}
};

export default {
  name: 'set',
  components: {
    QiniuUpload,
    draggable,
    KGoodsSelect,
    KGoodsSelectMultiple
  },

  data() {
    return {
      formData: { ...init_form_data },

      slide_limit: 10,
      slideGoodsSelectModal: false,
      slideGoodsSelectIndex: 0,

      node_limit: 20,
      nodeGoodsSelectMultipleModal: false,
      nodeGoodsSelectIndex: 0,

      disabledItemIds: []
    };
  },

  created() {
    this.get();
  },

  methods: {
    get: function () {
      io.get('clinic/setting.home.get')
        .then(data => {
          this.formData.slide_imgs = data.slide_imgs;
          this.formData.nodes = data.nodes;
          this.formData.goods_items = data.goods_items;
        })
        .catch(error => {
          this.$Message.error(error.errmsg);
        });
    },

    onSave: function () {
      let formData = { ...this.formData };

      formData.nodes.forEach(node => {
        if (node.title.trim() == '') {
          this.$Message.error('请填写节点标题');
          return;
        }
      });

      formData.slide_imgs = JSON.stringify(formData.slide_imgs);
      formData.nodes = JSON.stringify(formData.nodes);

      io.post('clinic/setting.home.edit', formData)
        .then(() => {
          this.$Message.success('保存成功');
        })
        .catch(error => {
          this.$Message.error(error.errmsg);
        });
    },

    onUploadSlideImageSuccess: function (imgUrl) {
      if (this.formData.slide_imgs.length < this.slide_limit) {
        this.formData.slide_imgs.push({
          img_url: imgUrl,
          goods_id: '',
          goods_name: ''
        });
      }
    },

    onAddSlideGoods: function (goods) {
      this.formData.slide_imgs[this.slideGoodsSelectIndex].goods_id = goods.id;
      this.formData.slide_imgs[this.slideGoodsSelectIndex].goods_name = goods.name;

      this.slideGoodsSelectModal = false;
    },

    onDelSlideGoods: function (index) {
      this.formData.slide_imgs[index].goods_id = '';
      this.formData.slide_imgs[index].goods_name = '';
    },

    onDelSlide: function (index) {
      this.formData.slide_imgs.splice(index, 1);
    },

    onAddNode: function () {
      if (this.formData.nodes.length < this.node_limit) {
        this.formData.nodes.push({
          title: '',
          goods_ids: []
        });
      }
    },

    onDelNode: function (index) {
      this.formData.nodes.splice(index, 1);
    },

    onOpenAddNodeGoodsModal: function (nodeKey) {
      console.log('lala');
      this.nodeGoodsSelectMultipleModal = true;
      this.nodeGoodsSelectIndex = nodeKey;

      let ids = [];
      this.formData.nodes[nodeKey].goods_ids.forEach(id => {
        ids.push(Number(id));
      });
      this.disabledItemIds = ids;
    },

    onAddNodeGoods: function (items) {
      items.forEach(item => {
        this.formData.nodes[this.nodeGoodsSelectIndex].goods_ids.push(item.id);
        this.$set(this.formData.goods_items, item.id, item);
      });

      this.nodeGoodsSelectMultipleModal = false;
    },

    onDelNodeGoods: function (nodeKey, itemKey) {
      this.formData.nodes[nodeKey].goods_ids.splice(itemKey, 1);
    }
  }
};
</script>

<style lang="less">
.home-set {
  .slide-node {
    border: 1px solid #eee;
    background-color: #f8f8f8;
    margin-top: 10px;
    padding: 5px;
    position: relative;

    &:hover .btn-delete {
      display: block;
      top: -9px;
      right: -9px;
    }

    .title1 {
      display: inline-block;
      padding: 3px 8px;
      background-color: #e2f3ff;
      border: 1px solid #3388ff4d;
      width: 180px;
      text-align: left;
      white-space: nowrap;
      text-overflow: ellipsis;
      overflow: hidden;
      vertical-align: middle;
      position: relative;

      .del {
        color: #000000;
        position: absolute;
        right: 4px;
        font-size: 18px;
        top: 2px;
      }
    }

    .title2 {
      display: inline-block;
      padding: 3px 8px;
      width: 157px;
      text-align: left;
      white-space: nowrap;
      text-overflow: ellipsis;
      overflow: hidden;
      vertical-align: middle;
      position: relative;
    }
  }

  .node {
    margin-top: 10px;
    position: relative;
    .node-del {
      position: absolute;
      right: -27px;
      top: 4px;
      display: none;
    }
    &:hover .node-del {
      display: block;
    }
    .node-add {
      text-align: center;
      line-height: 47px;
      width: 47px;
      display: inline-block;
      background-color: #fff;
      box-shadow: 0px 0px 2px #ddd;
    }

    .title {
    }
    .items {
      margin-top: 10px;
      width: 330px;
      padding: 5px 0 10px 10px;
      border: 1px solid #eee;
      background-color: #f8f8f8;
    }
    .item {
      margin: 0 5px 0 0;
      display: inline-block;
      padding: 5px 0 0 0;
      position: relative;
      img {
        width: 47px;
        border: 1px solid transparent;
        &:hover {
          border: 1px solid #155bd4;
          box-shadow: 0px 0px 3px #155bd4;
        }
      }
      &:hover .btn-delete {
        display: block;
        top: -5px;
        right: -5px;
      }
    }
  }

  .btn-delete {
    display: none;
    position: absolute;
    top: 0px;
    right: 0px;
    color: #fff;
    text-align: center;
    cursor: pointer;
    width: 18px;
    height: 18px;
    font-size: 14px;
    line-height: 16px;
    background: rgba(153, 153, 153, 0.6);
    border-radius: 10px;
    text-indent: 0;
    z-index: 10;
  }

  .btn-delete:hover {
    background: rgba(25, 25, 25, 0.6);
  }

  .ivu-upload-select {
    display: block;
  }
}
</style>
