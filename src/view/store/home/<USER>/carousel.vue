<template>
  <div class="carousel-wrapper">
    <div class="form-warpper">
      <Form class="form-warpper_left" inline label-position="left" @submit.native.prevent
            @keyup.enter.native="onSearch">
        <FormItem label="轮播图标题：">
          <Input type="text" v-model="queryFormData.title" placeholder="请输入轮播图标题"/>
        </FormItem>
        <FormItem label="状态">
          <Select v-model="queryFormData.status" style="" placeholder="全部状态">
            <Option value="">全部状态</Option>
            <Option :value="index" v-for="(item,index) in carousel_status_list" :key="item.kw">{{ item.desc }}</Option>
          </Select>
        </FormItem>
        <FormItem>
          <Button type="primary" class="space6" @click="onSearch">查询</Button>
          <Button class="space6" @click="initSearch">重置筛选</Button>
        </FormItem>

      </Form>
      <div class="form-inline-button-r">
        <Button type="primary" :to="{path:'/store/home/<USER>'}">新增轮播图</Button>
      </div>
    </div>
    <div class="table-wrapper">
      <Table :loading="tableLoading" :columns="tableCols" :data="list" size="small" border stripe class="pl-table"
             :height="$store.state.app.clientHeight-283">
        <template slot-scope="{row}" slot="show_time">
          {{ formatDate(row.show_start_time)+' - '+formatDate(row.show_end_time) }}
        </template>
        <template slot-scope="{row}" slot="status">
          {{ carousel_status_list[row.status].desc || '' }}
        </template>
        <template slot-scope="{row:{operator,create_time,update_time}}" slot="create_info">
          <div class="create-info">
            <span>创建人：{{operator}}</span>
            <span>创建时间：{{formatDate(create_time,'YYYY-MM-DD HH:mm')}}</span>
            <span>修改时间：{{formatDate(update_time,'YYYY-MM-DD HH:mm')}}</span>
          </div>
        </template>
        <template slot-scope="{row}" slot="action">
          <div class="action">
            <a  @click="queryDetail(row)" >详情</a>
            <a  v-if="row.status==='1'||row.status==='2'" @click="editCarousel(row)">编辑</a>
            <Poptip confirm transfer title="确定删除？" v-if="row.status !== '2'" @on-ok="deleteCarousel(row)">
              <a > 删除 </a>
            </Poptip>
            <Poptip confirm transfer title="确定停止？" v-if="row.status==='2'" @on-ok="stopShow(row)">
              <a > 停止 </a>
            </Poptip>
          </div>

        </template>
      </Table>

      <div class="block_20"></div>

      <KPage :total="total"
             :page-size.sync="queryFormData.pageSize"
             :current.sync="queryFormData.page"
             @on-change="onPageChange"
             style="text-align:right"
      />
    </div>
  </div>
</template>

<script>
import search from '@/mixins/search'
import S from 'utils/util' // Some commonly used tools

export default {
  name: 'carousel',
  mixins: [search],
  data() {
    return {
      init_query_form_data: {
        page: 1,
        pageSize: 20,
        title: '',
        status: '',
        prod_type: '',
      },
      queryFormData: {...this.init_query_form_data},
      apiName: 'getCarouselList',
      tableCols: [
        {
          title: 'ID',
          key: 'id',
        },
        {
          title: '标题',
          key: 'title',
        },
        {
          title: '展示时间',
          slot: 'show_time',
          tooltip: true,
          width: 220
        },
        {
          title: '状态',
          slot: 'status',
        },
        {
          title: '创建信息',
          slot: 'create_info',
          width:200
        },
        {
          title: '操作',
          slot: 'action',
          width: 160
        },
      ],
      carousel_status_list: [],
      jump_list: [],
    }
  },
  created() {
    this.$api.getCarouselStatusList().then(res => {
      console.log("-> res", res);
      this.carousel_status_list = res.slideimg_status_desc
      this.jump_list = res.slideimg_status_desc
      this.queryFormData = S.merge(this.queryFormData, this.$route.query)
      console.log('-> this.$route.query', this.$route.query)
      this.submitQueryForm(true)
    })
  },
  computed: {
    formatDate() {
      return (date,formatType='YYYY.MM.DD HH:mm')=>{
        if(date){
          return S.moment(date*1000).format(formatType)
        }

      }
    }
  },
  methods: {
    //详情
    queryDetail({id}) {
      // console.log("-> id", id);
      this.$router.push({
        path: '/store/home/<USER>',
        query:{
          id
        }
      })
    },
    //编辑
    editCarousel({id}){
      this.$router.push({
        path: '/store/home/<USER>',
        query:{
          id,
          isEdit: true
        }
      })
    },
    //删除
    deleteCarousel({id}){
      console.log(id)
      this.$api.deleteCarousel({id}).then(res => {
        console.log(res)
        this.$Message.success('删除成功')
        this.submitQueryForm()
      }).catch(err=>this.$Message.error(err))
    },
    //停止
    stopShow({id}){
      console.log(id)
      this.$api.endCarousel({id}).then(res => {
        console.log(res)
        this.$Message.success('停止成功')
        this.submitQueryForm()
      }).catch(err=>this.$Message.error(err))
    }
  },

}
</script>

<style scoped lang="less">
.form-warpper {
  display: flex;
  justify-content: space-between;

  .form-warpper_left {
    display: flex;
    align-items: center;
    width: 100%;
    justify-content: flex-start;
  }

  .form-inline-button-r {
    margin-right: 4px;
  }
}

.ivu-form-item {
  display: flex;
}
.create-info{
  display: flex;
  flex-direction: column;
}
.action{
  a{
    margin-right: 10px;
  }
}
</style>
