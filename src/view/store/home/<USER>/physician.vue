<template>
  <div class="carousel-wrapper">
    <div class="form-warpper">
      <Form
        class="form-warpper_left"
        inline
        label-position="left"
        @submit.native.prevent
        @keyup.enter.native="onSearch"
      >
        <FormItem label="医师名称：">
          <Input type="text" v-model="queryFormData.name" placeholder="医师名称" />
        </FormItem>
        <FormItem label="状态">
          <Select v-model="queryFormData.is_enable" style="" placeholder="选择状态">
            <Option :value="item.id" v-for="item in doctor_enable_desc" :key="item.id">{{ item.value }}</Option>
          </Select>
        </FormItem>
        <FormItem>
          <Button type="primary" class="space6" @click="onSearch">查询</Button>
          <Button class="space6" @click="initSearch">重置筛选</Button>
        </FormItem>
      </Form>
      <div class="form-inline-button-r">
        <Button type="primary" :to="{ path: '/store/home/<USER>' }">新增医师</Button>
      </div>
    </div>
    <div class="table-wrapper">
      <Table
        :loading="tableLoading"
        :columns="physicianCols"
        :data="list"
        size="small"
        border
        stripe
        class="pl-table"
        :height="$store.state.app.clientHeight - 282"
      >
        <template slot-scope="{ row }" slot="avatar">
          <div class="avatar">
            <Avatar :src="row.avatar | imageStyle('B.w300')" v-if="row.avatar" />
            <Avatar v-else shape="square" icon="ios-person" />
          </div>
        </template>
        <template slot-scope="{ row }" slot="order">
          <Tooltip content="输入展示顺序">
            <a @click="showChangeOrder(row)">{{ row.order }}</a>
          </Tooltip>
        </template>
        <template slot-scope="{ row }" slot="status">
          {{ statusList[row.is_enable].desc }}
        </template>
        <template slot-scope="{ row }" slot="createInfo">
          <div class="create-info">
            <span>创建人：{{ row.operator }}</span>
            <span>创建时间：{{ formatDate(row.create_time, 'YYYY-MM-DD HH:mm') }}</span>
            <span>修改时间：{{ formatDate(row.update_time, 'YYYY-MM-DD HH:mm') }}</span>
          </div>
        </template>
        <template slot-scope="{ row }" slot="action">
          <div class="action">
            <a @click="queryDetail(row)">详情</a>
            <a @click="editDoctor(row)">编辑</a>
            <Poptip confirm transfer title="确定删除？" v-if="row.is_enable === '2'" @on-ok="deletePhysician(row)">
              <a> 删除 </a>
            </Poptip>
            <Poptip
              confirm
              transfer
              :title="row.is_enable === '1' ? '确定禁用？' : '确定启用？'"
              @on-ok="changeAble(row)"
            >
              <a> {{ row.is_enable === '1' ? '禁用' : '启用' }} </a>
            </Poptip>
          </div>
        </template>
      </Table>

      <div class="block_20"></div>

      <KPage
        :total="total"
        :page-size.sync="queryFormData.pageSize"
        :current.sync="queryFormData.page"
        @on-change="onPageChange"
        style="text-align: right"
      />
    </div>
    <Modal v-model="changeOrderFlag" title="医师展示顺序">
      <div class="order-change">
        <span>医师展示顺序：</span>
        <InputNumber v-model="formData.order" placeholder="展示顺序" type="number" :max="255" style="width: 50%" />
      </div>
      <template slot="footer">
        <div>
          <Button type="default" @click="changeOrderFlag = false">取消</Button>
          <Button type="primary" @click="handleOk">确定</Button>
        </div>
      </template>
    </Modal>
  </div>
</template>

<script>
import search from '@/mixins/search';
import S from 'utils/util';

let init_query_form_data = {
  page: 1,
  pageSize: 20,
  name: '',
  status: '',
  prod_type: ''
};
export default {
  name: 'physician',
  mixins: [search],
  data() {
    return {
      formData: {
        id: '',
        order: 0
      },
      changeOrderFlag: false,
      queryFormData: { ...init_query_form_data },
      tableCols: [],
      apiName: 'getPhysicianList',
      doctor_enable_desc: [],
      statusList: [],
      physicianCols: [
        {
          title: 'ID',
          key: 'id'
        },
        {
          title: '医师姓名',
          key: 'name'
        },
        {
          title: '头像',
          slot: 'avatar'
        },
        {
          title: '简介',
          key: 'title'
        },
        {
          title: '展示顺序',
          slot: 'order'
        },
        {
          title: '状态',
          slot: 'status'
        },
        {
          title: '创建信息',
          slot: 'createInfo',
          width: 200
        },
        {
          title: '操作',
          slot: 'action',
          width: 160
        }
      ],
      changeOrderId: ''
    };
  },
  created() {
    this.$api.getPhysicianStatusList().then(res => {
      console.log('-> res', res);
      this.statusList = res.doctor_enable_desc;
      console.log('-> this.statusList', this.statusList);
      this.doctor_enable_desc = [];
      for (let resKey in res.doctor_enable_desc) {
        this.doctor_enable_desc.push({ id: resKey, value: res.doctor_enable_desc[resKey].desc });
      }
      console.log('-> this.statusList', this.doctor_enable_desc);
      this.queryFormData = S.merge(this.queryFormData, this.$route.query);
      this.submitQueryForm(true);
    });
  },
  computed: {
    formatDate() {
      return (date, formatType = 'YYYY.MM.DD HH:mm') => {
        if (date) {
          return S.moment(date * 1000).format(formatType);
        }
      };
    }
  },
  methods: {
    handleOk() {
      if (!this.formData.order && this.formData.order !== 0) {
        this.$Message.error('请输入顺序');
      } else {
        this.$api
          .rankingPhysician(this.formData)
          .then(
            res => {
              console.log('-> res', res);
              this.$Message.success('修改顺序成功');
              this.formData.order = 0;
              this.formData.id = '';
              this.changeOrderFlag = false;
              this.submitQueryForm();
            },
            err => {
              this.$Message.error(err.errmsg);
            }
          )
          .finally(() => {});
      }
    },
    showChangeOrder({ id, order }) {
      this.formData.id = id;
      this.formData.order = order - 0;
      this.changeOrderFlag = true;
    },
    changeAble({ id, is_enable }) {
      is_enable = is_enable === '1' ? '2' : '1';
      this.$api.editPhyAbleStatus({ id, is_enable }).then(
        res => {
          console.log('-> is_enable', is_enable);
          console.log('-> res', res);
          this.$Message.success(is_enable === '1' ? '启用成功' : '禁用成功');
          this.submitQueryForm();
        },
        err => {
          this.$Message.error(err.errmsg);
        }
      );
    },
    deletePhysician({ id }) {
      this.$api.deletePhysician({ id }).then(
        res => {
          console.log(res);
          this.$Message.success('删除成功');
          this.submitQueryForm();
        },
        err => {
          this.$Message.error(err.errmsg);
        }
      );
    },
    queryDetail({ id }) {
      this.$router.push({
        path: '/store/home/<USER>',
        query: { id }
      });
    },
    editDoctor({ id }) {
      this.$router.push({
        path: '/store/home/<USER>',
        query: { id, isEdit: true }
      });
    }
  }
};
</script>

<style scoped lang="less">
.form-warpper {
  display: flex;
  justify-content: space-between;

  .form-warpper_left {
    display: flex;
    align-items: center;
    width: 100%;
    justify-content: flex-start;
  }

  .form-inline-button-r {
    margin-right: 4px;
  }
}

.ivu-form-item {
  display: flex;
}

.create-info {
  display: flex;
  flex-direction: column;
}

.action {
  a {
    margin-right: 10px;
  }
}
</style>
