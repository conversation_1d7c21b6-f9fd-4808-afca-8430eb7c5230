<template>
	<div class="payments-detail-wrapper">
		<div class="block-header">出入库单信息</div>
		<div class="payments-box">
			<div>
				<div class="payments-item">
					<span class="payments-item-label">{{ type === 'IN' ? '入库单号：' : '出库单号：' }}</span>
					<span class="payments-item-content">{{ code }}</span>
				</div>
				<div class="payments-item">
					<span class="payments-item-label">{{ type === 'IN' ? '入库日期：' : '出库日期：' }}</span>
					<span class="payments-item-content">{{ ship_time | date_format }}</span>
				</div>
				<div class="payments-item">
					<span class="payments-item-label">采购主体：</span>
					<span class="payments-item-content">{{ clinic_name||rxj_name }}</span>
				</div>
				<!-- <div class="payments-item">
					<span class="payments-item-label">{{ type === 'IN' ? '验收/入库员：' : '验收/出库员：' }}</span>
					<span class="payments-item-content">诊所采购费</span>
				</div>
				<div class="payments-item">
					<span class="payments-item-label">备注：</span>
					<span class="payments-item-content">榕树家浦东1号店</span>
				</div> -->
			</div>
			<!-- <div v-else>
				<div class="payments-item">
					<span class="payments-item-label">出库单号：</span>
					<span class="payments-item-content">288282111</span>
				</div>
				<div class="payments-item">
					<span class="payments-item-label">出库日期：</span>
					<span class="payments-item-content">待审核</span>
				</div>
				<div class="payments-item">
					<span class="payments-item-label">出库类型：</span>
					<span class="payments-item-content">诊所采购</span>
				</div> -->
			<!-- <div class="payments-item">
				<span class="payments-item-label">出库员：</span>
				<span class="payments-item-content">法外狂徒张三</span>
			</div>
			<div class="payments-item">
				<span class="payments-item-label">备注：</span>
				<span class="payments-item-content">榕树家浦东1号店</span>
			</div> -->
		</div>
		
		<div class="payments-item">
			<span class="payments-item-label">出库商品清单：</span>
			<Table :columns="tableCols" :data="list" :summary-method="handleSummary" :width="900" border
			       show-summary></Table>
		</div>
		<div class="fixed-bottom-wrapper">
			<Button @click="$router.back()">返回</Button>
		</div>
	</div>

</template>

<script>
export default {
	name: 'detail',
	data() {
		return {
			list: [],
			type: this.$route.query.type, // 标识是出库还是入库
			clinic_name: '',
      rxj_name: '',
			code: '',
			ship_time: '',
			tableCols: [
				{title: '商品编号', key: 'spu_code', align: 'center'},
				{title: '商品', key: 'name', align: 'center'},
				// {title: '单位', key: 'price', align:'center'},
				{title: '采购单价', key: 'unit_price', align: 'center'},
				{title: '数量', key: 'num', align: 'center'},
				{title: '金额', key: 'com_price', align: 'center'},
				// {title: '备注', key: 'status', align:'center'},
			],
		}
	},
	computed: {},
	watch: {},
	created() {},
	mounted() {
		this.init()
	},
	methods: {
		// 初始化数据
		init() {
			this.postStockRrcordInfo()
		},
		handleSummary({columns, data}) {
			console.log('-> data', data)
			const sums = {}
			columns.forEach((column, index) => {
				const key = column.key
				if (index === 0) {
					sums[key] = {
						key,
						value: '合计',
					}
					return
				}
				if (index === 2) {
					sums[key] = {
						key,
						value: '',
					}
					return
				}
				const values = data.map(item => Number(item[key]))
				console.log('values', values)
				if (!values.every(value => isNaN(value))) {
					const v = values.reduce((prev, curr) => {
						const value = Number(curr)
						if (!isNaN(value)) {
							return prev + curr
						} else {
							return prev
						}
					}, 0)
					if(key === 'num'){
						sums[key] = {
							key,
							value: v
						}
					}else{
						sums[key] = {
							key,
							value: v.toFixed(2),
						}
					}
				
				} else {
					sums[key] = {
						key,
						value: '',
					}
				}
			})
			
			return sums
		},
		// 获取出入库详情
		postStockRrcordInfo() {
			let params = {id: this.$route.query.id || ''}
			this.$api.postStockRrcordInfo(params).then(res => {
				console.log('postStockRrcordInfo', res)
				this.list = res.items
				this.ship_time = res.ship_time
				this.code = res.code
				this.clinic_name = res.clinic_name
				this.rxj_name = res.rxj_name
			})
		},
	},
}
</script>

<style lang="less" scoped>
.payments-detail-wrapper {
	.payments-item {
		display: flex;
		margin-bottom: 8px;
		
		.payments-item-label {
			width: 100px;
			text-align: right;
		}
		
		.payments-item-content {
		
		}
	}
}
</style>
