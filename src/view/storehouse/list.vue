<template>
	<div class="f_order-wrapper">
		<Form :label-width="82" inline @submit.native.prevent @keyup.enter.native="onSearch">
			<div class="no-wrap">
				<FormItem label="出入库时间:">
					<DatePicker v-model="purchaseTimes" :options="disabledTime" class="time-range" clearable format="yyyy-MM-dd" placeholder="请输入创建时间" type="daterange" @on-change="purchaseTimesChange"
					></DatePicker>
				</FormItem>
<!--				<FormItem label="采购主体:">-->
<!--					<Input v-model="queryFormData.clinic_name" placeholder="请输入采购主体名称" clearable/>-->
<!--				</FormItem>-->
          <FormItem label="采购主体:">
            <surning-search ref="nursing-search" v-model="queryFormData.ent_code" @getType="(type)=>queryFormData.ent_type = type" :isClearable="true"></surning-search>
          </FormItem>
				<FormItem style="text-align: left">
					<Button class="mr10" type="primary" @click="onSearch">筛选</Button>
					<Button type="default" @click="onResetSearch">重置</Button>
				</FormItem>
			</div>
		</Form>
		<div class="table-wrapper">
			<div class="panel-nav">
				<a :class="{active: queryFormData.type === 'IN'}" class="nav" @click.prevent.capture="onStatusChange('IN')">
					入库单
				</a>
				<a :class="{active: $route.query.type == 'OUT'}" class="nav" @click.prevent.capture="onStatusChange('OUT')">
					出库单
					<!-- <Tag color="default">{{ 333 }}</Tag> -->
				</a>
			</div>
			<Table :columns="tabCols" :data="list" :loading="tableLoading" :height="$store.state.app.clientHeight-275">
				<template slot="create_time" slot-scope="{row}">
					<p>{{ row.create_time | date_format('YYYY-MM-DD')  }}</p>
				</template>
				<template slot="main_name" slot-scope="{row}">
					<p>{{ row.ent_name  }}</p>
				</template>
				<template slot="action" slot-scope="{row}">
					<a @click="$router.push({path: '/storehouse/detail', query: {type: row.type, id: row.id}})">详情</a>
				</template>
			</Table>
			<div class="block_20"></div>
			<KPage :current.sync="+queryFormData.page"
			       :page-size.sync="+queryFormData.pageSize"
			       :total="total"
			       style="text-align:center"
			       @on-change="handleCurrentChange"
			       @on-page-size-change="handleSizeChange"
			/>
		</div>
	
	</div>
</template>

<script>
import S from 'utils/util'
import search from '@/mixins/search'
import surningSearch from '@/components/surning-search/surning-search'

const init_query_form_data = {
	page: 1,
	pageSize: 20,
	st: '', // 开始时间
	et: '', // 结束时间
	clinic_name: '', // 诊所名称
	type: 'IN', // 出库还是入库
	r: '',
  ent_code:'',
  ent_type: ''
}
export default {
	name: 'list',
	mixins: [search],
  components: {
    surningSearch,
  },
	data() {
		return {
			apiName: 'postStockRrcordList', // 获取库存列表
			queryFormData: {...init_query_form_data},
			purchaseTimes: [],
			disabledTime: {
				disabledDate(date) {
					return date && date.valueOf() > Date.now()
				},
			},
			tabCols: [],
			tableCols_in: [
				{title: '入库单号', key: 'code', align: 'center'},
				{title: '入库时间', key: 'create_time', slot: 'create_time', align: 'center'},
				{title: '入库商品', key: 'prod_name', align: 'center', tooltip: true},
				{title: '采购主体', slot: 'main_name', align: 'center'},
				{title: '入库总数量', key: 'total_num', align: 'center'},
				{title: '入库总金额(元)', key: 'total_price', align: 'center'},
				{title: '操作', slot: 'action', align: 'center'},
			],
			out_stock_cols: [
				{title: '出库单号', key: 'code', align: 'center'},
				{title: '出库时间', key: 'create_time', slot: 'create_time', align: 'center'},
				{title: '出库商品', key: 'prod_name', align: 'center', tooltip: true},
				{title: '采购主体', slot: 'main_name', align: 'center'},
				{title: '出库总数量', key: 'total_num', align: 'center'},
				{title: '出库总金额(元)', key: 'total_price', align: 'center'},
				{title: '操作', slot: 'action', align: 'center'},
			],
		}
	},
	computed: {},
	watch: {},
	created() {
		this.queryFormData = S.merge(this.queryFormData, this.$route.query)
		this.tabCols = this.tableCols_in
		this.queryFormData = S.merge(this.queryFormData, this.$route.query)
		this.submitQueryForm(true)
	},
	mounted() {},
	methods: {
		onStatusChange(type) {
			if(type == 'OUT'){
				this.tabCols = this.out_stock_cols
			}else {
				this.tabCols = this.tableCols_in
			}
			this.queryFormData.page = 1
			this.queryFormData.type = type
			this.submitQueryForm()
		},
		onResetSearch() {
			this.queryFormData = {...init_query_form_data}
			this.purchaseTimes = []
			this.submitQueryForm()
      this.$refs['nursing-search'].clear()
		},
		purchaseTimesChange(times) {
			console.log('-> times', times)
			this.handleTimeChange(times)
		},
	},
	beforeRouteUpdate(to, from, next) {
		// S.log(to.params['nav-stack-key-dir'], 'nav-stack-key-dir')
		this.queryFormData = S.merge(init_query_form_data, to.query)
		this.loadList()
		next()
	},
	
}
</script>

<style lang="less" scoped>

</style>
