<template>
	<div class="f_order-wrapper">
		<Form :label-width="82" inline @submit.native.prevent @keyup.enter.native="onSearch">
			<div class="no-wrap">
				<FormItem label="出入库时间:">
					<DatePicker v-model="purchaseTimes" :options="disabledTime" class="time-range" clearable format="yyyy-MM-dd" placeholder="请输入创建时间" type="daterange" @on-change="purchaseTimesChange"
					></DatePicker>
				</FormItem>
				<FormItem label="诊所:">
					<clinic-search ref="clinic-search" v-model="queryFormData.clinic_id" ></clinic-search>
				</FormItem>
					<FormItem label="供应商:">
					<supplier-search ref="supplier-search" v-model="queryFormData.supplier_id" :isClearable="true"></supplier-search>
				</FormItem>
				<FormItem style="text-align: left">
					<Button class="mr10" type="primary" @click="onSearch">筛选</Button>
					<Button type="default" @click="onResetSearch">重置</Button>
				</FormItem>
			</div>
		</Form>
		<div class="table-wrapper">
			<div class="panel-nav">
				<a :class="{active: queryFormData.type === 'IN'}" class="nav" @click.prevent.capture="onStatusChange('IN')">
					入库单
				</a>
				<a :class="{active: $route.query.type == 'OUT'}" class="nav" @click.prevent.capture="onStatusChange('OUT')">
					出库单
					<!-- <Tag color="default">{{ 333 }}</Tag> -->
				</a>
			</div>
			<Table :columns="tabCols" :data="list" :loading="tableLoading" :height="$store.state.app.clientHeight-275">
				<template slot="create_time" slot-scope="{row}">
					<p>{{ row.create_time | date_format('YYYY-MM-DD HH:mm:ss') }}</p>
				</template>
        <template slot="main_name" slot-scope="{row}">
          <p>{{ row.clinic|| '-'  }}</p>
        </template>
				<template slot="action" slot-scope="{row}">
					<a @click="$router.push({path: '/purchase/clinic/detail', query: {order_code: row.cli_order_code, id: row.id}})">详情</a>
				</template>
			</Table>
			<div class="block_20"></div>
			<KPage :current.sync="+queryFormData.page"
			       :page-size.sync="+queryFormData.pageSize"
			       :total="total"
			       style="text-align:center"
			       @on-change="handleCurrentChange"
			       @on-page-size-change="handleSizeChange"
			/>
		</div>
	
	</div>
</template>

<script>
import S from 'utils/util'
import search from '@/mixins/search'
import clinicSearch from '@/components/clinic-search/clinic-search'
import supplierSearch from '@/components/supplier-search/supplier-search'

const init_query_form_data = {
	page: 1,
	pageSize: 20,
	st: '', // 开始时间
	et: '', // 结束时间
	supplier_id: '', // 供应商id
	clinic_id: '', // 诊所id
	type: 'IN', // 出库还是入库
	r: '',
}
export default {
	name: 'list',
	mixins: [search],
	components: {clinicSearch, supplierSearch},
	data() {
		return {
			apiName: 'getTStockRrcordList', // 获取库存列表
			queryFormData: {...init_query_form_data},
			purchaseTimes: [],
			disabledTime: {
				disabledDate(date) {
					return date && date.valueOf() > Date.now()
				},
			},
			tabCols: [],
			tableCols_in: [
				{title: '入库单号', key: 'code', align: 'center'},
				{title: '入库时间', key: 'create_time', align: 'center'},
				{title: '审核时间', key: 'audit_time', align: 'center'},
				{title: '供应商', key: 'supplier', align: 'center'},
				{title: '入库员', key: 'creator', align: 'center'},
				{title: '供应商发货单号', key: 'ship_order_code', align: 'center'},
				{title: '采购商品', key: 'goods_names', align: 'center', tooltip: true},
        {title: '采购诊所', slot: 'main_name', align: 'center'},

        // {title: '入库总金额', key: 'money', align: 'center'},
				{title: '操作', slot: 'action', align: 'center', width: 80},
			],
			out_stock_cols: [
				{title: '出库单号', key: 'code', align: 'center'},
				{title: '出库时间', key: 'create_time', align: 'center'},
        {title: '审核时间', key: 'audit_time', align: 'center', },
        {title: '供应商', key: 'supplier', align: 'center'},
				{title: '出库员', key: 'creator', align: 'center'},
				{title: '诊所采购单号', key: 'cli_order_code', align: 'center'},
				{title: '采购商品', key: 'goods_names', align: 'center', tooltip: true},
        {title: '采购诊所', slot: 'main_name', align: 'center'},
        // {title: '出库总金额', key: 'money', align: 'center'},
				{title: '操作', slot: 'action', align: 'center',width: 80},
			],
		}
	},
	computed: {},
	watch: {},
	created() {
		this.queryFormData = S.merge(this.queryFormData, this.$route.query)
		this.tabCols = this.tableCols_in
		this.queryFormData = S.merge(this.queryFormData, this.$route.query)
		this.submitQueryForm(true)
		// 获取诊所采购单的枚举
		this.getTStockRrcordOptions()
	},
	mounted() {},
	methods: {
		onStatusChange(type) {
			if(type == 'OUT'){
				this.tabCols = this.out_stock_cols
			}else {
				this.tabCols = this.tableCols_in
			}
			this.queryFormData.page = 1
			this.queryFormData.type = type
			this.submitQueryForm()
		},
		onResetSearch() {
			this.queryFormData = {...init_query_form_data}
			this.purchaseTimes = []
			this.submitQueryForm()
			this.$refs['clinic-search'].clear()
			this.$refs['supplier-search'].clear()
		},
		purchaseTimesChange(times) {
			console.log('-> times', times)
			this.handleTimeChange(times)
		},
		// *api 获取诊所采购单枚举
		getTStockRrcordOptions () {
			this.$api.getTStockRrcordOptions().then( res => {
				console.log('getTStockRrcordOptions', res);
				
			} )
		}
	},
	beforeRouteUpdate(to, from, next) {
		// S.log(to.params['nav-stack-key-dir'], 'nav-stack-key-dir')
		this.queryFormData = S.merge(init_query_form_data, to.query)
		this.loadList()
		next()
	},
	
}
</script>

<style lang="less" scoped>

</style>
