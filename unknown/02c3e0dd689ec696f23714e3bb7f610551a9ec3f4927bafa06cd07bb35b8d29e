<template>
  <div class="flex picture-wrapper">
    <!-- <div v-if="pdfList.length" class="pdf-box picture-list"> -->
    <!--   <Tooltip content="点击链接在新窗口查看" v-for="img in pdfList" :key="img"> -->
    <!--     <a :href="img" target="_blank">{{ img }}</a> -->
    <!--     <div class="picture-list__remove" @click="onRemove(key)" v-if="!isQueryDetail"></div> -->
    <!--   </Tooltip> -->
    <!-- </div> -->
    <div class="picture-list" v-viewer="{ url: 'data-source' }">
      <draggable v-model="getImgUrls" style="display: inline" @change="onDragChange">
        <div class="picture-list__pic" v-for="(url, key) in getImgUrls" :key="key">
          <div v-if="pdfList(url)" style="position: absolute; width: 100%; height: 100%; left: 0">
            <Tooltip content="点击在新窗口查看">
              <a
                :href="url"
                target="_blank"
                style="
                  font-size: 50px;
                  text-align: center;
                  display: inline-block;
                  width: 100%;
                  padding-top: 6px;
                  padding-left: 6px;
                "
              >
                <svg-icon iconClass="pdf"></svg-icon>
              </a>
            </Tooltip>
          </div>
          <img
            v-else
            :src="url + '-B.w300'"
            :data-source="url"
            style="position: absolute; width: 100%; height: 100%; left: 0"
          />
          <div class="picture-list__remove" @click="onRemove(key)" v-if="!isQueryDetail"></div>
        </div>

        <div class="picture-display" v-if="!isQueryDetail" style="display: inline-block">
          <div class="picture-upload" v-if="showUploadBtn">
            <Upload v-if="!isQueryDetail" :multiple="multiple" @on-success="onUploadSuccess" v-bind="$attrs">
              <div v-if="!isQueryDetail" class="picture-upload-icon">
                <span>{{ getImgUrls.length }} / {{ limit }}</span>
              </div>
            </Upload>
          </div>
          <div>
            <slot style="align-self: self-end"></slot>
          </div>
        </div>
      </draggable>
    </div>

    <!-- 添加图片样式会换行,暂时手动调整 -->
  </div>
</template>

<script>
import Vue from 'vue';
import Upload from '../qiniu-upload/qiniu-upload.vue';
import draggable from 'vuedraggable';
import 'viewerjs/dist/viewer.css';
import './index.less';
import Viewer from 'v-viewer';
import imageUrl from 'utils/imageUrl';

Vue.use(Viewer);

export default {
  name: 'k-picture',
  components: {
    Upload,
    draggable
  },
  props: {
    limit: {
      type: Number,
      default: 1
    },
    value: {
      type: [String, Array],
      default() {
        return [];
      }
    },
    isQueryDetail: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      showUploadBtn: true,
      imgUrls: [],
      isArray: true
    };
  },
  created() {
    // console.log( this.isQueryDetail )
    // console.log("-> %c this.$attrs  === %o", "font-size: 15px;color: green;", this.$attrs)
  },
  methods: {
    onUploadSuccess: function (imgUrl) {
      if (this.imgUrls.length < this.limit) {
        this.imgUrls.push(imgUrl);
        this.notifyEmit();
      }
    },
    onRemove: function (key) {
      this.imgUrls.splice(key, 1);
      this.notifyEmit();
    },
    onDragChange: function () {
      this.notifyEmit();
    },
    notifyEmit: function () {
      this.$emit('input', this.isArray ? this.imgUrls : this.imgUrls[0] || '');
      this.$emit('change', this.isArray ? this.imgUrls : this.imgUrls[0] || '');
    }
  },
  computed: {
    multiple() {
      return this.limit <= 1 ? false : true;
    },
    pdfList() {
      return function (url) {
        let lastPath = url.split('.').pop();
        console.log(lastPath);
        if (lastPath == 'pdf') {
          return true;
        } else {
          return false;
        }
        // return this.imgUrls.filter(item => {
        //   return item.indexOf('.pdf') > -1;
        // });
      };
    },
    getImgUrls: {
      get() {
        const imgSuffix = ['jpg', 'jpeg', 'png', 'gif', 'pdf'];
        return this.imgUrls.filter(item => {
          const suffix = item.split('.').pop();
          return imgSuffix.includes(suffix);
        });
      },
      set(val) {
        this.imgUrls = val;
      }
    }
  },
  watch: {
    value: {
      immediate: true,
      handler: function () {
        if (typeof this.value == 'string') {
          this.isArray = false;
          this.imgUrls = this.value != '' ? [this.value] : [];
        } else {
          this.imgUrls = [...this.value];
        }
      }
    },

    imgUrls: {
      immediate: true,
      handler: function () {
        // imageUrl
        // console.log( this.imgUrls )
        for (let key in this.imgUrls) {
          this.imgUrls[key] = imageUrl.imageCdnDomain(this.imgUrls[key]);
        }
        this.showUploadBtn = this.imgUrls.length >= this.limit ? false : true;
      }
    }
  }
};
</script>

<style lang="less" scoped></style>
