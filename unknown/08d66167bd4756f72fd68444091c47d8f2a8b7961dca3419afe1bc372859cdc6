<template>
  <div class="operator-box">
    <div class="operator-item" v-for="(item, index) in visibleActions">
      <a v-if="item.tagType === 'a'" @click="handleClick(item)">{{ item.label }}</a>
      <Poptip v-if="item.tagType === 'Poptip'" confirm transfer :title="item.confirmText" @on-ok="handleClick(item)">
        <a>{{ item.label }}</a>
      </Poptip>
      <Divider type="vertical" v-show="index !== visibleActions.length - 1 || additionalActions.length" />
    </div>
    <div class="additional-actions" v-if="additionalActions.length">
      <Poptip placement="bottom" width="auto" popper-class="dropdown-pop" trigger="hover">
        <div class="content" slot="content">
          <div v-for="(item, index) in additionalActions" :key="item.label + index">
            <div class="addition-item" v-if="item.tagType === 'a'" @click="handleClick(item)">
              {{ item.label }}
            </div>
            <Poptip
              v-if="item.tagType === 'Poptip'"
              confirm
              transfer
              :title="item.confirmText"
              @on-ok="handleClick(item)"
            >
              <div class="addition-item">{{ item.label }}</div>
            </Poptip>
          </div>
        </div>
        <a class="rsj-more"></a>
      </Poptip>
    </div>
  </div>
</template>

<script>
export default {
  name: 'OperationFolding',
  props: {
    actions: {
      type: Array,
      default: () => [],
    },
    maxVisibleActions: {
      type: Number,
      default: 2,
    },
    row: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      key: [],
    };
  },
  computed: {
    realActions() {
      const idCodes = this.$store.state.router.idcodes;
      return this.actions.filter(item => !item.isHidden && (!item.eleCode || idCodes.includes(item.eleCode)));
    },
    visibleActions() {
      return this.realActions.length <= this.maxVisibleActions
        ? this.realActions
        : this.realActions.slice(0, this.maxVisibleActions);
    },
    additionalActions() {
      return this.realActions.length > this.maxVisibleActions ? this.realActions.slice(this.maxVisibleActions) : [];
    },
  },
  mounted() {},
  methods: {
    handleClick(item, index) {
      item.handler(item.params, item.index);
    },
  },
};
</script>

<style lang="less" scoped>
.operator-box {
  display: inline-flex;
  justify-content: flex-end;
  align-items: center;
}

.rsj-more::before {
  font-weight: bold;
  display: inline-block;
  font-size: 14px;
  content: '\00B7\00B7\00B7'; /* Unicode for three middle dots */
}

.addition-item {
  font-size: 14px;
  line-height: 20px;
  text-align: center;
  min-width: 88px;
  padding: 4px 16px;
  box-sizing: border-box;
  cursor: pointer;
  margin-bottom: 4px;
  margin-top: 4px;
  color: #115bd4;

  &:hover {
    background-color: #e6efff;
  }
}
</style>
<style lang="less">
.dropdown-pop {
  min-width: unset;
}
</style>
