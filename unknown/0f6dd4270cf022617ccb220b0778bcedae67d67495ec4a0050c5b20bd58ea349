<template>
  <Select
    ref="supplier"
    transfer
    :value="value"
    :clearable="isClearable"
    :loading="searchLoading"
    :remote-method="search"
    filterable
    @on-clear="clearSub"
    @on-query-change="queryChange"
    class="filterable-select"
    placeholder="请输入搜索供应商"
    @on-select="selectSup"
  >
    <Option
      v-for="(option, index) in supplier_list"
      :key="option.id"
      :value="option.id"
      >{{ option.name }}</Option
    >
  </Select>
  <!-- <Select :value="value" style="width:200px" @on-select="selectSup" @on-clear="clearSub" :clearable="isClearable" placeholder="请选择供应商">
		<Option v-for="(option, index) in supplier_list"  :key="option.id" :value="option.id">{{ option.name }}</Option>
	</Select> -->
</template>

<script>
import util from "@/utils/util";
export default {
  name: "supplier-search",
  components: {},
  mixins: [],
  props: {
    isClearable: {
      type: Boolean,
      default: true,
    },
    value: {
      type: String,
      default: "",
    },
  },
  data() {
    return {
      searchLoading: false,
      // supplierList: [],
      supplier_list: [],
      query: "",
    };
  },
  computed: {},
  watch: {},
  created() {
    if (!this.$route.query.clinic_id) {
      this.searchMethod();
    } else {
      let list = JSON.parse(localStorage.getItem("supplier_list")) || [];
      this.supplier_list = list;
      this.$emit("input", list[0] && list[0].id);
    }
  },
  mounted() {},
  methods: {
    searchMethod: util.debounce(function (query) {
      this.searchLoading = true;
      this.$api.getCommonSupplierList({ name: query }).then(res => {
        this.searchLoading = false;
        this.supplier_list = res.list;
        localStorage.setItem(
          "supplier_list",
          JSON.stringify(this.supplier_list),
        );
      });
    }, 200),
    search() {},
    selectSup(val) {
      console.log("val", val);

      this.$emit("input", val.value);
    },
    queryChange(val) {
      this.searchMethod(val);
    },
    clear() {
      this.$refs["supplier"].clearSingleSelect();
    },
    clearSub() {
      this.searchMethod();
      this.$emit("input", "");
    },
  },
};
</script>

<style lang="less" scoped>
.filterable-select {
  ::v-deep .ivu-select-input {
    margin-top: -1px;
  }
}
</style>
