<template>
  <Modal
    :value="visible"
    :mask-closable="false"
    @on-visible-change="changeVisible"
    :title="title || (isAudit ? '审核驳回' : '作废')"
  >
    <div style="width: 100%">
      <p class="label mb10">请输入{{ refuseText || (isAudit ? '驳回' : '作废') }}原因：</p>
      <Input v-model="reason" :autosize="{ minRows: 6, maxRows: 6 }" style="max-width: 1000px" type="textarea" />
    </div>

    <div slot="footer">
      <Button @click="closeModal">取消</Button>
      <Button v-loading="loading" type="primary" @click="onRefuse">确定</Button>
    </div>
  </Modal>
</template>

<script>
export default {
  name: 'index',
  mixins: [],

  components: {},

  props: {
    visible: {
      type: Boolean,
      default: false
    },
    auditSalesOrder: {
      type: Function
    },
    invalidSalesOrder: {
      type: Function
    },
    isAudit: {
      type: Boolean,
      default: true
    },
    refuseText: {
      type: String,
      default: ''
    },
    title: {
      type: String,
      default: ''
    },
    loading: {
      type: Boolean,
      default: false
    }
  },

  data() {
    return {
      reason: '' //原因
    };
  },

  computed: {},

  watch: {},

  created() {},

  mounted() {},

  methods: {
    changeVisible(visible) {
      visible && (this.reason = '');
      !visible && this.closeModal();
    },
    closeModal() {
      this.$emit('update:visible', false);
    },
    onRefuse() {
      if (!this.reason) {
        this.$Message.error(`请输入${this.refuseText || (this.isAudit ? '驳回' : '作废')}原因`);
        return;
      }
      if (this.isAudit) {
        this.auditSalesOrder('REJECT', this.reason);
      } else {
        this.invalidSalesOrder('INVALID', this.reason);
      }
      this.closeModal();
    }
  },

  destroyed() {}
};
</script>

<style scoped lang="less"></style>
