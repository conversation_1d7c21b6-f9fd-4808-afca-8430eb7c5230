import {merge} from 'lodash';
import { number_format } from '@/utils/filters'; 

const areaLineOptions = (data = {}, purTitle, isMoney = false) => {
  console.log("-> data", data)
  const defaultConfig = {
    grid: {
      top: '10%',
      left: '3%',                
      right: '4%',                
      bottom: '10%',                
      // containLabel: true
      show: true,
      borderWidth: 0
    },
    tooltip: {
      show: true, // 开启悬浮框
      trigger: 'axis',
      position: function(point, params, dom, rect, size) {
        // 其中point为当前鼠标的位置，size中有两个属性：viewSize和contentSize，分别为外层div和tooltip提示框的大小
        var x = point[0] //
        var y = point[1]
        var viewWidth = size.viewSize[0]
        var viewHeight = size.viewSize[1]
        var boxWidth = size.contentSize[0]
        var boxHeight = size.contentSize[1]
        var posX = 0 // x坐标位置
        var posY = 0 // y坐标位置

        if (x < boxWidth) { // 左边放不开
          posX = 5
        } else { // 左边放的下
          posX = x - boxWidth/2
        }

        if (y < boxHeight) { // 上边放不开
          posY = 5
        } else { // 上边放得下
          posY = y - boxHeight - 25
        }

        return [posX, posY]
      },
      padding: 1, // 边框距离内容的距离
      left: 'center',
      backgroundColor: '#CCCCCC', // 边框色
      borderRadius:4,
      formatter: function (param) { // 自定义tooltip
        var text = '';
        let _symbol = isMoney ? '￥' : ''
        let value = isMoney ? number_format(param[0].value) : param[0].value
        text += `<div style="background: #fff;padding: 8px;border-radius:4px;position: relative;min-width: 100px">
              <div style="color:#999999;height:auto;width:100%;margin-right:5px;font-size:10px">${purTitle}</div>
              <div style="color:#000000;font-size:14px;font-weight:600;margin-top:4px;line-height:20px"><span>${_symbol}${value}</span></div>
              <div style="position: absolute;bottom:-11px;left:43%;width:0;height:0;border-top:11px solid #ccc;border-left:11px solid transparent;border-right:11px solid transparent">
              <div style="position: absolute;top:-11px;left:-10px;width:0;height:0;border-top:10px solid #fff;border-left:10px solid transparent;border-right:10px solid transparent"><div>
              <div>
              </div>`
        return text;
      },
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      splitLine: {
        show: true, // 开启x轴的网格线
        lineStyle: {
          color: '#ccc',
          opacity: .2,
        }
      },
      axisTick: {
        show: false, // 去除轴刻度线,
      },
      axisLabel: {
        color: '#999999', // 自定义x轴label颜色
      },
      axisLine: {
        show: false, // 去除x轴
      },
      data: []
    },
    yAxis: {
      type: 'value',
      show: false, // 去除y轴整体
      splitLine: {
        show: false, // 关闭y轴的网格线
      },
      axisTick: {
        show: false, // 去除轴刻度线,
      },
      axisLine: {
        show: false, // 去除y轴
      },
    },
    series: [
      {
        itemStyle: {
          normal: {
            color: '#E88E6D', // 拐点颜色
            borderColor: '#E88E6D', // 拐点边框色
            borderWidth: 3, // 拐点边框大小
            lineStyle:{
              color: '#E88E6D', // 设置线条颜色
              width: 2
            }
          }
        },
        data: data.seriesData,
        type: 'line',
        symbolSize: 10, // 设置拐点大小
        showSymbol:false, // 鼠标选中悬浮才会显示拐点
        areaStyle: {
          color: '#E88E6D', // 设置面积色
          opacity: .1, // 设置面积色透明度
        },  
        smooth: true
      }
    ],
  }
  const opt = merge(defaultConfig,data)
  return opt
}

export default {
  areaLineOptions,
}