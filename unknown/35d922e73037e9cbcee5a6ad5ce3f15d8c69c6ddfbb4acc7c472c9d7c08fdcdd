<template>
  <div class="video-upload">
    <div v-if="!videoFile" class="upload-trigger" @click="startUpload">
      <svg-icon name="upload-icon" style="font-size: 24px"></svg-icon>
      <input
        @change="handleFileChange"
        ref="fileInput"
        style="display: none"
        type="file"
        accept=".mp4, .mov, .m4v, .flv, .x-flv, .mkv, .wmv, .avi, .rmvb, .3gp"
      />
    </div>
    <div v-else class="local-video-card">
      <div class="video-name">{{ videoFile.name }}</div>
      <div class="video-size">{{ getFileSize }}MB</div>
      <el-progress
        style="width: 100%"
        :percentage="videos.progress"
        :show-text="false"
        color="linear-gradient( 270deg, #2B54E6 0%, #568DF4 100%)"
      />
      <div class="text" v-if="!videos.stop && !videos.error">
        {{ videos.progress }}% <span style="margin-left: 10px">{{ getMb }}MB</span>
      </div>
      <svg-icon
        v-if="videos.startUpload !== 'start' && videoFile"
        class="upload-delete-icon"
        name="upload-delete"
        @click="deleteFile"
      ></svg-icon>
    </div>
    <div class="help-desc">视频大小不超过1G，支持mp4，mov，m4v，flv，x-flv，mkv，wmv，avi，rmvb，3gp格式</div>
  </div>
</template>

<script>
import { $operator } from '@/utils/operation';
import * as qiniu from 'qiniu-js';
import { getImageKey } from '@/utils/imageUrl';
import { getUlid } from '@/utils/runtime';

export default {
  name: 'VideoUpload',
  data() {
    return {
      videoFile: null,
      videos: {
        observable: null,
        subscription: null,
        domain: '',
        progress: 0,
        stop: false,
        error: false,
        startUpload: 'unstart',
        size: 0,
      },
    };
  },
  computed: {
    getFileSize() {
      return this.videoFile && $operator.divide(this.videoFile.size, 1024 * 1024, 2);
    },
    getMb() {
      const current = ((+this.videos.progress * +this.videoFile.size) / 100 / 1024 / 1024)?.toFixed(2) || 0;
      console.log(
        '%c=>(VideoUpload.vue:59) current',
        'background-color: green; color: #fff; padding: 4px 20px; border-radius: 4px;',
        current
      );
      const total = (+this.videoFile.size / 1024 / 1024)?.toFixed(2) || 0;
      console.log(
        '%c=>(VideoUpload.vue:61) total',
        'background-color: green; color: #fff; padding: 4px 20px; border-radius: 4px;',
        total
      );
      return +total <= 0 ? '' : `(${current}/${total})`;
    },
  },
  methods: {
    startUpload() {
      this.$refs.fileInput.click();
    },
    handleFileChange(e) {
      const files = e.target.files;
      console.log('%c [ file ]-36', 'font-size:13px; background:#b4d5ee; color:#f8ffff;', files);
      // 保存本地文件
      const size = files[0].size / (1024 * 1024);
      if (size > 1024) {
        this.$Message.error('视频大小不能超过1G');
        return;
      }

      this.videoFile = files[0];
    },
    deleteFile() {
      this.videoFile = null;
    },
    validateVideo() {
      if (!this.videoFile) {
        this.$Message.error('请选择一个视频上传');
        return false;
      }
      return true;
    },
    async handleUpload(options) {
      const file = this.videoFile;
      // const res_token = await getQiuniuUploadToken()
      try {
        const data = await this.$api.getAssetsUploadToken({ asset_type: 2 });
        this.qiniuInfo = data;
        const token = data?.token || '';
        if (!token) {
          this.$Message.error('获取七牛token失败!');
          return;
        }
        const config = {
          useCdnDomain: true,
          concurrentRequestLimit: navigator?.hardwareConcurrency || 3,
        };
        const key = getImageKey(file.name, 'video');
        const putExtra = {
          fname: file.name, // 文件原文件名
          customVars: {
            'x:assetType': '2',
            'x:categoryId': JSON.stringify(options.category_id),
            'x:appKey': options.appKey,
            'x:deviceId': getUlid(),
          },
        };
        this.videos.domain = data?.domain || '';
        this.videos.subscription?.unsubscribe();
        this.videos.subscription = null;
        this.videos.observable = null;
        this.videos.progress = 0;
        this.videos.error = false;
        this.videos.stop = false;
        this.videos.startUpload = 'start';

        try {
          this.videos.observable = qiniu.upload(file, key, token, putExtra, config);
          this.videos.subscription = this.videos.observable?.subscribe({
            error: err => {
              console.log(
                '%c=>(VideoUpload.vue:97) err',
                'background-color: green; color: #fff; padding: 4px 20px; border-radius: 4px;',
                err
              );
              this.$Message.error('上传出错， 请稍后再试');
              this.videos.error = true;
            },
            next: resp => {
              const percent = +resp?.total?.percent?.toFixed(2) || 0;
              if (this.videos.startUpload === 'start') {
                const progress = +percent > +this.videos.progress ? percent : this.videos.progress;
                this.$set(this.videos, 'progress', progress);
              }
            },
            complete: resp => {
              if (resp.data.key) {
                this.videos.progress = 100;
                this.videos.size = resp.size;
                this.videos.startUpload = 'done';
                this.$emit('uploadSuccess', resp.data);
              }
            },
          });
        } catch (e) {
          console.log(e, 'err');
        }
      } catch (e) {
        this.$Message.error('获取七牛token失败!');
        return;
      }
    },
    cancelUpload() {
      this.videos.subscription?.unsubscribe();
    },
  },
};
</script>

<style lang="less" scoped>
.local-video-card {
  position: relative;
  width: 160px;
  min-height: 80px;
  box-sizing: border-box;
  font-size: 14px;
  background-color: #f7f8fa;
  margin-bottom: 12px;
  margin-right: 6px;
  background-size: 100% 100%;
  padding: 10px 12px;
  line-height: 20px;

  .video-name {
    font-size: 14px;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
  }

  .video-size {
    font-size: 12px;
    height: 20px;
  }

  &:hover {
    .upload-delete-icon {
      display: block;
    }
  }

  .local-image {
    width: 100%;
    height: 100%;
    object-fit: contain;
  }

  .upload-delete-icon {
    display: none;
    position: absolute;
    right: -5px;
    top: -5px;
    width: 20px;
    height: 20px;
    cursor: pointer;
  }
}

.upload-trigger {
  border-radius: 2px;
  width: 80px;
  height: 80px;
  box-sizing: border-box;
  cursor: pointer;
  background-color: #f7f8fa;
  color: #646566;
  border-color: #dcdee0;
  display: flex;
  justify-content: center;
  align-items: center;
  border-width: 1px;
  margin-bottom: 12px;
}

.help-desc {
  color: #969799;
  line-height: 14px;
  font-size: 12px;
  margin-top: 10px;
  margin-bottom: 0;
}
</style>
