@import '~view-design/src/styles/index.less';
@radius: 2px;
@btn-border-radius: @radius;
@btn-border-radius-small: @radius;
@border-radius-base: 6px;
@border-radius-small: @radius;
@avatar-border-radius: @radius;
//@border-color-base: #bbb;  // outside
@input-border-color: #bbb;

@primary-color          : #155BD4;
@text-color             : #333;
@link-color             : #155BD4;
@font-size-base         : 12px;
@font-size-small        : 12px;
@font-size-large        : @font-size-base + 2px;

@animation-time         : .01s;
@animation-time-quick   : .01s;
@transition-time        : .10s;

@table-thead-bg               : #f2f2f2;

//@font-family: -apple-system,BlinkMacSystemFont,Helvetica Neue,Helvetica,Roboto,Arial,PingFang SC,Hiragino Sans GB,Microsoft Yahei,SimSun,sans-serif;
//@font-family: "Helvetica Neue",Helvetica,"PingFang SC","Hiragino Sans GB","Microsoft YaHei","微软雅黑",<PERSON><PERSON>,sans-serif;
@font-family: Helvetica, STHeiti, "Microsoft YaHei", Verdana, Arial, Tahoma, sans-serif;
.ivu-btn {
  background-image: linear-gradient(transparent 90%, rgba(0, 0, 0, 0.06));
}

.ks-table {
  .ivu-table th, .ivu-table td {
    height: auto;
    padding: 10px 0;
  }
  .ivu-table:before {
    background-color:transparent;
  }
  .ivu-table-header thead tr th {
    border-bottom: 2px solid #ddd;
  }
  .ivu-table th, .ivu-table td {
    border-bottom: 1px solid #ddd;
  }
}
.pl-table {
  .ivu-table-header thead tr th {
    &:hover {
      background-color: #ebf7ff;
    }
    .ivu-table-cell{
      display: block;
    }
  }
  .ivu-table-cell .ivu-table-cell-slot {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    word-break: break-all;
    box-sizing: border-box;
  }
  .ivu-table-cell span{
    overflow: hidden;
    text-overflow: ellipsis;
    word-break: break-all;
    white-space: nowrap;
  }
}
.ivu-table-cell {
  padding: 2px 8px 2px 8px;
}

.ivu-select-selection, .ivu-checkbox-inner {
  border: 1px solid #bbb;
}

.ivu-form-inline {
  .ivu-input-wrapper, .ivu-select {
    width: 180px;
  }
}
.ivu-input-wrapper, .ivu-select {
  max-width: 300px;
}
.ivu-form-item {
  //margin-bottom: 16px;
  margin-bottom: 24px;
  .ivu-input-wrapper, .ivu-select {
    max-width: 100%;
  }
}
.ivu-spin-fix {
  background-color: #ffffff80;
}

.alignCenter {
  text-align: center;
}

.marginTop10 {
  margin-top: 10px;
}

.marginRight10 {
  margin-right: 10px;
}

.ivu-divider, .ivu-divider-vertical {
  margin: 0 6px;
}

.ivu-btn-text {
  border-color: #dcdee2;
  &:hover {
    border-color: #dcdee2;
  }
}

// .ivu-form .ivu-form-item-label {
//   font-size: 14px;
// }
.ivu-input {
  border:1px solid #BCC3D7;
}
.ivu-input-number-handler-wrap {
  display: none;
}

.ivu-message-notice-content-error {
  display: inline-block;
  pointer-events: all;
  padding: 11px 16px;
  border-radius: 2px;
  background: #fffbfb;
  position: relative;
  box-shadow: 0 1px 3px #ddd;
  font-size: 14px;
  color: #d00505;
}

@import './assets/css/kif-admin.less';


