<template>
  <Modal v-model="modalValue"
         title="选择服务"
         :mask-closable="false"
         :width="750"
         class-name="vertical-center-modal"
  >
    <div style="position: relative;">
      <Button to="/goods/service/list" target="_blank">服务列表</Button>
      <Dvd/><Dvd/>
      <a @click="onRefresh">刷新</a>
      <div style="position: absolute; right: 0px; top: 0px;">
        <Input v-model="queryFormData.keyword" placeholder="输入服务名称"
               clearable
               @keyup.enter.native="onSearch" @on-clear="onSearch"
               style="width: 180px">
          <Icon type="ios-search" slot="suffix" />
        </Input>
      </div>
    </div>
    <div class="block_10"></div>
    <div style="position: relative; height: 345px; overflow: auto;">
      <Table ref="selection"
             @on-select="onSelect" @on-select-cancel="onSelectCancel"
             @on-select-all="onSelectAll" @on-select-all-cancel="onSelectAllCancel"
             :columns="tableCols" :data="list" :loading="tableLoading">
        <template slot-scope="{row}" slot="info">
            <span class="clip">{{row.name}}</span>
        </template>
        <template slot-scope="{row}" slot="price">
          <p v-if="row.price">￥{{row.price|number_format}}</p>
          <p v-else>-</p>
        </template>
      </Table>

      <div class="block_20"></div>

      <KPage v-if="total > 0" :total="+total"
             :page-size="queryFormData.pageSize"
             :page-size-opts="[5]"
             :current="queryFormData.page"
             @on-change="handleCurrentChange"
             @on-page-size-change="handleSizeChange"
             style="text-align:center"
      />
    </div>
    <div slot="footer">
      <div v-if="Object.keys(selected_items).length > 0" style="display: inline-block;" class="lr15 text-muted">
        已选: 服务(<span class="text-error">{{Object.keys(selected_items).length}}</span>)
      </div>
      <Button @click="modalValue=false">取消</Button>
      <Button type="primary" @click="onConfirm">确定</Button>
    </div>
  </Modal>
</template>

<script>
/* eslint-disable */
import S from '@/utils/util' // Some commonly used tools
import io from "@/utils/request" // Http request
import * as runtime from '@/utils/runtime' // Runtime information
/* eslint-disable */

let init_query_from_data = {
  page: 1,
  pageSize: 5,
  keyword: '',
  status: 'ON',
}

export default {
  name: "k-goods-services",
  props: {
    value: {
      type: Boolean,
      default: false
    },
    disabledItemIds: {
      type: Array,
      default () {
        return []
      }
    },
    checkedService: {
      type: Array,
      default (){
        return []
      }
    }
  },

  data() {
    return {
      queryFormData: {...init_query_from_data},
      modalValue: false,

      tableCols: [
        {type: 'selection', width: 60},
        {title: '服务', slot: 'info'},
        {title: '类型', key: 'serv_type_text'},
        {title: '来源', key: 'source_platform_text'},
        {title: '价格', slot: 'price', width: 100},
      ],
      tableLoading: false,

      list: [],
      total: 0,
      statusDesc: {},

      selected_items: {},
    }
  },

  methods: {
    onSearch: function () {
      this.queryFormData.page = 1
      this.get()
    },

    handleSizeChange( val ) {
      console.log( '-> val', val )
      this.queryFormData.page = 1
      this.queryFormData.pageSize = val
      this.get()
    },
    handleCurrentChange( val ) {
      this.queryFormData.page = val
      this.get()
    },

    onRefresh: function () {
      this.get()
    },

    onSelect: function (selection, row) {
      this.$set(this.selected_items, row.id, row)
    },

    onSelectAll: function (selection) {
      selection.forEach(item => {
        this.$set(this.selected_items, item.id, item)
      })
    },

    onSelectCancel: function (selection, row) {
      this.$delete(this.selected_items, row.id)
    },

    onSelectAllCancel: function (selection) {
      for(let k in this.list) {
        this.$delete(this.selected_items, this.list[k].id)
      }
    },

    onConfirm: function () {
      let items = []
      for (let key in this.selected_items) {
        items.push(this.selected_items[key])
      }
      this.$emit('on-selected', items)
    },

    get: function () {
      this.tableLoading = true

      io.get('pms_opc/goods.libgoodsservice.list', {data: {...this.queryFormData}}).then(data => {
        this.list = this.handler(data.list)
        this.total = data.total

        this.tableLoading = false
      }).catch(error => {
        this.$Message.error(error.errmsg)
      })
    },

    handler: function (list) {
      for(let k in list) {
        for(let j in this.selected_items) {
          if (list[k].id == this.selected_items[j].id) {
            list[k]['_checked'] = true // 选中已选项
          }
        }

        if (S.inArray(Number(list[k].id), this.disabledItemIds)) {
          list[k]['_disabled'] = true // 选中已选项
        }
      }
      return list
    },

    clearQuery: function () {
      this.queryFormData = {...init_query_from_data}
      this.queryFormData.page = 1
      this.list = []
      this.total = 0
      this.selected_items = {}
    }
  },

  watch: {
    value: function (val) {
      this.modalValue = val
      if (val == true) {
        this.clearQuery()
        // 选中渲染
        this.onSelectAll(this.checkedService)
        this.get()
      }
    },

    modalValue: function (val) {
      this.$emit('input', val)
    },

    selected_items: function (val) {
      // S.log(this.selected_items)
    }
  }
}
</script>

<style lang="less">
.vertical-center-modal{
  display: flex;
  align-items: center;
  justify-content: center;
  .ivu-modal{
    top: 0;
  }
  //.ivu-modal-body {
  //  padding-bottom: 0;
  //}
}
.clip {
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
  width: 90%;
  display: inline-block;
}
</style>
