<template>
  <div class="flex" style="margin-top: 1px">
    <Select
      v-if="showType"
      v-model="ent_type"
      style="width: 120px"
      @on-change="changeEntType"
      placeholder="全部"
      clearable
    >
      <Option v-for="(item, index) in typeList" :key="index" :value="item.value" v-if="item.value !== 'OUTSIDE' || (item.value === 'OUTSIDE' && showOutside)">{{ item.label }}</Option>
    </Select>
    <Select
      ref="surning"
      transfer
      :value="value"
      :clearable="isClearable"
      :loading="searchLoading"
      :remote-method="search"
      filterable
      @on-clear="clearSub"
      @on-query-change="queryChange"
      class="filterable-select"
      :placeholder="placeholder"
      @on-select="selectSup"
    >
      <Option value="" v-if="showAll">全部</Option>
      <Option v-for="(option, index) in other_surning_list" :key="index" :value="option.ent_code">{{
        option.ent_name
      }}</Option>
    </Select>
  </div>
</template>

<script>
import util from '@/utils/util';
export default {
  name: 'surning-search',
  components: {},
  mixins: [],
  props: {
    isClearable: {
      type: Boolean,
      default: true
    },
    value: {
      type: String,
      default: ''
    },
    showAll: {
      type: Boolean,
      default: true
    },
    type: {
      type: String,
      default: ''
    },
    // prepayment: 表示放出省公司，默认不放
    source: {
      type: String,
      default: ''
    },
    placeholder: {
      type: String,
      default: '请输入对方主体名称'
    },
    // 展示采购主体类型，目前只开放采购单，后续全部放开可移除该属性
    showType: {
      type: Boolean,
      default: false
    },
    p_ent_type: {
      type: String,
      default: ''
    },
    showOutside: {
      type: Boolean,
      default: false
    },
    typeList: {
      type: Array,
      default: () => {
        return [
          { label: '直营运营中心', value: 'OPC' },
          // { label: '省公司', value: 'COM' },
          { label: '上海树家', value: 'PLAT' },
        ]
      }
    },
    // 默认补充参数
    default_params: {
      type: Object,
      default: () => {}
    },
    // 默认搜索字段，name支持搜索名称，nameId 支持名称+id搜索
    searchKeyword: {
      type: String,
      default: 'name'
    },
    // 外部绑定的字段
    routerKey: {
      type: String,
      default: 'ent_code'
    },
    // 存储在localStoage里面的值，用于刷新回显
    localStorageListName: {
      type: String,
      default: 'other_surning_list'
    },
  },
  data() {
    return {
      searchLoading: false,
      other_surning_list: [],
      query: '',
      // currentLabel: '20155b65-2ea3-44c5-9897-d0efaefbffae',
      ent_type: '',
      time: 0,
    };
  },
  computed: {},
  watch: {
    p_ent_type: {
      handler: function (val, oldVal) {
        this.ent_type = val;
      },
      immediate: true
    }
  },
  created() {
    setTimeout(() => {
      this.time = 200
    }, 10)
    if (!this.$route.query[this.routerKey]) {
      this.searchMethod();
    } else {
      this.$nextTick(() => {
        let list = JSON.parse(localStorage.getItem([this.localStorageListName])) || [];
        this.other_surning_list = list;
        this.$emit('input', list[0] && list[0].ent_code);
        this.$emit('getType', list[0] && list[0].ent_type);
        // 用label代替id进行二次搜索
        setTimeout(() => {
          this.$refs.surning.query = list[0] && list[0].ent_name
        },0)
      })
    }
  },
  mounted() {},
  methods: {
    searchMethod (query) {
      this.time == 0 ? this.unDebounceSearchMethod(query) : this.debounceSearchMethod(query)
    },
    debounceSearchMethod: util.debounce(function (query) {
      this.searchLoading = true;
      let params = {
        [this.searchKeyword]: query || '',
        ent_type: this.ent_type,
        source: this.source || '',
        fill_outside: this.showOutside ? 1 : '',
        ...this.default_params
      };
      this.$api.getAWSearchSide(params).then(res => {
        this.searchLoading = false;
        this.other_surning_list = res.list;
        localStorage.setItem([this.localStorageListName], JSON.stringify(this.other_surning_list));
      });
    }, 200),
    unDebounceSearchMethod (query) {
      this.searchLoading = true;
      let params = {
        [this.searchKeyword]: query || '',
        ent_type: this.ent_type,
        source: this.source || '',
        fill_outside: this.showOutside ? 1 : '',
        ...this.default_params
      };
      this.$api.getAWSearchSide(params).then(res => {
        this.searchLoading = false;
        this.other_surning_list = res.list;
        localStorage.setItem([this.localStorageListName], JSON.stringify(this.other_surning_list));
      });
    },
    search() {},
    selectSup(val) {
      console.log("%c=>(surning-search.vue:180) val",'font-size: 18px;color: #FF7043 ;', val);
      // let type = '';
      // this.other_surning_list.some(item => {
      //   if (item.ent_code == val.value) {
      //     type = item.ent_type;
      //   }
      // });
      const item = this.other_surning_list.find(item => item.ent_code == val.value)
      this.$emit('input', val.value);
      this.$emit('getType', this.ent_type || item.ent_type);
      this.$emit('change', val,item);
      // this.currentLabel = val.label;
    },
    queryChange(val) {
      // if (val === this.currentLabel) return;
      this.searchMethod(val);
    },
    clear() {
      // 列表页重置时必须使用
      this.$refs['surning'].clearSingleSelect();
      this.ent_type = '';
    },
    clearSub() {
      this.searchMethod();
      this.$emit('input', '');
      // 当展示前置搜索条件的时候，需要清除type类型
      if ( !this.showType ) {
        this.$emit('getType', '');
      }
      this.$emit('change', '');
    },
    changeEntType() {
      this.$emit('input', '');
      this.$emit('getType', this.ent_type);
      this.searchMethod();
    }
  }
};
</script>

<style lang="less" scoped>
.filterable-select {
  ::v-deep .ivu-select-input {
    margin-top: -1px;
  }
}
</style>
