<!-- 中医备案证名称的远程搜索 -->
<template>
  <Select
    ref="tcm"
    transfer
    :value="value"
    :clearable="isClearable"
    :loading="searchLoading"
    :remote-method="search"
    filterable
    :transfer-class-name="className"
    :disabled="disabled"
    @on-clear="clearSub"
    @on-query-change="queryChange"
    class="filterable-select"
    :placeholder="placeholder"
    :style="{ width: getWidth, height }"
    @on-select="selectSup"
  >
    <Option v-for="(option, index) in tcm_list" :key="option.enter_id" :value="option.enter_id">{{
      option.tcm_name
    }}</Option>
  </Select>
</template>

<script>
import util from '@/utils/util';
export default {
  name: 'tcm-name-search',
  components: {},
  mixins: [],
  props: {
    disabled: {
      type: Boolean,
      default: false
    },
    showAll: {
      type: Boolean,
      default: true
    },
    isClearable: {
      type: <PERSON>olean,
      default: true
    },
    value: {
      type: String,
      default: ''
    },
    width: {
      type: Number | String,
      default: 200
    },
    height: {
      type: String,
      default: '32px'
    },
    placeholder: {
      type: String,
      default: '请输入搜索中医备案证名称'
    },
    className: {
      type: String,
      default: ''
    },
    // 当传入的tcmName存在值的时候，说明要执行回显
    tcmName: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      searchLoading: false,
      // supplierList: [],
      tcm_list: [],
      query: ''
    };
  },
  computed: {
    getWidth() {
      if (typeof this.width === 'number') {
        return this.width + 'px';
      } else {
        return this.width;
      }
    }
  },
  watch: {},
  created() {
    if (!this.tcmName) {
      if (!this.$route.query.id) {
        this.searchMethod('');
      } else {
        // 用户详情回显
        let list = JSON.parse(localStorage.getItem('tcm_list')) || [];
        this.tcm_list = list;
        this.$emit('input', list[0] && list[0].ent_id);
      }
    }
  },
  mounted() {},
  methods: {
    searchMethod: util.debounce(function (query) {
      this.searchLoading = true;
      let params = {
        tcm_name: query,
        filter_tcm: 1
      };
      this.$api.getCommonClinicList(params).then(res => {
        this.searchLoading = false;

        this.tcm_list = res.list;
        localStorage.setItem('tcm_list', JSON.stringify(this.tcm_list));
      });
    }, 200),
    search() {},
    selectSup(val) {
      this.$emit('input', val.value);
      this.$emit('change', val.value);
    },
    queryChange(val) {
      if (this.tcmName) {
        this.searchMethod(this.tcmName);
        this.$emit('clearTcmName');
      } else {
        this.searchMethod(val);
      }
    },
    clear() {
      this.$refs.tcm.clearSingleSelect();
    },
    clearSub() {
      this.searchMethod();
      this.$emit('input', '');
      this.$emit('change', '');
    }
  }
};
</script>

<style lang="less" scoped>
.filterable-select {
  ::v-deep .ivu-select-input {
    margin-top: -1px;
  }
}
</style>
