<template>
  <Modal
      :value="value"
      width="950"
      footer-hide
      lock-scroll
      :mask-closable="false"
      @on-visible-change="visibleChange"
      >
      <div slot="header" class="flex auto-p">
        <p>物流包裹发货详情</p>
        <p class="ml10 tip-text">{{ express_name }}</p>
        <p class="ml10 tip-text">{{ express_no }}</p>
      </div>
      <div class="content" v-if="value">
        <Table :columns="columns1" :data="list" :loading="tableLoading" :height="500">
          <!-- 商品批号 -->
          <template slot-scope="{row, index}" slot="batch_code">
            <span>{{ row.batch_code || '-'}}</span>
          </template>
          
          <!-- 生产时间 -->
          <template slot-scope="{row, index}" slot="prod_date">
            <span>{{ row.prod_date | date_format("YYYY-MM-DD") }}</span>
          </template>

          <!-- 过期时间 -->
          <template slot-scope="{row, index}" slot="expire_date">
            <span>{{ row.expire_date | date_format("YYYY-MM-DD") }}</span>
          </template>
        </Table>
      </div>
  </Modal>
</template>

<script>
  export default {
    name: "logisticsDetail",
    components: {

    },
    mixins: [],
    props: {
      value: {
        type: Boolean,
        default: () => false
      },
      logisticsData: {
        type: Object,
        default: () => {
          return {
            
            pack_index: '',
            order_code: ''
            
          }
        }
      }
    },
    data () {
      return {
        express_name: '',
        express_no: '',
        // 物流包裹详情弹窗
        columns1: [
          { title: '商品编号', key: 'sku_code', align: 'center', minWidth: 70 },
          { title: '商品', key: 'generic_name', align: 'center' },
          { title: '商品类型', key: 'prod_type_desc', align: 'center' },
          { title: '规格', key: 'prod_spec', align: 'center' },
          { title: '商品单位', key: 'pack_unit', align: 'center', width: 80 },
          { title: '商品批号', slot: 'batch_code', align: 'center', minWidth: 70 },
          { title: '生产日期', slot: 'prod_date', align: 'center' },
          { title: '过期时间', slot: 'expire_date', align: 'center' },
          { title: '发货数量', key: 'num', align: 'center' },
        ],
        list: [],
        tableLoading: false,
      }
    },
    computed: {
    },
    watch: {

    },
    created() {
    },
    mounted() {

    },
    methods: {
      /* 弹窗 */ 
      closeable () {
        this.$emit('input', false)
      },
      visibleChange (val) {
        if ( val ) {
          this.getList()
        }else{
          this.closeable()
        }
      },

      /* api */ 
      getList () {
        this.tableLoading = true
        let params = {
          order_code: this.logisticsData.order_code,
          pack_index: this.logisticsData.pack_index,
        }
        this.$api.getPackinfo(params).then( res => {
          this.list = res.info.list
          this.express_name = res.info.express_name
          this.express_no = res.info.express_no
        } ).catch( err => {
          this.$Message.error(err.errmsg)
        } ).finally( () => {
          this.tableLoading = false
        })
      }
    },
    filters: {

    }
  }
</script>
<style lang="less" scoped>
p {
  margin: 0;
}
.auto-p {
  p {
    width: auto;
  }
}
.tip-text {
  color: #ccc;
  font-size: 14px;
}
.ml10 {
  margin-left: 10px;
}
.mt20 {
  margin-top: 20px;
}
</style>