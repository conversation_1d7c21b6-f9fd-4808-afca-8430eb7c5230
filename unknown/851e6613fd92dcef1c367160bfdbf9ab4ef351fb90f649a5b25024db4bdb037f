<template>
  <Modal
    :value="confirmVisible"
    :title="title"
    width="430px"
    :closable="closable"
    @on-visible-change="changeVisible"
    :class-name="getModalClass"
    transfer
  >
    <div slot="header"></div>
    <div class="content" v-if="!isCustomContent">
      <div class="title">
        <Icon type="ios-help-circle" size="26" color="#ff9900"></Icon>
        <span class="h-title">{{ contentTitle }}</span>
      </div>
      <div class="content-text">
        <p>{{ contentText }}</p>
      </div>
    </div>
    <div class="content" v-else>
      <slot name="custom-content"></slot>
    </div>
    <div slot="footer">
      <Button @click="cancel" v-if="showCancelBtn">{{ cancelText }}</Button>
      <Button type="primary" @click="confirmPass">{{ okText }}</Button>
    </div>
  </Modal>
</template>

<script>
export default {
  name: 'confirmModal',
  mixins: [],

  components: {},
  model: {
    prop: 'confirmVisible',
    event: 'update:confirmVisible',
  },
  props: {
    title: {
      type: String,
      default: '提示',
    },
    contentTitle: {
      type: String,
      default: '通过审核',
    },
    confirmVisible: {
      type: Boolean,
      default: false,
    },
    isCustomContent: {
      type: Boolean,
      default: false,
    },
    contentText: {
      type: String,
      default: '确定要通过审核吗？',
    },
    cancelText: {
      type: String,
      default: '取消',
    },
    showCancelBtn: {
      type: Boolean,
      default: true,
    },
    okText: {
      type: String,
      default: '确认',
    },
    closable: {
      type: Boolean,
      default: true,
    },
    isVerticalModal: {
      type: Boolean,
      default: true,
    },
  },

  data() {
    return {};
  },

  computed: {
    getModalClass() {
      return this.isVerticalModal ? 'confirm-modal vertical-center-modal' : 'confirm-modal';
    },
  },

  watch: {},

  created() {},

  mounted() {},

  destroyed() {},

  methods: {
    cancel() {
      this.changeVisible(false);
    },
    confirmPass() {
      this.$emit('on-confirm');
    },
    changeVisible(val) {
      !val && this.$emit('update:confirmVisible', val);
    },
  },
};
</script>

<style scoped lang="less">
.confirm-modal {
  .content {
    padding: 0px;
  }
}

::v-deep .ivu-modal-header {
  display: none;
}

::v-deep .ivu-modal-footer {
  border-top: none;
}

.title {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
  padding-left: 10px;
  padding-top: 10px;

  .h-title {
    font-size: 15px;
    font-weight: 500;
    margin-left: 16px;
    line-height: 26px;
  }
}

.content-text {
  padding-left: 52px;
}
</style>
