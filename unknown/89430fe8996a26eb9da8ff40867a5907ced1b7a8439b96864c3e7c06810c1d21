<template>
  <Modal
    :title="getTitle"
    :value="visible"
    class-name="vertical-center-modal upload-modal"
    :mask-closable="false"
    footer-hide
    width="830px"
    @on-visible-change="onVisibleChange"
  >
    <div class="upload-wrapper" v-loading="loading">
      <component
        :is="currentCom"
        v-if="!loading && visible"
        :category-tree="categoryListTree"
        :find-path-to-root-by-id="findPathToRootById"
        :tree-map="treeMap"
        :current-category-id="currentCategoryId"
        :asset-type="assetType"
        :limit="limit"
        @close="closeModal"
        @uploadSuccess="uploadSuccess"
        @refreshCategoryList="initCategoryData"
      />
    </div>
  </Modal>
</template>

<script>
import ImageUploadForm from './ImageUploadForm.vue';
import VideoUploadForm from './VideoUploadForm.vue';
// import { getUid } from '@/utils/runtime';

export default {
  name: 'UploadModal',
  components: {
    VideoUploadForm,
    ImageUploadForm,
  },
  model: {
    prop: 'visible',
    event: 'on-visible-change',
  },
  props: {
    assetType: {
      type: Number,
      default: 1,
    },
    limit: {
      type: Number,
      default: 9,
    },
    visible: {
      type: Boolean,
      default: false,
    },
    currentCategoryId: {
      type: [Number, String],
      required: true,
    },
  },
  data() {
    return {
      categoryListTree: [],
      treeMap: null,
      loading: false,
    };
  },
  computed: {
    getTitle() {
      return `上传${this.assetType === 1 ? '图片' : '视频'}`;
    },
    currentCom() {
      return this.assetType === 1 ? 'ImageUploadForm' : 'VideoUploadForm';
    },
  },
  created() {},
  methods: {
    uploadSuccess(data) {
      this.$emit('uploadSuccess', data);
    },
    async onVisibleChange(visible) {
      if (visible) {
        await this.initCategoryData();
      } else {
        this.closeModal();
      }
    },
    closeModal() {
      this.$emit('on-visible-change', false);
    },
    initCategoryData() {
      this.loading = true;
      return this.$api
        .getMtCategoryAllList({
          asset_type: this.assetType,
        })
        .then(res => {
          this.categoryList = res.list;
          let list = this.categoryList;
          this.initCategoryStatus(list, this.currentCategoryId);
          this.flatTreeHandler(list);
          this.categoryListTree = this.buildTreeAndSearch(res.list);
        })
        .catch(err => {
          this.$Message.error(err.message);
        })
        .finally(() => {
          this.loading = false;
        });
    },
    initCategoryStatus(data, categoryId) {
      data.forEach(item => {
        if (item.level === 0 && item.type === 2 && item.id) {
          item.isOpen = true;
        }
        if (item.id === categoryId) {
          item.isOpen = true;
          if (item.parent_id) {
            this.initCategoryStatus(data, item.parent_id);
          }
        }
      });
    },
    buildTreeAndSearch(data, rootName = null) {
      const map = new Map();
      let rootNode = null; // 用于存储搜索到的根节点
      // 初始化 Map，并附加 children 属性
      data.forEach(item => {
        if (item.type === 2 && item.level === 0) {
          this.defaultEditId = item.id;
        }
        item.isOpen = item.isOpen || false;
        map.set(item.id, { ...item, children: [] });
      });
      // 构建树结构
      data.forEach(item => {
        if (map.has(item.parent_id)) {
          const parent = map.get(item.parent_id);
          parent.children.push(map.get(item.id));
        }
      });
      // 如果指定了 rootName，搜索该节点
      if (rootName) {
        rootNode = Array.from(map.values()).filter(node => node.name.includes(rootName));
      }

      // 如果未指定 rootName 或未找到目标节点，返回完整的树
      if (!rootName || !rootNode) {
        return Array.from(map.values()).filter(node => !map.has(node.parent_id));
      }
      // 返回以指定节点为根的子树
      return rootNode;
    },
    findPathToRootById(id) {
      const path = [];
      // 通过 id 和 parentId 构建路径
      let currentId = id;
      // let currentParentId = parentId;
      while (currentId) {
        const node = this.treeMap.get(currentId);
        if (!node) break; // 如果找不到，结束查找
        path.unshift(node); // 将当前节点添加到路径前面
        currentId = node.parent_id; // 更新为父节点的 ID
      }
      return path;
    },
    // 将树结构平铺成 Map，方便查找
    flatTreeHandler(data) {
      const map = new Map();
      data.map(item => {
        map.set(item.id, item);
      });
      this.treeMap = map;
    },
  },
};
</script>

<style lang="less" scoped>
.upload-wrapper {
  height: 100%;
}
</style>
<style lang="less">
.upload-modal {
  .ivu-modal-body {
    height: 510px;
  }
}
</style>
