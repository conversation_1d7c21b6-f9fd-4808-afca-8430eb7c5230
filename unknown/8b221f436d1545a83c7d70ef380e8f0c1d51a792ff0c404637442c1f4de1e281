<template>
	<Select
      ref="partner"
			transfer
			:value="value"
			:clearable="isClearable"
			:loading="searchLoading"
			:remote-method="search"
			filterable
      :transfer-class-name="className"
      :disabled="disabled"
			@on-clear="clearSub"
			@on-query-change="queryChange"
			class="filterable-select"
			:placeholder="placeholder"
      :style="{width:getWidth,height}"
			@on-select="selectSup">
<!--    <Option value="" v-show="showAll">全部城市合伙人</Option>-->
		<Option v-for="(option, index) in partner_list" :key="option.id" :value="option.id">{{ option.name }}</Option>
	</Select>
</template>

<script>
import util from '@/utils/util'
export default {
	name: 'partner-search',
	components: {},
	mixins: [],
	props: {
    disabled: {
      type: Boolean,
      default: false
    },
    showAll: {
      type: Boolean,
      default: true
    },
		isClearable: {
			type: <PERSON>olean,
			default: true,
		},
		value: {
			type: String,
			default: ''
		},
    width: {
      type: Number|String,
      default: 200
    },
    height: {
      type: String,
      default: '32px'
    },
    placeholder: {
      type: String,
      default: '请输入搜索城市合伙人'
    },
    className: {
      type: String,
      default: ''
    }
	},
	data() {
		return {
			searchLoading: false,
			// supplierList: [],
			partner_list: [],
			query: ''
		}
	},
	computed: {
    getWidth(){
      if(typeof this.width === 'number'){
        return this.width+ 'px'
      }else {
        return this.width
      }
    }
	},
	watch: {},
	created() {
		if (!this.$route.query.company_id) {
			this.searchMethod()
		}else{
			let list = JSON.parse(localStorage.getItem('partner_list')) || []
			this.partner_list = list
			this.$emit('input', list[0] && list[0].id)
		}
	},
	mounted() {},
	methods: {
		searchMethod: util.debounce(function (query) {
			this.searchLoading = true
			this.$api.getPartnerList({name: query, status: 'PASS'}).then(res => {
				this.searchLoading = false

				this.partner_list = res.list
				localStorage.setItem('partner_list', JSON.stringify(this.partner_list))
			})
		},200),
		search () {},
		selectSup(val) {
      console.log("-> val", val)
			this.$emit('input', val.value)
      this.$emit('change', val.value)
		},
		queryChange (val) {
      console.log("-> val", val)
      if(val === '全部城市合伙人'){
        this.searchMethod('')
        return
      }
			this.searchMethod(val)
		},
		clear () {
			this.$refs.partner.clearSingleSelect()
		},
		clearSub(){
			this.searchMethod()
			this.$emit('input','')
      this.$emit('change', '')
    }
	},
}
</script>

<style lang="less" scoped>
.filterable-select{
	::v-deep .ivu-select-input{
		margin-top: -1px;
	}
}
</style>
