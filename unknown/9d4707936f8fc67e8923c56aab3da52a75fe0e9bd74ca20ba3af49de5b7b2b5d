module.exports = {
  "semi": true, // 语句末尾添加分号
  "singleQuote": true, // 使用单引号代替双引号
  "trailingComma": 'none', // 在对象或数组最后一个元素后面是否加逗号（在ES5中加尾逗号）
  "bracketSpacing": true, // 在对象，数组括号与文字之间加空格 "{ foo: bar }"
  "jsxBracketSameLine": false, // 在jsx中把'>' 是否单独放一行
  "arrowParens": 'avoid', // (x) => {} 是否要有小括号
  "printWidth": 120, // 一行的字符数，如果超过会进行换行，默认为80
  "tabWidth": 2, // 一个tab代表几个空格数
  "useTabs": false // 启用tab缩进
}
