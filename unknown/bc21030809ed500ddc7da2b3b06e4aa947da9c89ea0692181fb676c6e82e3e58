export default {
  name: 'text-format',
  bind(el, binding) {
    formatText(el, binding);
  },
  update(el, binding) {
    formatText(el, binding);
  }
};

const formatText = (el, binding) => {
  let displayWhenEmpty = binding.arg || '-';

  if (binding.value == null || binding.value === '') {
    el.innerHTML = displayWhenEmpty;
  } else {
    if (binding.modifiers.number) {
      const precision = binding.arg || 2;
      el.innerHTML = '￥' + formatNumber(binding.value, precision);
    } else {
      el.innerHTML = binding.value;
    }
  }
};
// 数字精读，补全
export const formatNumber = (value, decimalPlaces) => {
  let a = '',
    b,
    c,
    i;
  if (!value) {
    value = 0;
  }
  a = value.toString();
  b = a.indexOf('.');
  c = a.length;
  if (decimalPlaces === 0) {
    if (b !== -1) {
      a = a.substring(0, b);
    }
  } else {
    //如果没有小数点
    if (b === -1) {
      a = a + '.';
      for (i = 1; i <= decimalPlaces; i++) {
        a = a + '0';
      }
    } else {
      //有小数点，超出位数自动截取，否则补0
      a = a.substring(0, b + decimalPlaces + 1);
      for (i = c; i <= b + decimalPlaces; i++) {
        a = a + '0';
      }
    }
  }
  return a;
};
