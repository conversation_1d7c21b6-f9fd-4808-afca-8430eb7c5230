<template>
  <div style="display: inline-block">
    <draggable v-model="getImgUrls" :disabled="disabled" handle=".image-item" @change="onDragChange" filter=".no-drag">
      <div v-for="(img, index) in getImgUrls" :key="img + index" class="image-item" @click="previewImg(index)">
        <img :src="img + '-B.w300'" :alt="img" />
        <div v-if="!disabled" class="delete-icon" @click="deleteImg(index)"></div>
      </div>
      <div v-if="getLimit > 0 && !disabled" slot="footer" class="image-item no-drag" @click="myMaterialVisible = true">
        <div class="upload-v2">
          <div class="upload-v2-icon">
            <svg-icon style="font-size: 24px" name="upload-v2"></svg-icon>
          </div>
        </div>
      </div>
    </draggable>
    <my-material v-model="myMaterialVisible" :limit="getLimit" @uploadSuccess="uploadSuccess"></my-material>
  </div>
</template>

<script>
import draggable from 'vuedraggable';
import MyMaterial from './MyMaterial.vue';

export default {
  name: 'MaterialPicture',
  components: {
    draggable,
    MyMaterial
  },
  props: {
    limit: {
      type: Number,
      default: 9
    },
    disabled: {
      type: Boolean,
      default: false
    },
    value: {
      type: [String, Array],
      default() {
        return '';
      }
    }
  },
  data() {
    return {
      key: 'value',
      imgUrls: [],
      myMaterialVisible: false
    };
  },
  computed: {
    getLimit() {
      console.log(this.limit);
      return this.limit - this.imgUrls.length;
    },
    getImgUrls: {
      get() {
        // const imgSuffix = ['jpg', 'jpeg', 'png', 'gif'];
        // return this.imgUrls.filter(item => {
        //   // console.log(item, 'image item')
        //   const suffix = item.split('.').pop();
        //   // console.log(suffix, 'image suffix')
        //   return imgSuffix.includes(suffix);
        // });
        return this.imgUrls;
      },
      set(val) {
        this.imgUrls = val;
      }
    }
  },
  watch: {
    value: {
      immediate: true,
      handler: function () {
        if (typeof this.value == 'string') {
          let regUrl = (this.value && this.value.replace(/(\.jpg|\.png|\.gif|\.jpeg)-B\..*$/, '$1')) || '';
          this.imgUrls = this.value != '' ? [regUrl] : [];
        } else {
          if (this.value instanceof Array) {
            this.imgUrls = [];
            this.value.forEach(url => {
              this.imgUrls.push((url && url.replace(/(\.jpg|\.png|\.gif|\.jpeg)-B\..*$/, '$1')) || '');
            });
          } else {
            this.imgUrls = [];
          }
        }
      }
    }
    // imgUrls: {
    //   immediate: true,
    //   handler: function () {},
    // },
  },
  methods: {
    deleteImg(index) {
      this.imgUrls.splice(index, 1);
      this.$emit('on-remove', this.getImgUrls[index]);
      this.notify();
    },
    previewImg(index) {
      this.$viewerApi({
        options: {
          toolbar: true,
          initialViewIndex: index
        },
        images: this.imgUrls
      });
    },
    onDragChange() {
      this.notify();
    },
    uploadSuccess(data) {
      this.imgUrls = this.imgUrls.concat(data);
      this.$emit('on-success', data[0]);
      this.notify();
    },
    notify() {
      const isArrayImg = this.limit > 1 || Array.isArray(this.value);
      this.$emit('input', isArrayImg ? this.imgUrls : this.imgUrls[0] || '');
    }
  }
};
</script>

<style lang="less" scoped>
.app-image-list {
  max-width: 720px;
  min-height: 88px;
}

.image-item {
  float: left;
  margin: 0 8px 8px 0;
  width: 78px;
  height: 78px;
  // border: 1px solid #ddd;
  background-color: #fff;
  position: relative;
  overflow: visible;
  cursor: pointer;
  position: relative;

  &:hover {
    .delete-icon {
      display: block;
    }
  }

  img {
    height: 100%;
    width: 100%;
    object-fit: cover;
  }

  .delete-icon {
    display: none;
    position: absolute;
    width: 20px;
    height: 20px;
    top: -10px;
    right: -10px;
    cursor: pointer;
    background: url(https://img-sn-i01s-cdn.rsjxx.com/backstage/2019/0509/103116_32332.png) no-repeat;
    background-size: 20px 20px;
    background-position: 50%;
  }
}

.no-drag {
  border: none;
  background: #f5f6f9;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  margin-right: 0;
  margin-bottom: 0;
  .upload-v2 {
    display: inline-block;
  }
}
</style>
