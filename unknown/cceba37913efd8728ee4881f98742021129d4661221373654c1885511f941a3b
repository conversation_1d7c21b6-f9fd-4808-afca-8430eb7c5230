
# fpc-pms-center

## Project setup
```
npm install
```

### Compiles and hot-reloads for development
```
npm run serve:test // 测试环境配置
npm run serve:test:74 // 预发布环境配置
npm run serve:prod // 生产环境配置
```

### Compiles and minifies for production
```
npm run build:test // 测试环境配置
npm run build:test：74 // 预发布环境配置
npm run build:prod // 生产环境配置
```

### Customize configuration
See [Configuration Reference](https://cli.vuejs.org/config/).
]()