<template>
  <Modal
      :value="visible"
      :mask-closable=false
      :before-close="cancel"
      @on-cancel="cancel"
      lock-scroll
      width="960"
      :title="title">
    <div class="goods-content">
      <div class="top">
        <div class="flex flex-item-between">
          <div>
            <Button to="/goods/products/list" target="_blank" type="default">货品列表</Button> <span class="margin-left10 cursor aStyle" @click="reload">刷新</span>
          </div>
          <Input class="cursor-wrapper" v-model="queryFormData.name" @keyup.enter.native="onSearch"  icon="ios-search" @on-click="onSearch"  placeholder="输入货品名称" style="width: 40%;minWidth: 100px;" />
        </div>

        <!-- table -->
        <Table :loading="tableLoading" stripe :columns="goodsColumns" :data="list" class="margin-top16" height="424">
          <template slot-scope="{row, index}" slot="box">
            <Checkbox v-model="row.checked" :disabled="row.disabled" @on-change="checkboxEvent(row)"></Checkbox>
          </template>

          <template slot-scope="{row, index}" slot="is_split">
            <span v-if="row.is_split == '1'">是</span>
            <span v-else>否</span>
          </template>

          <template slot-scope="{row, index}" slot="split_price">
            <span v-if="row.is_split == '1'">{{ row.split_price }}</span>
            <span v-else>-</span>
          </template>

        </Table>

        <KPage :total="total"
               class="margin-top16"
               :page-size="queryFormData.pageSize"
               :current="queryFormData.page"
               :page-size-opts="[5,8,20]"
               @on-change="handleCurrentChange"
               @on-page-size-change="handleSizeChange"
               style="text-align:center"
        />
      </div>
    </div>

    <div class="goods-footer" slot="footer">
      已选: 货品({{ checkedList.length || 0 }})
      <Button type="default" @click="cancel" class="margin-left10">取消</Button>
      <Button type="primary" @click="confirm" :loading="confirmLoading">确定</Button>
    </div>
  </Modal>
</template>

<script>
import io from "@/utils/request" // Http request
let init_query_form_data = {
  page: 1,
  pageSize: 8,
  name: '',
  status: 1,
}
export default {
  name: "chooseGoods",
  components: {

  },
  mixins: [],
  props: {
    title: {
      type: String,
      default: '选择货品'
    },
    visible: {
      type: Boolean,
      default: false
    },
    checkedGoods: {
      type: Array,
      default: () => []
    }
  },
  data () {
    return {

      // 分页
      queryFormData: {...init_query_form_data},
      total: 0,
      tableLoading: false,

      confirmLoading: false, // 确定加载loading

      goodsColumns: [
        { title: ' ',slot: 'box', align:'center', width: 50 },
        { title: '货品',key: 'generic_name', align:'center' },
        { title: '类型',key: 'prod_type_text' , align:'center' },
        { title: '包装单位',key: 'prod_unit' , align:'center' },
        { title: '单价',key: 'retail_price' , align:'center' },
        { title: '是否拆零',slot: 'is_split' , align:'center' },
        { title: '拆零价',slot: 'split_price' , align:'center' },
      ],
      list: [],
      checkedIdList: [],
      checkedList: [],
    }
  },
  computed: {

  },
  watch: {
    visible (val) {
      if ( !val ) {
        // Modal关闭,做一些你希望的操作
        this.resetPageData()
      }else {
        this.getList()
      }
    }
  },
  created() {

  },
  mounted() {

  },
  methods: {
    // 属性
    reload () {
      this.resetPageData()
      this.getList()
    },

    resetPageData() {
      this.queryFormData = {...init_query_form_data}
      this.checkedIdList = []
      this.checkedList = []
    },

    handleSizeChange( val ) {
      console.log( '-> val', val )
      this.queryFormData.page = 1
      this.queryFormData.pageSize = val
      this.getList()
    },

    handleCurrentChange( val ) {
      this.queryFormData.page = val
      this.getList()
    },

    // 勾选事件
    checkboxEvent (row) {
      let index = this.checkedIdList.indexOf(row.id)

      console.log('index', index, row);
      if (row.checked) {
        if (index > -1) return
        this.checkedIdList.push(row.id)
        this.checkedList.push(row)
      }else {
        if (index < 0) return
        this.checkedIdList.splice(index,1)
        this.checkedList.splice(index,1)
      }
    },

    // 已勾选的数据回显
    echoChecked () {
      this.checkedList.forEach( checked_item => {
        this.list.forEach( (item, index) =>{
          if (checked_item.id === item.id) {
            this.list[index].checked = true
          }
        } )
      } )
    },

    // 如果外部已经选择了货品,并且手动设置了,那么弹窗里面对应的货品不可再被选中,处于禁选状态
    isDisabledGoodsItem () {
      let goodsIdList = []
      this.checkedGoods.forEach( item => goodsIdList.push(item.prod_id) )

      goodsIdList.forEach( id_item => {
        this.list.forEach( list_item => {
          if (id_item === list_item.id) {
            list_item.disabled = true
            list_item.checked = true
          }
        } )
      } )
    },

    closeDia () {
      this.$emit('update:visible', false)
    },
    cancel () {
      this.closeDia()
    },
    confirm () {
      this.$emit('selectGoods', this.$lodash.cloneDeep(this.checkedList))
      this.closeDia()
    },

    onSearch () {
      this.queryFormData.page = 1
      this.getList()
    },

    handleList (list) {
      list.map( item => {
        item.checked = false
        item.disabled = false
      } )
      return list
    },

    getList () {
      this.tableLoading = true
      let params = {...this.queryFormData}
      this.$api.getProdLibList(params).then(data => {
        this.list = this.handleList(data.list)
        this.total = data.total
        this.statusDesc = data.statusDesc
        this.typeDesc = data.typeDesc

        this.tableLoading = false
        setTimeout( () => {
          this.echoChecked()
        }, 3 )
        this.isDisabledGoodsItem()
      }).catch(error => {
        this.$Message.error(error.errmsg)
      }).finally(() => {
        this.tableLoading = false
      })
    },
  },
  filters: {

  }
}
</script>
<style lang="less" scoped>
.cursor {
  cursor: pointer;
}

.aStyle {
  color: #155BD4;
}

.margin-left10 {
  margin-left: 10px;
}

.margin-top16 {
  margin-top: 16px;
}
::v-deep .cursor-wrapper {
  .ivu-icon-ios-search {
    cursor: pointer;
  }
}
</style>