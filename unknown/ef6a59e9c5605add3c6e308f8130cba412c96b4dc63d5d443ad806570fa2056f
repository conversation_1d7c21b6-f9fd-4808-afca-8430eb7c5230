<template>
  <div class="image-upload-form">
    <Form :model="formData" :label-width="120" class="image-form">
      <FormItem label="本地视频" prop="video" required>
        <video-upload ref="videoUpload" @uploadSuccess="uploadSuccess"></video-upload>
      </FormItem>
      <FormItem label="所在分组" required prop="parent_id">
        <Select ref="categorySelect" v-model="formData.parent_id" style="width: 346px" placeholder="请输入分组名称">
          <Option :value="formData.parent_id || ''" :label="getNodesPath" v-show="false"></Option>
          <category-tree
            :category-tree="categoryTree"
            :current-category-id="formData.parent_id"
            :category-select="selectCategory"
            is-upload
          />
        </Select>
      </FormItem>
    </Form>
    <div class="submit-btn">
      <Button @click="closeModal" :loading="cancelLoading">取消</Button>
      <Button type="primary" :loading="submitLoading" class="ml-16" @click="submitForm">确定</Button>
    </div>
  </div>
</template>

<script>
import CategoryTree from './CategoryTree.vue';
import VideoUpload from './Upload/VideoUpload.vue';
import { getAssetAppKey } from '@/utils/runtime';

export default {
  name: 'VideoUploadForm',
  components: { CategoryTree, VideoUpload },
  props: {
    categoryTree: {
      type: Array,
      default: () => [],
    },
    treeMap: {
      type: Map,
      default: () => null,
    },
    findPathToRootById: {
      type: Function,
      default: () => {},
    },
    currentCategoryId: {
      type: [Number, String],
      default: '',
    },
    storePrefix: {
      type: String,
      default: '',
    },
    assetType: {
      type: Number,
      default: 1,
    },
  },
  data() {
    return {
      formData: {
        parent_id: '',
        upload_type: 1,
        images: [],
      },
      categoryNodes: [],
      uploadTypes: [
        {
          value: 1,
          label: '本地上传',
        },
        {
          value: 2,
          label: '网络图片',
        },
      ],
      qiniuInfo: {
        domain: '',
        scope: '',
        token: '',
      },
      submitLoading: false,
      cancelLoading: false,
    };
  },
  computed: {
    getNodesPath() {
      return this.categoryNodes.map(node => node.name).join('/');
    },
  },
  watch: {
    currentCategoryId: {
      handler(val) {
        if (val) {
          this.formData.parent_id = val;
          this.categoryNodes = this.findPathToRootById(val);
        }
      },
      immediate: true,
    },
  },
  methods: {
    uploadSuccess(data) {
      console.log('%c=>(VideoUploadForm.vue:104) data', 'color: #ECA233;font-size: 16px;', data);
      this.submitLoading = false;
      this.$Message.success('上传成功');
      this.$emit('uploadSuccess', data.domain + '/' + data.key);
      this.closeModal();
    },
    selectCategory({ id }) {
      this.$refs.categorySelect.visible = false;
      this.categoryNodes = this.findPathToRootById(id);
      this.formData.parent_id = id;
      localStorage.setItem(this.storePrefix, id);
    },
    closeModal() {
      this.formData.images = [];
      if (this.$refs.videoUpload.videos?.startUpload === 'start') {
        this.cancelLoading = true;
        this.$refs.videoUpload.cancelUpload();
        setTimeout(() => {
          this.cancelLoading = false;
          this.$emit('close');
        }, 2000);
      } else {
        this.$emit('close');
      }
    },
    submitForm() {
      console.log(this.currentCategoryId);
      const videoUpload = this.$refs.videoUpload;
      if (videoUpload.validateVideo()) {
        this.submitLoading = true;
        this.$refs.videoUpload.handleUpload({
          category_id: this.currentCategoryId,
          appKey: getAssetAppKey(),
          asset_type: 2,
        });
      }
    },
    //获取图片key
  },
};
</script>

<style lang="less" scoped>
.image-upload-form {
  height: 100%;
  width: 100%;
  position: relative;

  .submit-btn {
    position: absolute;
    bottom: 0;
    right: 0;
  }
}
</style>
