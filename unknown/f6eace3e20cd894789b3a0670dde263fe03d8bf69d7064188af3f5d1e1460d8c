import moment from "moment";
import {isEmpty} from "lodash";

const empty = {
    name: 'empty',
    //只调用一次，指令第一次绑定到元素时调用。
    bind: function (el, binding) {
        init(el, binding);
    },
    //被绑定元素插入父节点时调用 (仅保证父节点存在，但不一定已被插入文档中)。
    inserted: function () {},
    // 所在组件的 VNode 更新时调用，但是可能发生在其子 VNode 更新之前。指令的值可能发生了改变，
    // 也可能没有。但是你可以通过比较更新前后的值来忽略不必要的模板更新
    update: function (el, binding) {
        init(el, binding);
    },
    // 指令所在组件的 VNode 及其子 VNode 全部更新后调用。
    componentUpdated: function (el, binding) {
        init(el, binding);
    },
    // 只调用一次，指令与元素解绑时调用。
    unbind: function (el) {
        el.remove()
    }
}
const isTime = (value) => {
    value = value * 1000
    const date = new Date(value)
    return date.getTime() === Number(value)
}
const init = (el, binding) => {
    const { time, money } = binding.modifiers || {}
    let bindingValue = binding.value
    let value = '';
    if (typeof bindingValue === 'object') {
        value = bindingValue.value
    } else {
        value = bindingValue
    }
    if (time && isTime(value)) {
        const format = 'YYYY-MM-DD HH:mm:ss'
        const text =  moment(value*1000).format(bindingValue?.format || format)
        el.innerHTML = text
        return
    }
    if (money && !isEmpty(value)) {
        return;
    }
    if (!isEmpty(value)) {
        el.innerHTML = value
        return;
    }
    el.innerHTML = '-'
}
export default empty