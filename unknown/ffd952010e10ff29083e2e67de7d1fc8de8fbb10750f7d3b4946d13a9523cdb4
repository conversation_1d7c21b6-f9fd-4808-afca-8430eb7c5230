<template>
  <div class="flex" style="margin-top: 1px">
    <Select
      v-if="showType"
      ref="subjectType"
      v-model="ent_type"
      style="width: 80px"
      @on-change="changeEntType"
      placeholder="全部"
      clearable
    >
      <Option v-for="(item, index) in typeList" :key="index" :value="item.value">{{ item.label }}</Option>
    </Select>
    <Select
      ref="subject"
      transfer
      :value="value"
      :clearable="isClearable"
      :loading="searchLoading"
      :remote-method="search"
      filterable
      @on-clear="clearSub"
      @on-query-change="queryChange"
      class="filterable-select"
      :placeholder="placeholder"
      @on-select="selectSup"
    >
      <!--      <Option value="" v-show="showAll">全部</Option>-->
      <Option v-for="(option, index) in subject_list" :key="index" :value="option.ent_code" :label="option.ent_name">
        <span>{{ option.ent_name }}</span>
      </Option>
    </Select>
  </div>
</template>

<script>
import util from '@/utils/util';
export default {
  name: 'subject-search',
  components: {},
  mixins: [],
  props: {
    isClearable: {
      type: Boolean,
      default: true,
    },
    value: {
      type: String,
      default: '',
    },
    showAll: {
      type: Boolean,
      default: true,
    },
    type: {
      type: String,
      default: '',
    },
    placeholder: {
      type: String,
      default: '请输入收款方',
    },
    // 展示采购主体类型，目前只开放采购单，后续全部放开可移除该属性
    showType: {
      type: Boolean,
      default: false,
    },
    p_ent_type: {
      type: String,
      default: ''
    },
    showOutside: {
      type: Boolean,
      default: false,
    },
    typeList: {
      type: Array,
      default: () => {
        return [
          { label: '省公司', value: 'COM' },
          { label: '上海树家', value: 'PLAT' },
        ]
      }
    },
    // 默认补充参数
    default_params: {
      type: Object,
      default: () => {}
    },
    // 默认搜索字段，name支持搜索名称，nameId 支持名称+id搜索
    searchKeyword: {
      type: String,
      default: 'name'
    },
    // 外部绑定的字段
    routerKey: {
      type: String,
      default: 'payee_id'
    },
    // 存储在localStoage里面的值，用于刷新回显
    localStorageListName: {
      type: String,
      default: 'subject_list'
    },
  },
  data() {
    return {
      searchLoading: false,
      subject_list: [],
      query: '',
      ent_type: '',
      time: 0,
    };
  },
  computed: {},
  watch: {
    p_ent_type: {
      handler: function (val, oldVal) {
        this.ent_type = val;
      },
      immediate: true
    }
  },
  created() {
    setTimeout(() => {
      this.time = 200
    }, 10)

    if (!this.$route.query[this.routerKey]) {
      // 12.27 兼容诊所搜索
      this.searchMethod();
    } else {
      let list = JSON.parse(localStorage.getItem([this.localStorageListName])) || [];
      this.subject_list = list;
      this.$emit('input', list[0] && list[0].ent_code);
      this.$emit('getType', list[0] && list[0].ent_type);
    }
  },
  mounted() {},
  methods: {
    searchMethod (query) {
      this.time == 0 ? this.unDebounceSearchMethod(query) : this.debounceSearchMethod(query)
    },
    debounceSearchMethod: util.debounce(function (query) {
      this.searchLoading = true;
      let params = {
        [this.searchKeyword]: query || '',
        ent_type: this.ent_type || this.type,
        ...this.default_params
      };
      this.$api.searchAWSearchSide(params).then(res => {
        this.searchLoading = false;
        this.subject_list = res.list;
        localStorage.setItem([this.localStorageListName], JSON.stringify(this.subject_list));
      });
    }, 200),
    unDebounceSearchMethod (query) {
      this.searchLoading = true;
      let params = {
        [this.searchKeyword]: query || '',
        ent_type: this.ent_type || this.type,
        company_id: this.company_id,
        fill_outside: this.showOutside ? 1 : '',
        ...this.default_params
      };
      this.$api.searchAWSearchSide(params).then(res => {
        this.searchLoading = false;
        this.subject_list = res.list;
        localStorage.setItem([this.localStorageListName], JSON.stringify(this.subject_list));
      });
    },
    search() {},
    selectSup(val) {
      let type = '';
      this.subject_list.some(item => {
        if (item.ent_code == val.value) {
          type = item.ent_type;
        }
      });
      this.$emit('input', val.value);
      this.$emit('getType', this.ent_type || type);
    },
    queryChange(val) {
      this.searchMethod(val);
    },
    clear() {
      this.$refs['subject'].clearSingleSelect();
      this.ent_type = '';
    },
    clearSub() {
      this.searchMethod();
      this.$emit('input', '');
      this.$emit('getType', '');
    },
    changeEntType() {
      this.$emit('input', '');
      this.$emit('getType', this.ent_type);
      this.searchMethod();
    },
    clearEntType() {
      this.ent_type = '';
    },
  },
};
</script>

<style lang="less" scoped>
.filterable-select {
  ::v-deep .ivu-select-input {
    margin-top: -1px;
  }
}
.disabled-tag {
  color: #ccc;
  margin-left: 10px;
}
</style>
